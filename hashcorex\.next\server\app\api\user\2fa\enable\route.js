"use strict";(()=>{var e={};e.id=8275,e.ids=[1111,8275],e.modules={812:(e,t,r)=>{r.d(t,{SL:()=>n});var a=r(31183),i=r(6710),s=r(21111);class n{static async isEnabled(e){let t=await a.prisma.user.findUnique({where:{id:e},select:{twoFactorEnabled:!0}});return t?.twoFactorEnabled||!1}static async getStatus(e){let t=await a.prisma.user.findUnique({where:{id:e},select:{twoFactorEnabled:!0,twoFactorEnabledAt:!0}});return{enabled:t?.twoFactorEnabled||!1,enabledAt:t?.twoFactorEnabledAt||void 0}}static async enableTwoFactor(e,t,r){let s=await a.prisma.user.findUnique({where:{id:e},select:{id:!0,email:!0,firstName:!0,twoFactorEnabled:!0}});return s?s.twoFactorEnabled?{success:!1,error:"2FA is already enabled"}:(await a.prisma.user.update({where:{id:e},data:{twoFactorEnabled:!0,twoFactorEnabledAt:new Date}}),await i.AJ.create({action:"EMAIL_TWO_FACTOR_ENABLED",userId:e,details:{email:s.email,enabledAt:new Date().toISOString()},ipAddress:t||"unknown",userAgent:r||"unknown"}),{success:!0}):{success:!1,error:"User not found"}}static async disableTwoFactor(e,t,r){let s=await a.prisma.user.findUnique({where:{id:e},select:{id:!0,email:!0,twoFactorEnabled:!0}});return s?s.twoFactorEnabled?(await a.prisma.user.update({where:{id:e},data:{twoFactorEnabled:!1,twoFactorEnabledAt:null}}),await i.AJ.create({action:"EMAIL_TWO_FACTOR_DISABLED",userId:e,details:{email:s.email,disabledAt:new Date().toISOString()},ipAddress:t||"unknown",userAgent:r||"unknown"}),{success:!0}):{success:!1,error:"2FA is not enabled"}:{success:!1,error:"User not found"}}static async sendTwoFactorOTP(e,t,r){let n=await a.prisma.user.findUnique({where:{id:e},select:{id:!0,email:!0,firstName:!0,twoFactorEnabled:!0}});if(!n)return{success:!1,error:"User not found"};if(!n.twoFactorEnabled)return{success:!1,error:"2FA is not enabled for this user"};let o=(0,s.X)(),l=new Date;l.setMinutes(l.getMinutes()+10),await i.oV.create({email:n.email,otp:o,purpose:"two_factor_auth",expiresAt:l});try{if(await s.emailService.sendOTPEmail(n.email,o,n.firstName,"two_factor_auth"))return await i.AJ.create({action:"EMAIL_TWO_FACTOR_OTP_SENT",userId:e,details:{email:n.email,sentAt:new Date().toISOString(),expiresAt:l.toISOString()},ipAddress:t||"unknown",userAgent:r||"unknown"}),{success:!0,expiresAt:l};return{success:!1,error:"Failed to send 2FA code. Please try again."}}catch(e){return console.error("Failed to send 2FA OTP:",e),{success:!1,error:"Failed to send 2FA code. Please try again."}}}static async verifyTwoFactorOTP(e,t,r,s){let n=await a.prisma.user.findUnique({where:{id:e},select:{id:!0,email:!0,twoFactorEnabled:!0}});if(!n||!n.twoFactorEnabled)return{valid:!1};let o=await i.oV.findValid(n.email,"two_factor_auth");return o?o.otp!==t?(await i.AJ.create({action:"EMAIL_TWO_FACTOR_VERIFICATION_FAILED",userId:e,details:{reason:"Invalid OTP",failedAt:new Date().toISOString()},ipAddress:r||"unknown",userAgent:s||"unknown"}),{valid:!1}):(await i.oV.markAsVerified(o.id),await i.AJ.create({action:"EMAIL_TWO_FACTOR_VERIFIED",userId:e,details:{email:n.email,verifiedAt:new Date().toISOString()},ipAddress:r||"unknown",userAgent:s||"unknown"}),{valid:!0}):(await i.AJ.create({action:"EMAIL_TWO_FACTOR_VERIFICATION_FAILED",userId:e,details:{reason:"No valid OTP found",failedAt:new Date().toISOString()},ipAddress:r||"unknown",userAgent:s||"unknown"}),{valid:!1})}static async requiresTwoFactorVerification(e){return await this.isEnabled(e)}static async cleanupExpiredOTPs(){try{return(await a.prisma.oTP.deleteMany({where:{purpose:"two_factor_auth",expiresAt:{lt:new Date}}})).count}catch(e){return console.error("Failed to cleanup expired 2FA OTPs:",e),0}}}setInterval(async function e(){let e=await n.cleanupExpiredOTPs();e>0&&console.log(`Cleaned up ${e} expired 2FA OTPs`)},18e5)},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},21111:(e,t,r)=>{r.d(t,{X:()=>o,emailService:()=>n});var a=r(49526),i=r(6710);class s{async getEmailConfig(){try{let e=await i.T8.getEmailSettings();if(!e||!e.smtpHost||!e.smtpUser||!e.smtpPassword)return console.warn("Email configuration not found or incomplete"),null;return{host:e.smtpHost,port:e.smtpPort||587,secure:e.smtpSecure||!1,user:e.smtpUser,password:e.smtpPassword,fromName:e.fromName||"HashCoreX",fromEmail:e.fromEmail||e.smtpUser}}catch(e){return console.error("Failed to get email configuration:",e),null}}async initializeTransporter(e=!1){try{if(this.config=await this.getEmailConfig(),!this.config)return console.warn("Email configuration not available - email service disabled"),!1;if(!this.config.host||!this.config.user||!this.config.password)return console.error("Email configuration incomplete - missing host, user, or password"),!1;if(this.transporter=a.createTransport({host:this.config.host,port:this.config.port,secure:this.config.secure,auth:{user:this.config.user,pass:this.config.password},tls:{rejectUnauthorized:!1},connectionTimeout:1e4,greetingTimeout:5e3,socketTimeout:1e4}),e)console.log("Email transporter initialized (verification skipped)");else{let e=this.transporter.verify(),t=new Promise((e,t)=>setTimeout(()=>t(Error("Email verification timeout")),15e3));await Promise.race([e,t]),console.log("Email transporter initialized and verified successfully")}return!0}catch(e){return console.error("Failed to initialize email transporter:",e),this.transporter=null,this.config=null,!1}}async sendEmail(e){try{if((!this.transporter||!this.config)&&(console.log("Email transporter not initialized, attempting to initialize..."),!await this.initializeTransporter(!0)))return console.warn("Email service not configured - skipping email send"),!1;if(!e.to||!e.subject)return console.error("Invalid email data - missing recipient or subject"),!1;let t={from:`"${this.config.fromName}" <${this.config.fromEmail}>`,to:e.to,subject:e.subject,html:e.html,text:e.text},r=this.transporter.sendMail(t),a=new Promise((e,t)=>setTimeout(()=>t(Error("Email send timeout")),3e4)),i=await Promise.race([r,a]);return console.log("Email sent successfully:",i.messageId),!0}catch(e){return console.error("Failed to send email:",e),this.transporter=null,this.config=null,!1}}async sendOTPEmail(e,t,r,a="email_verification"){let i="otp_verification";"password_reset"===a?i="password_reset_otp":"two_factor_auth"===a&&(i="two_factor_otp");let s=await this.getEmailTemplate(i);if(!s)return console.error(`Email template '${i}' not found. Please ensure email templates are seeded.`),!1;let n=s.htmlContent,o=s.textContent||"";return n=(n=n.replace(/{{firstName}}/g,r||"User")).replace(/{{otp}}/g,t),o=(o=o.replace(/{{firstName}}/g,r||"User")).replace(/{{otp}}/g,t),await this.sendEmail({to:e,subject:s.subject,html:n,text:o})}async getEmailTemplate(e){try{return await i.T8.getEmailTemplate(e)}catch(e){return console.error("Failed to get email template:",e),null}}async testConnection(){try{if(this.config=await this.getEmailConfig(),!this.config)return console.error("Email configuration not found or incomplete"),!1;return this.transporter=a.createTransport({host:this.config.host,port:this.config.port,secure:this.config.secure,auth:{user:this.config.user,pass:this.config.password},tls:{rejectUnauthorized:!1}}),await this.transporter.verify(),console.log("Email connection test successful"),!0}catch(e){throw console.error("Email connection test failed:",e),this.transporter=null,e}}constructor(){this.transporter=null,this.config=null}}let n=new s,o=()=>Math.floor(1e5+9e5*Math.random()).toString()},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37366:e=>{e.exports=require("dns")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94038:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>m,serverHooks:()=>h,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>f});var a={};r.r(a),r.d(a,{POST:()=>p});var i=r(96559),s=r(48088),n=r(37719),o=r(32190),l=r(39542),c=r(812),u=r(82180);let d=async e=>{let{authenticated:t,user:r}=await (0,l.b9)(e);if(!t||!r){let e=(0,u.pz)("Authentication required");return(0,u.N1)(e)}let a=await c.SL.enableTwoFactor(r.id,e.headers.get("x-forwarded-for")||"unknown",e.headers.get("user-agent")||"unknown");if(!a.success)return o.NextResponse.json({success:!1,error:a.error},{status:400});let i=o.NextResponse.json({success:!0,message:"Two-factor authentication enabled successfully",data:{enabled:!0,method:"email",enabledAt:new Date().toISOString()}});return i.headers.set("X-Content-Type-Options","nosniff"),i.headers.set("X-Frame-Options","DENY"),i.headers.set("X-XSS-Protection","1; mode=block"),i},p=(0,u.nC)(d,{endpoint:"/api/user/2fa/enable",requireAuth:!0}),m=new i.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/user/2fa/enable/route",pathname:"/api/user/2fa/enable",filename:"route",bundlePath:"app/api/user/2fa/enable/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\user\\2fa\\enable\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:w,workUnitAsyncStorage:f,serverHooks:h}=m;function g(){return(0,n.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:f})}},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580,7911,9526,925,5033],()=>r(94038));module.exports=a})();