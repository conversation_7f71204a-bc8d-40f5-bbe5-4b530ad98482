/**
 * Data Encryption System
 * Implements AES-256-GCM encryption for sensitive data at rest
 */

import crypto from 'crypto';
import { config } from './envValidation';

// Encryption configuration
const ALGORITHM = 'aes-256-gcm';
const KEY_LENGTH = 32; // 256 bits
const IV_LENGTH = 16; // 128 bits
const TAG_LENGTH = 16; // 128 bits
const SALT_LENGTH = 32; // 256 bits

// Encryption result interface
interface EncryptionResult {
  encrypted: string;
  iv: string;
  tag: string;
  salt: string;
}

// Decryption input interface
interface DecryptionInput {
  encrypted: string;
  iv: string;
  tag: string;
  salt: string;
}

// Field encryption class
export class FieldEncryption {
  private static masterKey: Buffer | null = null;

  // Initialize master key from environment
  private static getMasterKey(): Buffer {
    if (!this.masterKey) {
      const keyString = process.env.ENCRYPTION_KEY || process.env.JWT_SECRET;
      if (!keyString) {
        throw new Error('ENCRYPTION_KEY or JWT_SECRET environment variable is required');
      }
      
      // Derive a consistent key from the secret
      this.masterKey = crypto.scryptSync(keyString, 'hashcorex-salt', KEY_LENGTH);
    }
    return this.masterKey;
  }

  // Encrypt sensitive data
  static encrypt(data: string): string {
    try {
      if (!data || data.trim() === '') {
        return data; // Don't encrypt empty strings
      }

      const masterKey = this.getMasterKey();
      
      // Generate random salt and IV
      const salt = crypto.randomBytes(SALT_LENGTH);
      const iv = crypto.randomBytes(IV_LENGTH);
      
      // Derive key from master key and salt
      const key = crypto.scryptSync(masterKey, salt, KEY_LENGTH);
      
      // Create cipher
      const cipher = crypto.createCipher(ALGORITHM, key);
      cipher.setAAD(salt); // Additional authenticated data
      
      // Encrypt data
      let encrypted = cipher.update(data, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      // Get authentication tag
      const tag = cipher.getAuthTag();
      
      // Combine all components
      const result: EncryptionResult = {
        encrypted,
        iv: iv.toString('hex'),
        tag: tag.toString('hex'),
        salt: salt.toString('hex'),
      };
      
      // Return as base64 encoded JSON
      return Buffer.from(JSON.stringify(result)).toString('base64');
      
    } catch (error) {
      console.error('Encryption error:', error);
      throw new Error('Failed to encrypt data');
    }
  }

  // Decrypt sensitive data
  static decrypt(encryptedData: string): string {
    try {
      if (!encryptedData || encryptedData.trim() === '') {
        return encryptedData; // Return empty strings as-is
      }

      // Check if data is already decrypted (backward compatibility)
      if (!this.isEncrypted(encryptedData)) {
        return encryptedData;
      }

      const masterKey = this.getMasterKey();
      
      // Parse encrypted data
      const dataString = Buffer.from(encryptedData, 'base64').toString('utf8');
      const data: DecryptionInput = JSON.parse(dataString);
      
      // Convert hex strings back to buffers
      const salt = Buffer.from(data.salt, 'hex');
      const iv = Buffer.from(data.iv, 'hex');
      const tag = Buffer.from(data.tag, 'hex');
      
      // Derive key from master key and salt
      const key = crypto.scryptSync(masterKey, salt, KEY_LENGTH);
      
      // Create decipher
      const decipher = crypto.createDecipher(ALGORITHM, key);
      decipher.setAAD(salt); // Additional authenticated data
      decipher.setAuthTag(tag);
      
      // Decrypt data
      let decrypted = decipher.update(data.encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      return decrypted;
      
    } catch (error) {
      console.error('Decryption error:', error);
      // Return original data if decryption fails (backward compatibility)
      return encryptedData;
    }
  }

  // Check if data is encrypted
  static isEncrypted(data: string): boolean {
    try {
      if (!data || data.trim() === '') {
        return false;
      }
      
      // Try to parse as base64 JSON
      const decoded = Buffer.from(data, 'base64').toString('utf8');
      const parsed = JSON.parse(decoded);
      
      // Check if it has the expected structure
      return (
        typeof parsed === 'object' &&
        typeof parsed.encrypted === 'string' &&
        typeof parsed.iv === 'string' &&
        typeof parsed.tag === 'string' &&
        typeof parsed.salt === 'string'
      );
    } catch {
      return false;
    }
  }

  // Encrypt email addresses
  static encryptEmail(email: string): string {
    if (!email || !email.includes('@')) {
      return email;
    }
    return this.encrypt(email.toLowerCase());
  }

  // Decrypt email addresses
  static decryptEmail(encryptedEmail: string): string {
    const decrypted = this.decrypt(encryptedEmail);
    return decrypted.toLowerCase();
  }

  // Encrypt wallet addresses
  static encryptWalletAddress(address: string): string {
    if (!address || address.length < 10) {
      return address;
    }
    return this.encrypt(address);
  }

  // Decrypt wallet addresses
  static decryptWalletAddress(encryptedAddress: string): string {
    return this.decrypt(encryptedAddress);
  }

  // Encrypt personal information
  static encryptPersonalInfo(info: string): string {
    if (!info || info.trim().length < 2) {
      return info;
    }
    return this.encrypt(info.trim());
  }

  // Decrypt personal information
  static decryptPersonalInfo(encryptedInfo: string): string {
    return this.decrypt(encryptedInfo);
  }

  // Hash sensitive data for searching (one-way)
  static hashForSearch(data: string): string {
    if (!data || data.trim() === '') {
      return data;
    }
    
    const masterKey = this.getMasterKey();
    return crypto.createHmac('sha256', masterKey)
      .update(data.toLowerCase().trim())
      .digest('hex');
  }

  // Encrypt file paths
  static encryptFilePath(filePath: string): string {
    if (!filePath || !filePath.includes('/')) {
      return filePath;
    }
    return this.encrypt(filePath);
  }

  // Decrypt file paths
  static decryptFilePath(encryptedFilePath: string): string {
    return this.decrypt(encryptedFilePath);
  }
}

// Database field encryption helpers
export class DatabaseEncryption {
  
  // Encrypt user data before saving
  static encryptUserData(userData: any): any {
    const encrypted = { ...userData };
    
    if (encrypted.email) {
      encrypted.email = FieldEncryption.encryptEmail(encrypted.email);
    }
    
    if (encrypted.firstName) {
      encrypted.firstName = FieldEncryption.encryptPersonalInfo(encrypted.firstName);
    }
    
    if (encrypted.lastName) {
      encrypted.lastName = FieldEncryption.encryptPersonalInfo(encrypted.lastName);
    }
    
    if (encrypted.withdrawalAddress) {
      encrypted.withdrawalAddress = FieldEncryption.encryptWalletAddress(encrypted.withdrawalAddress);
    }
    
    return encrypted;
  }

  // Decrypt user data after retrieval
  static decryptUserData(userData: any): any {
    if (!userData) return userData;
    
    const decrypted = { ...userData };
    
    if (decrypted.email) {
      decrypted.email = FieldEncryption.decryptEmail(decrypted.email);
    }
    
    if (decrypted.firstName) {
      decrypted.firstName = FieldEncryption.decryptPersonalInfo(decrypted.firstName);
    }
    
    if (decrypted.lastName) {
      decrypted.lastName = FieldEncryption.decryptPersonalInfo(decrypted.lastName);
    }
    
    if (decrypted.withdrawalAddress) {
      decrypted.withdrawalAddress = FieldEncryption.decryptWalletAddress(decrypted.withdrawalAddress);
    }
    
    return decrypted;
  }

  // Encrypt KYC document data
  static encryptKYCData(kycData: any): any {
    const encrypted = { ...kycData };
    
    if (encrypted.filePath) {
      encrypted.filePath = FieldEncryption.encryptFilePath(encrypted.filePath);
    }
    
    return encrypted;
  }

  // Decrypt KYC document data
  static decryptKYCData(kycData: any): any {
    if (!kycData) return kycData;
    
    const decrypted = { ...kycData };
    
    if (decrypted.filePath) {
      decrypted.filePath = FieldEncryption.decryptFilePath(decrypted.filePath);
    }
    
    return decrypted;
  }
}

// Utility functions for migration
export class EncryptionMigration {
  
  // Migrate existing data to encrypted format
  static async migrateUserData(): Promise<void> {
    // This would be used in a migration script
    console.log('Data encryption migration would be implemented here');
    console.log('This should be run as a separate migration script');
  }

  // Check if field needs encryption
  static needsEncryption(data: string): boolean {
    return !FieldEncryption.isEncrypted(data);
  }
}

export default FieldEncryption;
