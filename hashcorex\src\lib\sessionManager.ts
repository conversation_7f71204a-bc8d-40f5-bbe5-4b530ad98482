/**
 * Advanced Session Management System
 * Handles session invalidation, device tracking, and security events
 */

import { prisma } from './prisma';
import { systemLogDb } from './database';

// Session interface
interface UserSession {
  id: string;
  userId: string;
  deviceId: string;
  deviceName?: string;
  ipAddress: string;
  userAgent: string;
  isActive: boolean;
  lastActivity: Date;
  expiresAt: Date;
  createdAt: Date;
}

// Device fingerprint interface
interface DeviceFingerprint {
  userAgent: string;
  ipAddress: string;
  acceptLanguage?: string;
  acceptEncoding?: string;
  deviceId: string;
}

// Session management class
export class SessionManager {
  
  // Create a new session
  static async createSession(
    userId: string,
    deviceFingerprint: DeviceFingerprint,
    expiresIn: number = 30 * 24 * 60 * 60 * 1000 // 30 days
  ): Promise<string> {
    const sessionId = this.generateSessionId();
    const expiresAt = new Date(Date.now() + expiresIn);
    
    // Check if session already exists for this device
    const existingSession = await prisma.userSession.findFirst({
      where: {
        userId,
        deviceId: deviceFingerprint.deviceId,
        isActive: true,
      },
    });

    if (existingSession) {
      // Update existing session
      await prisma.userSession.update({
        where: { id: existingSession.id },
        data: {
          lastActivity: new Date(),
          expiresAt,
          ipAddress: deviceFingerprint.ipAddress,
          userAgent: deviceFingerprint.userAgent,
        },
      });
      
      return existingSession.id;
    }

    // Create new session
    const session = await prisma.userSession.create({
      data: {
        id: sessionId,
        userId,
        deviceId: deviceFingerprint.deviceId,
        deviceName: this.extractDeviceName(deviceFingerprint.userAgent),
        ipAddress: deviceFingerprint.ipAddress,
        userAgent: deviceFingerprint.userAgent,
        isActive: true,
        lastActivity: new Date(),
        expiresAt,
      },
    });

    // Log session creation
    await systemLogDb.create({
      action: 'SESSION_CREATED',
      userId,
      details: {
        sessionId: session.id,
        deviceId: deviceFingerprint.deviceId,
        deviceName: session.deviceName,
        ipAddress: deviceFingerprint.ipAddress,
      },
      ipAddress: deviceFingerprint.ipAddress,
      userAgent: deviceFingerprint.userAgent,
    });

    return session.id;
  }

  // Validate and update session
  static async validateSession(sessionId: string, deviceFingerprint: DeviceFingerprint): Promise<UserSession | null> {
    const session = await prisma.userSession.findFirst({
      where: {
        id: sessionId,
        isActive: true,
        expiresAt: { gt: new Date() },
      },
    });

    if (!session) {
      return null;
    }

    // Check device fingerprint for security
    if (session.deviceId !== deviceFingerprint.deviceId) {
      await this.invalidateSession(sessionId, 'DEVICE_MISMATCH');
      return null;
    }

    // Update last activity
    await prisma.userSession.update({
      where: { id: sessionId },
      data: {
        lastActivity: new Date(),
        ipAddress: deviceFingerprint.ipAddress, // Update IP if changed
      },
    });

    return session;
  }

  // Invalidate a specific session
  static async invalidateSession(sessionId: string, reason: string): Promise<void> {
    const session = await prisma.userSession.findUnique({
      where: { id: sessionId },
    });

    if (session) {
      await prisma.userSession.update({
        where: { id: sessionId },
        data: { isActive: false },
      });

      // Log session invalidation
      await systemLogDb.create({
        action: 'SESSION_INVALIDATED',
        userId: session.userId,
        details: {
          sessionId,
          reason,
          invalidatedAt: new Date().toISOString(),
        },
        ipAddress: session.ipAddress,
        userAgent: session.userAgent,
      });
    }
  }

  // Invalidate all sessions for a user (except current)
  static async invalidateAllUserSessions(userId: string, exceptSessionId?: string): Promise<void> {
    const whereClause: any = {
      userId,
      isActive: true,
    };

    if (exceptSessionId) {
      whereClause.id = { not: exceptSessionId };
    }

    const sessions = await prisma.userSession.findMany({
      where: whereClause,
    });

    if (sessions.length > 0) {
      await prisma.userSession.updateMany({
        where: whereClause,
        data: { isActive: false },
      });

      // Log mass session invalidation
      await systemLogDb.create({
        action: 'ALL_SESSIONS_INVALIDATED',
        userId,
        details: {
          invalidatedSessions: sessions.length,
          exceptSessionId: exceptSessionId || null,
          reason: 'SECURITY_EVENT',
          invalidatedAt: new Date().toISOString(),
        },
      });
    }
  }

  // Invalidate sessions on security events
  static async invalidateOnSecurityEvent(
    userId: string, 
    event: 'PASSWORD_CHANGE' | 'EMAIL_CHANGE' | 'ROLE_CHANGE' | 'ACCOUNT_COMPROMISE',
    currentSessionId?: string
  ): Promise<void> {
    await this.invalidateAllUserSessions(userId, currentSessionId);

    // Log security event
    await systemLogDb.create({
      action: 'SECURITY_EVENT_SESSION_INVALIDATION',
      userId,
      details: {
        event,
        currentSessionPreserved: !!currentSessionId,
        timestamp: new Date().toISOString(),
      },
    });
  }

  // Get active sessions for a user
  static async getUserActiveSessions(userId: string): Promise<UserSession[]> {
    return await prisma.userSession.findMany({
      where: {
        userId,
        isActive: true,
        expiresAt: { gt: new Date() },
      },
      orderBy: { lastActivity: 'desc' },
    });
  }

  // Clean up expired sessions
  static async cleanupExpiredSessions(): Promise<number> {
    const result = await prisma.userSession.updateMany({
      where: {
        OR: [
          { expiresAt: { lt: new Date() } },
          { lastActivity: { lt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) } }, // 7 days inactive
        ],
        isActive: true,
      },
      data: { isActive: false },
    });

    return result.count;
  }

  // Generate secure session ID
  private static generateSessionId(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < 64; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  // Extract device name from user agent
  private static extractDeviceName(userAgent: string): string {
    // Simple device detection
    if (userAgent.includes('Mobile')) return 'Mobile Device';
    if (userAgent.includes('Tablet')) return 'Tablet';
    if (userAgent.includes('Windows')) return 'Windows PC';
    if (userAgent.includes('Macintosh')) return 'Mac';
    if (userAgent.includes('Linux')) return 'Linux PC';
    return 'Unknown Device';
  }

  // Generate device fingerprint
  static generateDeviceFingerprint(
    userAgent: string,
    ipAddress: string,
    acceptLanguage?: string,
    acceptEncoding?: string
  ): DeviceFingerprint {
    // Create a simple device ID based on user agent and other factors
    const deviceString = `${userAgent}-${acceptLanguage || ''}-${acceptEncoding || ''}`;
    const deviceId = this.hashString(deviceString);

    return {
      userAgent,
      ipAddress,
      acceptLanguage,
      acceptEncoding,
      deviceId,
    };
  }

  // Simple hash function for device ID
  private static hashString(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  // Check for suspicious activity
  static async checkSuspiciousActivity(userId: string): Promise<{
    suspicious: boolean;
    reasons: string[];
  }> {
    const reasons: string[] = [];
    
    // Check for multiple active sessions from different IPs
    const activeSessions = await this.getUserActiveSessions(userId);
    const uniqueIPs = new Set(activeSessions.map(s => s.ipAddress));
    
    if (uniqueIPs.size > 3) {
      reasons.push('Multiple IP addresses detected');
    }

    // Check for rapid session creation
    const recentSessions = await prisma.userSession.findMany({
      where: {
        userId,
        createdAt: { gt: new Date(Date.now() - 60 * 60 * 1000) }, // Last hour
      },
    });

    if (recentSessions.length > 5) {
      reasons.push('Rapid session creation detected');
    }

    return {
      suspicious: reasons.length > 0,
      reasons,
    };
  }
}

// Cleanup job - should be run periodically
export async function cleanupExpiredSessions(): Promise<void> {
  const cleaned = await SessionManager.cleanupExpiredSessions();
  console.log(`Cleaned up ${cleaned} expired sessions`);
}

// Start cleanup interval (run every hour)
if (typeof window === 'undefined') { // Server-side only
  setInterval(cleanupExpiredSessions, 60 * 60 * 1000); // 1 hour
}
