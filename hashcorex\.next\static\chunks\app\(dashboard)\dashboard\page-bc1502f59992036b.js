(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6337],{2612:(e,s,t)=>{Promise.resolve().then(t.bind(t,6365))},4105:(e,s,t)=>{"use strict";t.d(s,{A:()=>l,AuthProvider:()=>i});var a=t(5155),r=t(2115);let n=(0,r.createContext)(void 0),i=e=>{let{children:s}=e,[t,i]=(0,r.useState)(null),[l,c]=(0,r.useState)(!0);(0,r.useEffect)(()=>{d()},[]);let d=async()=>{try{let e=await fetch("/api/auth/me",{credentials:"include"});if(e.ok){let s=await e.json();s.success&&i(s.data)}}catch(e){console.error("Auth check failed:",e)}finally{c(!1)}},o=async(e,s)=>{c(!0);try{let t=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e,password:s})}),a=await t.json();if(!a.success)throw Error(a.error||"Login failed");i(a.data.user),setTimeout(()=>{d()},100)}catch(e){throw e}finally{c(!1)}},x=async(e,s,t,a,r,n,l,d)=>{c(!0);try{let c=await fetch(l?"/api/auth/register?side=".concat(l):"/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,firstName:s,lastName:t,password:a,confirmPassword:r,referralCode:n,otp:d})}),o=await c.json();if(!o.success)throw Error(o.error||"Registration failed");i({id:o.data.user.id,email:o.data.user.email,firstName:o.data.user.firstName||"",lastName:o.data.user.lastName||"",referralId:o.data.user.referralId,role:o.data.user.role||"USER",kycStatus:o.data.user.kycStatus,isActive:o.data.user.isActive||!0,profilePicture:o.data.user.profilePicture||null,createdAt:o.data.user.createdAt,updatedAt:o.data.user.updatedAt})}catch(e){throw e}finally{c(!1)}},m=async()=>{try{await fetch("/api/auth/logout",{method:"POST",credentials:"include"})}catch(e){console.error("Logout error:",e)}finally{i(null)}},h=async()=>{await d()};return(0,a.jsx)(n.Provider,{value:{user:t,loading:l,login:o,register:x,logout:m,refreshUser:h},children:s})},l=()=>{let e=(0,r.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},6365:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>eR});var a=t(5155),r=t(2115),n=t(4105),i=t(5695),l=t(6874),c=t.n(l),d=t(6660),o=t(8505),x=t(7508),m=t(3783),h=t(1539),u=t(3109),p=t(9785),g=t(7580),j=t(5525),f=t(1366),y=t(381),N=t(4416),b=t(4835),v=t(4783),w=t(3861),A=t(6474),k=t(1007),E=t(5339);let S=e=>{var s,t,l;let{children:S,activeTab:C,onTabChange:T}=e,{user:P,logout:R}=(0,n.A)(),I=(0,i.useRouter)(),[D,F]=(0,r.useState)(!1),[M,O]=(0,r.useState)(!1),[U,W]=(0,r.useState)(!1),B=(0,r.useRef)(null),Z=[{id:"overview",label:"Overview",icon:m.A},{id:"mining",label:"Mining Units",icon:h.A},{id:"earnings",label:"Earnings",icon:u.A},{id:"wallet",label:"Wallet",icon:p.A},{id:"referrals",label:"Network",icon:g.A},{id:"kyc",label:"KYC Verification",icon:j.A},{id:"support",label:"Support",icon:f.A}],L=(null==P?void 0:P.role)==="ADMIN"?[...Z,{id:"admin",label:"Admin Panel",icon:y.A}]:Z,_=async()=>{await R()},H=e=>{"admin"===e?I.push("/admin"):(T(e),F(!1))};return(0,r.useEffect)(()=>{let e=e=>{B.current&&!B.current.contains(e.target)&&W(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]),(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 flex",children:[D&&(0,a.jsx)("div",{className:"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden",onClick:()=>F(!1)}),(0,a.jsx)("aside",{className:"\n        fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-xl border-r border-gray-200\n        transform transition-all duration-300 ease-in-out\n        ".concat(D?"translate-x-0":"-translate-x-full lg:translate-x-0","\n      "),children:(0,a.jsxs)("div",{className:"flex flex-col h-screen",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between h-14 px-5 border-b border-gray-200 bg-white flex-shrink-0",children:[(0,a.jsxs)(c(),{href:"/",className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-yellow-500 rounded-lg flex items-center justify-center",children:(0,a.jsx)(x.MX,{className:"h-5 w-5 text-white"})}),(0,a.jsx)("span",{className:"text-lg font-bold text-gray-900",children:"HashCoreX"})]}),(0,a.jsx)("button",{onClick:()=>F(!1),className:"lg:hidden p-1.5 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors",children:(0,a.jsx)(N.A,{className:"h-4 w-4"})})]}),(0,a.jsx)("nav",{className:"flex-1 px-3 py-4 space-y-1 min-h-0",children:L.map(e=>{let s=e.icon,t=C===e.id;return(0,a.jsxs)("button",{onClick:()=>H(e.id),className:"\n                    w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-left transition-all duration-200 group\n                    ".concat(t?"bg-yellow-500 text-white shadow-md":"text-gray-700 hover:bg-gray-100 hover:text-gray-900","\n                  "),children:[(0,a.jsx)(s,{className:"h-4 w-4 ".concat(t?"text-white":"text-gray-500 group-hover:text-gray-700")}),(0,a.jsx)("span",{className:"font-medium text-sm",children:e.label})]},e.id)})}),(0,a.jsx)("div",{className:"px-3 py-3 border-t border-gray-200 bg-gray-50 flex-shrink-0",children:(0,a.jsxs)("button",{onClick:_,className:"w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-gray-600 hover:bg-red-50 hover:text-red-600 transition-all duration-200 group",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 group-hover:text-red-600"}),(0,a.jsx)("span",{className:"font-medium text-sm",children:"Logout"})]})})]})}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col min-w-0 lg:ml-64",children:[(0,a.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200 sticky top-0 z-30",children:(0,a.jsx)("div",{className:"px-4 sm:px-6 lg:px-8 xl:px-12",children:(0,a.jsxs)(d.so,{justify:"between",align:"center",className:"h-16",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("button",{onClick:()=>F(!0),className:"lg:hidden p-2 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors",children:(0,a.jsx)(v.A,{className:"h-6 w-6"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-xl font-bold text-gray-900 capitalize",children:(null==(s=L.find(e=>e.id===C))?void 0:s.label)||"Dashboard"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 hidden sm:block",children:(e=>{switch(e){case"overview":default:return"Manage your mining operations and earnings";case"mining":return"Purchase and manage your mining units";case"earnings":return"Track your mining rewards and commissions";case"wallet":return"Manage deposits, withdrawals, and balance";case"referrals":return"Build and manage your referral network";case"kyc":return"Complete your identity verification";case"support":return"Get help and manage support tickets";case"profile":return"Manage your account information and preferences";case"admin":return"Manage platform operations and users"}})(C)})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("button",{className:"relative p-2 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors",children:[(0,a.jsx)(w.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{className:"absolute top-1 right-1 h-2 w-2 bg-red-500 rounded-full"})]}),(null==P?void 0:P.kycStatus)!=="APPROVED"&&("kyc"===C||"wallet"===C)&&(0,a.jsxs)("div",{className:"\n                    px-3 py-1.5 rounded-lg text-xs font-semibold border\n                    ".concat((null==P?void 0:P.kycStatus)==="PENDING"?"bg-solar-50 text-solar-700 border-solar-200":"bg-red-50 text-red-700 border-red-200","\n                  "),children:["KYC: ",null==P?void 0:P.kycStatus]}),(0,a.jsxs)("div",{className:"relative",ref:B,children:[(0,a.jsxs)("button",{onClick:()=>W(!U),className:"flex items-center space-x-2 p-1 rounded-lg hover:bg-gray-100 transition-colors",children:[(0,a.jsx)(o.ph,{src:null==P?void 0:P.profilePicture,alt:"Profile",size:32,className:"rounded-lg",fallbackText:(null==P||null==(t=P.firstName)?void 0:t.charAt(0).toUpperCase())||(null==P?void 0:P.email.charAt(0).toUpperCase()),fallbackBgColor:"bg-yellow-500",loading:"lazy"}),(0,a.jsx)(A.A,{className:"h-4 w-4 text-gray-500 transition-transform ".concat(U?"rotate-180":"")})]}),U&&(0,a.jsxs)("div",{className:"absolute right-0 mt-2 w-64 bg-white rounded-xl shadow-lg border border-gray-200 py-2 z-50",children:[(0,a.jsx)("div",{className:"px-4 py-3 border-b border-gray-100",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(o.ph,{src:null==P?void 0:P.profilePicture,alt:"Profile",size:40,className:"rounded-lg",fallbackText:(null==P||null==(l=P.firstName)?void 0:l.charAt(0).toUpperCase())||(null==P?void 0:P.email.charAt(0).toUpperCase()),fallbackBgColor:"bg-yellow-500",loading:"lazy"}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("p",{className:"text-sm font-semibold text-gray-900 truncate",children:(null==P?void 0:P.firstName)&&(null==P?void 0:P.lastName)?"".concat(P.firstName," ").concat(P.lastName):null==P?void 0:P.email.split("@")[0]}),(0,a.jsxs)("p",{className:"text-xs text-gray-600",children:["ID: ",null==P?void 0:P.referralId]})]})]})}),(0,a.jsxs)("div",{className:"py-1",children:[(0,a.jsxs)("button",{onClick:()=>{W(!1),T("profile")},className:"w-full flex items-center space-x-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",children:[(0,a.jsx)(k.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Profile Settings"})]}),(0,a.jsx)("div",{className:"border-t border-gray-100 my-1"}),(0,a.jsxs)("button",{onClick:()=>{W(!1),_()},className:"w-full flex items-center space-x-3 px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors",children:[(0,a.jsx)(b.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Sign Out"})]})]})]})]})]})]})})}),(null==P?void 0:P.kycStatus)!=="APPROVED"&&!M&&(0,a.jsx)("div",{className:"bg-yellow-50 border-b border-yellow-200",children:(0,a.jsx)("div",{className:"px-4 sm:px-6 lg:px-8 xl:px-12",children:(0,a.jsxs)("div",{className:"flex items-center justify-between py-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(E.A,{className:"h-5 w-5 text-solar-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-solar-800",children:(null==P?void 0:P.kycStatus)==="PENDING"?"KYC verification in progress":"Complete your KYC verification"}),(0,a.jsx)("p",{className:"text-xs text-solar-600",children:(null==P?void 0:P.kycStatus)==="PENDING"?"Your documents are being reviewed. This usually takes 1-3 business days.":"Verify your identity to enable withdrawals and unlock all features."})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(null==P?void 0:P.kycStatus)!=="PENDING"&&(0,a.jsx)(o.$n,{size:"sm",onClick:()=>T("kyc"),className:"bg-solar-600 hover:bg-solar-700 text-white",children:"Complete KYC"}),(0,a.jsx)("button",{onClick:()=>O(!0),className:"text-solar-500 hover:text-solar-700 p-1",children:(0,a.jsx)(N.A,{className:"h-4 w-4"})})]})]})})}),(0,a.jsx)("main",{className:"flex-1 bg-gray-50 overflow-y-auto",children:(0,a.jsx)("div",{className:"px-4 sm:px-6 lg:px-8 xl:px-12 py-6",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto",children:S})})})]})]})};var C=t(4186),T=t(9037),P=t(9434),R=t(8500);let I=()=>{let[e,s]=(0,r.useState)([]),[t,n]=(0,r.useState)(!0),[i,l]=(0,r.useState)(null),c=(0,r.useRef)(null),d=[{id:"bitcoin",symbol:"BTC",name:"Bitcoin",current_price:43250,price_change_percentage_24h:2.5,image:"/crypto-icons/btc.png"},{id:"ethereum",symbol:"ETH",name:"Ethereum",current_price:2650,price_change_percentage_24h:-1.2,image:"/crypto-icons/eth.png"},{id:"tether",symbol:"USDT",name:"Tether",current_price:1,price_change_percentage_24h:.1,image:"/crypto-icons/usdt.png"},{id:"binancecoin",symbol:"BNB",name:"BNB",current_price:315.5,price_change_percentage_24h:1.8,image:"/crypto-icons/bnb.png"},{id:"solana",symbol:"SOL",name:"Solana",current_price:98.75,price_change_percentage_24h:3.2,image:"/crypto-icons/sol.png"}],o=["bitcoin","ethereum","tether","binancecoin","solana","usd-coin","xrp","staked-ether","dogecoin","cardano","tron","avalanche-2","chainlink","polygon","wrapped-bitcoin","internet-computer","near","uniswap","litecoin","dai","ethereum-classic","stellar","monero","bitcoin-cash","cosmos"],x=async()=>{try{let e,t;l(null);try{let a=new AbortController,r=setTimeout(()=>a.abort(),8e3);if(e=await fetch("https://api.coingecko.com/api/v3/simple/price?ids=".concat(o.join(","),"&vs_currencies=usd&include_24hr_change=true"),{headers:{Accept:"application/json"},signal:a.signal}),clearTimeout(r),!e.ok)throw Error("CoinGecko API error: ".concat(e.status));t=await e.json();let i=o.map(e=>{let s=t[e];return s?{id:e,symbol:D(e),name:function(e){return({bitcoin:"Bitcoin",ethereum:"Ethereum",tether:"Tether",binancecoin:"BNB",solana:"Solana","usd-coin":"USD Coin",xrp:"XRP","staked-ether":"Lido Staked Ether",dogecoin:"Dogecoin",cardano:"Cardano",tron:"TRON","avalanche-2":"Avalanche",chainlink:"Chainlink",polygon:"Polygon","wrapped-bitcoin":"Wrapped Bitcoin","internet-computer":"Internet Computer",near:"NEAR Protocol",uniswap:"Uniswap",litecoin:"Litecoin",dai:"Dai","ethereum-classic":"Ethereum Classic",stellar:"Stellar",monero:"Monero","bitcoin-cash":"Bitcoin Cash",cosmos:"Cosmos"})[e]||e}(e),current_price:s.usd,price_change_percentage_24h:s.usd_24h_change||0,image:function(e){var s;return"https://cryptologos.cc/logos/".concat({bitcoin:"bitcoin",ethereum:"ethereum",tether:"tether",binancecoin:"bnb",solana:"solana","usd-coin":"usd-coin",xrp:"xrp","staked-ether":"ethereum",dogecoin:"dogecoin",cardano:"cardano",tron:"tron","avalanche-2":"avalanche",chainlink:"chainlink",polygon:"polygon","wrapped-bitcoin":"wrapped-bitcoin","internet-computer":"internet-computer",near:"near-protocol",uniswap:"uniswap",litecoin:"litecoin",dai:"multi-collateral-dai","ethereum-classic":"ethereum-classic",stellar:"stellar",monero:"monero","bitcoin-cash":"bitcoin-cash",cosmos:"cosmos"}[s=e]||s,"-").concat(D(e).toLowerCase(),"-logo.png")}(e)}:null}).filter(Boolean);s(i),n(!1);return}catch(a){console.warn("Direct CoinGecko API failed, trying fallback API:",a);let e=await fetch("/api/crypto/prices",{headers:{Accept:"application/json"}});if(!e.ok)throw Error("Both direct and fallback APIs failed");let t=await e.json();if(t.success&&t.data){s(t.data),n(!1);return}throw Error("Invalid fallback API response")}}catch(e){console.warn("Error fetching crypto prices, using fallback data:",e),s(d),l(null),n(!1)}};(0,r.useEffect)(()=>{s(d),n(!1),x().catch(()=>{});let e=setInterval(()=>{x().catch(()=>{})},3e5);return()=>{clearInterval(e)}},[]),(0,r.useEffect)(()=>{if(!c.current||0===e.length)return;let s=c.current,t=0,a=280*e.length,r=()=>{(t+=.5)>=a/2&&(t=0),s.scrollLeft=t,requestAnimationFrame(r)},n=requestAnimationFrame(r);return()=>cancelAnimationFrame(n)},[e]);let m=e=>e>=1?"$".concat(e.toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2})):"$".concat(e.toFixed(6)),h=e=>"".concat(e>=0?"+":"").concat(e.toFixed(2),"%");return t?(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-50 to-gray-100 rounded-2xl p-6 mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Live Crypto Prices"}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Loading..."})]}),(0,a.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"}),(0,a.jsx)("span",{className:"ml-3 text-gray-600",children:"Fetching real-time crypto prices..."})]})]}):i?(0,a.jsx)("div",{className:"bg-red-50 rounded-2xl p-6 mb-8",children:(0,a.jsx)("div",{className:"flex items-center justify-center text-red-600",children:(0,a.jsxs)("span",{children:["⚠️ ",i]})})}):(0,a.jsx)("div",{className:"bg-gradient-to-r from-gray-50 to-gray-100 rounded-2xl p-6 mb-8 overflow-hidden",children:(0,a.jsx)("div",{ref:c,className:"flex space-x-4 overflow-x-hidden scrollbar-hide",style:{scrollBehavior:"auto"},children:[...e,...e].map((e,s)=>(0,a.jsx)("div",{className:"flex-shrink-0 bg-white rounded-xl p-4 shadow-sm border border-gray-200 min-w-[260px]",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center overflow-hidden",children:(0,a.jsx)("img",{src:"https://cdn.jsdelivr.net/gh/spothq/cryptocurrency-icons@master/32/color/".concat(e.id,".png"),alt:e.name,className:"w-8 h-8 object-contain",onError:s=>{let t=s.target;t.src="https://raw.githubusercontent.com/spothq/cryptocurrency-icons/master/32/color/".concat(e.symbol.toLowerCase(),".png"),t.onerror=()=>{t.style.display="none";let s=t.parentElement;s&&(s.style.backgroundColor=({bitcoin:"#F7931A",ethereum:"#627EEA",tether:"#26A17B",binancecoin:"#F3BA2F",solana:"#9945FF","usd-coin":"#2775CA","staked-ether":"#00D4AA",xrp:"#23292F",dogecoin:"#C2A633",tron:"#FF060A",cardano:"#0033AD","avalanche-2":"#E84142",chainlink:"#375BD2",polygon:"#8247E5","wrapped-bitcoin":"#FF6B35","internet-computer":"#29ABE2",near:"#00C08B",uniswap:"#FF007A",litecoin:"#BFBBBB",dai:"#F5AC37","ethereum-classic":"#328332",stellar:"#7D00FF",monero:"#FF6600","bitcoin-cash":"#8DC351",cosmos:"#2E3148"})[e.id]||"#6B7280",s.innerHTML='<span class="text-sm font-bold text-white">'.concat(e.symbol.toUpperCase(),"</span>"))}}})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-semibold text-gray-900 text-sm",children:e.symbol.toUpperCase()}),(0,a.jsx)("div",{className:"text-xs text-gray-500 truncate max-w-[100px]",children:e.name})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("div",{className:"font-bold text-gray-900 text-sm",children:m(e.current_price)}),(0,a.jsxs)("div",{className:"flex items-center text-xs font-medium ".concat(e.price_change_percentage_24h>=0?"text-green-600":"text-red-600"),children:[e.price_change_percentage_24h>=0?(0,a.jsx)(u.A,{className:"h-3 w-3 mr-1"}):(0,a.jsx)(R.A,{className:"h-3 w-3 mr-1"}),h(e.price_change_percentage_24h)]})]})]})},"".concat(e.id,"-").concat(s)))})})};function D(e){return({bitcoin:"BTC",ethereum:"ETH",tether:"USDT",binancecoin:"BNB",solana:"SOL","usd-coin":"USDC",xrp:"XRP","staked-ether":"stETH",dogecoin:"DOGE",cardano:"ADA",tron:"TRX","avalanche-2":"AVAX",chainlink:"LINK",polygon:"MATIC","wrapped-bitcoin":"WBTC","internet-computer":"ICP",near:"NEAR",uniswap:"UNI",litecoin:"LTC",dai:"DAI","ethereum-classic":"ETC",stellar:"XLM",monero:"XMR","bitcoin-cash":"BCH",cosmos:"ATOM"})[e]||e.toUpperCase()}let F=e=>{let{onTabChange:s}=e,{user:t}=(0,n.A)(),[i,l]=(0,r.useState)(null),[c,m]=(0,r.useState)(!0),[j,f]=(0,r.useState)((0,P.ZU)());(0,r.useEffect)(()=>{y();let e=setInterval(()=>{f((0,P.ZU)())},1e3);return()=>clearInterval(e)},[]);let y=async()=>{try{let n=new AbortController,i=setTimeout(()=>n.abort(),1e4),[c,d,o,x]=await Promise.all([fetch("/api/wallet/balance",{credentials:"include",signal:n.signal}).catch(()=>({ok:!1,json:()=>Promise.resolve({success:!1})})),fetch("/api/earnings",{credentials:"include",signal:n.signal}).catch(()=>({ok:!1,json:()=>Promise.resolve({success:!1})})),fetch("/api/mining-units",{credentials:"include",signal:n.signal}).catch(()=>({ok:!1,json:()=>Promise.resolve({success:!1})})),fetch("/api/referrals/tree?depth=1",{credentials:"include",signal:n.signal}).catch(()=>({ok:!1,json:()=>Promise.resolve({success:!1})}))]);clearTimeout(i);let[m,h,u,p]=await Promise.all([c.json(),d.json(),o.json(),x.json()]);if(m.success&&h.success&&u.success&&p.success){var e,s,t,a,r;let n=u.data.reduce((e,s)=>e+s.thsAmount,0),i=u.data.filter(e=>"ACTIVE"===e.status),c=i.length>0?{id:i[0].id,progressPercentage:i[0].progressPercentage||0,remainingCapacity:i[0].remainingCapacity||0,thsAmount:i[0].thsAmount}:null;l({totalTHS:n,estimatedEarnings:h.data.estimatedEarnings,walletBalance:m.data.balance,pendingEarnings:m.data.pendingEarnings,activeUnits:u.data.length,totalEarnings:h.data.totalEarnings,directReferrals:p.data.statistics.totalDirectReferrals,binaryPoints:p.data.statistics.binaryPoints,miningUnits:{totalUnits:(null==(e=u.summary)?void 0:e.totalUnits)||u.data.length,expiredUnits:(null==(s=u.summary)?void 0:s.expiredUnits)||0,nextToExpire:c,totalMiningEarnings:(null==(t=u.summary)?void 0:t.totalMiningEarnings)||0,totalReferralEarnings:(null==(a=u.summary)?void 0:a.totalReferralEarnings)||0,totalBinaryEarnings:(null==(r=u.summary)?void 0:r.totalBinaryEarnings)||0}})}}catch(e){console.warn("Failed to fetch dashboard stats, using fallback data:",e),l({totalTHS:0,estimatedEarnings:{next7Days:0,next30Days:0,next365Days:0},walletBalance:0,pendingEarnings:0,activeUnits:0,totalEarnings:0,directReferrals:0,binaryPoints:{leftPoints:0,rightPoints:0},miningUnits:{totalUnits:0,expiredUnits:0,nextToExpire:null,totalMiningEarnings:0,totalReferralEarnings:0,totalBinaryEarnings:0}})}finally{m(!1)}};return c?(0,a.jsx)("div",{className:"space-y-6",children:Array.from({length:3}).map((e,s)=>(0,a.jsx)("div",{className:"animate-pulse",children:(0,a.jsx)("div",{className:"h-32 bg-gray-200 rounded-xl"})},s))}):i?(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"rounded-2xl p-8 text-white shadow-lg relative overflow-hidden",style:{backgroundImage:"url(/wcbg.jpg)",backgroundSize:"cover",backgroundPosition:"center",backgroundRepeat:"no-repeat"},children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-60 rounded-2xl"}),(0,a.jsxs)("div",{className:"relative z-10",children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold mb-3",children:[(0,a.jsx)("span",{className:"text-orange-400",children:"Welcome to HashCoreX"}),(null==t?void 0:t.firstName)&&(0,a.jsxs)("span",{className:"text-orange-300",children:[" ",t.firstName]})]}),(0,a.jsx)("p",{className:"text-gray-100 text-lg leading-relaxed",children:"Your sustainable mining dashboard. Track your earnings, manage your mining units, and grow your referral network."})]})]}),(0,a.jsx)(I,{}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Key Metrics"}),(0,a.jsxs)(d.xA,{cols:{default:1,sm:2,lg:4},gap:6,children:[(0,a.jsx)(o.Zp,{children:(0,a.jsx)(o.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600 mb-2",children:"Total Mining Power"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-dark-900",children:(0,P.jI)(i.totalTHS)})]}),(0,a.jsx)("div",{className:"h-14 w-14 bg-solar-100 rounded-xl flex items-center justify-center",children:(0,a.jsx)(h.A,{className:"h-7 w-7 text-solar-600"})})]})})}),(0,a.jsx)(o.Zp,{children:(0,a.jsx)(o.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600 mb-2",children:"Wallet Balance"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-eco-600",children:(0,P.vv)(i.walletBalance)})]}),(0,a.jsx)("div",{className:"h-14 w-14 bg-eco-100 rounded-xl flex items-center justify-center",children:(0,a.jsx)(p.A,{className:"h-7 w-7 text-eco-600"})})]})})}),(0,a.jsx)(o.Zp,{children:(0,a.jsx)(o.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600 mb-2",children:"Total Earnings"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-dark-900",children:(0,P.vv)(i.totalEarnings)})]}),(0,a.jsx)("div",{className:"h-14 w-14 bg-blue-100 rounded-xl flex items-center justify-center",children:(0,a.jsx)(u.A,{className:"h-7 w-7 text-blue-600"})})]})})}),(0,a.jsx)(o.Zp,{children:(0,a.jsx)(o.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600 mb-2",children:"Direct Referrals"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-dark-900",children:i.directReferrals})]}),(0,a.jsx)("div",{className:"h-14 w-14 bg-purple-100 rounded-xl flex items-center justify-center",children:(0,a.jsx)(g.A,{className:"h-7 w-7 text-purple-600"})})]})})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Earnings Overview"}),(0,a.jsxs)(d.xA,{cols:{default:1,lg:2},gap:8,children:[(0,a.jsxs)(o.Zp,{children:[(0,a.jsx)(o.aR,{className:"pb-4",children:(0,a.jsxs)(o.ZB,{className:"flex items-center space-x-3 text-lg",children:[(0,a.jsx)("div",{className:"h-10 w-10 bg-eco-100 rounded-lg flex items-center justify-center",children:(0,a.jsx)(u.A,{className:"h-5 w-5 text-eco-600"})}),(0,a.jsx)("span",{children:"Estimated Earnings"})]})}),(0,a.jsxs)(o.Wu,{className:"pt-0",children:[(0,a.jsxs)("div",{className:"space-y-5",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center py-2",children:[(0,a.jsx)("span",{className:"text-gray-600 font-medium",children:"Next 7 Days"}),(0,a.jsx)("span",{className:"font-bold text-eco-600 text-lg",children:(0,P.vv)(i.estimatedEarnings.next7Days)})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center py-2",children:[(0,a.jsx)("span",{className:"text-gray-600 font-medium",children:"Next 30 Days"}),(0,a.jsx)("span",{className:"font-bold text-eco-600 text-lg",children:(0,P.vv)(i.estimatedEarnings.next30Days)})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center py-2",children:[(0,a.jsx)("span",{className:"text-gray-600 font-medium",children:"Next 365 Days"}),(0,a.jsx)("span",{className:"font-bold text-eco-600 text-lg",children:(0,P.vv)(i.estimatedEarnings.next365Days)})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center py-2",children:[(0,a.jsx)("span",{className:"text-gray-600 font-medium",children:"Next 2 Years"}),(0,a.jsx)("span",{className:"font-bold text-eco-600 text-lg",children:(0,P.vv)(i.estimatedEarnings.next2Years)})]})]}),(0,a.jsx)("div",{className:"mt-6 p-4 bg-eco-50 rounded-xl",children:(0,a.jsx)("p",{className:"text-sm text-eco-700 font-medium",children:"* Based on current mining units and average ROI"})})]})]}),(0,a.jsxs)(o.Zp,{className:"hover:shadow-lg transition-shadow duration-200",children:[(0,a.jsx)(o.aR,{className:"pb-4",children:(0,a.jsxs)(o.ZB,{className:"flex items-center space-x-3 text-lg",children:[(0,a.jsx)("div",{className:"h-10 w-10 bg-solar-100 rounded-lg flex items-center justify-center",children:(0,a.jsx)(C.A,{className:"h-5 w-5 text-solar-600"})}),(0,a.jsx)("span",{children:"Next Payout"})]})}),(0,a.jsx)(o.Wu,{className:"pt-0",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-6 font-medium",children:"Weekly payout every Saturday at 15:00 UTC"}),(0,a.jsxs)("div",{className:"grid grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"bg-solar-50 rounded-xl p-3 mb-2",children:(0,a.jsx)("div",{className:"text-2xl font-bold text-solar-600",children:j.days})}),(0,a.jsx)("div",{className:"text-sm text-gray-600 font-medium",children:"Days"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"bg-solar-50 rounded-xl p-3 mb-2",children:(0,a.jsx)("div",{className:"text-2xl font-bold text-solar-600",children:j.hours})}),(0,a.jsx)("div",{className:"text-sm text-gray-600 font-medium",children:"Hours"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"bg-solar-50 rounded-xl p-3 mb-2",children:(0,a.jsx)("div",{className:"text-2xl font-bold text-solar-600",children:j.minutes})}),(0,a.jsx)("div",{className:"text-sm text-gray-600 font-medium",children:"Min"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"bg-solar-50 rounded-xl p-3 mb-2",children:(0,a.jsx)("div",{className:"text-2xl font-bold text-solar-600",children:j.seconds})}),(0,a.jsx)("div",{className:"text-sm text-gray-600 font-medium",children:"Sec"})]})]}),i.pendingEarnings>0&&(0,a.jsx)("div",{className:"mt-6 p-4 bg-solar-50 rounded-xl",children:(0,a.jsxs)("p",{className:"text-sm text-solar-700 font-semibold",children:[(0,a.jsx)("strong",{className:"text-lg",children:(0,P.vv)(i.pendingEarnings)})," pending"]})})]})})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Quick Actions"}),(0,a.jsx)(o.Zp,{children:(0,a.jsx)(o.Wu,{className:"p-8",children:(0,a.jsxs)(d.xA,{cols:{default:1,sm:2,lg:3},gap:6,children:[(0,a.jsxs)(o.$n,{className:"h-20 flex flex-col items-center justify-center space-y-3 text-base font-semibold rounded-xl bg-green-600 text-white border-0",onClick:()=>null==s?void 0:s("mining"),children:[(0,a.jsx)(x.NC,{className:"h-7 w-7"}),(0,a.jsx)("span",{children:"Buy Mining Power"})]}),(0,a.jsxs)(o.$n,{variant:"outline",className:"h-20 flex flex-col items-center justify-center space-y-3 text-base font-semibold rounded-xl border-2 border-green-500 text-green-600",onClick:()=>null==s?void 0:s("wallet"),children:[(0,a.jsx)(x.Lc,{className:"h-7 w-7"}),(0,a.jsx)("span",{children:"Withdraw USDT"})]}),(0,a.jsxs)(o.$n,{variant:"outline",className:"h-20 flex flex-col items-center justify-center space-y-3 text-base font-semibold rounded-xl border-2 border-green-500 text-green-600",onClick:()=>null==s?void 0:s("referrals"),children:[(0,a.jsx)(g.A,{className:"h-7 w-7"}),(0,a.jsx)("span",{children:"Build Network"})]})]})})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Mining Units Overview"}),(0,a.jsxs)(d.xA,{cols:{default:1,lg:2},gap:8,children:[(0,a.jsxs)(o.Zp,{children:[(0,a.jsx)(o.aR,{className:"pb-4",children:(0,a.jsxs)(o.ZB,{className:"flex items-center space-x-3 text-lg",children:[(0,a.jsx)("div",{className:"h-10 w-10 bg-eco-100 rounded-lg flex items-center justify-center",children:(0,a.jsx)(u.A,{className:"h-5 w-5 text-eco-600"})}),(0,a.jsx)("span",{children:"Earnings Breakdown"})]})}),(0,a.jsx)(o.Wu,{className:"pt-0",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center p-3 bg-green-50 rounded-lg",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Mining Earnings"}),(0,a.jsx)("span",{className:"font-bold text-green-600",children:(0,P.vv)(i.miningUnits.totalMiningEarnings)})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center p-3 bg-blue-50 rounded-lg",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Referral Earnings"}),(0,a.jsx)("span",{className:"font-bold text-blue-600",children:(0,P.vv)(i.miningUnits.totalReferralEarnings)})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center p-3 bg-purple-50 rounded-lg",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Binary Earnings"}),(0,a.jsx)("span",{className:"font-bold text-purple-600",children:(0,P.vv)(i.miningUnits.totalBinaryEarnings)})]})]})})]}),(0,a.jsxs)(o.Zp,{children:[(0,a.jsx)(o.aR,{className:"pb-4",children:(0,a.jsxs)(o.ZB,{className:"flex items-center space-x-3 text-lg",children:[(0,a.jsx)("div",{className:"h-10 w-10 bg-orange-100 rounded-lg flex items-center justify-center",children:(0,a.jsx)(C.A,{className:"h-5 w-5 text-orange-600"})}),(0,a.jsx)("span",{children:"FIFO Expiration System"})]})}),(0,a.jsx)(o.Wu,{className:"pt-0",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"p-4 bg-orange-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-sm text-gray-600 mb-2",children:"Units expire at 5x investment using FIFO order"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Total Units:"}),(0,a.jsx)("span",{className:"font-medium ml-2",children:i.miningUnits.totalUnits})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Expired:"}),(0,a.jsx)("span",{className:"font-medium ml-2 text-red-600",children:i.miningUnits.expiredUnits})]})]})]}),i.miningUnits.nextToExpire?(0,a.jsxs)("div",{className:"p-4 bg-yellow-50 rounded-lg border border-yellow-200",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-700 mb-2",children:"Next to Expire (FIFO #1)"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Mining Power:"}),(0,a.jsx)("span",{className:"font-medium",children:(0,P.jI)(i.miningUnits.nextToExpire.thsAmount)})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Progress:"}),(0,a.jsxs)("span",{className:"font-medium text-orange-600",children:[i.miningUnits.nextToExpire.progressPercentage.toFixed(1),"%"]})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Remaining:"}),(0,a.jsx)("span",{className:"font-medium text-green-600",children:(0,P.vv)(i.miningUnits.nextToExpire.remainingCapacity)})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2 mt-2",children:(0,a.jsx)("div",{className:"bg-orange-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat(Math.min(i.miningUnits.nextToExpire.progressPercentage,100),"%")}})})]})]}):(0,a.jsx)("div",{className:"p-4 bg-gray-50 rounded-lg text-center",children:(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"No active mining units"})})]})})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Binary Network Summary"}),(0,a.jsxs)(o.Zp,{children:[(0,a.jsx)(o.aR,{className:"pb-4",children:(0,a.jsxs)(o.ZB,{className:"flex items-center space-x-3 text-lg",children:[(0,a.jsx)("div",{className:"h-10 w-10 bg-solar-100 rounded-lg flex items-center justify-center",children:(0,a.jsx)(T.A,{className:"h-5 w-5 text-solar-600"})}),(0,a.jsx)("span",{children:"Network Performance"})]})}),(0,a.jsx)(o.Wu,{className:"pt-0",children:(0,a.jsxs)(d.xA,{cols:{default:1,sm:3},gap:8,children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"bg-solar-50 rounded-xl p-6 mb-3",children:(0,a.jsx)("div",{className:"text-3xl font-bold text-solar-600",children:i.binaryPoints.leftPoints})}),(0,a.jsx)("div",{className:"text-sm text-gray-600 font-medium",children:"Left Points"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"bg-solar-50 rounded-xl p-6 mb-3",children:(0,a.jsx)("div",{className:"text-3xl font-bold text-solar-600",children:i.binaryPoints.rightPoints})}),(0,a.jsx)("div",{className:"text-sm text-gray-600 font-medium",children:"Right Points"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"bg-eco-50 rounded-xl p-6 mb-3",children:(0,a.jsx)("div",{className:"text-3xl font-bold text-eco-600",children:Math.min(i.binaryPoints.leftPoints,i.binaryPoints.rightPoints)})}),(0,a.jsx)("div",{className:"text-sm text-gray-600 font-medium",children:"Potential Match"})]})]})})]})]})]}):(0,a.jsx)(o.Zp,{children:(0,a.jsx)(o.Wu,{className:"text-center py-8",children:(0,a.jsx)("p",{className:"text-gray-500",children:"Failed to load dashboard data"})})})};var M=t(5868),O=t(6740);let U=e=>{let{onPurchaseComplete:s}=e,[t,n]=(0,r.useState)({thsAmount:"",investmentAmount:""}),[i,l]=(0,r.useState)(!1),[c,d]=(0,r.useState)(""),[m,u]=(0,r.useState)(50),[p,g]=(0,r.useState)([{minTHS:0,maxTHS:10,dailyReturnMin:.3,dailyReturnMax:.5,monthlyReturnMin:10,monthlyReturnMax:15},{minTHS:10,maxTHS:50,dailyReturnMin:.4,dailyReturnMax:.6,monthlyReturnMin:10,monthlyReturnMax:15},{minTHS:50,maxTHS:999999,dailyReturnMin:.5,dailyReturnMax:.7,monthlyReturnMin:10,monthlyReturnMax:15}]),[j,f]=(0,r.useState)(100),{showConfirm:y,hideConfirm:N,ConfirmDialog:b}=(0,o.G_)(),{showMessage:v,hideMessage:w,MessageBoxComponent:A}=(0,o.eC)();(0,r.useEffect)(()=>{k()},[]);let k=async()=>{try{let e=await fetch("/api/admin/settings/pricing");if(e.ok){let s=await e.json();s.success&&(u(s.data.thsPrice),g(s.data.earningsRanges),f(s.data.minPurchaseAmount))}}catch(e){console.error("Failed to fetch pricing:",e)}},E=e=>p.find(s=>e>=s.minTHS&&e<=s.maxTHS)||p[p.length-1],S=async e=>{e.preventDefault(),d("");try{let e=parseFloat(t.thsAmount),s=parseFloat(t.investmentAmount);if(!e||!s||e<=0||s<=0)throw Error("Please enter valid amounts");if(s<j)throw Error("Minimum purchase amount is $".concat(j));let{daily:r,weekly:n,monthly:i,range:l}=T();y({title:"Confirm Mining Unit Purchase",message:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"TH/s Amount:"}),(0,a.jsxs)("span",{className:"font-semibold",children:[e.toFixed(4)," TH/s"]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Investment Amount:"}),(0,a.jsxs)("span",{className:"font-semibold",children:["$",s.toFixed(2)," USD"]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Price per TH/s:"}),(0,a.jsxs)("span",{className:"font-semibold",children:["$",m.toFixed(2)," USD"]})]})]}),(0,a.jsxs)("div",{className:"bg-green-50 p-4 rounded-lg space-y-2",children:[(0,a.jsxs)("h4",{className:"font-semibold text-green-800",children:["Estimated Earnings (Average ROI: ",l?"".concat(l.dailyReturnMin,"-").concat(l.dailyReturnMax,"%"):"0.4%",")"]}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-sm",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"font-semibold text-green-600",children:["$",r.toFixed(2)]}),(0,a.jsx)("div",{className:"text-green-700",children:"Daily"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"font-semibold text-green-600",children:["$",n.toFixed(2)]}),(0,a.jsx)("div",{className:"text-green-700",children:"Weekly"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"font-semibold text-green-600",children:["$",i.toFixed(2)]}),(0,a.jsx)("div",{className:"text-green-700",children:"Monthly"})]})]})]}),(0,a.jsx)("div",{className:"bg-blue-50 p-3 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-blue-800",children:[(0,a.jsx)("strong",{children:"Important:"})," Mining units are active for 24 months and expire when 5x investment is earned."]})})]}),confirmText:"Purchase Mining Unit",cancelText:"Cancel",variant:"default",onConfirm:()=>C(e,s)})}catch(e){d(e.message||"Invalid purchase details")}},C=async(e,t)=>{l(!0);try{let a=await fetch("/api/mining-units",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({thsAmount:e,investmentAmount:t})}),r=await a.json();if(!r.success)throw Error(r.error||"Purchase failed");v({title:"Mining Unit Purchased",message:"Your mining unit has been purchased successfully! You will start earning daily returns within 24 hours.",variant:"success",buttonText:"OK"}),n({thsAmount:"",investmentAmount:""}),s&&s()}catch(e){v({title:"Purchase Failed",message:e.message||"Failed to purchase mining unit. Please try again.",variant:"error",buttonText:"OK"})}finally{l(!1)}},T=()=>{let e=parseFloat(t.investmentAmount)||0,s=parseFloat(t.thsAmount)||0;if(e<=0||s<=0)return{daily:0,weekly:0,monthly:0,range:null};let a=E(s),r=e*((a.dailyReturnMin+a.dailyReturnMax)/2)/100;return{daily:r,weekly:7*r,monthly:30*r,range:a}},R=T();return(0,a.jsxs)(o.Zp,{children:[(0,a.jsx)(o.aR,{className:"pb-4",children:(0,a.jsxs)(o.ZB,{className:"flex items-center space-x-3 text-xl",children:[(0,a.jsx)("div",{className:"h-10 w-10 bg-solar-100 rounded-lg flex items-center justify-center",children:(0,a.jsx)(x.NC,{className:"h-6 w-6 text-solar-600"})}),(0,a.jsx)("span",{children:"Purchase Mining Power"})]})}),(0,a.jsxs)(o.Wu,{className:"pt-0",children:[(0,a.jsxs)("form",{onSubmit:S,className:"space-y-8",children:[c&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-xl text-sm font-medium",children:c}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-solar-50 to-eco-50 rounded-xl p-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4",children:[(0,a.jsxs)("div",{className:"text-center sm:text-left",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-600 block mb-1",children:"Current TH/s Price"}),(0,a.jsxs)("span",{className:"text-2xl font-bold text-solar-600",children:[(0,P.vv)(m)," / TH/s"]})]}),(0,a.jsxs)("div",{className:"text-center sm:text-right",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-600 block mb-1",children:R.range?"Daily ROI Range (".concat(R.range.minTHS,"-").concat(R.range.maxTHS," TH/s)"):"Daily ROI Range"}),(0,a.jsx)("span",{className:"text-xl font-bold text-eco-600",children:R.range?"".concat(R.range.dailyReturnMin,"% - ").concat(R.range.dailyReturnMax,"%"):"0.3% - 0.7%"})]})]}),(0,a.jsxs)("div",{className:"border-t border-gray-200 pt-4",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-3",children:"TH/s Based Earnings Tiers"}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-3",children:p.map((e,s)=>(0,a.jsxs)("div",{className:"bg-white rounded-lg p-3 border border-gray-200",children:[(0,a.jsxs)("div",{className:"text-xs font-medium text-gray-600 mb-1",children:[e.minTHS," - ",999999===e.maxTHS?"∞":e.maxTHS," TH/s"]}),(0,a.jsxs)("div",{className:"text-sm font-bold text-eco-600",children:[e.dailyReturnMin,"% - ",e.dailyReturnMax,"%"]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:["Monthly: ",e.monthlyReturnMin,"% - ",e.monthlyReturnMax,"%"]})]},s))})]})]}),(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsx)("div",{children:(0,a.jsx)(o.pd,{label:"TH/s Amount",type:"number",step:"0.0001",min:"0",value:t.thsAmount,onChange:e=>{let s=(parseFloat(e.target.value)||0)*m;n({thsAmount:e.target.value,investmentAmount:s>0?s.toFixed(2):""})},placeholder:"Enter TH/s amount",leftIcon:(0,a.jsx)(h.A,{className:"h-4 w-4"}),className:"h-12"})}),(0,a.jsx)("div",{children:(0,a.jsx)(o.pd,{label:"Investment Amount (USD)",type:"number",step:"0.01",min:j,value:t.investmentAmount,onChange:e=>{let s=(parseFloat(e.target.value)||0)/m;n({thsAmount:s>0?s.toFixed(4):"",investmentAmount:e.target.value})},placeholder:"Enter investment amount",leftIcon:(0,a.jsx)(M.A,{className:"h-4 w-4"}),className:"h-12"})})]})}),R.daily>0&&(0,a.jsxs)("div",{className:"bg-gradient-to-r from-eco-50 to-green-50 rounded-xl p-6",children:[(0,a.jsxs)("h4",{className:"text-base font-semibold text-gray-800 mb-4 flex items-center",children:[(0,a.jsx)("div",{className:"h-8 w-8 bg-eco-100 rounded-lg flex items-center justify-center mr-3",children:(0,a.jsx)(O.A,{className:"h-4 w-4 text-eco-600"})}),"Estimated Earnings ",R.range?"(Average ROI: ".concat((0,P.ZV)((R.range.dailyReturnMin+R.range.dailyReturnMax)/2,1),"%)"):""]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-6",children:[(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)("div",{className:"bg-white rounded-xl p-4 shadow-sm",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-eco-600 mb-1",children:(0,P.vv)(R.daily)}),(0,a.jsx)("div",{className:"text-sm text-gray-600 font-medium",children:"Daily"})]})}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)("div",{className:"bg-white rounded-xl p-4 shadow-sm",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-eco-600 mb-1",children:(0,P.vv)(R.weekly)}),(0,a.jsx)("div",{className:"text-sm text-gray-600 font-medium",children:"Weekly"})]})}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)("div",{className:"bg-white rounded-xl p-4 shadow-sm",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-eco-600 mb-1",children:(0,P.vv)(R.monthly)}),(0,a.jsx)("div",{className:"text-sm text-gray-600 font-medium",children:"Monthly"})]})})]})]}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-xl p-6",children:[(0,a.jsx)("h4",{className:"text-base font-semibold text-gray-800 mb-4",children:"Important Notes:"}),(0,a.jsxs)("ul",{className:"text-sm text-gray-600 space-y-2",children:[(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"text-solar-500 mr-2",children:"•"}),(0,a.jsxs)("span",{children:["Minimum purchase: $",j]})]}),(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"text-solar-500 mr-2",children:"•"}),(0,a.jsx)("span",{children:"Mining units are active for 24 months"})]}),(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"text-solar-500 mr-2",children:"•"}),(0,a.jsx)("span",{children:"Units expire when 5x investment is earned"})]}),(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"text-solar-500 mr-2",children:"•"}),(0,a.jsx)("span",{children:"Weekly payouts every Saturday at 15:00 UTC"})]}),(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"text-solar-500 mr-2",children:"•"}),(0,a.jsx)("span",{children:"ROI varies daily based on mining performance"})]})]})]}),(0,a.jsxs)(o.$n,{type:"submit",size:"lg",className:"w-full h-14 text-lg font-semibold rounded-xl",loading:i,disabled:!t.thsAmount||!t.investmentAmount||parseFloat(t.investmentAmount)<j,children:[(0,a.jsx)(x.Lc,{className:"h-6 w-6 mr-3"}),"Purchase Mining Unit"]})]}),(0,a.jsx)(b,{}),(0,a.jsx)(A,{})]})]})};var W=t(9074);let B=()=>{let[e,s]=(0,r.useState)(null),[t,n]=(0,r.useState)(!0),[i,l]=(0,r.useState)((0,P.ZU)());(0,r.useEffect)(()=>{c();let e=setInterval(()=>{l((0,P.ZU)())},1e3);return()=>clearInterval(e)},[]);let c=async()=>{try{let e=await fetch("/api/earnings",{credentials:"include"});if(e.ok){let t=await e.json();t.success&&s(t.data)}}catch(e){console.error("Failed to fetch earnings data:",e)}finally{n(!1)}};if(t)return(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)("div",{className:"animate-pulse",children:(0,a.jsx)("div",{className:"h-32 bg-gray-200 rounded-xl"})})});if(!e)return(0,a.jsx)(o.Zp,{children:(0,a.jsx)(o.Wu,{className:"text-center py-8",children:(0,a.jsx)("p",{className:"text-gray-500",children:"Failed to load earnings data"})})});let x=e=>{switch(e){case"MINING_EARNINGS":return"text-solar-600";case"DIRECT_REFERRAL":return"text-eco-600";case"BINARY_BONUS":return"text-blue-600";default:return"text-gray-600"}},m=e=>{switch(e){case"MINING_EARNINGS":return"Mining";case"DIRECT_REFERRAL":return"Direct Referral";case"BINARY_BONUS":return"Binary Bonus";default:return e}};return(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Earnings Overview"}),(0,a.jsxs)(d.xA,{cols:{default:1,sm:2,lg:4},gap:6,children:[(0,a.jsx)(o.Zp,{children:(0,a.jsx)(o.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600 mb-2",children:"Total Earnings"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-dark-900",children:(0,P.vv)(e.totalEarnings)})]}),(0,a.jsx)("div",{className:"h-14 w-14 bg-eco-100 rounded-xl flex items-center justify-center",children:(0,a.jsx)(M.A,{className:"h-7 w-7 text-eco-600"})})]})})}),(0,a.jsx)(o.Zp,{children:(0,a.jsx)(o.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600 mb-2",children:"Pending Earnings"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-solar-600",children:(0,P.vv)(e.pendingEarnings)})]}),(0,a.jsx)("div",{className:"h-14 w-14 bg-solar-100 rounded-xl flex items-center justify-center",children:(0,a.jsx)(C.A,{className:"h-7 w-7 text-solar-600"})})]})})}),(0,a.jsx)(o.Zp,{children:(0,a.jsx)(o.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600 mb-2",children:"Mining Earnings"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-dark-900",children:(0,P.vv)(e.miningEarnings)})]}),(0,a.jsx)("div",{className:"h-14 w-14 bg-gray-100 rounded-xl flex items-center justify-center",children:(0,a.jsx)(u.A,{className:"h-7 w-7 text-gray-600"})})]})})}),(0,a.jsx)(o.Zp,{children:(0,a.jsx)(o.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600 mb-2",children:"Referral Earnings"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-dark-900",children:(0,P.vv)(e.referralEarnings)})]}),(0,a.jsx)("div",{className:"h-14 w-14 bg-blue-100 rounded-xl flex items-center justify-center",children:(0,a.jsx)(u.A,{className:"h-7 w-7 text-blue-600"})})]})})})]})]}),(0,a.jsxs)(d.xA,{cols:{default:1,lg:2},gap:6,children:[(0,a.jsxs)(o.Zp,{children:[(0,a.jsx)(o.aR,{children:(0,a.jsxs)(o.ZB,{className:"flex items-center space-x-2",children:[(0,a.jsx)(W.A,{className:"h-5 w-5 text-solar-500"}),(0,a.jsx)("span",{children:"Next Payout"})]})}),(0,a.jsx)(o.Wu,{children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Weekly payout every Saturday at 15:00 UTC"}),(0,a.jsxs)("div",{className:"grid grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-solar-600",children:i.days}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"Days"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-solar-600",children:i.hours}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"Hours"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-solar-600",children:i.minutes}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"Minutes"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-solar-600",children:i.seconds}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"Seconds"})]})]}),e.pendingEarnings>0&&(0,a.jsx)("div",{className:"mt-4 p-3 bg-solar-50 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-solar-700",children:[(0,a.jsx)("strong",{children:(0,P.vv)(e.pendingEarnings)})," will be transferred to your wallet"]})})]})})]}),(0,a.jsxs)(o.Zp,{children:[(0,a.jsx)(o.aR,{children:(0,a.jsxs)(o.ZB,{className:"flex items-center space-x-2",children:[(0,a.jsx)(u.A,{className:"h-5 w-5 text-eco-500"}),(0,a.jsx)("span",{children:"Estimated Earnings"})]})}),(0,a.jsxs)(o.Wu,{children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Next 7 Days"}),(0,a.jsx)("span",{className:"font-semibold text-eco-600",children:(0,P.vv)(e.estimatedEarnings.next7Days)})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Next 30 Days"}),(0,a.jsx)("span",{className:"font-semibold text-eco-600",children:(0,P.vv)(e.estimatedEarnings.next30Days)})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Next 365 Days"}),(0,a.jsx)("span",{className:"font-semibold text-eco-600",children:(0,P.vv)(e.estimatedEarnings.next365Days)})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Next 2 Years"}),(0,a.jsx)("span",{className:"font-semibold text-eco-600",children:(0,P.vv)(e.estimatedEarnings.next2Years)})]})]}),(0,a.jsx)("div",{className:"mt-4 p-3 bg-gray-50 rounded-lg",children:(0,a.jsx)("p",{className:"text-xs text-gray-600",children:"* Estimates based on current mining units and average ROI"})})]})]})]}),(0,a.jsxs)(o.Zp,{children:[(0,a.jsx)(o.aR,{children:(0,a.jsx)(o.ZB,{children:"Recent Earnings"})}),(0,a.jsx)(o.Wu,{children:e.recentEarnings.length>0?(0,a.jsx)("div",{className:"space-y-3",children:e.recentEarnings.slice(0,10).map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-sm font-medium ".concat(x(e.type)),children:m(e.type)}),(0,a.jsx)("span",{className:"text-xs text-gray-500",children:(0,P.r6)(e.createdAt)})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.description})]}),(0,a.jsx)("div",{className:"text-right",children:(0,a.jsxs)("span",{className:"font-semibold text-eco-600",children:["+",(0,P.vv)(e.amount)]})})]},e.id))}):(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("p",{className:"text-gray-500",children:"No earnings yet"}),(0,a.jsx)("p",{className:"text-sm text-gray-400 mt-1",children:"Purchase mining units to start earning"})]})})]}),(0,a.jsxs)(o.Zp,{children:[(0,a.jsx)(o.aR,{children:(0,a.jsxs)(o.ZB,{className:"flex items-center space-x-2",children:[(0,a.jsx)(C.A,{className:"h-5 w-5 text-blue-500"}),(0,a.jsx)("span",{children:"FIFO Earnings Allocation System"})]})}),(0,a.jsx)(o.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-medium text-blue-900 mb-2",children:"How It Works"}),(0,a.jsxs)("ul",{className:"text-sm text-blue-800 space-y-1",children:[(0,a.jsx)("li",{children:"• All earnings (Mining + Referral + Binary) are allocated to your oldest mining units first"}),(0,a.jsx)("li",{children:"• Mining units expire when they reach 5x their investment amount"}),(0,a.jsx)("li",{children:"• Units expire in First-In-First-Out (FIFO) order - oldest units expire first"}),(0,a.jsx)("li",{children:"• This ensures fair and predictable expiration timing"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm",children:[(0,a.jsxs)("div",{className:"p-3 bg-green-50 rounded-lg text-center",children:[(0,a.jsx)("div",{className:"font-medium text-green-700",children:"Mining Earnings"}),(0,a.jsx)("div",{className:"text-xs text-green-600 mt-1",children:"Daily ROI from your mining units"})]}),(0,a.jsxs)("div",{className:"p-3 bg-blue-50 rounded-lg text-center",children:[(0,a.jsx)("div",{className:"font-medium text-blue-700",children:"Referral Earnings"}),(0,a.jsx)("div",{className:"text-xs text-blue-600 mt-1",children:"10% commission from direct referrals"})]}),(0,a.jsxs)("div",{className:"p-3 bg-purple-50 rounded-lg text-center",children:[(0,a.jsx)("div",{className:"font-medium text-purple-700",children:"Binary Earnings"}),(0,a.jsx)("div",{className:"text-xs text-purple-600 mt-1",children:"Weekly matching bonuses"})]})]}),(0,a.jsxs)("div",{className:"p-4 bg-yellow-50 rounded-lg border border-yellow-200",children:[(0,a.jsx)("h4",{className:"font-medium text-yellow-900 mb-2",children:"\uD83D\uDCA1 Pro Tip"}),(0,a.jsx)("p",{className:"text-sm text-yellow-800",children:"Check your Mining Units tab to see the detailed earnings breakdown for each unit and track which unit will expire next in the FIFO queue."})]})]})})]})]})};var Z=t(4870),L=t(978),_=t(646),H=t(4861),V=t(6932),G=t(3904),Y=t(7924),z=t(1243),$=t(4357),q=t(1284);let K=()=>{let[e,s]=(0,r.useState)(null),[t,n]=(0,r.useState)(null),[i,l]=(0,r.useState)([]),[c,d]=(0,r.useState)(!0),[x,m]=(0,r.useState)(!1),[h,u]=(0,r.useState)(null),[g,j]=(0,r.useState)(null),[f,y]=(0,r.useState)(""),[N,b]=(0,r.useState)(!1),[v,w]=(0,r.useState)(null),A=async()=>{try{d(!0);let e=await fetch("/api/wallet/deposit/info",{credentials:"include"});if(!e.ok)throw Error("Failed to fetch deposit information");let t=await e.json();if(t.success)s(t.data.depositInfo),n(t.data.userStats),l(t.data.deposits);else throw Error(t.error||"Failed to fetch deposit information")}catch(e){u(e instanceof Error?e.message:"An error occurred")}finally{d(!1)}},k=async()=>{try{let e=await fetch("/api/wallet/deposit/info",{credentials:"include"});if(!e.ok)return;let t=await e.json();t.success&&(s(t.data.depositInfo),n(t.data.userStats),l(t.data.deposits))}catch(e){console.error("Silent fetch failed:",e)}},E=async()=>{if(null==e?void 0:e.depositAddress)try{await navigator.clipboard.writeText(e.depositAddress),b(!0),setTimeout(()=>b(!1),2e3)}catch(e){console.error("Failed to copy address:",e)}},S=async e=>{if(e.preventDefault(),f.trim())try{m(!0),u(null),j(null);let e=await fetch("/api/wallet/deposit/verify",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({transactionId:f.trim()})}),s=await e.json();s.success?("PENDING_VERIFICATION"===s.data.status?j("Transaction submitted successfully! We are now verifying your deposit. This may take up to 2 minutes."):j("Deposit verified successfully! ".concat((0,P.vv)(s.data.amount)," USDT has been credited to your wallet.")),y(""),await A()):u(s.error||"Failed to verify transaction")}catch(e){u(e instanceof Error?e.message:"An error occurred")}finally{m(!1)}},T=e=>{switch(e){case"COMPLETED":case"CONFIRMED":return(0,a.jsx)(_.A,{className:"w-4 h-4 text-green-600"});case"PENDING_VERIFICATION":return(0,a.jsx)(G.A,{className:"w-4 h-4 text-blue-600 animate-spin"});case"WAITING_FOR_CONFIRMATIONS":return(0,a.jsx)(C.A,{className:"w-4 h-4 text-orange-600"});case"PENDING":return(0,a.jsx)(C.A,{className:"w-4 h-4 text-yellow-600"});case"FAILED":case"REJECTED":return(0,a.jsx)(z.A,{className:"w-4 h-4 text-red-600"});default:return(0,a.jsx)(C.A,{className:"w-4 h-4 text-gray-600"})}},R=e=>{switch(e){case"COMPLETED":case"CONFIRMED":return"text-green-700 bg-green-100";case"PENDING_VERIFICATION":return"text-blue-700 bg-blue-100";case"WAITING_FOR_CONFIRMATIONS":return"text-orange-700 bg-orange-100";case"PENDING":return"text-yellow-700 bg-yellow-100";case"FAILED":case"REJECTED":return"text-red-700 bg-red-100";default:return"text-gray-700 bg-gray-100"}},I=(e,s,t)=>{switch(e){case"COMPLETED":case"CONFIRMED":return"Deposit completed successfully";case"PENDING_VERIFICATION":return"Verifying transaction on blockchain...";case"WAITING_FOR_CONFIRMATIONS":return"Waiting for confirmations (".concat(s||0,"/").concat(t||10,")");case"PENDING":return"Transaction verified, processing deposit...";case"FAILED":return"Transaction verification failed";case"REJECTED":return"Deposit rejected by admin";default:return"Processing..."}},D=(e,s,t)=>{switch(e){case"COMPLETED":case"CONFIRMED":default:return null;case"PENDING_VERIFICATION":return"Within 2 minutes";case"WAITING_FOR_CONFIRMATIONS":let a=(t||10)-(s||0);return a>0?"~".concat(3*a," minutes"):"Processing...";case"PENDING":return"Within 1 minute"}};return((0,r.useEffect)(()=>{A();let e=setInterval(()=>{k()},3e4);return i.some(e=>["PENDING_VERIFICATION","WAITING_FOR_CONFIRMATIONS","PENDING"].includes(e.status))&&(v&&clearInterval(v),w(setInterval(()=>{k()},1e4))),()=>{clearInterval(e),v&&clearInterval(v)}},[i.length]),(0,r.useEffect)(()=>{!i.some(e=>["PENDING_VERIFICATION","WAITING_FOR_CONFIRMATIONS","PENDING"].includes(e.status))&&v&&(clearInterval(v),w(null))},[i,v]),c)?(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)(G.A,{className:"w-8 h-8 animate-spin text-blue-600"})}):e?e.depositEnabled?e.depositAddress?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Deposit USDT"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"Add funds to your wallet using USDT TRC20"})]}),(0,a.jsxs)("button",{onClick:A,className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors",children:[(0,a.jsx)(G.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Refresh"})]})]}),t&&(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsx)(o.Zp,{className:"bg-white border-gray-200 shadow-sm",children:(0,a.jsx)(o.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Total Deposited"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[(0,P.vv)(t.totalDeposited)," USDT"]})]}),(0,a.jsx)(M.A,{className:"w-8 h-8 text-green-600"})]})})}),(0,a.jsx)(o.Zp,{className:"bg-white border-gray-200 shadow-sm",children:(0,a.jsx)(o.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Successful Deposits"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:t.depositCount})]}),(0,a.jsx)(_.A,{className:"w-8 h-8 text-blue-600"})]})})}),(0,a.jsx)(o.Zp,{className:"bg-white border-gray-200 shadow-sm",children:(0,a.jsx)(o.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Pending Deposits"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:t.pendingDeposits})]}),(0,a.jsx)(C.A,{className:"w-8 h-8 text-yellow-600"})]})})})]}),(0,a.jsxs)(o.Zp,{className:"bg-white border-gray-200 shadow-sm",children:[(0,a.jsx)(o.aR,{children:(0,a.jsxs)(o.ZB,{className:"text-gray-900 flex items-center space-x-2",children:[(0,a.jsx)(q.A,{className:"w-5 h-5 text-blue-600"}),(0,a.jsx)("span",{children:"How to Deposit"})]})}),(0,a.jsxs)(o.Wu,{className:"space-y-4",children:[(0,a.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold",children:"1"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-900 font-medium",children:"Send USDT TRC20 to the address below"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Only send USDT on the TRC20 network. Other tokens or networks will result in loss of funds."})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold",children:"2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-900 font-medium",children:"Copy your transaction ID"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"After sending, copy the transaction ID from your wallet or block explorer."})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold",children:"3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-900 font-medium",children:"Submit transaction ID below"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Paste your transaction ID in the form below to verify and credit your deposit."})]})]})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Network:"}),(0,a.jsx)("span",{className:"text-gray-900 ml-2 font-medium",children:e.network})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Currency:"}),(0,a.jsx)("span",{className:"text-gray-900 ml-2 font-medium",children:e.currency})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Min Amount:"}),(0,a.jsxs)("span",{className:"text-gray-900 ml-2 font-medium",children:[(0,P.vv)(e.minDepositAmount)," USDT"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Max Amount:"}),(0,a.jsxs)("span",{className:"text-gray-900 ml-2 font-medium",children:[(0,P.vv)(e.maxDepositAmount)," USDT"]})]})]})]})]}),(0,a.jsxs)(o.Zp,{className:"bg-white border-gray-200 shadow-sm",children:[(0,a.jsx)(o.aR,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(o.ZB,{className:"text-gray-900",children:"Deposit Address"}),e.tronNetwork&&(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 rounded-full ".concat("mainnet"===e.tronNetwork?"bg-green-500":"bg-orange-500"," animate-pulse")}),(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"mainnet"===e.tronNetwork?"Mainnet Active":"Testnet Active"})]})]})}),(0,a.jsxs)(o.Wu,{className:"space-y-4",children:[(0,a.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1 mr-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"USDT Address"}),e.tronNetwork&&(0,a.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat("mainnet"===e.tronNetwork?"bg-green-100 text-green-700":"bg-orange-100 text-orange-700"),children:"mainnet"===e.tronNetwork?"Mainnet":"Testnet"})]}),(0,a.jsx)("p",{className:"text-gray-900 font-mono text-sm break-all",children:e.depositAddress})]}),(0,a.jsxs)("button",{onClick:E,className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors",children:[N?(0,a.jsx)(_.A,{className:"w-4 h-4"}):(0,a.jsx)($.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:N?"Copied!":"Copy"})]})]})}),(0,a.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(z.A,{className:"w-5 h-5 text-yellow-600 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-yellow-700 font-medium",children:"Important Notice"}),(0,a.jsx)("p",{className:"text-gray-700 text-sm mt-1",children:"Only send USDT on the TRC20 network to this address. Sending other cryptocurrencies or using different networks will result in permanent loss of funds."})]})]})})]})]}),(0,a.jsxs)(o.Zp,{className:"bg-white border-gray-200 shadow-sm",children:[(0,a.jsx)(o.aR,{children:(0,a.jsx)(o.ZB,{className:"text-gray-900",children:"Verify Your Deposit"})}),(0,a.jsx)(o.Wu,{children:(0,a.jsxs)("form",{onSubmit:S,className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-gray-700 text-sm mb-2",children:"Transaction ID"}),(0,a.jsx)(o.pd,{type:"text",value:f,onChange:e=>y(e.target.value),placeholder:"Enter your TRON transaction ID (64 characters)",className:"bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:border-blue-500",disabled:x}),(0,a.jsx)("p",{className:"text-gray-600 text-xs mt-1",children:"Transaction ID should be 64 characters long and contain only letters and numbers"})]}),h&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,a.jsx)("p",{className:"text-red-700",children:h})}),g&&(0,a.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:(0,a.jsx)("p",{className:"text-green-700",children:g})}),(0,a.jsx)(o.$n,{type:"submit",disabled:!f.trim()||x,className:"w-full bg-blue-600 hover:bg-blue-700 text-white disabled:opacity-50",children:x?(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(G.A,{className:"w-4 h-4 animate-spin"}),(0,a.jsx)("span",{children:"Verifying Transaction..."})]}):"Verify Deposit"})]})})]}),i.length>0&&(0,a.jsxs)(o.Zp,{className:"bg-white border-gray-200 shadow-sm",children:[(0,a.jsx)(o.aR,{children:(0,a.jsx)(o.ZB,{className:"text-gray-900",children:"Recent Deposits"})}),(0,a.jsx)(o.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:i.map(s=>(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[T(s.status),(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-gray-900 font-medium",children:[s.amount>0?(0,P.vv)(s.amount):"---"," USDT"]}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:(0,P.Yq)(s.createdAt)})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("div",{className:"inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ".concat(R(s.status)),children:(0,a.jsx)("span",{children:s.status.replace("_"," ")})}),(0,a.jsxs)("p",{className:"text-gray-600 text-xs mt-1",children:["TX: ",s.transactionId.slice(0,8),"...",s.transactionId.slice(-8)]})]})]}),(0,a.jsxs)("div",{className:"mt-3 p-3 bg-blue-50 border border-blue-200 rounded",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("p",{className:"text-blue-700 text-sm",children:I(s.status,s.confirmations,null==e?void 0:e.minConfirmations)}),D(s.status,s.confirmations,null==e?void 0:e.minConfirmations)&&(0,a.jsxs)("p",{className:"text-blue-600 text-xs",children:["ETA: ",D(s.status,s.confirmations,null==e?void 0:e.minConfirmations)]})]}),s.confirmations>0&&(null==e?void 0:e.minConfirmations)&&(0,a.jsxs)("div",{className:"mt-2",children:[(0,a.jsxs)("div",{className:"flex justify-between text-xs text-blue-600 mb-1",children:[(0,a.jsx)("span",{children:"Confirmations"}),(0,a.jsxs)("span",{children:[s.confirmations,"/",e.minConfirmations]})]}),(0,a.jsx)("div",{className:"w-full bg-blue-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:"".concat(Math.min(s.confirmations/e.minConfirmations*100,100),"%")}})})]})]}),s.failureReason&&(0,a.jsx)("div",{className:"mt-3 p-3 bg-red-50 border border-red-200 rounded",children:(0,a.jsx)("p",{className:"text-red-700 text-sm",children:s.failureReason})})]},s.id))})})]})]}):(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(p.A,{className:"h-12 w-12 text-gray-600 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Deposit Address Not Configured"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Please contact support to enable deposits."})]}):(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(z.A,{className:"h-12 w-12 text-yellow-600 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Deposits Temporarily Disabled"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Deposit service is currently unavailable. Please check back later."})]}):(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(z.A,{className:"h-12 w-12 text-red-600 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Deposit Service Unavailable"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Unable to load deposit information. Please try again later."})]})},J=()=>{let[e,s]=(0,r.useState)("overview"),[t,n]=(0,r.useState)(null),[i,l]=(0,r.useState)(null),[c,x]=(0,r.useState)(!0),[m,h]=(0,r.useState)(!1),[u,g]=(0,r.useState)({amount:"",usdtAddress:""}),[f,y]=(0,r.useState)(""),[N,b]=(0,r.useState)(!1),[v,w]=(0,r.useState)(""),{showConfirm:A,hideConfirm:k,ConfirmDialog:E}=(0,o.G_)(),{showMessage:S,hideMessage:T,MessageBoxComponent:R}=(0,o.eC)(),[I,D]=(0,r.useState)([]),[F,M]=(0,r.useState)(!1),[O,U]=(0,r.useState)(""),[W,B]=(0,r.useState)("ALL"),[q,J]=(0,r.useState)("ALL"),[X,Q]=(0,r.useState)(!1),[ee,es]=(0,r.useState)([]),[et,ea]=(0,r.useState)([]),[er,en]=(0,r.useState)(null),[ei,el]=(0,r.useState)(!1);(0,r.useEffect)(()=>{ec(),eh(),eo(),eu();let e=setInterval(()=>{ed(),ex()},3e4);return()=>{clearInterval(e)}},[]),(0,r.useEffect)(()=>{eo()},[O,W,q]);let ec=async()=>{try{let e=await fetch("/api/wallet/balance",{credentials:"include"});if(e.ok){let s=await e.json();s.success&&n(s.data)}}catch(e){console.error("Failed to fetch wallet data:",e)}finally{x(!1)}},ed=async()=>{try{let e=await fetch("/api/wallet/balance",{credentials:"include"});if(e.ok){let s=await e.json();s.success&&n(s.data)}}catch(e){console.error("Failed to fetch wallet data silently:",e)}},eo=async()=>{try{M(!0);let e=new URLSearchParams;e.append("limit","50"),O&&e.append("search",O),"ALL"!==W&&e.append("type",W),"ALL"!==q&&e.append("status",q);let s=await fetch("/api/wallet/transactions?".concat(e),{credentials:"include"});if(s.ok){let e=await s.json();e.success&&(D(e.data.transactions),es(e.data.filters.transactionTypes),ea(e.data.filters.statusOptions))}}catch(e){console.error("Failed to fetch transactions:",e)}finally{M(!1)}},ex=async()=>{try{let e=new URLSearchParams;e.append("limit","50"),O&&e.append("search",O),"ALL"!==W&&e.append("type",W),"ALL"!==q&&e.append("status",q);let s=await fetch("/api/wallet/transactions?".concat(e),{credentials:"include"});if(s.ok){let e=await s.json();e.success&&(D(e.data.transactions),es(e.data.filters.transactionTypes),ea(e.data.filters.statusOptions))}}catch(e){console.error("Failed to fetch transactions silently:",e)}},em=e=>{en(e),el(!0)},eh=async()=>{try{let e=await fetch("/api/wallet/withdrawal-settings",{credentials:"include"});if(e.ok){let s=await e.json();s.success&&l(s.data)}}catch(e){console.error("Failed to fetch withdrawal settings:",e)}},eu=async()=>{try{let e=await fetch("/api/user/withdrawal-address",{credentials:"include"});if(e.ok){let s=await e.json();s.success&&s.data.withdrawalAddress&&(y(s.data.withdrawalAddress),u.usdtAddress||g(e=>({...e,usdtAddress:s.data.withdrawalAddress})))}}catch(e){console.error("Failed to fetch user withdrawal address:",e)}},ep=async e=>{e.preventDefault(),w("");try{let e=parseFloat(u.amount);if(!e||e<=0)throw Error("Please enter a valid amount");if(!u.usdtAddress)throw Error("Please enter a USDT address");let s=(null==i?void 0:i.fixedFee)||3,t=e*((null==i?void 0:i.percentageFee)||1)/100,r=s+t,n=e-r;A({title:"Confirm Withdrawal",message:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Withdrawal Amount:"}),(0,a.jsxs)("span",{className:"font-semibold",children:["$",e.toFixed(2)," USDT"]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Fixed Fee:"}),(0,a.jsxs)("span",{className:"text-red-600",children:["-$",s.toFixed(2)," USDT"]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Percentage Fee (1%):"}),(0,a.jsxs)("span",{className:"text-red-600",children:["-$",t.toFixed(2)," USDT"]})]}),(0,a.jsxs)("div",{className:"border-t pt-2 flex justify-between font-semibold",children:[(0,a.jsx)("span",{className:"text-green-600",children:"You Receive:"}),(0,a.jsxs)("span",{className:"text-green-600",children:["$",n.toFixed(2)," USDT"]})]})]}),(0,a.jsx)("div",{className:"bg-blue-50 p-3 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-blue-800",children:[(0,a.jsx)("strong",{children:"USDT Address:"}),(0,a.jsx)("br",{}),u.usdtAddress]})}),(0,a.jsx)("div",{className:"bg-yellow-50 p-3 rounded-lg",children:(0,a.jsx)("p",{className:"text-sm text-yellow-800",children:"⚠️ Please double-check your address. Transactions cannot be reversed."})})]}),confirmText:"Confirm Withdrawal",cancelText:"Cancel",variant:"warning",onConfirm:()=>eg(e)})}catch(e){w(e.message||"Invalid withdrawal details")}},eg=async e=>{b(!0);try{let t=await fetch("/api/wallet/withdraw",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({amount:e,usdtAddress:u.usdtAddress})}),a=await t.json();if(!a.success)throw Error(a.error||"Withdrawal failed");S({title:"Withdrawal Submitted",message:"Your withdrawal request has been submitted successfully. It will be processed within 3 business days.",variant:"success",buttonText:"OK"}),g({amount:"",usdtAddress:""}),s("overview"),ec(),eo()}catch(e){S({title:"Withdrawal Failed",message:e.message||"Failed to process withdrawal. Please try again.",variant:"error",buttonText:"OK"})}finally{b(!1)}},ej=e=>{switch(e){case"WITHDRAWAL":return(0,a.jsx)(Z.A,{className:"h-4 w-4 text-red-500"});case"DEPOSIT":case"MINING_EARNINGS":case"DIRECT_REFERRAL":case"BINARY_BONUS":return(0,a.jsx)(L.A,{className:"h-4 w-4 text-eco-500"});default:return(0,a.jsx)(p.A,{className:"h-4 w-4 text-gray-500"})}},ef=e=>{switch(e){case"WITHDRAWAL":case"PURCHASE":return"text-red-600";case"DEPOSIT":case"MINING_EARNINGS":case"DIRECT_REFERRAL":case"BINARY_BONUS":return"text-eco-600";default:return"text-gray-600"}},ey=e=>{switch(e){case"COMPLETED":case"APPROVED":return(0,a.jsx)(_.A,{className:"h-4 w-4 text-eco-500"});case"PENDING":return(0,a.jsx)(C.A,{className:"h-4 w-4 text-solar-500"});case"FAILED":case"REJECTED":return(0,a.jsx)(H.A,{className:"h-4 w-4 text-red-500"});default:return(0,a.jsx)(C.A,{className:"h-4 w-4 text-gray-500"})}},eN=e=>{if(!i||!e)return{fixedFee:0,percentageFee:0,totalFees:0,totalDeduction:0,netAmount:0};let s=i.fixedFee,t=e*i.percentageFee/100,a=s+t;return{fixedFee:s,percentageFee:t,totalFees:a,totalDeduction:e+a,netAmount:e}};if(c)return(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)("div",{className:"animate-pulse",children:(0,a.jsx)("div",{className:"h-32 bg-gray-200 rounded-xl"})})});if(!t)return(0,a.jsx)(o.Zp,{children:(0,a.jsx)(o.Wu,{className:"text-center py-8",children:(0,a.jsx)("p",{className:"text-gray-500",children:"Failed to load wallet data"})})});let eb=[{id:"overview",label:"Overview",icon:p.A},{id:"deposit",label:"Deposit",icon:L.A},{id:"withdraw",label:"Withdraw",icon:Z.A}],ev=()=>(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsx)("div",{children:(0,a.jsxs)(d.xA,{cols:{default:1,lg:2},gap:8,children:[(0,a.jsx)(o.Zp,{children:(0,a.jsxs)(o.Wu,{className:"p-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600 mb-2",children:"Available Balance"}),(0,a.jsx)("p",{className:"text-4xl font-bold text-dark-900",children:(0,P.vv)(t.balance)})]}),(0,a.jsx)("div",{className:"h-16 w-16 bg-eco-100 rounded-xl flex items-center justify-center",children:(0,a.jsx)(p.A,{className:"h-8 w-8 text-eco-600"})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3",children:[(0,a.jsxs)(o.$n,{onClick:()=>s("deposit"),variant:"outline",className:"h-12 text-base font-semibold rounded-xl",children:[(0,a.jsx)(L.A,{className:"h-5 w-5 mr-2"}),"Deposit"]}),(0,a.jsxs)(o.$n,{onClick:()=>s("withdraw"),className:"h-12 text-base font-semibold rounded-xl",disabled:t.balance<10,children:[(0,a.jsx)(Z.A,{className:"h-5 w-5 mr-2"}),"Withdraw"]})]})]})}),(0,a.jsx)(o.Zp,{children:(0,a.jsxs)(o.Wu,{className:"p-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600 mb-2",children:"Pending Earnings"}),(0,a.jsx)("p",{className:"text-4xl font-bold text-solar-600",children:(0,P.vv)(t.pendingEarnings)})]}),(0,a.jsx)("div",{className:"h-16 w-16 bg-solar-100 rounded-xl flex items-center justify-center",children:(0,a.jsx)(C.A,{className:"h-8 w-8 text-solar-600"})})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 font-medium",children:"Will be transferred on Saturday at 15:00 UTC"})]})})]})}),(0,a.jsxs)(o.Zp,{children:[(0,a.jsx)(o.aR,{children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4",children:[(0,a.jsx)(o.ZB,{children:"Transaction History"}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row items-stretch sm:items-center gap-2",children:[(0,a.jsxs)(o.$n,{variant:"outline",size:"sm",onClick:()=>Q(!X),className:"flex items-center justify-center space-x-2 w-full sm:w-auto",children:[(0,a.jsx)(V.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Filters"})]}),(0,a.jsxs)(o.$n,{variant:"outline",size:"sm",onClick:eo,disabled:F,className:"flex items-center justify-center space-x-2 w-full sm:w-auto",children:[(0,a.jsx)(G.A,{className:"h-4 w-4 ".concat(F?"animate-spin":"")}),(0,a.jsx)("span",{children:"Refresh"})]})]})]})}),(0,a.jsxs)(o.Wu,{children:[(0,a.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(Y.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,a.jsx)(o.pd,{placeholder:"Search transactions by description, type, or reference...",value:O,onChange:e=>U(e.target.value),className:"pl-10"})]}),X&&(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Transaction Type"}),(0,a.jsx)("select",{value:W,onChange:e=>B(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-eco-500 text-sm",children:ee.map(e=>(0,a.jsx)("option",{value:e,children:"ALL"===e?"All Types":e.replace("_"," ")},e))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Status"}),(0,a.jsx)("select",{value:q,onChange:e=>J(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-eco-500 text-sm",children:et.map(e=>(0,a.jsx)("option",{value:e,children:"ALL"===e?"All Status":e.replace("_"," ")},e))})]})]})]}),F?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(G.A,{className:"h-8 w-8 animate-spin mx-auto text-gray-400 mb-2"}),(0,a.jsx)("p",{className:"text-gray-500",children:"Loading transactions..."})]}):I.length>0?(0,a.jsx)("div",{className:"space-y-3",children:I.map(e=>(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer gap-3",onClick:()=>em(e),children:[(0,a.jsxs)("div",{className:"flex items-start sm:items-center space-x-3 flex-1 min-w-0",children:[(0,a.jsx)("div",{className:"flex-shrink-0 mt-1 sm:mt-0",children:ej(e.type)}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,a.jsx)("p",{className:"font-medium text-dark-900 truncate flex-1",children:e.description}),e.description.includes("excess discarded due to 5x mining unit limit cap")&&(0,a.jsx)("div",{className:"flex-shrink-0 mt-0.5",title:"Amount reduced due to mining unit capacity limits",children:(0,a.jsx)("svg",{className:"h-4 w-4 text-amber-500",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:space-x-4 text-sm text-gray-500 mt-1 gap-1 sm:gap-0",children:[(0,a.jsx)("span",{className:"text-xs sm:text-sm",children:(0,P.r6)(e.createdAt)}),(0,a.jsx)("span",{className:"px-2 py-1 bg-gray-200 rounded text-xs w-fit",children:e.type.replace("_"," ")}),e.reference&&(0,a.jsxs)("span",{className:"px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs cursor-pointer hover:bg-blue-200 w-fit",onClick:s=>{s.stopPropagation(),(0,P.lW)(e.reference)},title:"Click to copy reference",children:["Ref: ",e.reference.substring(0,8),"..."]})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between sm:justify-end sm:space-x-3 gap-2 flex-shrink-0",children:[(0,a.jsxs)("span",{className:"font-semibold text-lg sm:text-base ".concat(ef(e.type)),children:["WITHDRAWAL"===e.type||"PURCHASE"===e.type?"-":"+",(0,P.vv)(e.amount)]}),(0,a.jsx)("div",{className:"flex-shrink-0",children:ey(e.status)})]})]},e.id))}):(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("p",{className:"text-gray-500",children:"No transactions found"}),(O||"ALL"!==W||"ALL"!==q)&&(0,a.jsx)("p",{className:"text-sm text-gray-400 mt-2",children:"Try adjusting your search or filter criteria"})]})]})]})]}),ew=()=>{let e=parseFloat(u.amount)||0,r=eN(e);return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Withdraw USDT"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"Withdraw funds from your wallet to your USDT TRC20 address"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsx)(o.Zp,{className:"bg-white border-gray-200 shadow-sm",children:(0,a.jsx)(o.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Available Balance"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[(0,P.vv)((null==t?void 0:t.balance)||0)," USDT"]})]}),(0,a.jsx)(p.A,{className:"w-8 h-8 text-green-600"})]})})}),(0,a.jsx)(o.Zp,{className:"bg-white border-gray-200 shadow-sm",children:(0,a.jsx)(o.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Minimum Withdrawal"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[(0,P.vv)((null==i?void 0:i.minWithdrawalAmount)||10)," USDT"]})]}),(0,a.jsx)(Z.A,{className:"w-8 h-8 text-blue-600"})]})})}),(0,a.jsx)(o.Zp,{className:"bg-white border-gray-200 shadow-sm",children:(0,a.jsx)(o.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Network"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"USDT (TRC20)"})]}),(0,a.jsx)(j.A,{className:"w-8 h-8 text-purple-600"})]})})})]}),(0,a.jsxs)(o.Zp,{className:"bg-white border-gray-200 shadow-sm",children:[(0,a.jsx)(o.aR,{children:(0,a.jsx)(o.ZB,{className:"text-gray-900",children:"Withdrawal Details"})}),(0,a.jsx)(o.Wu,{children:(0,a.jsxs)("form",{onSubmit:ep,className:"space-y-6",children:[v&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg text-sm",children:v}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Withdrawal Amount (USDT)"}),(0,a.jsx)(o.pd,{type:"number",step:"0.01",min:(null==i?void 0:i.minWithdrawalAmount)||10,max:(null==t?void 0:t.balance)||0,value:u.amount,onChange:e=>g(s=>({...s,amount:e.target.value})),placeholder:"Enter amount to withdraw",className:"bg-white border-gray-300 text-gray-900",required:!0}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["Min: ",(0,P.vv)((null==i?void 0:i.minWithdrawalAmount)||10)," USDT"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"USDT TRC20 Address"}),(0,a.jsx)(o.pd,{type:"text",value:u.usdtAddress,onChange:e=>g(s=>({...s,usdtAddress:e.target.value})),placeholder:"Enter your USDT TRC20 address",className:"bg-white border-gray-300 text-gray-900",required:!0}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:f&&u.usdtAddress===f?"Auto-filled from your saved withdrawal address":"Only TRC20 addresses are supported"})]})]}),e>0&&(0,a.jsxs)(o.Zp,{className:"bg-gray-50 border-gray-200",children:[(0,a.jsx)(o.aR,{children:(0,a.jsx)(o.ZB,{className:"text-lg text-gray-900",children:"Transaction Summary"})}),(0,a.jsx)(o.Wu,{children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Withdrawal Amount:"}),(0,a.jsxs)("span",{className:"font-semibold text-gray-900",children:[(0,P.vv)(e)," USDT"]})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Fixed Fee:"}),(0,a.jsxs)("span",{className:"font-semibold text-red-600",children:["-",(0,P.vv)(r.fixedFee)," USDT"]})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("span",{className:"text-gray-600",children:["Percentage Fee (",(null==i?void 0:i.percentageFee)||0,"%):"]}),(0,a.jsxs)("span",{className:"font-semibold text-red-600",children:["-",(0,P.vv)(r.percentageFee)," USDT"]})]}),(0,a.jsxs)("div",{className:"border-t border-gray-300 pt-3",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-900 font-medium",children:"Total Fees:"}),(0,a.jsxs)("span",{className:"font-bold text-red-600",children:[(0,P.vv)(r.totalFees)," USDT"]})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center mt-2",children:[(0,a.jsx)("span",{className:"text-gray-900 font-medium",children:"Total Deduction:"}),(0,a.jsxs)("span",{className:"font-bold text-red-600",children:[(0,P.vv)(r.totalDeduction)," USDT"]})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center mt-3 p-3 bg-green-50 rounded-lg",children:[(0,a.jsx)("span",{className:"text-green-800 font-bold text-lg",children:"You Receive:"}),(0,a.jsxs)("span",{className:"font-bold text-green-600 text-xl",children:[(0,P.vv)(r.netAmount)," USDT"]})]})]})]})})]})]}),(0,a.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(z.A,{className:"w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-yellow-800 mb-2",children:"Important Information"}),(0,a.jsxs)("ul",{className:"text-sm text-yellow-700 space-y-1",children:[(0,a.jsx)("li",{children:"• Only USDT TRC20 addresses are supported"}),(0,a.jsx)("li",{children:"• Withdrawals require KYC verification"}),(0,a.jsxs)("li",{children:["• Processing time: ",(null==i?void 0:i.processingDays)||3," business days"]}),(0,a.jsx)("li",{children:"• Double-check your address - transactions cannot be reversed"}),(0,a.jsx)("li",{children:"• Fees are deducted from your balance in addition to the withdrawal amount"})]})]})]})}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3",children:[(0,a.jsx)(o.$n,{type:"button",variant:"outline",onClick:()=>s("overview"),className:"flex-1 border-gray-300 text-gray-700 hover:bg-gray-50",children:"Back to Overview"}),(0,a.jsx)(o.$n,{type:"submit",loading:N,className:"flex-1 bg-yellow-500 hover:bg-yellow-600 text-white",disabled:!t||!i||e<((null==i?void 0:i.minWithdrawalAmount)||10)||r.totalDeduction>t.balance,children:r.totalDeduction>((null==t?void 0:t.balance)||0)?"Insufficient Balance":"Submit Withdrawal"})]})]})})]})]})};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"border-b border-gray-200",children:(0,a.jsx)("nav",{className:"-mb-px flex space-x-8",children:eb.map(t=>{let r=t.icon,n=e===t.id;return(0,a.jsxs)("button",{onClick:()=>s(t.id),className:"\n                  flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm transition-colors\n                  ".concat(n?"border-solar-500 text-solar-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300","\n                "),children:[(0,a.jsx)(r,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:t.label})]},t.id)})})}),(()=>{switch(e){case"deposit":return(0,a.jsx)(K,{});case"withdraw":return ew();default:return ev()}})(),ei&&er&&(0,a.jsx)(o.aF,{isOpen:ei,onClose:()=>el(!1),title:"Transaction Details",size:"lg",children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[ej(er.type),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-lg text-dark-900",children:er.description}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[er.type.replace("_"," ")," • ",(0,P.r6)(er.createdAt)]})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[ey(er.status),(0,a.jsx)("span",{className:"px-3 py-1 rounded-full text-sm font-medium ".concat("COMPLETED"===er.status||"CONFIRMED"===er.status?"bg-eco-100 text-eco-700":"PENDING"===er.status?"bg-solar-100 text-solar-700":"bg-red-100 text-red-700"),children:er.status})]}),(0,a.jsxs)("p",{className:"text-xl font-bold ".concat(ef(er.type)),children:["WITHDRAWAL"===er.type||"PURCHASE"===er.type?"-":"+",(0,P.vv)(er.amount)]})]})]}),er.description&&(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Description"}),(0,a.jsxs)("div",{className:"p-3 rounded-lg text-sm ".concat(er.description.includes("excess discarded due to 5x mining unit limit cap")?"bg-amber-50 border border-amber-200":"bg-gray-50"),children:[(0,a.jsx)("p",{className:"text-gray-900 leading-relaxed",children:er.description}),er.description.includes("excess discarded due to 5x mining unit limit cap")&&(0,a.jsxs)("div",{className:"mt-2 flex items-start space-x-2",children:[(0,a.jsx)("div",{className:"flex-shrink-0 mt-0.5",children:(0,a.jsx)("svg",{className:"h-4 w-4 text-amber-600",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),(0,a.jsxs)("p",{className:"text-amber-800 text-xs",children:[(0,a.jsx)("strong",{children:"Note:"})," Some amount was not credited due to mining unit capacity limits (5x investment return cap)."]})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Transaction ID"}),(0,a.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,a.jsx)("p",{className:"text-sm text-gray-900 font-mono bg-gray-100 px-2 py-1 rounded break-all flex-1 min-w-0",children:er.id}),(0,a.jsx)("button",{onClick:()=>(0,P.lW)(er.id),className:"text-gray-400 hover:text-gray-600 flex-shrink-0 mt-1",children:(0,a.jsx)($.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Type"}),(0,a.jsx)("p",{className:"text-sm text-gray-900",children:er.type.replace("_"," ")})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[ey(er.status),(0,a.jsx)("span",{className:"text-sm text-gray-900",children:er.status})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Date & Time"}),(0,a.jsx)("p",{className:"text-sm text-gray-900",children:(0,P.r6)(er.createdAt)})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[er.reference&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Reference"}),(0,a.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,a.jsx)("p",{className:"text-sm text-gray-900 font-mono bg-gray-100 px-2 py-1 rounded break-all flex-1 min-w-0",children:er.reference}),(0,a.jsx)("button",{onClick:()=>(0,P.lW)(er.reference),className:"text-gray-400 hover:text-gray-600 flex-shrink-0 mt-1",children:(0,a.jsx)($.A,{className:"h-4 w-4"})})]})]}),er.txid&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Transaction Hash"}),(0,a.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,a.jsx)("p",{className:"text-sm text-gray-900 font-mono bg-gray-100 px-2 py-1 rounded break-all flex-1 min-w-0",children:er.txid}),(0,a.jsx)("button",{onClick:()=>(0,P.lW)(er.txid),className:"text-gray-400 hover:text-gray-600 flex-shrink-0 mt-1",children:(0,a.jsx)($.A,{className:"h-4 w-4"})})]})]}),er.usdtAddress&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"DEPOSIT"===er.type?"Deposit Address":"Withdrawal Address"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("p",{className:"text-sm text-gray-900 font-mono bg-gray-100 px-2 py-1 rounded break-all",children:er.usdtAddress}),(0,a.jsx)("button",{onClick:()=>(0,P.lW)(er.usdtAddress),className:"text-gray-400 hover:text-gray-600 flex-shrink-0",children:(0,a.jsx)($.A,{className:"h-4 w-4"})})]})]}),void 0!==er.confirmations&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Confirmations"}),(0,a.jsx)("p",{className:"text-sm text-gray-900",children:er.confirmations})]}),er.processedAt&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Processed At"}),(0,a.jsx)("p",{className:"text-sm text-gray-900",children:(0,P.r6)(er.processedAt)})]})]})]}),er.rejectionReason&&(0,a.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"font-medium text-red-800 mb-2",children:"Rejection Reason"}),(0,a.jsx)("p",{className:"text-sm text-red-700",children:er.rejectionReason})]}),er.senderAddress&&(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"font-medium text-blue-800 mb-2",children:"Sender Information"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("p",{className:"text-sm text-blue-700 font-mono",children:er.senderAddress}),(0,a.jsx)("button",{onClick:()=>(0,P.lW)(er.senderAddress),className:"text-blue-400 hover:text-blue-600",children:(0,a.jsx)($.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-4 border-t",children:[(0,a.jsx)(o.$n,{variant:"outline",onClick:()=>el(!1),children:"Close"}),er.txid&&(0,a.jsxs)(o.$n,{onClick:()=>{let e="https://tronscan.org/#/transaction/".concat(er.txid);window.open(e,"_blank")},className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{children:"View on Explorer"}),(0,a.jsx)(Z.A,{className:"h-4 w-4"})]})]})]})}),(0,a.jsx)(E,{}),(0,a.jsx)(R,{})]})};var X=t(6516),Q=t(8749),ee=t(2657),es=t(2713),et=t(6785);function ea(){let[e,s]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{s(!0)},[]),e}let er=()=>{let[e,s]=(0,r.useState)(null),[t,n]=(0,r.useState)(!0),i=function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e3,[t,a]=(0,r.useState)(null),n=ea();return(0,r.useEffect)(()=>{if(!n)return;a(e());let t=setInterval(()=>{a(e())},s);return()=>clearInterval(t)},[n,e,s]),t}(P.D1)||{days:0,hours:0,minutes:0,seconds:0};(0,r.useEffect)(()=>{l()},[]);let l=async()=>{try{let e=await fetch("/api/binary-points/info",{credentials:"include"});if(e.ok){let t=await e.json();t.success&&s(t.data)}}catch(e){console.error("Failed to fetch binary points info:",e)}finally{n(!1)}};if(t)return(0,a.jsx)("div",{className:"space-y-6",children:Array.from({length:3}).map((e,s)=>(0,a.jsx)("div",{className:"animate-pulse",children:(0,a.jsx)("div",{className:"h-32 bg-gray-200 rounded-xl"})},s))});if(!e)return(0,a.jsx)(o.Zp,{children:(0,a.jsx)(o.Wu,{className:"text-center py-8",children:(0,a.jsx)("p",{className:"text-gray-500",children:"Failed to load binary points information"})})});let c=e=>{let{value:s,max:t,color:r="bg-green-500",warningThreshold:n=90}=e,i=s/t*100;return(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3",children:(0,a.jsx)("div",{className:"h-3 rounded-full transition-all duration-300 ".concat(i>=n?"bg-red-500":r),style:{width:"".concat(Math.min(i,100),"%")}})})};return(0,a.jsxs)("div",{className:"space-y-6",children:[e.hasWarnings&&(0,a.jsxs)(o.Zp,{className:"border-orange-200 bg-orange-50",children:[(0,a.jsx)(o.aR,{children:(0,a.jsxs)(o.ZB,{className:"flex items-center gap-2 text-orange-800",children:[(0,a.jsx)(z.A,{className:"h-5 w-5"}),"Important Notices"]})}),(0,a.jsx)(o.Wu,{children:(0,a.jsx)("ul",{className:"space-y-2",children:e.warnings.map((e,s)=>(0,a.jsxs)("li",{className:"flex items-start gap-2 text-orange-700",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0"}),(0,a.jsx)("span",{className:"text-sm",children:e})]},s))})})]}),(0,a.jsxs)(o.Zp,{children:[(0,a.jsx)(o.aR,{children:(0,a.jsxs)(o.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(es.A,{className:"h-5 w-5 text-green-600"}),"Current Binary Points Status"]})}),(0,a.jsxs)(o.Wu,{children:[(0,a.jsxs)(d.xA,{cols:{default:1,md:2},gap:6,children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Left Side Points"}),(0,a.jsx)("span",{className:"text-lg font-bold text-green-600",children:(0,P.ZV)(e.currentPoints.left,0)})]}),(0,a.jsx)(c,{value:e.currentPoints.leftCapped,max:e.limits.maxPointsPerSide,color:"bg-green-500"}),(0,a.jsxs)("div",{className:"flex justify-between text-xs text-gray-500",children:[(0,a.jsx)("span",{children:"0"}),(0,a.jsxs)("span",{children:[(0,P.ZV)(e.limits.maxPointsPerSide,0)," (cap)"]})]}),e.pressureOut.leftAmount>0&&(0,a.jsxs)("p",{className:"text-xs text-red-600",children:[(0,P.ZV)(e.pressureOut.leftAmount,0)," points will be flushed"]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Right Side Points"}),(0,a.jsx)("span",{className:"text-lg font-bold text-blue-600",children:(0,P.ZV)(e.currentPoints.right,0)})]}),(0,a.jsx)(c,{value:e.currentPoints.rightCapped,max:e.limits.maxPointsPerSide,color:"bg-blue-500"}),(0,a.jsxs)("div",{className:"flex justify-between text-xs text-gray-500",children:[(0,a.jsx)("span",{children:"0"}),(0,a.jsxs)("span",{children:[(0,P.ZV)(e.limits.maxPointsPerSide,0)," (cap)"]})]}),e.pressureOut.rightAmount>0&&(0,a.jsxs)("p",{className:"text-xs text-red-600",children:[(0,P.ZV)(e.pressureOut.rightAmount,0)," points will be flushed"]})]})]}),(0,a.jsxs)("div",{className:"mt-6 p-4 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(et.A,{className:"h-5 w-5 text-purple-600"}),(0,a.jsx)("span",{className:"font-medium text-gray-900",children:"Points Available for Matching"})]}),(0,a.jsx)("span",{className:"text-2xl font-bold text-purple-600",children:(0,P.ZV)(e.currentPoints.matchable,0)})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600 mt-2",children:["Minimum of left (",(0,P.ZV)(e.currentPoints.leftCapped,0),") and right (",(0,P.ZV)(e.currentPoints.rightCapped,0),") sides"]})]})]})]}),(0,a.jsxs)(o.Zp,{children:[(0,a.jsx)(o.aR,{children:(0,a.jsxs)(o.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(C.A,{className:"h-5 w-5 text-blue-600"}),"Next Weekly Binary Matching"]})}),(0,a.jsx)(o.Wu,{children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:e.nextMatching.schedule}),(0,a.jsxs)("div",{className:"grid grid-cols-4 gap-4 mb-4",suppressHydrationWarning:!0,children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:i.days}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"Days"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:i.hours}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"Hours"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:i.minutes}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"Minutes"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:i.seconds}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"Seconds"})]})]}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:e.nextMatching.description})]})})]}),(0,a.jsxs)(o.Zp,{children:[(0,a.jsx)(o.aR,{children:(0,a.jsxs)(o.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(M.A,{className:"h-5 w-5 text-green-600"}),"Earnings Estimation"]})}),(0,a.jsx)(o.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-green-50 rounded-lg",children:[(0,a.jsx)("span",{className:"font-medium text-green-800",children:"Estimated Weekly Payout"}),(0,a.jsx)("span",{className:"text-xl font-bold text-green-600",children:(0,P.vv)(e.earnings.estimatedPayout)})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Point Value:"}),(0,a.jsxs)("div",{className:"font-semibold",children:[(0,P.vv)(e.earnings.pointValue)," per point"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Your Matchable Points:"}),(0,a.jsx)("div",{className:"font-semibold",children:(0,P.ZV)(e.earnings.matchablePoints,0)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Estimated Payout:"}),(0,a.jsx)("div",{className:"font-semibold",children:(0,P.vv)(e.earnings.estimatedPayout)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Max Points Per Side:"}),(0,a.jsx)("div",{className:"font-semibold",children:(0,P.ZV)(e.limits.maxPointsPerSide,0)})]})]})]})})]}),e.history.recentMatches.length>0&&(0,a.jsxs)(o.Zp,{children:[(0,a.jsx)(o.aR,{children:(0,a.jsxs)(o.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(u.A,{className:"h-5 w-5 text-purple-600"}),"Recent Binary Matching History"]})}),(0,a.jsx)(o.Wu,{children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-purple-50 rounded-lg",children:[(0,a.jsx)("span",{className:"font-medium text-purple-800",children:"Average Weekly Earnings"}),(0,a.jsx)("span",{className:"text-lg font-bold text-purple-600",children:(0,P.vv)(e.history.averageWeeklyEarnings)})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"Last 4 Weeks"}),e.history.recentMatches.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:(0,P.vv)(e.amount)}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:(0,P.Yq)(e.date)})]}),(0,a.jsx)("div",{className:"text-xs text-gray-600 max-w-xs text-right",children:e.description})]},s))]})]})})]}),(0,a.jsxs)(o.Zp,{children:[(0,a.jsx)(o.aR,{children:(0,a.jsxs)(o.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(q.A,{className:"h-5 w-5 text-blue-600"}),"How Binary Matching Works"]})}),(0,a.jsx)(o.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4 text-sm text-gray-700",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"Point Accumulation"}),(0,a.jsxs)("ul",{className:"space-y-1 ml-4",children:[(0,a.jsx)("li",{children:"• Every $1 invested by your downline = 1 binary point"}),(0,a.jsx)("li",{children:"• Points are added to left or right side based on placement"}),(0,a.jsxs)("li",{children:["• Maximum ",(0,P.ZV)(e.limits.maxPointsPerSide,0)," points per side"]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"Weekly Matching Process"}),(0,a.jsxs)("ul",{className:"space-y-1 ml-4",children:[(0,a.jsx)("li",{children:"• Matching occurs every Saturday at 15:00 UTC"}),(0,a.jsx)("li",{children:"• Matched points = minimum of left and right sides"}),(0,a.jsxs)("li",{children:["• Fixed earnings: ",(0,P.vv)(e.limits.pointValue)," per matched point"]}),(0,a.jsx)("li",{children:"• Excess points beyond cap are flushed (pressure-out)"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"Pressure-Out System"}),(0,a.jsxs)("ul",{className:"space-y-1 ml-4",children:[(0,a.jsxs)("li",{children:["• Points exceeding ",(0,P.ZV)(e.limits.maxPointsPerSide,0)," per side are automatically flushed"]}),(0,a.jsx)("li",{children:"• Encourages balanced team building on both sides"}),(0,a.jsx)("li",{children:"• Prevents point hoarding and ensures fair distribution"})]})]})]})})]})]})};var en=t(9121),ei=t(9212),el=t(2903),ec=t(4804);t(469);let ed=()=>{let e=(0,r.useRef)(null),[s,t]=(0,r.useState)(null),[n,i]=(0,r.useState)(!0),[l,c]=(0,r.useState)(new Set),[d,x]=(0,r.useState)(!0),m={top:50,right:120,bottom:50,left:120},h=1200-m.left-m.right,p=800-m.bottom-m.top,j=async()=>{try{i(!0);let e=await fetch("/api/referrals/tree?depth=20&enhanced=true&expanded=".concat(Array.from(l).join(",")),{credentials:"include"});if(e.ok){let s=await e.json();s.success&&t(s.data)}}catch(e){console.error("Failed to fetch binary tree data:",e)}finally{i(!1)}},f=(0,r.useCallback)(e=>(0,en.Ay)(e,s=>{let t=l.has(s.user.id);if(!(s===e||t))return null;let a=[];return s.leftChild&&(d||s.leftChild.user.isActive)&&a.push(s.leftChild),s.rightChild&&(d||s.rightChild.user.isActive)&&a.push(s.rightChild),a.length>0?a:null}),[d,l]),y=(0,r.useCallback)(()=>{if(s&&e.current)try{let t=(0,el.A)(e.current);t.selectAll("*").remove();let a=t.attr("width",h+m.left+m.right).attr("height",p+m.top+m.bottom),r=a.append("g").attr("class","tree-container").attr("transform","translate(".concat(m.left,",").concat(m.top,")")),n=(0,ec.s_)().scaleExtent([.1,3]).on("zoom",e=>{r.attr("transform",e.transform)});t.call(n);let i=(0,ei.A)().size([h,p]).nodeSize([250,200]).separation((e,s)=>e.parent===s.parent?2:3),c=f(s.treeStructure),d=i(c),o=d.descendants(),x=d.links(),u=1/0,g=-1/0;o.forEach(e=>{e.x<u&&(u=e.x),e.x>g&&(g=e.x)});let j=h/2-(u+g)/2;o.forEach(e=>{e.x+=j}),r.append("g").attr("class","links").selectAll(".link").data(x).enter().append("path").attr("class","link").attr("d",e=>{let s=e.source,t=e.target;return"M".concat(s.x,",").concat(s.y+60,"\n                C").concat(s.x,",").concat((s.y+t.y)/2,"\n                 ").concat(t.x,",").concat((s.y+t.y)/2,"\n                 ").concat(t.x,",").concat(t.y-60)}).style("fill","none").style("stroke","#94a3b8").style("stroke-width","2px").style("stroke-opacity",.6);let y=r.append("g").attr("class","nodes").selectAll(".node").data(o).enter().append("g").attr("class","node").attr("transform",e=>"translate(".concat(e.x-100,",").concat(e.y-60,")")).style("cursor","pointer");y.append("rect").attr("width",200).attr("height",120).attr("rx",12).attr("ry",12).style("fill","white").style("stroke",e=>e.data.user.isActive?(e.data.hasLeftChild||e.data.hasRightChild)&&!l.has(e.data.user.id)?"#3b82f6":"#10b981":"#6b7280").style("stroke-width",e=>(e.data.hasLeftChild||e.data.hasRightChild)&&!l.has(e.data.user.id)?3:2).style("filter","drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1))"),y.append("circle").attr("cx",100).attr("cy",25).attr("r",18).style("fill",e=>e.data.user.isActive?"#10b981":"#6b7280"),y.each(function(e){let s=(0,el.A)(this),t=e.data.user;t.profilePicture?(s.append("defs").append("pattern").attr("id","profile-".concat(t.id)).attr("patternUnits","objectBoundingBox").attr("width",1).attr("height",1).append("image").attr("href",t.profilePicture).attr("width",36).attr("height",36).attr("preserveAspectRatio","xMidYMid slice"),s.append("circle").attr("cx",100).attr("cy",25).attr("r",17).style("fill","url(#profile-".concat(t.id,")")).style("stroke",t.isActive?"#10b981":"#6b7280").style("stroke-width",2)):s.append("text").attr("x",100).attr("y",25).attr("dy","0.35em").style("text-anchor","middle").style("fill","white").style("font-weight","bold").style("font-size","12px").text(()=>{let e=t.firstName||"",s=t.lastName||"";return"".concat(e[0]||"").concat(s[0]||"").toUpperCase()})}),y.append("text").attr("x",100).attr("y",55).style("text-anchor","middle").style("font-weight","600").style("font-size","13px").style("fill","#1f2937").text(e=>"".concat(e.data.user.firstName," ").concat(e.data.user.lastName)),y.append("rect").attr("x",75).attr("y",65).attr("width",50).attr("height",16).attr("rx",8).style("fill",e=>e.data.user.isActive?"#dcfce7":"#f3f4f6").style("stroke",e=>e.data.user.isActive?"#16a34a":"#6b7280").style("stroke-width",1),y.append("text").attr("x",100).attr("y",73).attr("dy","0.35em").style("text-anchor","middle").style("font-size","10px").style("font-weight","500").style("fill",e=>e.data.user.isActive?"#16a34a":"#6b7280").text(e=>e.data.user.isActive?"Active":"Inactive"),y.filter(e=>e.data.sponsorInfo).append("text").attr("x",100).attr("y",90).style("text-anchor","middle").style("font-size","9px").style("fill","#3b82f6").text(e=>"Sponsor: ".concat(e.data.sponsorInfo.firstName," ").concat(e.data.sponsorInfo.lastName)),y.append("text").attr("x",100).attr("y",105).style("text-anchor","middle").style("font-size","10px").style("font-weight","500").style("fill","#4b5563").text(e=>"Team: ".concat(e.data.teamCounts.total," (L:").concat(e.data.teamCounts.left," R:").concat(e.data.teamCounts.right,")"));let b=y.filter(e=>e.data.hasLeftChild||e.data.hasRightChild);b.append("circle").attr("cx",185).attr("cy",15).attr("r",10).style("fill",e=>l.has(e.data.user.id)?"#ef4444":"#3b82f6").style("stroke","white").style("stroke-width",2).style("cursor","pointer").style("transition","all 0.2s ease").style("transform-origin","center").on("click",function(e,s){e.preventDefault(),e.stopPropagation(),N(s.data.user.id)}),b.append("text").attr("x",185).attr("y",15).attr("dy","0.35em").style("text-anchor","middle").style("font-size","12px").style("font-weight","bold").style("fill","white").style("cursor","pointer").style("pointer-events","none").text(e=>l.has(e.data.user.id)?"−":"+");let v=a.append("g").attr("class","zoom-controls").attr("transform","translate(".concat(h+m.left-100,", 20)")),w=v.append("g").style("cursor","pointer").on("click",()=>{t.transition().duration(300).call(n.scaleBy,1.5)});w.append("rect").attr("width",30).attr("height",30).attr("rx",4).style("fill","white").style("stroke","#d1d5db").style("stroke-width",1),w.append("text").attr("x",15).attr("y",15).attr("dy","0.35em").style("text-anchor","middle").style("font-size","16px").style("font-weight","bold").text("+");let A=v.append("g").attr("transform","translate(0, 35)").style("cursor","pointer").on("click",()=>{t.transition().duration(300).call(n.scaleBy,.67)});A.append("rect").attr("width",30).attr("height",30).attr("rx",4).style("fill","white").style("stroke","#d1d5db").style("stroke-width",1),A.append("text").attr("x",15).attr("y",15).attr("dy","0.35em").style("text-anchor","middle").style("font-size","16px").style("font-weight","bold").text("−");let k=v.append("g").attr("transform","translate(0, 70)").style("cursor","pointer").on("click",()=>{t.transition().duration(500).call(n.transform,ec.GS)});k.append("rect").attr("width",30).attr("height",30).attr("rx",4).style("fill","white").style("stroke","#d1d5db").style("stroke-width",1),k.append("text").attr("x",15).attr("y",15).attr("dy","0.35em").style("text-anchor","middle").style("font-size","12px").style("font-weight","bold").text("⌂")}catch(e){console.error("Error rendering D3 tree:",e)}},[s,l,d,f]),N=(0,r.useCallback)(e=>{let s=window.pageYOffset||document.documentElement.scrollTop,t=window.pageXOffset||document.documentElement.scrollLeft;c(s=>{let t=new Set(s);return t.has(e)?t.delete(e):t.add(e),t}),setTimeout(()=>{window.scrollTo(t,s)},100)},[]),b=async e=>{try{await (0,P.lW)(e)}catch(e){console.error("Failed to copy link:",e)}};return((0,r.useEffect)(()=>{j()},[l]),(0,r.useEffect)(()=>{s&&y()},[s,y,l]),n)?(0,a.jsx)("div",{className:"flex items-center justify-center h-96",children:(0,a.jsx)(G.A,{className:"w-8 h-8 animate-spin text-blue-400"})}):s?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsx)(o.Zp,{children:(0,a.jsx)(o.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Direct Referrals"}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:s.statistics.totalDirectReferrals})]}),(0,a.jsx)(g.A,{className:"h-8 w-8 text-blue-500"})]})})}),(0,a.jsx)(o.Zp,{children:(0,a.jsx)(o.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Left Side"}),(0,a.jsxs)("p",{className:"text-lg font-bold text-green-600",children:[s.statistics.leftReferrals||0," users"]}),(0,a.jsxs)("p",{className:"text-sm text-green-500",children:[s.statistics.binaryPoints.leftPoints," points"]})]}),(0,a.jsx)(u.A,{className:"h-8 w-8 text-green-500"})]})})}),(0,a.jsx)(o.Zp,{children:(0,a.jsx)(o.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Right Side"}),(0,a.jsxs)("p",{className:"text-lg font-bold text-orange-600",children:[s.statistics.rightReferrals||0," users"]}),(0,a.jsxs)("p",{className:"text-sm text-orange-500",children:[s.statistics.binaryPoints.rightPoints," points"]})]}),(0,a.jsx)(u.A,{className:"h-8 w-8 text-orange-500"})]})})}),(0,a.jsx)(o.Zp,{children:(0,a.jsx)(o.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Binary Points"}),(0,a.jsxs)("p",{className:"text-lg font-bold text-purple-600",children:[s.statistics.binaryPoints.matchedPoints," matched"]}),(0,a.jsxs)("p",{className:"text-sm text-purple-500",children:[Math.min(s.statistics.binaryPoints.leftPoints,s.statistics.binaryPoints.rightPoints)," available"]})]}),(0,a.jsx)(T.A,{className:"h-8 w-8 text-purple-500"})]})})})]}),(0,a.jsxs)(o.Zp,{children:[(0,a.jsx)(o.aR,{children:(0,a.jsxs)(o.ZB,{className:"flex items-center space-x-2",children:[(0,a.jsx)(X.A,{className:"h-5 w-5 text-blue-500"}),(0,a.jsx)("span",{children:"Referral Links"})]})}),(0,a.jsxs)(o.Wu,{children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"General Referral Link"}),(0,a.jsxs)("div",{className:"flex mt-1",children:[(0,a.jsx)("input",{type:"text",value:s.referralLinks.general,readOnly:!0,className:"flex-1 px-3 py-2 border border-gray-300 rounded-l-lg text-sm bg-gray-50"}),(0,a.jsx)(o.$n,{size:"sm",onClick:()=>b(s.referralLinks.general),className:"rounded-l-none",children:(0,a.jsx)($.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Left Side Link"}),(0,a.jsxs)("div",{className:"flex mt-1",children:[(0,a.jsx)("input",{type:"text",value:s.referralLinks.left,readOnly:!0,className:"flex-1 px-3 py-2 border border-gray-300 rounded-l-lg text-sm bg-gray-50"}),(0,a.jsx)(o.$n,{size:"sm",onClick:()=>b(s.referralLinks.left),className:"rounded-l-none",children:(0,a.jsx)($.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Right Side Link"}),(0,a.jsxs)("div",{className:"flex mt-1",children:[(0,a.jsx)("input",{type:"text",value:s.referralLinks.right,readOnly:!0,className:"flex-1 px-3 py-2 border border-gray-300 rounded-l-lg text-sm bg-gray-50"}),(0,a.jsx)(o.$n,{size:"sm",onClick:()=>b(s.referralLinks.right),className:"rounded-l-none",children:(0,a.jsx)($.A,{className:"h-4 w-4"})})]})]})]}),(0,a.jsxs)("div",{className:"mt-4 space-y-3",children:[(0,a.jsx)("div",{className:"p-3 bg-blue-50 rounded-lg",children:(0,a.jsxs)("p",{className:"text-xs text-blue-700",children:[(0,a.jsx)("strong",{children:"Enhanced Placement:"})," New users are automatically placed in your weaker leg for optimal network balance. Use specific side links to target placement."]})}),(0,a.jsx)("div",{className:"p-3 bg-green-50 rounded-lg",children:(0,a.jsxs)("p",{className:"text-xs text-green-700",children:[(0,a.jsx)("strong",{children:"Binary Matching:"})," Points are matched weekly on Saturdays at 15:00 UTC. Each $100 investment by your downline = 1 binary point. Maximum 100 points per side."]})})]})]})]}),(0,a.jsxs)(o.Zp,{children:[(0,a.jsx)(o.aR,{children:(0,a.jsxs)(o.ZB,{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{children:"Binary Tree Structure"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(o.$n,{size:"sm",variant:"outline",onClick:()=>x(!d),children:[d?(0,a.jsx)(Q.A,{className:"h-4 w-4"}):(0,a.jsx)(ee.A,{className:"h-4 w-4"}),d?"Hide Inactive":"Show Inactive"]}),(0,a.jsxs)(o.$n,{size:"sm",onClick:j,children:[(0,a.jsx)(G.A,{className:"h-4 w-4 mr-2"}),"Refresh"]})]})]})}),(0,a.jsxs)(o.Wu,{children:[(0,a.jsx)("div",{className:"border border-gray-200 rounded-lg overflow-hidden bg-gradient-to-br from-slate-50 to-blue-50",children:(0,a.jsx)("svg",{ref:e,className:"w-full",style:{height:"700px"}})}),(0,a.jsxs)("div",{className:"mt-4 text-sm text-gray-600 space-y-1",children:[(0,a.jsxs)("p",{children:["• ",(0,a.jsx)("strong",{children:"Zoom:"})," Use mouse wheel or zoom controls"]}),(0,a.jsxs)("p",{children:["• ",(0,a.jsx)("strong",{children:"Pan:"})," Click and drag to move around"]}),(0,a.jsxs)("p",{children:["• ",(0,a.jsx)("strong",{children:"Expand/Collapse:"})," Click the + or - button on nodes"]})]})]})]}),(0,a.jsx)(er,{})]}):(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(g.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Tree Data"}),(0,a.jsx)("p",{className:"text-gray-500",children:"Unable to load your binary tree structure."})]})};var eo=t(6766),ex=t(7434),em=t(9869),eh=function(e){return e.ID_DOCUMENT="ID_DOCUMENT",e.SELFIE="SELFIE",e}({}),eu=function(e){return e.NATIONAL_ID="NATIONAL_ID",e.PASSPORT="PASSPORT",e.DRIVING_LICENSE="DRIVING_LICENSE",e}({}),ep=function(e){return e.FRONT="FRONT",e.BACK="BACK",e}({});let eg=()=>{let{user:e,refreshUser:s}=(0,n.A)(),[t,i]=(0,r.useState)([]),[l,c]=(0,r.useState)(!0),[d,x]=(0,r.useState)(!1),[m,h]=(0,r.useState)(""),[u,p]=(0,r.useState)(!1),[g,f]=(0,r.useState)(null),[y,N]=(0,r.useState)({}),[b,v]=(0,r.useState)("select");(0,r.useEffect)(()=>{w()},[]);let w=async()=>{try{let e=await fetch("/api/kyc/documents",{credentials:"include"});if(e.ok){let s=await e.json();s.success&&i(s.data)}}catch(e){console.error("Failed to fetch KYC documents:",e)}finally{c(!1)}},A=(e,s)=>s.type.startsWith("image/")?s.size>5242880?void h("File size must be less than 5MB"):void(h(""),N(t=>({...t,[e]:s}))):void h("Please upload an image file"),k=e=>{let s=["selfie"];return e===eu.PASSPORT?s.push("id_front"):s.push("id_front","id_back"),s},S=()=>!!g&&k(g).every(e=>y[e]),T=async()=>{if(!g||!S())return void h("Please complete all required uploads");p(!0),h("");try{for(let e of k(g)){let s=y[e];if(!s)continue;let t=new FormData;t.append("file",s),t.append("idType",g),"selfie"===e?t.append("documentType",eh.SELFIE):(t.append("documentType",eh.ID_DOCUMENT),t.append("documentSide","id_front"===e?ep.FRONT:ep.BACK));let a=await fetch("/api/kyc/upload",{method:"POST",credentials:"include",body:t}),r=await a.json();if(!r.success)throw Error(r.error||"Upload failed")}await w(),await s(),N({}),f(null),v("select")}catch(e){h(e.message||"Submission failed")}finally{p(!1)}},P=e=>{switch(e){case"APPROVED":return(0,a.jsx)(_.A,{className:"h-5 w-5 text-eco-500"});case"REJECTED":return(0,a.jsx)(H.A,{className:"h-5 w-5 text-red-500"});case"PENDING":return(0,a.jsx)(C.A,{className:"h-5 w-5 text-solar-500"});default:return(0,a.jsx)(E.A,{className:"h-5 w-5 text-gray-500"})}},R=e=>{switch(e){case"APPROVED":return"bg-eco-100 text-eco-700";case"REJECTED":return"bg-red-100 text-red-700";case"PENDING":return"bg-solar-100 text-solar-700";default:return"bg-gray-100 text-gray-700"}},I=e=>{switch(e){case eu.NATIONAL_ID:return"National ID";case eu.PASSPORT:return"Passport";case eu.DRIVING_LICENSE:return"Driving License";default:return""}},D=e=>{let{documentKey:s,title:t,description:r,required:n}=e,i="file-".concat(s),l=y[s];return(0,a.jsxs)("div",{className:"border border-gray-200 rounded-lg p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-dark-900",children:[t," ",n&&(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:r})]}),l&&(0,a.jsx)(_.A,{className:"h-5 w-5 text-eco-500"})]}),l?(0,a.jsx)("div",{className:"space-y-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)("img",{src:URL.createObjectURL(l),alt:t,className:"w-16 h-16 object-cover rounded-lg"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-dark-900",children:l.name}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:[(l.size/1024/1024).toFixed(2)," MB"]})]}),(0,a.jsx)(o.$n,{variant:"outline",size:"sm",onClick:()=>{N(e=>{let t={...e};return delete t[s],t})},children:"Remove"})]})}):(0,a.jsxs)("div",{children:[(0,a.jsx)("input",{id:i,type:"file",accept:"image/*",onChange:e=>{var t;let a=null==(t=e.target.files)?void 0:t[0];a&&A(s,a)},className:"hidden"}),(0,a.jsx)("label",{htmlFor:i,className:"flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100",children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center pt-5 pb-6",children:[(0,a.jsx)(em.A,{className:"h-8 w-8 text-gray-400 mb-2"}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:[(0,a.jsx)("span",{className:"font-semibold",children:"Click to upload"})," or drag and drop"]}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"PNG, JPG up to 5MB"})]})})]})]})};return l?(0,a.jsxs)(o.Zp,{children:[(0,a.jsx)(o.aR,{children:(0,a.jsxs)(o.ZB,{className:"flex items-center space-x-2",children:[(0,a.jsx)(j.A,{className:"h-5 w-5 text-solar-500"}),(0,a.jsx)("span",{children:"KYC Verification"})]})}),(0,a.jsx)(o.Wu,{children:(0,a.jsxs)("div",{className:"animate-pulse space-y-4",children:[(0,a.jsx)("div",{className:"h-32 bg-gray-200 rounded-lg"}),(0,a.jsx)("div",{className:"h-32 bg-gray-200 rounded-lg"})]})})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(o.Zp,{children:[(0,a.jsx)(o.aR,{children:(0,a.jsxs)(o.ZB,{className:"flex items-center space-x-2",children:[(0,a.jsx)(j.A,{className:"h-5 w-5 text-solar-500"}),(0,a.jsx)("span",{children:"KYC Verification Status"})]})}),(0,a.jsxs)(o.Wu,{children:[(null==e?void 0:e.kycStatus)==="APPROVED"?(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center text-center space-y-4",children:[(0,a.jsx)("div",{className:"flex items-center justify-center",children:(0,a.jsx)(eo.default,{src:"/kyc.png",alt:"KYC Verification",width:80,height:80,className:"object-contain"})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-lg font-semibold text-dark-900",children:["Status: ",(0,a.jsx)("span",{className:"text-eco-600",children:"APPROVED"})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-2",children:"Your identity has been verified. You can now make withdrawals."})]})]}):(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[P((null==e?void 0:e.kycStatus)||"PENDING"),(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-lg font-semibold text-dark-900",children:["Status: ",(0,a.jsx)("span",{className:"".concat((null==e?void 0:e.kycStatus)==="REJECTED"?"text-red-600":"text-solar-600"),children:(null==e?void 0:e.kycStatus)||"PENDING"})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[(null==e?void 0:e.kycStatus)==="PENDING"&&"Your documents are being reviewed. This usually takes 1-3 business days.",(null==e?void 0:e.kycStatus)==="REJECTED"&&"Your verification was rejected. Please re-upload your documents."]})]})]}),(null==e?void 0:e.kycStatus)!=="APPROVED"&&(0,a.jsxs)("div",{className:"mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-blue-900 mb-2",children:"Why do we need KYC verification?"}),(0,a.jsxs)("ul",{className:"text-sm text-blue-700 space-y-1",children:[(0,a.jsx)("li",{children:"• Comply with international financial regulations"}),(0,a.jsx)("li",{children:"• Protect your account from unauthorized access"}),(0,a.jsx)("li",{children:"• Enable secure withdrawals to your wallet"}),(0,a.jsx)("li",{children:"• Prevent fraud and money laundering"})]})]})]})]}),(null==e?void 0:e.kycStatus)!=="APPROVED"&&(0,a.jsxs)(a.Fragment,{children:[t.length>0&&(0,a.jsxs)(o.Zp,{children:[(0,a.jsx)(o.aR,{children:(0,a.jsx)(o.ZB,{children:"Current Documents"})}),(0,a.jsx)(o.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:t.map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)("img",{src:e.filePath,alt:"".concat(e.documentType," document"),className:"w-16 h-16 object-cover rounded-lg"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-dark-900",children:e.documentType===eh.SELFIE?"Selfie Photo":"".concat(I(e.idType)," - ").concat(e.documentSide)}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[P(e.status),(0,a.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(R(e.status)),children:e.status})]}),e.rejectionReason&&(0,a.jsxs)("p",{className:"text-xs text-red-600 mt-1",children:["Rejection reason: ",e.rejectionReason]})]})]},e.id))})})]}),((null==e?void 0:e.kycStatus)==="REJECTED"||!(t.length>0))&&(0,a.jsxs)(a.Fragment,{children:["select"===b&&(0,a.jsx)(()=>(0,a.jsxs)(o.Zp,{children:[(0,a.jsx)(o.aR,{children:(0,a.jsxs)(o.ZB,{className:"flex items-center space-x-2",children:[(0,a.jsx)(ex.A,{className:"h-5 w-5 text-solar-500"}),(0,a.jsx)("span",{children:"Select ID Document Type"})]})}),(0,a.jsxs)(o.Wu,{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-6",children:"Choose the type of government-issued ID you want to upload for verification."}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:Object.values(eu).map(e=>(0,a.jsx)("button",{onClick:()=>{f(e),v("upload")},className:"p-4 border-2 border-gray-200 rounded-lg hover:border-solar-500 hover:bg-solar-50 transition-colors text-left",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(ex.A,{className:"h-6 w-6 text-solar-500"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-dark-900",children:I(e)}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:e===eu.PASSPORT?"Front side only":"Front and back required"})]})]})},e))})]})]}),{}),"upload"===b&&g&&(0,a.jsxs)(o.Zp,{children:[(0,a.jsx)(o.aR,{children:(0,a.jsxs)(o.ZB,{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{children:["Upload ",I(g)," Documents"]}),(0,a.jsx)(o.$n,{variant:"outline",size:"sm",onClick:()=>{v("select"),f(null),N({})},children:"Change ID Type"})]})}),(0,a.jsxs)(o.Wu,{children:[m&&(0,a.jsx)("div",{className:"mb-4 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg text-sm",children:m}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(D,{documentKey:"id_front",title:"".concat(I(g)," - Front"),description:"Upload a clear photo of the front side of your ID",required:!0}),g!==eu.PASSPORT&&(0,a.jsx)(D,{documentKey:"id_back",title:"".concat(I(g)," - Back"),description:"Upload a clear photo of the back side of your ID",required:!0}),(0,a.jsx)(D,{documentKey:"selfie",title:"Selfie with ID",description:"Take a selfie holding your ID document next to your face",required:!0})]}),(0,a.jsxs)("div",{className:"mt-6 flex justify-end space-x-3",children:[(0,a.jsx)(o.$n,{variant:"outline",onClick:()=>{v("select"),f(null),N({})},disabled:u,children:"Cancel"}),(0,a.jsx)(o.$n,{onClick:T,disabled:!S()||u,loading:u,children:"Submit KYC Documents"})]}),(0,a.jsxs)("div",{className:"mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-yellow-900 mb-2",children:"Document Requirements:"}),(0,a.jsxs)("ul",{className:"text-sm text-yellow-700 space-y-1",children:[(0,a.jsx)("li",{children:"• Documents must be clear and readable"}),(0,a.jsx)("li",{children:"• All four corners of the ID must be visible"}),(0,a.jsx)("li",{children:"• No blurred, cropped, or edited images"}),(0,a.jsx)("li",{children:"• Selfie must clearly show your face and the ID"}),(0,a.jsx)("li",{children:"• File formats: JPG, PNG (max 5MB each)"})]})]})]})]})]})]})]})},ej=()=>{var e;let[s,t]=(0,r.useState)([]),[n,i]=(0,r.useState)(!0),[l,c]=(0,r.useState)({});ea(),(0,r.useEffect)(()=>{m()},[]);let d=async e=>{try{if(!e||0===e.length)return void c({});let s=[...new Set(e.map(e=>e.thsAmount))];if(0===s.length)return void c({});let t=await fetch("/api/mining-units/average-roi",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({thsAmounts:s})});if(t.ok){let s=await t.json();if(s.success){let t={};e.forEach(e=>{let a=s.data[e.thsAmount.toString()];t[e.id]=a||e.dailyROI}),c(t)}else{console.error("Failed to calculate average ROIs:",s.error);let t={};e.forEach(e=>{t[e.id]=e.dailyROI}),c(t)}}else throw Error("API request failed")}catch(t){console.error("Error calculating average ROIs:",t);let s={};e&&e.length>0&&e.forEach(e=>{s[e.id]=e.dailyROI}),c(s)}},m=async()=>{try{let e=await fetch("/api/mining-units",{credentials:"include"});if(e.ok){let s=await e.json();s.success&&(t(s.data||[]),await d(s.data||[]))}}catch(e){console.error("Failed to fetch mining units:",e)}finally{i(!1)}},h=e=>{let s=5*e.investmentAmount;return Math.min((e.miningEarnings+e.referralEarnings+e.binaryEarnings)/s*100,100)},u=e=>e.miningEarnings+e.referralEarnings+e.binaryEarnings,p=e=>Math.max(0,5*e.investmentAmount-u(e)),g=()=>s.filter(e=>"ACTIVE"===e.status).sort((e,s)=>new Date(e.createdAt).getTime()-new Date(s.createdAt).getTime()).map((e,s)=>({unitId:e.id,order:s+1})),j=e=>"ACTIVE"===e?"bg-eco-100 text-eco-700":"bg-gray-100 text-gray-700";return n?(0,a.jsxs)(o.Zp,{children:[(0,a.jsx)(o.aR,{children:(0,a.jsxs)(o.ZB,{className:"flex items-center space-x-2",children:[(0,a.jsx)(x.NC,{className:"h-5 w-5 text-solar-500"}),(0,a.jsx)("span",{children:"Mining Units"})]})}),(0,a.jsx)(o.Wu,{children:(0,a.jsx)("div",{className:"animate-pulse space-y-4",children:Array.from({length:3}).map((e,s)=>(0,a.jsx)("div",{className:"h-16 bg-gray-200 rounded-lg"},s))})})]}):(0,a.jsxs)(o.Zp,{children:[(0,a.jsx)(o.aR,{children:(0,a.jsxs)(o.ZB,{className:"flex items-center space-x-2",children:[(0,a.jsx)(x.NC,{className:"h-5 w-5 text-solar-500"}),(0,a.jsxs)("span",{children:["Mining Units (",s.length,")"]})]})}),(0,a.jsx)(o.Wu,{children:s.length>0?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"hidden md:block overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"border-b border-gray-200",children:[(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-700",children:"Order"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-700",children:"Mining Power"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-700",children:"Investment"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-700",children:"Earnings Breakdown"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-700",children:"Progress to 5x"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-700",children:"Remaining"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-700",children:"Status"})]})}),(0,a.jsx)("tbody",{children:s.map(e=>{let s=g().find(s=>s.unitId===e.id);return(0,a.jsxs)("tr",{className:"border-b border-gray-100 hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"py-4 px-4",children:"ACTIVE"===e.status&&s?(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("span",{className:"bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium",children:["#",s.order]}),(0,a.jsx)("span",{className:"text-xs text-gray-500",children:"Next to expire"})]}):(0,a.jsx)("span",{className:"text-gray-400 text-xs",children:"-"})}),(0,a.jsx)("td",{className:"py-4 px-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(x.NC,{className:"h-4 w-4 text-solar-500"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:(0,P.jI)(e.thsAmount)}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:[(0,P.ZV)(l[e.id]||e.dailyROI,2),"% daily average"]})]})]})}),(0,a.jsx)("td",{className:"py-4 px-4",children:(0,a.jsx)("span",{className:"font-medium",children:(0,P.vv)(e.investmentAmount)})}),(0,a.jsx)("td",{className:"py-4 px-4",children:(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"flex justify-between text-xs",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Mining:"}),(0,a.jsx)("span",{className:"font-medium text-green-600",children:(0,P.vv)(e.miningEarnings)})]}),(0,a.jsxs)("div",{className:"flex justify-between text-xs",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Referral:"}),(0,a.jsx)("span",{className:"font-medium text-blue-600",children:(0,P.vv)(e.referralEarnings)})]}),(0,a.jsxs)("div",{className:"flex justify-between text-xs",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Binary:"}),(0,a.jsx)("span",{className:"font-medium text-purple-600",children:(0,P.vv)(e.binaryEarnings)})]}),(0,a.jsxs)("div",{className:"border-t pt-1 flex justify-between text-sm font-medium",children:[(0,a.jsx)("span",{children:"Total:"}),(0,a.jsx)("span",{className:"text-eco-600",children:(0,P.vv)(u(e))})]})]})}),(0,a.jsxs)("td",{className:"py-4 px-4",children:[(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3",children:(0,a.jsx)("div",{className:"bg-eco-500 h-3 rounded-full transition-all duration-300",style:{width:"".concat(h(e),"%")}})}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 mt-1 flex justify-between",children:[(0,a.jsxs)("span",{children:[(0,P.ZV)(h(e),1),"%"]}),(0,a.jsxs)("span",{children:[(0,P.vv)(5*e.investmentAmount)," max"]})]})]}),(0,a.jsx)("td",{className:"py-4 px-4",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("span",{className:"font-medium text-gray-700",children:(0,P.vv)(p(e))}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"remaining"})]})}),(0,a.jsx)("td",{className:"py-4 px-4",children:(0,a.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(j(e.status)),children:e.status})})]},e.id)})})]})}),(0,a.jsx)("div",{className:"md:hidden space-y-4",children:s.map(e=>{let s=g().find(s=>s.unitId===e.id);return(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(x.NC,{className:"h-5 w-5 text-solar-500"}),(0,a.jsx)("span",{className:"font-semibold",children:(0,P.jI)(e.thsAmount)}),"ACTIVE"===e.status&&s&&(0,a.jsxs)("span",{className:"bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium",children:["#",s.order]})]}),(0,a.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(j(e.status)),children:e.status})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"Investment"}),(0,a.jsx)("div",{className:"font-medium",children:(0,P.vv)(e.investmentAmount)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"Daily ROI Average"}),(0,a.jsxs)("div",{className:"font-medium text-eco-600",children:[(0,P.ZV)(l[e.id]||e.dailyROI,2),"%"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"Remaining"}),(0,a.jsx)("div",{className:"font-medium text-orange-600",children:(0,P.vv)(p(e))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"Max Earnings"}),(0,a.jsx)("div",{className:"font-medium",children:(0,P.vv)(5*e.investmentAmount)})]})]}),(0,a.jsxs)("div",{className:"mb-3 p-3 bg-white rounded-lg",children:[(0,a.jsx)("div",{className:"text-xs font-medium text-gray-700 mb-2",children:"Earnings Breakdown"}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"flex justify-between text-xs",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Mining:"}),(0,a.jsx)("span",{className:"font-medium text-green-600",children:(0,P.vv)(e.miningEarnings)})]}),(0,a.jsxs)("div",{className:"flex justify-between text-xs",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Referral:"}),(0,a.jsx)("span",{className:"font-medium text-blue-600",children:(0,P.vv)(e.referralEarnings)})]}),(0,a.jsxs)("div",{className:"flex justify-between text-xs",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Binary:"}),(0,a.jsx)("span",{className:"font-medium text-purple-600",children:(0,P.vv)(e.binaryEarnings)})]}),(0,a.jsxs)("div",{className:"border-t pt-1 flex justify-between text-sm font-medium",children:[(0,a.jsx)("span",{children:"Total:"}),(0,a.jsx)("span",{className:"text-eco-600",children:(0,P.vv)(u(e))})]})]})]}),(0,a.jsxs)("div",{className:"mb-2",children:[(0,a.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 mb-1",children:[(0,a.jsx)("span",{children:"Progress to 5x"}),(0,a.jsxs)("span",{children:[(0,P.ZV)(h(e),1),"%"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3",children:(0,a.jsx)("div",{className:"bg-eco-500 h-3 rounded-full transition-all duration-300",style:{width:"".concat(h(e),"%")}})})]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:["Started: ",(0,P.r6)(e.startDate)," • Expires: ",(0,P.r6)(e.expiryDate)]})]},e.id)})}),(0,a.jsxs)("div",{className:"mt-6 p-4 bg-solar-50 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-medium text-dark-900 mb-3",children:"Mining Units Summary"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Total Mining Power: "}),(0,a.jsx)("span",{className:"font-medium",children:(0,P.jI)(s.reduce((e,s)=>e+s.thsAmount,0))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Total Investment: "}),(0,a.jsx)("span",{className:"font-medium",children:(0,P.vv)(s.reduce((e,s)=>e+s.investmentAmount,0))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Active Units: "}),(0,a.jsx)("span",{className:"font-medium text-green-600",children:s.filter(e=>"ACTIVE"===e.status).length})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Expired Units: "}),(0,a.jsx)("span",{className:"font-medium text-red-600",children:s.filter(e=>"EXPIRED"===e.status).length})]})]}),(0,a.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[(0,a.jsx)("h5",{className:"font-medium text-dark-900 mb-2",children:"Total Earnings Breakdown"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Mining Earnings: "}),(0,a.jsx)("span",{className:"font-medium text-green-600",children:(0,P.vv)(s.reduce((e,s)=>e+s.miningEarnings,0))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Referral Earnings: "}),(0,a.jsx)("span",{className:"font-medium text-blue-600",children:(0,P.vv)(s.reduce((e,s)=>e+s.referralEarnings,0))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Binary Earnings: "}),(0,a.jsx)("span",{className:"font-medium text-purple-600",children:(0,P.vv)(s.reduce((e,s)=>e+s.binaryEarnings,0))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Total Earned: "}),(0,a.jsx)("span",{className:"font-medium text-eco-600",children:(0,P.vv)(s.reduce((e,s)=>e+u(s),0))})]})]})]}),s.filter(e=>"ACTIVE"===e.status).length>1&&(0,a.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[(0,a.jsx)("h5",{className:"font-medium text-dark-900 mb-2",children:"FIFO Expiration Order"}),(0,a.jsx)("p",{className:"text-xs text-gray-600 mb-2",children:"Mining units expire when they reach 5x their investment amount. Units expire in First-In-First-Out (FIFO) order."}),(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Next to expire: "}),(0,a.jsxs)("span",{className:"font-medium text-orange-600",children:["Unit #",(null==(e=g()[0])?void 0:e.order)||"N/A","(",(0,P.ZV)(h(s.find(e=>{var s;return e.id===(null==(s=g()[0])?void 0:s.unitId)})||s[0]),1),"% complete)"]})]})]})]})]}):(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(x.NC,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Mining Units"}),(0,a.jsx)("p",{className:"text-gray-500 mb-4",children:"You haven't purchased any mining units yet. Start mining to earn daily returns!"})]})})]})};var ef=t(5040),ey=t(4616),eN=t(9947),eb=t(2486);let ev=[{id:"1",category:"Getting Started",question:"How do I start mining with HashCoreX?",answer:"To start mining with HashCoreX, follow these simple steps:\n\n1. Complete KYC Verification: First, complete your KYC verification in the Profile section. This is required for all mining activities.\n\n2. Deposit Funds: Go to your Wallet and deposit USDT (TRC20) to fund your account. The minimum deposit amount is configured by the admin.\n\n3. Purchase Mining Units: Navigate to the Mining section and purchase mining units. Choose your desired TH/s amount and investment level.\n\n4. Start Earning: Your mining units will start generating daily returns automatically. Earnings are calculated based on your TH/s amount and current market conditions.\n\n5. Track Progress: Monitor your earnings in the Earnings section and watch your mining units progress toward their 5x investment return limit.\n\nRemember: Mining units expire after 24 months or when they reach 5x their investment amount, whichever comes first.",icon:u.A,tags:["mining","getting started","kyc","deposit"]},{id:"2",category:"Mining Units",question:"How are daily returns calculated?",answer:"Daily returns are calculated using a dynamic system based on your mining unit's TH/s amount:\n\nDynamic ROI System:\n• Different TH/s ranges have different return rates\n• Returns are calculated as: (Investment Amount \xd7 Daily ROI%) \xf7 100\n• ROI percentages are determined by admin-configured ranges\n\nExample Ranges:\n• 0-10 TH/s: 0.3% - 0.5% daily\n• 10-50 TH/s: 0.4% - 0.6% daily\n• 50+ TH/s: 0.5% - 0.7% daily\n\nImportant Notes:\n• Returns are credited daily but paid out weekly (Sundays at 00:00 AM GMT+5:30)\n• Mining units expire when they reach 5x their investment amount\n• FIFO system: Oldest units receive earnings first\n• All earnings are subject to market conditions and admin configuration",icon:u.A,tags:["mining","returns","calculation","roi"]},{id:"3",category:"Wallet & Payments",question:"How do deposits and withdrawals work?",answer:"Deposits:\n• Only USDT (TRC20) deposits are accepted\n• Minimum deposit amount is set by admin (typically $50)\n• Deposits are automatically verified on the Tron blockchain\n• Funds are available immediately after confirmation\n\nWithdrawals:\n• Minimum withdrawal: $50\n• Fixed fee: $3 + 1% of withdrawal amount\n• Processing time: Up to 3 business days\n• Only to verified TRC20 addresses\n\nWallet Balance:\n• Available Balance: Funds ready for use or withdrawal\n• Pending Balance: Earnings waiting for weekly distribution\n• Total Earnings: Cumulative earnings from all sources\n\nImportant:\n• Complete KYC verification before making withdrawals\n• Ensure your TRC20 address is correct before submitting\n• Withdrawal fees are deducted from your balance",icon:p.A,tags:["wallet","deposit","withdrawal","usdt","trc20"]},{id:"4",category:"Referral System",question:"How does the binary referral system work?",answer:"HashCoreX uses a binary tree referral system with three placement types:\n\nPlacement Types:\n1. General Referral: Placed in weaker leg automatically\n2. Left-Side Referral: Specifically placed on left side\n3. Right-Side Referral: Specifically placed on right side\n\nEarning Structure:\n• Direct Referral Commission: 10% one-time bonus when your referral purchases mining units\n• Binary Matching Bonus: Weekly matching of binary points (1 point = $10)\n\nBinary Points:\n• Earned when your referrals purchase mining units\n• $150 purchase = 1.5 points (supports 2 decimal places)\n• Points are matched weekly between left and right sides\n• Excess points reset to 0 after matching\n\nRequirements:\n• You must have active mining units to earn commissions\n• Binary matching occurs weekly at 15:00 UTC\n• All earnings are allocated to your mining units using FIFO system",icon:g.A,tags:["referral","binary","commission","points","matching"]},{id:"5",category:"Account & Security",question:"What is KYC and why is it required?",answer:"KYC (Know Your Customer) Verification:\n\nKYC is a mandatory verification process required for all HashCoreX users to ensure compliance with financial regulations.\n\nKYC Process:\n1. Personal Information: Provide accurate personal details\n2. ID Document: Upload government-issued ID (passport, driver's license, etc.)\n3. Selfie Verification: Take a selfie holding your ID document\n4. Admin Review: Our team reviews your submission (typically 24-48 hours)\n\nKYC Status:\n• Not Submitted: KYC documents not yet uploaded\n• Pending: Under admin review\n• Approved: Verification complete - full access granted\n• Rejected: Resubmission required with correct documents\n\nWhy KYC is Required:\n• Legal compliance with financial regulations\n• Account security and fraud prevention\n• Enables withdrawals and full platform access\n• Protects both users and the platform\n\nImportant: Profile name fields become read-only after KYC submission to prevent fraud.",icon:j.A,tags:["kyc","verification","security","compliance","identity"]},{id:"6",category:"Technical Support",question:"What should I do if I encounter technical issues?",answer:"Common Solutions:\n\nLogin Issues:\n• Clear browser cache and cookies\n• Try incognito/private browsing mode\n• Ensure you're using the correct email address\n• Check if your account is active\n\nTransaction Issues:\n• Verify transaction hash on Tron blockchain explorer\n• Check if you're using the correct network (Mainnet/Testnet)\n• Ensure sufficient balance for fees\n• Wait for blockchain confirmations\n\nDisplay Issues:\n• Refresh the page\n• Try a different browser\n• Disable browser extensions temporarily\n• Check your internet connection\n\nStill Need Help?\n1. Create a support ticket with detailed information\n2. Include screenshots if possible\n3. Provide transaction hashes for payment issues\n4. Specify your browser and device type\n\nResponse Times:\n• General inquiries: 24-48 hours\n• Technical issues: 12-24 hours\n• Payment issues: Priority handling within 12 hours",icon:y.A,tags:["technical","support","troubleshooting","login","transactions"]},{id:"7",category:"Mining Units",question:"What happens when mining units expire?",answer:'Mining units expire under two conditions:\n\nExpiry Conditions:\n• After 24 months from purchase date\n• When they reach 5x their investment amount (whichever comes first)\n\nFIFO Expiry System:\n• Oldest mining units expire first\n• Earnings are allocated to oldest units first\n• This ensures fair distribution and maximum returns\n\nWhat Happens at Expiry:\n• Unit stops generating daily returns\n• All accumulated earnings are credited to your wallet\n• Unit status changes to "Expired"\n• No further earnings from that specific unit\n\nImportant Notes:\n• You can track each unit\'s progress toward 5x return\n• Expired units are shown in your mining history\n• Purchase new units to continue earning\n• All earnings are subject to weekly payout schedule',icon:C.A,tags:["mining","expiry","fifo","returns","units"]},{id:"8",category:"Wallet & Payments",question:"How do I set up my TRC20 wallet address?",answer:"Setting up your TRC20 wallet address for withdrawals:\n\nSupported Wallets:\n• TronLink (Browser extension)\n• Trust Wallet (Mobile app)\n• Atomic Wallet (Desktop/Mobile)\n• Any wallet supporting TRC20 tokens\n\nSetup Process:\n1. Go to Profile Settings → Billing & Payments\n2. Enter your TRC20 wallet address\n3. Verify the address is correct (double-check!)\n4. Save the address for future withdrawals\n\nImportant Security Tips:\n• Always copy-paste addresses, never type manually\n• Test with a small amount first\n• Ensure your wallet supports USDT TRC20\n• Keep your private keys secure and never share them\n• Use only your own wallet addresses\n\nNetwork Information:\n• Network: Tron (TRX)\n• Token Standard: TRC20\n• Supported Token: USDT\n• Withdrawal fees apply as per platform settings",icon:p.A,tags:["wallet","trc20","setup","withdrawal","address"]},{id:"9",category:"Referral System",question:"How can I maximize my referral earnings?",answer:"Strategies to maximize your referral earnings:\n\nBuilding Your Network:\n• Share your referral links on social media\n• Explain the benefits of HashCoreX to friends and family\n• Use different placement types strategically\n• Focus on quality referrals who will be active\n\nPlacement Strategy:\n• Use general referrals for automatic balancing\n• Use left/right specific placements for strategic building\n• Monitor your binary tree balance regularly\n• Help your referrals understand the system\n\nEarning Optimization:\n• Maintain active mining units to earn commissions\n• Focus on both direct referrals and binary matching\n• Track your binary points weekly\n• Reinvest earnings to purchase more mining units\n\nBest Practices:\n• Provide support to your referrals\n• Share educational content about mining\n• Be transparent about risks and rewards\n• Build long-term relationships, not just quick referrals\n\nRemember: You must have active mining units to earn any referral commissions.",icon:u.A,tags:["referral","earnings","strategy","network","optimization"]},{id:"10",category:"Account & Security",question:"How do I reset my password?",answer:"Password reset process:\n\nStep-by-Step Process:\n1. Go to the login page\n2. Click \"Forgot Password\" link\n3. Enter your registered email address\n4. Check your email for OTP verification code\n5. Enter the OTP code when prompted\n6. Create a new strong password\n7. Confirm your new password\n8. You'll be automatically redirected to dashboard\n\nPassword Requirements:\n• Minimum 8 characters\n• Include uppercase and lowercase letters\n• Include at least one number\n• Include at least one special character\n• Avoid common passwords\n\nSecurity Tips:\n• Use a unique password for HashCoreX\n• Consider using a password manager\n• Don't share your password with anyone\n• Change your password regularly\n• Log out from shared devices\n\nTroubleshooting:\n• Check spam folder for OTP email\n• Ensure email address is correct\n• Wait a few minutes for email delivery\n• Contact support if issues persist",icon:j.A,tags:["password","reset","security","otp","login"]},{id:"11",category:"Getting Started",question:"What are the minimum requirements to start?",answer:"Minimum requirements to start mining with HashCoreX:\n\nFinancial Requirements:\n• Minimum deposit: $50 USDT (TRC20)\n• Minimum mining unit purchase: As configured by admin\n• Transaction fees: Network fees for deposits/withdrawals\n\nAccount Requirements:\n• Valid email address for registration\n• Complete KYC verification (mandatory)\n• TRC20 wallet for deposits and withdrawals\n• Active status (maintained by having mining units)\n\nTechnical Requirements:\n• Modern web browser (Chrome, Firefox, Safari, Edge)\n• Stable internet connection\n• Email access for verification and notifications\n• Basic understanding of cryptocurrency transactions\n\nGetting Started Checklist:\n1. ✓ Register with valid email\n2. ✓ Verify email address\n3. ✓ Complete KYC verification\n4. ✓ Set up TRC20 wallet\n5. ✓ Make first deposit ($50+ USDT)\n6. ✓ Purchase first mining units\n7. ✓ Start earning daily returns\n\nTime Investment:\n• Initial setup: 30-60 minutes\n• Daily monitoring: 5-10 minutes\n• Weekly review: 15-30 minutes",icon:ef.A,tags:["requirements","minimum","getting started","checklist","setup"]},{id:"12",category:"Mining Units",question:"How do I track my mining performance?",answer:"Tracking your mining performance effectively:\n\nDashboard Overview:\n• Total active TH/s amount\n• Daily earnings summary\n• Pending vs available balance\n• Mining units status overview\n\nDetailed Tracking:\n• Individual unit performance\n• Progress toward 5x return limit\n• Daily ROI percentages (7-day average)\n• Earnings allocation per unit\n\nKey Metrics to Monitor:\n• Total investment amount\n• Total earnings to date\n• Average daily return percentage\n• Time remaining until expiry\n• FIFO allocation progress\n\nPerformance Analysis:\n• Compare actual vs expected returns\n• Monitor market condition impacts\n• Track weekly payout schedules\n• Review binary earnings allocation\n\nOptimization Tips:\n• Reinvest earnings for compound growth\n• Monitor unit expiry dates\n• Balance investment across different TH/s ranges\n• Keep track of referral earnings contribution\n\nReports Available:\n• Transaction history\n• Earnings breakdown\n• Mining unit details\n• Referral performance summary",icon:u.A,tags:["tracking","performance","monitoring","analytics","optimization"]}],ew=()=>{let[e,s]=(0,r.useState)([]),[t,n]=(0,r.useState)(!0),[i,l]=(0,r.useState)(!1),[c,d]=(0,r.useState)(null),[x,m]=(0,r.useState)(null),[h,u]=(0,r.useState)(""),[p,g]=(0,r.useState)("All"),[j,y]=(0,r.useState)(1),[N,b]=(0,r.useState)({subject:"",message:"",priority:"MEDIUM"}),[v,w]=(0,r.useState)(""),[A,k]=(0,r.useState)(!1);(0,r.useEffect)(()=>{M()},[]);let S=["All",...Array.from(new Set(ev.map(e=>e.category)))],T=ev.filter(e=>{let s=e.question.toLowerCase().includes(h.toLowerCase())||e.answer.toLowerCase().includes(h.toLowerCase())||e.tags.some(e=>e.toLowerCase().includes(h.toLowerCase())),t="All"===p||e.category===p;return s&&t}),R=Math.ceil(T.length/6),I=(j-1)*6,D=I+6,F=T.slice(I,D);r.useEffect(()=>{y(1)},[h,p]);let M=async()=>{try{let e=await fetch("/api/support/tickets",{credentials:"include"});if(e.ok){let t=await e.json();t.success&&s(t.data)}}catch(e){console.error("Failed to fetch tickets:",e)}finally{n(!1)}},O=async e=>{e.preventDefault(),k(!0);try{(await fetch("/api/support/tickets",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(N)})).ok&&(b({subject:"",message:"",priority:"MEDIUM"}),l(!1),M())}catch(e){console.error("Failed to create ticket:",e)}finally{k(!1)}},U=async e=>{if(v.trim())try{if((await fetch("/api/support/tickets/".concat(e,"/responses"),{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({message:v})})).ok){w(""),M();let s=await fetch("/api/support/tickets",{credentials:"include"});if(s.ok){let t=(await s.json()).data.find(s=>s.id===e);t&&d(t)}}}catch(e){console.error("Failed to add response:",e)}},W=e=>{switch(e){case"OPEN":return(0,a.jsx)(E.A,{className:"h-4 w-4 text-red-500"});case"IN_PROGRESS":return(0,a.jsx)(C.A,{className:"h-4 w-4 text-solar-500"});case"RESOLVED":case"CLOSED":return(0,a.jsx)(_.A,{className:"h-4 w-4 text-eco-500"});default:return(0,a.jsx)(f.A,{className:"h-4 w-4 text-gray-500"})}},B=e=>{switch(e){case"OPEN":return"bg-red-100 text-red-700";case"IN_PROGRESS":return"bg-solar-100 text-solar-700";case"RESOLVED":case"CLOSED":return"bg-eco-100 text-eco-700";default:return"bg-gray-100 text-gray-700"}},Z=e=>{switch(e){case"URGENT":return"bg-red-100 text-red-700";case"HIGH":return"bg-orange-100 text-orange-700";case"MEDIUM":return"bg-solar-100 text-solar-700";default:return"bg-gray-100 text-gray-700"}};return t?(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4 mb-4"}),(0,a.jsx)("div",{className:"h-64 bg-gray-200 rounded"})]})}):(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Support Center"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"Get help and manage support tickets"})]}),(0,a.jsxs)(o.$n,{onClick:()=>l(!0),className:"flex items-center gap-2",children:[(0,a.jsx)(ey.A,{className:"h-4 w-4"}),"New Ticket"]})]}),(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)(o.Zp,{children:[(0,a.jsx)(o.aR,{children:(0,a.jsxs)(o.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(f.A,{className:"h-5 w-5"}),"Support Tickets"]})}),(0,a.jsx)(o.Wu,{children:e.length>0?(0,a.jsx)("div",{className:"space-y-3",children:e.map(e=>(0,a.jsxs)("div",{onClick:()=>d(e),className:"p-4 border border-gray-200 rounded-lg cursor-pointer",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 truncate flex-1",children:e.subject}),(0,a.jsxs)("div",{className:"flex items-center gap-2 ml-2",children:[W(e.status),(0,a.jsx)("span",{className:"text-xs px-2 py-1 rounded-full ".concat(B(e.status)),children:e.status})]})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-2 line-clamp-2",children:e.message}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-500",children:[(0,a.jsx)("span",{className:"px-2 py-1 rounded-full ".concat(Z(e.priority)),children:e.priority}),(0,a.jsx)("span",{children:(0,P.r6)(e.createdAt)})]})]},e.id))}):(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(f.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Support Tickets"}),(0,a.jsx)("p",{className:"text-gray-600",children:"You haven't created any support tickets yet."})]})})]})}),(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)(o.Zp,{children:[(0,a.jsx)(o.aR,{children:(0,a.jsxs)(o.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(eN.A,{className:"h-5 w-5"}),"Help Tutorials & FAQ"]})}),(0,a.jsxs)(o.Wu,{children:[(0,a.jsx)("div",{className:"mb-6 space-y-4",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)(o.pd,{placeholder:"Search FAQs...",value:h,onChange:e=>u(e.target.value),className:"w-full"})}),(0,a.jsx)("div",{className:"sm:w-48",children:(0,a.jsx)("select",{value:p,onChange:e=>g(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-solar-500 focus:border-transparent",children:S.map(e=>(0,a.jsx)("option",{value:e,children:e},e))})})]})}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6",children:F.map(e=>{let s=e.icon;return(0,a.jsxs)("div",{onClick:()=>m(e),className:"p-6 border border-gray-200 rounded-lg cursor-pointer hover:border-solar-300 hover:shadow-md transition-all duration-200 bg-white",children:[(0,a.jsxs)("div",{className:"flex items-start gap-3 mb-3",children:[(0,a.jsx)("div",{className:"p-2 bg-solar-100 rounded-lg",children:(0,a.jsx)(s,{className:"h-5 w-5 text-solar-600"})}),(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)("span",{className:"text-xs font-medium text-solar-600 bg-solar-50 px-2 py-1 rounded-full",children:e.category})})]}),(0,a.jsx)("h4",{className:"font-semibold text-gray-900 mb-2 line-clamp-2",children:e.question}),(0,a.jsx)("p",{className:"text-sm text-gray-600 line-clamp-3 mb-3",children:e.answer.split("\n")[0]}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-1",children:[e.tags.slice(0,3).map(e=>(0,a.jsx)("span",{className:"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded",children:e},e)),e.tags.length>3&&(0,a.jsxs)("span",{className:"text-xs text-gray-500",children:["+",e.tags.length-3," more"]})]})]},e.id)})}),0===T.length&&(0,a.jsxs)("div",{className:"text-center py-12 col-span-full",children:[(0,a.jsx)(ef.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No FAQs Found"}),(0,a.jsx)("p",{className:"text-gray-600",children:h||"All"!==p?"Try adjusting your search or filter criteria.":"FAQ content is being updated."})]}),T.length>6&&(0,a.jsxs)("div",{className:"col-span-full flex items-center justify-between mt-6 pt-6 border-t border-gray-200",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["Showing ",I+1," to ",Math.min(D,T.length)," of ",T.length," articles"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>y(e=>Math.max(e-1,1)),disabled:1===j,className:"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),Array.from({length:R},(e,s)=>s+1).map(e=>(0,a.jsx)("button",{onClick:()=>y(e),className:"px-3 py-2 text-sm font-medium rounded-md ".concat(j===e?"bg-solar-600 text-white":"text-gray-500 bg-white border border-gray-300 hover:bg-gray-50"),children:e},e)),(0,a.jsx)("button",{onClick:()=>y(e=>Math.min(e+1,R)),disabled:j===R,className:"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"})]})]})]})]})}),(0,a.jsx)(o.aF,{isOpen:i,onClose:()=>l(!1),title:"Create Support Ticket",children:(0,a.jsxs)("form",{onSubmit:O,className:"space-y-4",children:[(0,a.jsx)(o.pd,{label:"Subject",value:N.subject,onChange:e=>b(s=>({...s,subject:e.target.value})),placeholder:"Brief description of your issue",required:!0}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Priority"}),(0,a.jsxs)("select",{value:N.priority,onChange:e=>b(s=>({...s,priority:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-solar-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"LOW",children:"Low"}),(0,a.jsx)("option",{value:"MEDIUM",children:"Medium"}),(0,a.jsx)("option",{value:"HIGH",children:"High"}),(0,a.jsx)("option",{value:"URGENT",children:"Urgent"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Message"}),(0,a.jsx)("textarea",{value:N.message,onChange:e=>b(s=>({...s,message:e.target.value})),placeholder:"Describe your issue in detail...",rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-solar-500 focus:border-transparent",required:!0})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)(o.$n,{type:"button",variant:"outline",onClick:()=>l(!1),className:"flex-1",children:"Cancel"}),(0,a.jsx)(o.$n,{type:"submit",loading:A,className:"flex-1",children:"Create Ticket"})]})]})}),c&&(0,a.jsx)(o.aF,{isOpen:!!c,onClose:()=>d(null),title:"Ticket: ".concat(c.subject),size:"xl",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[W(c.status),(0,a.jsx)("span",{className:"text-sm px-2 py-1 rounded-full ".concat(B(c.status)),children:c.status}),(0,a.jsx)("span",{className:"text-sm px-2 py-1 rounded-full ".concat(Z(c.priority)),children:c.priority})]}),(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"Original Message:"}),(0,a.jsx)("p",{className:"text-gray-900",children:c.message}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 mt-2",children:["Created: ",(0,P.r6)(c.createdAt)]})]}),c.responses.length>0&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"Responses:"}),c.responses.map(e=>(0,a.jsxs)("div",{className:"p-3 rounded-lg ".concat(e.isAdmin?"bg-blue-50 border-l-4 border-blue-500":"bg-gray-50"),children:[(0,a.jsx)("p",{className:"text-gray-900",children:e.message}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:[e.isAdmin?"Support Team":"You"," • ",(0,P.r6)(e.createdAt)]})]},e.id))]}),"CLOSED"!==c.status&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("textarea",{value:v,onChange:e=>w(e.target.value),placeholder:"Add a response...",rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-solar-500 focus:border-transparent"}),(0,a.jsxs)(o.$n,{onClick:()=>U(c.id),disabled:!v.trim(),className:"w-full",children:[(0,a.jsx)(eb.A,{className:"h-4 w-4 mr-2"}),"Send Response"]})]})]})}),x&&(0,a.jsx)(o.aF,{isOpen:!!x,onClose:()=>m(null),title:x.question,size:"xl",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 pb-4 border-b border-gray-200",children:[(0,a.jsx)("div",{className:"p-3 bg-solar-100 rounded-lg",children:(0,a.jsx)(x.icon,{className:"h-6 w-6 text-solar-600"})}),(0,a.jsx)("div",{children:(0,a.jsx)("span",{className:"text-sm font-medium text-solar-600 bg-solar-50 px-3 py-1 rounded-full",children:x.category})})]}),(0,a.jsx)("div",{className:"prose prose-sm max-w-none",children:(0,a.jsx)("div",{className:"whitespace-pre-line text-gray-700 leading-relaxed",children:x.answer})}),(0,a.jsx)("div",{className:"pt-4 border-t border-gray-200",children:(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700 mr-2",children:"Tags:"}),x.tags.map(e=>(0,a.jsx)("span",{className:"text-sm text-solar-600 bg-solar-50 px-3 py-1 rounded-full",children:e},e))]})}),(0,a.jsxs)("div",{className:"pt-4 border-t border-gray-200",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:"Still need help? Create a support ticket for personalized assistance."}),(0,a.jsxs)(o.$n,{onClick:()=>{m(null),l(!0)},className:"w-full sm:w-auto",children:[(0,a.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"Create Support Ticket"]})]})]})})]})};var eA=t(1586),ek=t(4229),eE=t(9803),eS=t(6767),eC=t(1264);let eT=()=>{let{user:e}=(0,n.A)(),[s,t]=(0,r.useState)("profile"),[i,l]=(0,r.useState)({emailNotifications:!0,pushNotifications:!0,smsNotifications:!1,marketingEmails:!1}),[c,d]=(0,r.useState)({firstName:"",lastName:"",email:"",referralId:""}),[x,m]=(0,r.useState)(!0),[h,u]=(0,r.useState)(!1),[p,g]=(0,r.useState)(!1),[f,N]=(0,r.useState)({currentPassword:"",newPassword:"",confirmPassword:""}),[b,v]=(0,r.useState)(!1),[A,E]=(0,r.useState)(!1),[S,C]=(0,r.useState)(""),[T,P]=(0,r.useState)(!1),{showMessage:R,MessageBoxComponent:I}=(0,o.eC)();(0,r.useEffect)(()=>{e&&d({firstName:e.firstName||"",lastName:e.lastName||"",email:e.email||"",referralId:e.referralId||""}),D(),F(),M()},[e]);let D=async()=>{try{let e=await fetch("/api/user/notification-settings",{credentials:"include"});if(e.ok){let s=await e.json();s.success&&l(s.data)}}catch(e){console.error("Failed to fetch notification settings:",e)}finally{m(!1)}},F=async()=>{try{let e=await fetch("/api/user/withdrawal-address",{credentials:"include"}),s=await e.json();s.success&&C(s.data.withdrawalAddress||"")}catch(e){console.error("Failed to fetch user data:",e)}},M=async()=>{try{let e=await fetch("/api/user/2fa/status",{credentials:"include"}),s=await e.json();s.success&&v(s.data.enabled)}catch(e){console.error("Failed to fetch 2FA status:",e)}},O=async e=>{try{u(!0),(await fetch("/api/user/notification-settings",{method:"PUT",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(e)})).ok&&l(e)}catch(e){console.error("Failed to update notification settings:",e)}finally{u(!1)}},U=async()=>{try{u(!0),console.log("Profile update:",c)}catch(e){console.error("Failed to update profile:",e)}finally{u(!1)}},W=async()=>{if(f.newPassword!==f.confirmPassword)return void R({title:"Password Mismatch",message:"New passwords do not match",variant:"error"});try{u(!0),console.log("Password update"),N({currentPassword:"",newPassword:"",confirmPassword:""})}catch(e){console.error("Failed to update password:",e)}finally{u(!1)}},B=async s=>{try{var t;P(!0);let a=new FormData;a.append("profilePicture",s);let r=await fetch("/api/user/profile-picture",{method:"POST",credentials:"include",body:a}),n=await r.json();if(!n.success)throw Error(n.error||"Failed to upload profile picture");await (null==e||null==(t=e.refreshUser)?void 0:t.call(e)),R({title:"Success",message:"Profile picture updated successfully",variant:"success"})}catch(e){console.error("Profile picture upload error:",e),R({title:"Upload Failed",message:e instanceof Error?e.message:"Failed to upload profile picture",variant:"error"})}finally{P(!1)}},Z=async()=>{try{var s;P(!0);let t=await fetch("/api/user/profile-picture",{method:"DELETE",credentials:"include"}),a=await t.json();if(!a.success)throw Error(a.error||"Failed to remove profile picture");await (null==e||null==(s=e.refreshUser)?void 0:s.call(e)),R({title:"Success",message:"Profile picture removed successfully",variant:"success"})}catch(e){console.error("Profile picture removal error:",e),R({title:"Removal Failed",message:e instanceof Error?e.message:"Failed to remove profile picture",variant:"error"})}finally{P(!1)}};if(x)return(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-500"})});let L=[{id:"profile",label:"Profile Information",icon:k.A},{id:"security",label:"Security Settings",icon:j.A},{id:"notifications",label:"Notification Settings",icon:w.A},{id:"billing",label:"Billing & Payments",icon:eA.A}],_=()=>{let s=(null==e?void 0:e.kycStatus)==="PENDING"||(null==e?void 0:e.kycStatus)==="APPROVED";return(0,a.jsxs)(o.Zp,{children:[(0,a.jsx)(o.aR,{children:(0,a.jsxs)(o.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(k.A,{className:"h-5 w-5"}),"Profile Information"]})}),(0,a.jsxs)(o.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-4 pb-6 border-b border-gray-200",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Profile Picture"}),(0,a.jsx)(o.vp,{currentPicture:null==e?void 0:e.profilePicture,onUpload:B,onRemove:Z,loading:T,disabled:h})]}),s&&(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(j.A,{className:"h-4 w-4 text-blue-600"}),(0,a.jsx)("p",{className:"text-sm text-blue-700 font-medium",children:"Profile names cannot be changed after KYC submission"})]}),(0,a.jsx)("p",{className:"text-xs text-blue-600 mt-1",children:"Your identity has been verified and profile information is now locked for security."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"First Name"}),(0,a.jsx)(o.pd,{type:"text",value:c.firstName,onChange:e=>d(s=>({...s,firstName:e.target.value})),disabled:s,className:s?"bg-gray-100 border-gray-300 text-gray-500":"bg-white border-gray-300 text-gray-900"}),s&&(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Cannot be changed after KYC submission"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Last Name"}),(0,a.jsx)(o.pd,{type:"text",value:c.lastName,onChange:e=>d(s=>({...s,lastName:e.target.value})),disabled:s,className:s?"bg-gray-100 border-gray-300 text-gray-500":"bg-white border-gray-300 text-gray-900"}),s&&(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Cannot be changed after KYC submission"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),(0,a.jsx)(o.pd,{type:"email",value:c.email,disabled:!0,className:"bg-gray-100 border-gray-300 text-gray-500"}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Email cannot be changed"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Referral ID"}),(0,a.jsx)(o.pd,{type:"text",value:c.referralId,disabled:!0,className:"bg-gray-100 border-gray-300 text-gray-500"}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Your unique referral identifier"})]}),(0,a.jsxs)(o.$n,{onClick:U,disabled:h||s,className:"w-full bg-yellow-500 hover:bg-yellow-600 text-white disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,a.jsx)(ek.A,{className:"h-4 w-4 mr-2"}),h?"Saving...":s?"Profile Locked":"Save Profile"]})]})]})},H=()=>(0,a.jsxs)(o.Zp,{children:[(0,a.jsx)(o.aR,{children:(0,a.jsxs)(o.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(j.A,{className:"h-5 w-5"}),"Security Settings"]})}),(0,a.jsxs)(o.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Current Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(o.pd,{type:p?"text":"password",value:f.currentPassword,onChange:e=>N(s=>({...s,currentPassword:e.target.value})),className:"bg-white border-gray-300 text-gray-900 pr-10"}),(0,a.jsx)("button",{type:"button",onClick:()=>g(!p),className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:p?(0,a.jsx)(Q.A,{className:"h-4 w-4 text-gray-400"}):(0,a.jsx)(ee.A,{className:"h-4 w-4 text-gray-400"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"New Password"}),(0,a.jsx)(o.pd,{type:p?"text":"password",value:f.newPassword,onChange:e=>N(s=>({...s,newPassword:e.target.value})),className:"bg-white border-gray-300 text-gray-900"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Confirm New Password"}),(0,a.jsx)(o.pd,{type:p?"text":"password",value:f.confirmPassword,onChange:e=>N(s=>({...s,confirmPassword:e.target.value})),className:"bg-white border-gray-300 text-gray-900"})]}),(0,a.jsxs)(o.$n,{onClick:W,disabled:h||!f.currentPassword||!f.newPassword||!f.confirmPassword,className:"w-full bg-red-500 hover:bg-red-600 text-white",children:[(0,a.jsx)(eE.A,{className:"h-4 w-4 mr-2"}),h?"Updating...":"Update Password"]}),(0,a.jsxs)("div",{className:"border-t pt-6 mt-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900 flex items-center gap-2",children:[(0,a.jsx)(eS.A,{className:"h-5 w-5"}),"Two-Factor Authentication"]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Add an extra layer of security to your account with email-based verification codes."})]}),(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsx)("button",{onClick:Y,disabled:A,className:"relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ".concat(b?"bg-green-600":"bg-gray-200"),children:(0,a.jsx)("span",{className:"inline-block h-4 w-4 transform rounded-full bg-white transition-transform ".concat(b?"translate-x-6":"translate-x-1")})})})]}),(0,a.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(j.A,{className:"w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-blue-800 mb-1",children:b?"Two-Factor Authentication Enabled":"Enable Two-Factor Authentication"}),(0,a.jsx)("p",{className:"text-sm text-blue-700",children:b?"Your account is protected with email-based two-factor authentication. You will receive a verification code via email when logging in.":"Protect your account by enabling two-factor authentication. You will receive a verification code via email each time you log in."})]})]})})]})]})]}),V=()=>(0,a.jsxs)(o.Zp,{children:[(0,a.jsx)(o.aR,{children:(0,a.jsxs)(o.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(y.A,{className:"h-5 w-5"}),"Notification Settings"]})}),(0,a.jsxs)(o.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(eC.A,{className:"h-5 w-5 text-gray-500"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:"Email Notifications"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Receive updates via email"})]})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:i.emailNotifications,onChange:e=>O({...i,emailNotifications:e.target.checked}),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-500"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(w.A,{className:"h-5 w-5 text-gray-500"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:"Push Notifications"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Browser notifications"})]})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:i.pushNotifications,onChange:e=>O({...i,pushNotifications:e.target.checked}),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-500"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(eS.A,{className:"h-5 w-5 text-gray-500"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:"SMS Notifications"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Text message alerts"})]})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:i.smsNotifications,onChange:e=>O({...i,smsNotifications:e.target.checked}),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-500"})]})]})]})]}),G=async()=>{try{if(u(!0),S&&!S.match(/^T[A-Za-z1-9]{33}$/))return void R({title:"Invalid Address",message:'Invalid USDT TRC20 address format. Address must start with "T" and be 34 characters long.',variant:"error"});let e=await fetch("/api/user/withdrawal-address",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({withdrawalAddress:S.trim()})});if(!e.ok){let s=await e.text();throw console.error("Response not OK:",e.status,s),Error("Server error: ".concat(e.status))}let s=await e.json();if(!s.success)throw Error(s.error||"Failed to update withdrawal address");R({title:"Success",message:"Withdrawal address updated successfully!",variant:"success"})}catch(e){console.error("Failed to update withdrawal address:",e),R({title:"Error",message:"Error: ".concat(e.message),variant:"error"})}finally{u(!1)}},Y=async()=>{try{E(!0);let e=await fetch(b?"/api/user/2fa/disable":"/api/user/2fa/enable",{method:"POST",credentials:"include"}),s=await e.json();s.success?(v(!b),R({title:"Success",message:b?"Two-factor authentication disabled successfully":"Two-factor authentication enabled successfully",variant:"success"})):R({title:"Error",message:s.error||"Failed to update 2FA settings",variant:"error"})}catch(e){console.error("Failed to toggle 2FA:",e),R({title:"Error",message:"Failed to update 2FA settings",variant:"error"})}finally{E(!1)}},z=()=>(0,a.jsxs)(o.Zp,{children:[(0,a.jsx)(o.aR,{children:(0,a.jsxs)(o.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(eA.A,{className:"h-5 w-5"}),"Billing & Payments"]})}),(0,a.jsxs)(o.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Default Withdrawal Address"}),(0,a.jsx)(o.pd,{type:"text",value:S,onChange:e=>C(e.target.value),placeholder:"Enter your USDT TRC20 address",className:"bg-white border-gray-300 text-gray-900"}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"This address will be automatically filled in withdrawal forms. Only USDT TRC20 addresses are supported."})]}),(0,a.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(j.A,{className:"w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-blue-800 mb-1",children:"Security Notice"}),(0,a.jsx)("p",{className:"text-sm text-blue-700",children:"Your withdrawal address is encrypted and stored securely. You can update it anytime, but make sure to double-check the address as transactions cannot be reversed."})]})]})}),(0,a.jsxs)(o.$n,{onClick:G,disabled:h,className:"w-full bg-yellow-500 hover:bg-yellow-600 text-white",children:[(0,a.jsx)(ek.A,{className:"h-4 w-4 mr-2"}),h?"Saving...":"Save Withdrawal Address"]})]})]});return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Profile & Settings"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"Manage your account information and preferences"})]}),(0,a.jsx)("div",{className:"border-b border-gray-200",children:(0,a.jsx)("nav",{className:"-mb-px flex space-x-8",children:L.map(e=>{let r=e.icon,n=s===e.id;return(0,a.jsxs)("button",{onClick:()=>t(e.id),className:"\n                  flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm transition-colors\n                  ".concat(n?"border-yellow-500 text-yellow-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300","\n                "),children:[(0,a.jsx)(r,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:e.label})]},e.id)})})}),(0,a.jsx)("div",{className:"mt-6",children:(()=>{switch(s){case"profile":default:return _();case"security":return H();case"notifications":return V();case"billing":return z()}})()}),(0,a.jsx)(I,{})]})};class eP extends r.Component{static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,s){console.error("ErrorBoundary caught an error:",e,s)}render(){if(this.state.hasError){if(this.props.fallback){let e=this.props.fallback;return(0,a.jsx)(e,{error:this.state.error,resetError:this.resetError})}return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,a.jsxs)("div",{className:"max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)("svg",{className:"w-8 h-8 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Something went wrong"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"We encountered an unexpected error. Please try refreshing the page."}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("button",{onClick:this.resetError,className:"w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:"Try Again"}),(0,a.jsx)("button",{onClick:()=>window.location.reload(),className:"w-full bg-gray-200 text-gray-800 px-4 py-2 rounded-lg hover:bg-gray-300 transition-colors",children:"Refresh Page"})]}),this.state.error&&(0,a.jsxs)("details",{className:"mt-4 text-left",children:[(0,a.jsx)("summary",{className:"text-sm text-gray-500 cursor-pointer",children:"Error Details"}),(0,a.jsx)("pre",{className:"mt-2 text-xs text-red-600 bg-red-50 p-2 rounded overflow-auto",children:this.state.error.message})]})]})})}return this.props.children}constructor(e){super(e),this.resetError=()=>{this.setState({hasError:!1,error:void 0})},this.state={hasError:!1}}}function eR(){let{user:e,loading:s}=(0,n.A)(),t=(0,i.useRouter)(),[l,c]=(0,r.useState)("overview");return((0,r.useEffect)(()=>{s||e||t.push("/login")},[e,s,t]),s)?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)(o.Rh,{size:"lg",text:"Loading dashboard..."})}):e?(0,a.jsx)(eP,{children:(0,a.jsx)(S,{activeTab:l,onTabChange:c,children:(()=>{switch(l){case"overview":return(0,a.jsx)(F,{onTabChange:c});case"mining":return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(U,{onPurchaseComplete:()=>window.location.reload()}),(0,a.jsx)(ej,{})]});case"earnings":return(0,a.jsx)(B,{});case"wallet":return(0,a.jsx)(J,{});case"referrals":return(0,a.jsx)(ed,{});case"kyc":return(0,a.jsx)(eg,{});case"support":return(0,a.jsx)(ew,{});case"profile":return(0,a.jsx)(eT,{});default:return(0,a.jsx)(F,{})}})()})}):null}}},e=>{var s=s=>e(e.s=s);e.O(0,[36,4641,854,905,4279,8441,1684,7358],()=>s(2612)),_N_E=e.O()}]);