"use strict";(()=>{var e={};e.id=1826,e.ids=[1826],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29189:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>g,routeModule:()=>c,serverHooks:()=>h,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>m});var a={};t.r(a),t.d(a,{GET:()=>p});var s=t(96559),i=t(48088),n=t(37719),o=t(32190),l=t(39542),u=t(6710),d=t(31183);async function p(e){try{let{authenticated:r,user:t}=await (0,l.b9)(e);if(!r||!t)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});let a=await u.FW.findByUserId(t.id),s=parseFloat(await u.rs.get("MAX_BINARY_POINTS_PER_SIDE")||"10"),i=parseFloat(await u.rs.get("BINARY_POINT_VALUE")||"10"),n=a?.leftPoints||0,p=a?.rightPoints||0,c=a?.matchedPoints||0,f=a?.totalMatched||0,m=Math.min(n,s),h=Math.min(p,s),g=Math.min(m,h),E=Math.max(0,n-s),S=Math.max(0,p-s),_=E+S,T=new Date(Date.now()-24192e5),y=await d.prisma.transaction.findMany({where:{userId:t.id,type:"BINARY_BONUS",status:"COMPLETED",createdAt:{gte:T}},orderBy:{createdAt:"desc"},take:4}),R=m/s*100,x=h/s*100,I=[];n>.9*s&&I.push(`Left side approaching cap (${n.toFixed(0)}/${s})`),p>.9*s&&I.push(`Right side approaching cap (${p.toFixed(0)}/${s})`),_>0&&I.push(`${_.toFixed(0)} points will be flushed due to pressure-out`);let A={currentPoints:{left:n,right:p,leftCapped:m,rightCapped:h,matchable:g,matched:c,totalMatched:f},limits:{maxPointsPerSide:s,pointValue:i},progress:{leftProgress:R,rightProgress:x,leftNearCap:R>90,rightNearCap:x>90},pressureOut:{leftAmount:E,rightAmount:S,totalAmount:_,willOccur:_>0},earnings:{estimatedPayout:g*i,pointValue:i,matchablePoints:g},history:{recentMatches:y.map(e=>({amount:e.amount,date:e.createdAt,description:e.description})),averageWeeklyEarnings:y.length>0?y.reduce((e,r)=>e+r.amount,0)/y.length:0},warnings:I,hasWarnings:I.length>0,nextMatching:{schedule:"Weekly on Saturdays at 15:00 UTC",description:"Binary points are matched weekly, not daily"}};return o.NextResponse.json({success:!0,data:A})}catch(e){return console.error("Binary points info fetch error:",e),o.NextResponse.json({success:!1,error:"Failed to fetch binary points information"},{status:500})}}let c=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/binary-points/info/route",pathname:"/api/binary-points/info",filename:"route",bundlePath:"app/api/binary-points/info/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\binary-points\\info\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:f,workUnitAsyncStorage:m,serverHooks:h}=c;function g(){return(0,n.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:m})}},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37366:e=>{e.exports=require("dns")},39542:(e,r,t)=>{t.d(r,{b9:()=>S,HU:()=>h,qc:()=>R,Lx:()=>T,DY:()=>_,DT:()=>y});var a=t(85663),s=t(43205),i=t.n(s),n=t(6710),o=t(45697);let l=o.z.object({DATABASE_URL:o.z.string().url("DATABASE_URL must be a valid PostgreSQL URL"),DIRECT_URL:o.z.string().url("DIRECT_URL must be a valid PostgreSQL URL"),JWT_SECRET:o.z.string().min(32,"JWT_SECRET must be at least 32 characters long").refine(e=>{let r=/[A-Z]/.test(e),t=/[a-z]/.test(e),a=/\d/.test(e),s=/[!@#$%^&*(),.?":{}|<>]/.test(e);return r&&t&&a&&s},"JWT_SECRET should contain uppercase, lowercase, numbers, and special characters"),JWT_EXPIRES_IN:o.z.string().default("30d"),NODE_ENV:o.z.enum(["development","production","test"]).default("development"),NEXT_PUBLIC_APP_URL:o.z.string().url().optional(),PORT:o.z.string().regex(/^\d+$/).transform(Number).default("3000"),SMTP_HOST:o.z.string().optional(),SMTP_PORT:o.z.string().regex(/^\d+$/).transform(Number).optional(),SMTP_USER:o.z.string().email().optional(),SMTP_PASSWORD:o.z.string().optional(),SMTP_SECURE:o.z.string().transform(e=>"true"===e).optional(),TRON_NETWORK:o.z.enum(["mainnet","testnet"]).default("testnet"),TRON_API_KEY:o.z.string().optional(),USDT_CONTRACT_ADDRESS:o.z.string().optional(),MAX_FILE_SIZE:o.z.string().regex(/^\d+$/).transform(Number).default("10485760"),UPLOAD_DIR:o.z.string().default("./public/uploads"),BCRYPT_ROUNDS:o.z.string().regex(/^\d+$/).transform(Number).default("12"),SESSION_TIMEOUT:o.z.string().regex(/^\d+$/).transform(Number).default("1800"),RATE_LIMIT_WINDOW_MS:o.z.string().regex(/^\d+$/).transform(Number).default("900000"),RATE_LIMIT_MAX_REQUESTS:o.z.string().regex(/^\d+$/).transform(Number).default("100"),COINGECKO_API_URL:o.z.string().url().default("https://api.coingecko.com/api/v3"),LOG_LEVEL:o.z.enum(["error","warn","info","debug"]).default("info"),ENABLE_REQUEST_LOGGING:o.z.string().transform(e=>"true"===e).default("false"),ENABLE_REGISTRATION:o.z.string().transform(e=>"true"===e).default("true"),ENABLE_KYC:o.z.string().transform(e=>"true"===e).default("true"),ENABLE_WITHDRAWALS:o.z.string().transform(e=>"true"===e).default("true")}).refine(e=>{let r=[e.SMTP_HOST,e.SMTP_PORT,e.SMTP_USER,e.SMTP_PASSWORD],t=r.some(e=>void 0!==e),a=r.every(e=>void 0!==e);return!t||!!a},{message:"If SMTP configuration is provided, all SMTP fields (HOST, PORT, USER, PASSWORD) must be provided"});function u(){try{let e=l.safeParse(process.env);if(!e.success){let r=e.error.errors.map(e=>`${e.path.join(".")}: ${e.message}`);return{success:!1,errors:r}}return{success:!0,data:e.data}}catch(e){return{success:!1,errors:["Failed to validate environment variables"]}}}let d=null;function p(){if(!d){let e=u();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),d=e.data,console.log("✅ Environment variables validated successfully")}return d}let c={jwt:{secret:()=>p().JWT_SECRET,expiresIn:()=>p().JWT_EXPIRES_IN},security:{bcryptRounds:()=>p().BCRYPT_ROUNDS,sessionTimeout:()=>p().SESSION_TIMEOUT,maxFileSize:()=>p().MAX_FILE_SIZE,uploadDir:()=>p().UPLOAD_DIR}};if("phase-production-build"!==process.env.NEXT_PHASE){let e=u();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),console.log("✅ Environment variables validated successfully")}let f=async e=>await a.Ay.hash(e,c.security.bcryptRounds()),m=async(e,r)=>await a.Ay.compare(e,r),h=e=>i().sign(e,c.jwt.secret(),{expiresIn:c.jwt.expiresIn()}),g=e=>{try{return i().verify(e,c.jwt.secret())}catch(e){return null}},E=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",r="HC";for(let t=0;t<8;t++)r+=e.charAt(Math.floor(Math.random()*e.length));return r},S=async e=>{let r=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!r)return{authenticated:!1,user:null};let t=g(r);if(!t)return{authenticated:!1,user:null};let a=await n.Gy.findByEmail(t.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},_=async e=>{let r,a;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let t=await n.Gy.findByReferralId(e.referralCode);if(!t)throw Error("Invalid referral code");r=t.id}let s=await f(e.password),i=!1;do a=E(),i=!await n.Gy.findByReferralId(a);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(r){let{placeUserByReferralType:a}=await t.e(2746).then(t.bind(t,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(r,o.id,s)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},T=async e=>{let r=await n.Gy.findByEmail(e.email);if(!r||!await m(e.password,r.password))throw Error("Invalid email or password");return{token:h({userId:r.id,email:r.email}),user:{id:r.id,email:r.email,referralId:r.referralId,kycStatus:r.kycStatus}}},y=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),R=async e=>{let r=await n.Gy.findById(e);return r?.role==="ADMIN"}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,580,7911,925],()=>t(29189));module.exports=a})();