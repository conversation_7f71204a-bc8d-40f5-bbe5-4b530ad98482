"use strict";(()=>{var e={};e.id=9315,e.ids=[9315],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37366:e=>{e.exports=require("dns")},39542:(e,t,r)=>{r.d(t,{b9:()=>S,HU:()=>E,qc:()=>_,Lx:()=>A,DY:()=>T,DT:()=>x});var s=r(85663),a=r(43205),i=r.n(a),n=r(6710),o=r(45697);let u=o.z.object({DATABASE_URL:o.z.string().url("DATABASE_URL must be a valid PostgreSQL URL"),DIRECT_URL:o.z.string().url("DIRECT_URL must be a valid PostgreSQL URL"),JWT_SECRET:o.z.string().min(32,"JWT_SECRET must be at least 32 characters long").refine(e=>{let t=/[A-Z]/.test(e),r=/[a-z]/.test(e),s=/\d/.test(e),a=/[!@#$%^&*(),.?":{}|<>]/.test(e);return t&&r&&s&&a},"JWT_SECRET should contain uppercase, lowercase, numbers, and special characters"),JWT_EXPIRES_IN:o.z.string().default("30d"),NODE_ENV:o.z.enum(["development","production","test"]).default("development"),NEXT_PUBLIC_APP_URL:o.z.string().url().optional(),PORT:o.z.string().regex(/^\d+$/).transform(Number).default("3000"),SMTP_HOST:o.z.string().optional(),SMTP_PORT:o.z.string().regex(/^\d+$/).transform(Number).optional(),SMTP_USER:o.z.string().email().optional(),SMTP_PASSWORD:o.z.string().optional(),SMTP_SECURE:o.z.string().transform(e=>"true"===e).optional(),TRON_NETWORK:o.z.enum(["mainnet","testnet"]).default("testnet"),TRON_API_KEY:o.z.string().optional(),USDT_CONTRACT_ADDRESS:o.z.string().optional(),MAX_FILE_SIZE:o.z.string().regex(/^\d+$/).transform(Number).default("10485760"),UPLOAD_DIR:o.z.string().default("./public/uploads"),BCRYPT_ROUNDS:o.z.string().regex(/^\d+$/).transform(Number).default("12"),SESSION_TIMEOUT:o.z.string().regex(/^\d+$/).transform(Number).default("1800"),RATE_LIMIT_WINDOW_MS:o.z.string().regex(/^\d+$/).transform(Number).default("900000"),RATE_LIMIT_MAX_REQUESTS:o.z.string().regex(/^\d+$/).transform(Number).default("100"),COINGECKO_API_URL:o.z.string().url().default("https://api.coingecko.com/api/v3"),LOG_LEVEL:o.z.enum(["error","warn","info","debug"]).default("info"),ENABLE_REQUEST_LOGGING:o.z.string().transform(e=>"true"===e).default("false"),ENABLE_REGISTRATION:o.z.string().transform(e=>"true"===e).default("true"),ENABLE_KYC:o.z.string().transform(e=>"true"===e).default("true"),ENABLE_WITHDRAWALS:o.z.string().transform(e=>"true"===e).default("true")}).refine(e=>{let t=[e.SMTP_HOST,e.SMTP_PORT,e.SMTP_USER,e.SMTP_PASSWORD],r=t.some(e=>void 0!==e),s=t.every(e=>void 0!==e);return!r||!!s},{message:"If SMTP configuration is provided, all SMTP fields (HOST, PORT, USER, PASSWORD) must be provided"});function d(){try{let e=u.safeParse(process.env);if(!e.success){let t=e.error.errors.map(e=>`${e.path.join(".")}: ${e.message}`);return{success:!1,errors:t}}return{success:!0,data:e.data}}catch(e){return{success:!1,errors:["Failed to validate environment variables"]}}}let l=null;function c(){if(!l){let e=d();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),l=e.data,console.log("✅ Environment variables validated successfully")}return l}let p={jwt:{secret:()=>c().JWT_SECRET,expiresIn:()=>c().JWT_EXPIRES_IN},security:{bcryptRounds:()=>c().BCRYPT_ROUNDS,sessionTimeout:()=>c().SESSION_TIMEOUT,maxFileSize:()=>c().MAX_FILE_SIZE,uploadDir:()=>c().UPLOAD_DIR}};if("phase-production-build"!==process.env.NEXT_PHASE){let e=d();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),console.log("✅ Environment variables validated successfully")}let m=async e=>await s.Ay.hash(e,p.security.bcryptRounds()),f=async(e,t)=>await s.Ay.compare(e,t),E=e=>i().sign(e,p.jwt.secret(),{expiresIn:p.jwt.expiresIn()}),g=e=>{try{return i().verify(e,p.jwt.secret())}catch(e){return null}},R=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},S=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=g(t);if(!r)return{authenticated:!1,user:null};let s=await n.Gy.findByEmail(r.email);return s?{authenticated:!0,user:s}:{authenticated:!1,user:null}},T=async e=>{let t,s;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await n.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let a=await m(e.password),i=!1;do s=R(),i=!await n.Gy.findByReferralId(s);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:a,referralId:s});if(t){let{placeUserByReferralType:s}=await r.e(2746).then(r.bind(r,2746)),a="general";"left"===e.placementSide?a="left":"right"===e.placementSide&&(a="right"),await s(t,o.id,a)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},A=async e=>{let t=await n.Gy.findByEmail(e.email);if(!t||!await f(e.password,t.password))throw Error("Invalid email or password");return{token:E({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},x=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),_=async e=>{let t=await n.Gy.findById(e);return t?.role==="ADMIN"}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},74621:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>p,serverHooks:()=>E,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>f});var s={};r.r(s),r.d(s,{GET:()=>l,POST:()=>c});var a=r(96559),i=r(48088),n=r(37719),o=r(32190),u=r(39542),d=r(6710);async function l(e){try{let t,{authenticated:r,user:s}=await (0,u.b9)(e);if(!r||!s)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if(!await (0,u.qc)(s.id))return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let{searchParams:a}=new URL(e.url),i=parseInt(a.get("limit")||"50"),n=parseInt(a.get("offset")||"0"),l=a.get("status"),c=a.get("userId")||void 0;t=c?await d.J6.findByUserId(c,{status:l||void 0,limit:Math.min(i,100),offset:n}):await d.J6.findAll({status:l||void 0,limit:Math.min(i,100),offset:n});let p=await d.J6.getDepositStats();return o.NextResponse.json({success:!0,data:{deposits:t.map(e=>({id:e.id,userId:e.userId,user:e.user?{id:e.user.id,email:e.user.email,firstName:e.user.firstName,lastName:e.user.lastName}:null,transactionId:e.transactionId,amount:e.amount,usdtAmount:e.usdtAmount,tronAddress:e.tronAddress,senderAddress:e.senderAddress,status:e.status,blockNumber:e.blockNumber,blockTimestamp:e.blockTimestamp,confirmations:e.confirmations,verifiedAt:e.verifiedAt,processedAt:e.processedAt,failureReason:e.failureReason,createdAt:e.createdAt,updatedAt:e.updatedAt})),stats:{totalDeposits:p.totalDeposits,totalAmount:p.totalAmount,pendingDeposits:p.pendingDeposits},pagination:{limit:i,offset:n,hasMore:t.length===i},filters:{status:l,userId:c}}})}catch(e){return console.error("Admin deposits fetch error:",e),o.NextResponse.json({success:!1,error:"Failed to fetch deposits"},{status:500})}}async function c(e){try{let{authenticated:t,user:r}=await (0,u.b9)(e);if(!t||!r)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if(!await (0,u.qc)(r.id))return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let{action:s,transactionId:a,reason:i}=await e.json();if(!s||!a)return o.NextResponse.json({success:!1,error:"Action and transaction ID are required"},{status:400});if(!await d.J6.findByTransactionId(a))return o.NextResponse.json({success:!1,error:"Deposit not found"},{status:404});return o.NextResponse.json({success:!1,error:"Manual deposit actions are no longer supported. Deposits are processed automatically by the system."},{status:400})}catch(e){return console.error("Admin deposit action error:",e),o.NextResponse.json({success:!1,error:"Failed to process deposit action"},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/deposits/route",pathname:"/api/admin/deposits",filename:"route",bundlePath:"app/api/admin/deposits/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\deposits\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:f,serverHooks:E}=p;function g(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:f})}},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,7911,925],()=>r(74621));module.exports=s})();