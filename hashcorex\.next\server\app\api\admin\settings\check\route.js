"use strict";(()=>{var e={};e.id=8860,e.ids=[8860],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},58827:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>g,routeModule:()=>c,serverHooks:()=>x,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};t.r(s),t.d(s,{GET:()=>p});var n=t(96559),a=t(48088),i=t(37719),o=t(32190),u=t(6710);async function p(e){try{let{searchParams:r}=new URL(e.url),t=r.get("key");if(!t)return o.NextResponse.json({success:!1,error:"Setting key is required"},{status:400});let s=await u.rs.get(t);return o.NextResponse.json({success:!0,key:t,value:s||null})}catch(s){console.error("Admin setting check error:",s);let r=new URL(e.url).searchParams.get("key"),t=null;return"registrationEnabled"===r&&(t="true"),"kycRequired"===r&&(t="true"),o.NextResponse.json({success:!0,key:r,value:t,fallback:!0})}}let c=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/admin/settings/check/route",pathname:"/api/admin/settings/check",filename:"route",bundlePath:"app/api/admin/settings/check/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\settings\\check\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:x}=c;function g(){return(0,i.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,925],()=>t(58827));module.exports=s})();