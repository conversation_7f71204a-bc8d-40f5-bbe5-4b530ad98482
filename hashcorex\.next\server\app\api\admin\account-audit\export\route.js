"use strict";(()=>{var e={};e.id=9126,e.ids=[9126],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19052:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>p,serverHooks:()=>R,workAsyncStorage:()=>E,workUnitAsyncStorage:()=>m});var a={};r.r(a),r.d(a,{GET:()=>c});var s=r(96559),i=r(48088),n=r(37719),o=r(32190),l=r(39542),u=r(31183),d=r(82629);async function c(e){try{let{authenticated:t,user:r}=await (0,l.b9)(e);if(!t||!r)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if(!await (0,l.qc)(r.id))return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let a=new URL(e.url),s=a.searchParams.get("userId"),i=a.searchParams.get("startDate"),n=a.searchParams.get("endDate"),d={};if(i&&(d.gte=new Date(i)),n){let e=new Date(n);e.setHours(23,59,59,999),d.lte=e}if(s){let e=await u.prisma.user.findUnique({where:{id:s},include:{transactions:{where:Object.keys(d).length>0?{createdAt:d}:void 0,orderBy:{createdAt:"asc"}},walletBalance:!0}});if(!e)return o.NextResponse.json({success:!1,error:"User not found"},{status:404});let t="Date,Transaction ID,Type,Description,Amount,Status,Running Balance,Notes\n",r=0,a=e.transactions;t+=`# Account Audit Report for ${e.firstName} ${e.lastName}
# Email: ${e.email}
# Referral ID: ${e.referralId}
# Report Generated: ${new Date().toISOString()}
# Period: ${i||"All time"} to ${n||"Present"}
# Current Wallet Balance: $${e.walletBalance?.balance||0}

Date,Transaction ID,Type,Description,Amount,Status,Running Balance,Notes
`,a.forEach(e=>{let a=new Date(e.createdAt).toISOString().split("T")[0],s=new Date(e.createdAt).toTimeString().split(" ")[0],i=e.amount;"COMPLETED"===e.status&&(["DEPOSIT","MINING_EARNINGS","DIRECT_REFERRAL","BINARY_BONUS","ADMIN_CREDIT"].includes(e.type)?r+=i:["WITHDRAWAL","PURCHASE","ADMIN_DEBIT"].includes(e.type)&&(r-=Math.abs(i)));let n=[];"DEPOSIT"===e.type&&n.push("Deposit to wallet"),"WITHDRAWAL"===e.type&&n.push("Withdrawal from wallet"),"MINING_EARNINGS"===e.type&&n.push("Mining earnings payout"),"DIRECT_REFERRAL"===e.type&&n.push("Direct referral commission"),"BINARY_BONUS"===e.type&&n.push("Binary bonus payout"),"PURCHASE"===e.type&&n.push("Mining unit purchase"),t+=`"${a} ${s}","${e.id}","${e.type}","${e.description||""}","${i}","${e.status}","${r.toFixed(2)}","${n.join("; ")}"
`});let l=a.filter(e=>"DEPOSIT"===e.type&&"COMPLETED"===e.status).reduce((e,t)=>e+t.amount,0),c=a.filter(e=>"WITHDRAWAL"===e.type&&"COMPLETED"===e.status).reduce((e,t)=>e+t.amount,0),p=a.filter(e=>["MINING_EARNINGS","DIRECT_REFERRAL","BINARY_BONUS"].includes(e.type)&&"COMPLETED"===e.status).reduce((e,t)=>e+t.amount,0),E=a.filter(e=>"PURCHASE"===e.type&&"COMPLETED"===e.status).reduce((e,t)=>e+t.amount,0),m=a.filter(e=>"ADMIN_CREDIT"===e.type&&"COMPLETED"===e.status).reduce((e,t)=>e+t.amount,0),R=a.filter(e=>"ADMIN_DEBIT"===e.type&&"COMPLETED"===e.status).reduce((e,t)=>e+Math.abs(t.amount),0),f=l+p+m-c-E-R,g=e.walletBalance?.availableBalance||0,T=g-f;return t+=`
# SUMMARY
"Total Deposits","","","","${l}","","",""
"Total Withdrawals","","","","${c}","","",""
"Total Earnings","","","","${p}","","",""
"Total Purchases","","","","${E}","","",""
"Admin Credits","","","","${m}","","",""
"Admin Debits","","","","${R}","","",""
"Calculated Balance","","","","${f.toFixed(2)}","","",""
"Current Balance","","","","${g.toFixed(2)}","","",""
"Balance Mismatch","","","","${T.toFixed(2)}","","","${Math.abs(T)>=.01?"DISCREPANCY DETECTED":"BALANCED"}"
`,new o.NextResponse(t,{headers:{"Content-Type":"text/csv","Content-Disposition":`attachment; filename="user-audit-${s}-${new Date().toISOString().split("T")[0]}.csv"`}})}{let e=await u.prisma.user.findMany({include:{transactions:{where:Object.keys(d).length>0?{createdAt:d}:void 0},walletBalance:!0},orderBy:{createdAt:"desc"}}),t="User ID,Name,Email,Referral ID,Current Balance,Calculated Balance,Balance Mismatch,Total Deposits,Total Withdrawals,Total Earnings,Transaction Count,Has Discrepancies,Last Activity\n";return e.forEach(e=>{let r=e.transactions,a=r.filter(e=>"DEPOSIT"===e.type&&"COMPLETED"===e.status).reduce((e,t)=>e+t.amount,0),s=r.filter(e=>"WITHDRAWAL"===e.type&&"COMPLETED"===e.status).reduce((e,t)=>e+t.amount,0),i=r.filter(e=>["MINING_EARNINGS","DIRECT_REFERRAL","BINARY_BONUS"].includes(e.type)&&"COMPLETED"===e.status).reduce((e,t)=>e+t.amount,0),n=r.filter(e=>"PURCHASE"===e.type&&"COMPLETED"===e.status).reduce((e,t)=>e+t.amount,0),o=r.filter(e=>"ADMIN_CREDIT"===e.type&&"COMPLETED"===e.status).reduce((e,t)=>e+t.amount,0),l=r.filter(e=>"ADMIN_DEBIT"===e.type&&"COMPLETED"===e.status).reduce((e,t)=>e+Math.abs(t.amount),0),u=a+i+o-s-n-l,d=e.walletBalance?.availableBalance||0,c=d-u,p=Math.abs(c)>=.01,E=r.length>0?r[0].createdAt.toISOString():e.createdAt.toISOString();t+=`"${e.id}","${e.firstName} ${e.lastName}","${e.email}","${e.referralId}","${d.toFixed(2)}","${u.toFixed(2)}","${c.toFixed(2)}","${a.toFixed(2)}","${s.toFixed(2)}","${i.toFixed(2)}","${r.length}","${p?"YES":"NO"}","${E}"
`}),new o.NextResponse(t,{headers:{"Content-Type":"text/csv","Content-Disposition":`attachment; filename="account-audit-${new Date().toISOString().split("T")[0]}.csv"`}})}}catch(t){return console.error("Account audit export API error:",t),await d.v5.logApiError(e,t,"ACCOUNT_AUDIT_EXPORT_ERROR"),o.NextResponse.json({success:!1,error:"Failed to export audit data"},{status:500})}}let p=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/account-audit/export/route",pathname:"/api/admin/account-audit/export",filename:"route",bundlePath:"app/api/admin/account-audit/export/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\account-audit\\export\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:E,workUnitAsyncStorage:m,serverHooks:R}=p;function f(){return(0,n.patchFetch)({workAsyncStorage:E,workUnitAsyncStorage:m})}},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37366:e=>{e.exports=require("dns")},39542:(e,t,r)=>{r.d(t,{b9:()=>T,HU:()=>R,qc:()=>S,Lx:()=>I,DY:()=>A,DT:()=>D});var a=r(85663),s=r(43205),i=r.n(s),n=r(6710),o=r(45697);let l=o.z.object({DATABASE_URL:o.z.string().url("DATABASE_URL must be a valid PostgreSQL URL"),DIRECT_URL:o.z.string().url("DIRECT_URL must be a valid PostgreSQL URL"),JWT_SECRET:o.z.string().min(32,"JWT_SECRET must be at least 32 characters long").refine(e=>{let t=/[A-Z]/.test(e),r=/[a-z]/.test(e),a=/\d/.test(e),s=/[!@#$%^&*(),.?":{}|<>]/.test(e);return t&&r&&a&&s},"JWT_SECRET should contain uppercase, lowercase, numbers, and special characters"),JWT_EXPIRES_IN:o.z.string().default("30d"),NODE_ENV:o.z.enum(["development","production","test"]).default("development"),NEXT_PUBLIC_APP_URL:o.z.string().url().optional(),PORT:o.z.string().regex(/^\d+$/).transform(Number).default("3000"),SMTP_HOST:o.z.string().optional(),SMTP_PORT:o.z.string().regex(/^\d+$/).transform(Number).optional(),SMTP_USER:o.z.string().email().optional(),SMTP_PASSWORD:o.z.string().optional(),SMTP_SECURE:o.z.string().transform(e=>"true"===e).optional(),TRON_NETWORK:o.z.enum(["mainnet","testnet"]).default("testnet"),TRON_API_KEY:o.z.string().optional(),USDT_CONTRACT_ADDRESS:o.z.string().optional(),MAX_FILE_SIZE:o.z.string().regex(/^\d+$/).transform(Number).default("10485760"),UPLOAD_DIR:o.z.string().default("./public/uploads"),BCRYPT_ROUNDS:o.z.string().regex(/^\d+$/).transform(Number).default("12"),SESSION_TIMEOUT:o.z.string().regex(/^\d+$/).transform(Number).default("1800"),RATE_LIMIT_WINDOW_MS:o.z.string().regex(/^\d+$/).transform(Number).default("900000"),RATE_LIMIT_MAX_REQUESTS:o.z.string().regex(/^\d+$/).transform(Number).default("100"),COINGECKO_API_URL:o.z.string().url().default("https://api.coingecko.com/api/v3"),LOG_LEVEL:o.z.enum(["error","warn","info","debug"]).default("info"),ENABLE_REQUEST_LOGGING:o.z.string().transform(e=>"true"===e).default("false"),ENABLE_REGISTRATION:o.z.string().transform(e=>"true"===e).default("true"),ENABLE_KYC:o.z.string().transform(e=>"true"===e).default("true"),ENABLE_WITHDRAWALS:o.z.string().transform(e=>"true"===e).default("true")}).refine(e=>{let t=[e.SMTP_HOST,e.SMTP_PORT,e.SMTP_USER,e.SMTP_PASSWORD],r=t.some(e=>void 0!==e),a=t.every(e=>void 0!==e);return!r||!!a},{message:"If SMTP configuration is provided, all SMTP fields (HOST, PORT, USER, PASSWORD) must be provided"});function u(){try{let e=l.safeParse(process.env);if(!e.success){let t=e.error.errors.map(e=>`${e.path.join(".")}: ${e.message}`);return{success:!1,errors:t}}return{success:!0,data:e.data}}catch(e){return{success:!1,errors:["Failed to validate environment variables"]}}}let d=null;function c(){if(!d){let e=u();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),d=e.data,console.log("✅ Environment variables validated successfully")}return d}let p={jwt:{secret:()=>c().JWT_SECRET,expiresIn:()=>c().JWT_EXPIRES_IN},security:{bcryptRounds:()=>c().BCRYPT_ROUNDS,sessionTimeout:()=>c().SESSION_TIMEOUT,maxFileSize:()=>c().MAX_FILE_SIZE,uploadDir:()=>c().UPLOAD_DIR}};if("phase-production-build"!==process.env.NEXT_PHASE){let e=u();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),console.log("✅ Environment variables validated successfully")}let E=async e=>await a.Ay.hash(e,p.security.bcryptRounds()),m=async(e,t)=>await a.Ay.compare(e,t),R=e=>i().sign(e,p.jwt.secret(),{expiresIn:p.jwt.expiresIn()}),f=e=>{try{return i().verify(e,p.jwt.secret())}catch(e){return null}},g=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},T=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=f(t);if(!r)return{authenticated:!1,user:null};let a=await n.Gy.findByEmail(r.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},A=async e=>{let t,a;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await n.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let s=await E(e.password),i=!1;do a=g(),i=!await n.Gy.findByReferralId(a);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(t){let{placeUserByReferralType:a}=await r.e(2746).then(r.bind(r,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(t,o.id,s)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},I=async e=>{let t=await n.Gy.findByEmail(e.email);if(!t||!await m(e.password,t.password))throw Error("Invalid email or password");return{token:R({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},D=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),S=async e=>{let t=await n.Gy.findById(e);return t?.role==="ADMIN"}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},82629:(e,t,r)=>{r.d(t,{v5:()=>s});var a=r(6710);class s{static async logError(e){try{let t=e.error instanceof Error?e.error:Error(String(e.error)),r={action:e.action,userId:e.userId,adminId:e.adminId,details:{message:t.message,stack:t.stack,name:t.name,requestUrl:e.requestUrl,requestMethod:e.requestMethod,requestBody:e.requestBody,additionalData:e.additionalData,timestamp:new Date().toISOString()},ipAddress:e.ipAddress,userAgent:e.userAgent};await a.AJ.create(r),console.error(`[${e.action}] Error logged:`,{message:t.message,stack:t.stack,userId:e.userId,adminId:e.adminId,url:e.requestUrl})}catch(t){console.error("Failed to log error to database:",t),console.error("Original error:",e.error)}}static async logApiError(e,t,r,a,s,i){try{let n=null;try{if("GET"!==e.method&&e.headers.get("content-type")?.includes("application/json")){let t=e.clone();(n=await t.json()).password&&(n.password="[REDACTED]"),n.token&&(n.token="[REDACTED]"),n.apiKey&&(n.apiKey="[REDACTED]")}}catch(e){}await this.logError({action:r,error:t,userId:a,adminId:s,ipAddress:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||"unknown",userAgent:e.headers.get("user-agent")||"unknown",requestUrl:e.url,requestMethod:e.method,requestBody:n,additionalData:i})}catch(e){console.error("Failed to log API error:",e),console.error("Original error:",t)}}static async logClientError(e){try{await a.AJ.create({action:"CLIENT_ERROR",userId:e.userId,details:{message:e.message,stack:e.stack,url:e.url,timestamp:e.timestamp,additionalData:e.additionalData},userAgent:e.userAgent})}catch(e){console.error("Failed to log client error:",e)}}static async logAuthError(e,t,r,a){await this.logApiError(e,t,"AUTH_ERROR",void 0,void 0,{email:r,...a})}static async logDatabaseError(e,t,r,s,i){try{await a.AJ.create({action:"DATABASE_ERROR",userId:s,details:{message:e instanceof Error?e.message:String(e),stack:e instanceof Error?e.stack:void 0,operation:t,table:r,timestamp:new Date().toISOString(),additionalData:i}})}catch(t){console.error("Failed to log database error:",t),console.error("Original error:",e)}}static async logBusinessError(e,t,r,a,s){await this.logError({action:"BUSINESS_LOGIC_ERROR",error:e,userId:r,adminId:a,additionalData:{operation:t,...s}})}static async logExternalApiError(e,t,r,a,s){await this.logError({action:"EXTERNAL_API_ERROR",error:e,userId:a,additionalData:{service:t,endpoint:r,...s}})}static async logValidationError(e,t,r,a,s){await this.logError({action:"VALIDATION_ERROR",error:e,userId:a,additionalData:{field:t,value:r,...s}})}}},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580,7911,925],()=>r(19052));module.exports=a})();