"use strict";(()=>{var e={};e.id=5399,e.ids=[5399],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37366:e=>{e.exports=require("dns")},39542:(e,t,r)=>{r.d(t,{b9:()=>R,HU:()=>E,qc:()=>_,Lx:()=>g,DY:()=>I,DT:()=>S});var s=r(85663),a=r(43205),n=r.n(a),i=r(6710),o=r(45697);let l=o.z.object({DATABASE_URL:o.z.string().url("DATABASE_URL must be a valid PostgreSQL URL"),DIRECT_URL:o.z.string().url("DIRECT_URL must be a valid PostgreSQL URL"),JWT_SECRET:o.z.string().min(32,"JWT_SECRET must be at least 32 characters long").refine(e=>{let t=/[A-Z]/.test(e),r=/[a-z]/.test(e),s=/\d/.test(e),a=/[!@#$%^&*(),.?":{}|<>]/.test(e);return t&&r&&s&&a},"JWT_SECRET should contain uppercase, lowercase, numbers, and special characters"),JWT_EXPIRES_IN:o.z.string().default("30d"),NODE_ENV:o.z.enum(["development","production","test"]).default("development"),NEXT_PUBLIC_APP_URL:o.z.string().url().optional(),PORT:o.z.string().regex(/^\d+$/).transform(Number).default("3000"),SMTP_HOST:o.z.string().optional(),SMTP_PORT:o.z.string().regex(/^\d+$/).transform(Number).optional(),SMTP_USER:o.z.string().email().optional(),SMTP_PASSWORD:o.z.string().optional(),SMTP_SECURE:o.z.string().transform(e=>"true"===e).optional(),TRON_NETWORK:o.z.enum(["mainnet","testnet"]).default("testnet"),TRON_API_KEY:o.z.string().optional(),USDT_CONTRACT_ADDRESS:o.z.string().optional(),MAX_FILE_SIZE:o.z.string().regex(/^\d+$/).transform(Number).default("10485760"),UPLOAD_DIR:o.z.string().default("./public/uploads"),BCRYPT_ROUNDS:o.z.string().regex(/^\d+$/).transform(Number).default("12"),SESSION_TIMEOUT:o.z.string().regex(/^\d+$/).transform(Number).default("1800"),RATE_LIMIT_WINDOW_MS:o.z.string().regex(/^\d+$/).transform(Number).default("900000"),RATE_LIMIT_MAX_REQUESTS:o.z.string().regex(/^\d+$/).transform(Number).default("100"),COINGECKO_API_URL:o.z.string().url().default("https://api.coingecko.com/api/v3"),LOG_LEVEL:o.z.enum(["error","warn","info","debug"]).default("info"),ENABLE_REQUEST_LOGGING:o.z.string().transform(e=>"true"===e).default("false"),ENABLE_REGISTRATION:o.z.string().transform(e=>"true"===e).default("true"),ENABLE_KYC:o.z.string().transform(e=>"true"===e).default("true"),ENABLE_WITHDRAWALS:o.z.string().transform(e=>"true"===e).default("true")}).refine(e=>{let t=[e.SMTP_HOST,e.SMTP_PORT,e.SMTP_USER,e.SMTP_PASSWORD],r=t.some(e=>void 0!==e),s=t.every(e=>void 0!==e);return!r||!!s},{message:"If SMTP configuration is provided, all SMTP fields (HOST, PORT, USER, PASSWORD) must be provided"});function u(){try{let e=l.safeParse(process.env);if(!e.success){let t=e.error.errors.map(e=>`${e.path.join(".")}: ${e.message}`);return{success:!1,errors:t}}return{success:!0,data:e.data}}catch(e){return{success:!1,errors:["Failed to validate environment variables"]}}}let d=null;function c(){if(!d){let e=u();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),d=e.data,console.log("✅ Environment variables validated successfully")}return d}let p={jwt:{secret:()=>c().JWT_SECRET,expiresIn:()=>c().JWT_EXPIRES_IN},security:{bcryptRounds:()=>c().BCRYPT_ROUNDS,sessionTimeout:()=>c().SESSION_TIMEOUT,maxFileSize:()=>c().MAX_FILE_SIZE,uploadDir:()=>c().UPLOAD_DIR}};if("phase-production-build"!==process.env.NEXT_PHASE){let e=u();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),console.log("✅ Environment variables validated successfully")}let f=async e=>await s.Ay.hash(e,p.security.bcryptRounds()),m=async(e,t)=>await s.Ay.compare(e,t),E=e=>n().sign(e,p.jwt.secret(),{expiresIn:p.jwt.expiresIn()}),A=e=>{try{return n().verify(e,p.jwt.secret())}catch(e){return null}},T=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},R=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=A(t);if(!r)return{authenticated:!1,user:null};let s=await i.Gy.findByEmail(r.email);return s?{authenticated:!0,user:s}:{authenticated:!1,user:null}},I=async e=>{let t,s;if(await i.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await i.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let a=await f(e.password),n=!1;do s=T(),n=!await i.Gy.findByReferralId(s);while(!n);let o=await i.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:a,referralId:s});if(t){let{placeUserByReferralType:s}=await r.e(2746).then(r.bind(r,2746)),a="general";"left"===e.placementSide?a="left":"right"===e.placementSide&&(a="right"),await s(t,o.id,a)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},g=async e=>{let t=await i.Gy.findByEmail(e.email);if(!t||!await m(e.password,t.password))throw Error("Invalid email or password");return{token:E({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},S=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),_=async e=>{let t=await i.Gy.findById(e);return t?.role==="ADMIN"}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79306:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>E,routeModule:()=>c,serverHooks:()=>m,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>f});var s={};r.r(s),r.d(s,{GET:()=>d});var a=r(96559),n=r(48088),i=r(37719),o=r(32190),l=r(39542),u=r(6710);async function d(e){try{let{authenticated:t,user:r}=await (0,l.b9)(e);if(!t||!r)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});let{searchParams:s}=new URL(e.url),a=parseInt(s.get("limit")||"20"),n=parseInt(s.get("offset")||"0"),i=s.get("search")||"",d=s.get("type")||"",c=s.get("status")||"",p=s.get("dateFrom")||"",f=s.get("dateTo")||"",m={userId:r.id};d&&"ALL"!==d&&(m.type=d),c&&"ALL"!==c&&(m.status=c),(p||f)&&(m.createdAt={},p&&(m.createdAt.gte=new Date(p)),f&&(m.createdAt.lte=new Date(f+"T23:59:59.999Z")));let E={limit:Math.min(a,100),offset:n};i&&(E.search=i),d&&"ALL"!==d&&(E.types=[d]),c&&"ALL"!==c&&(E.status=c);let A=(await u.DR.findByUserId(r.id,E)).filter(e=>"ADMIN_CREDIT"!==e.type&&"ADMIN_DEBIT"!==e.type).map(e=>{if("WITHDRAWAL"===e.type&&e.reference)return{id:e.id,type:e.type,category:"TRANSACTION",amount:e.amount,description:e.description,status:e.status,reference:e.reference,createdAt:e.createdAt,txid:null,usdtAddress:null,rejectionReason:null,processedAt:null};if("DEPOSIT"===e.type){let t=e.description.match(/TX: ([a-fA-F0-9]+)/),r=t?t[1]:null;return{id:e.id,type:e.type,category:"TRANSACTION",amount:e.amount,description:e.description,status:e.status,reference:e.reference,createdAt:e.createdAt,txid:r,usdtAddress:null,rejectionReason:null,processedAt:null}}return{id:e.id,type:e.type,category:"TRANSACTION",amount:e.amount,description:e.description,status:e.status,reference:e.reference,createdAt:e.createdAt,txid:null,usdtAddress:null,rejectionReason:null,processedAt:null}});A.sort((e,t)=>new Date(t.createdAt).getTime()-new Date(e.createdAt).getTime());let T=A.slice(n,n+a);return o.NextResponse.json({success:!0,data:{transactions:T,pagination:{limit:a,offset:n,total:A.length,hasMore:n+a<A.length},filters:{transactionTypes:["ALL","MINING_EARNINGS","DIRECT_REFERRAL","BINARY_BONUS","DEPOSIT","WITHDRAWAL","PURCHASE"],statusOptions:["ALL","PENDING","COMPLETED","FAILED","CANCELLED","CONFIRMED","PENDING_VERIFICATION","WAITING_FOR_CONFIRMATIONS"]}}})}catch(e){return console.error("Wallet transactions fetch error:",e),o.NextResponse.json({success:!1,error:"Failed to fetch wallet transactions"},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/wallet/transactions/route",pathname:"/api/wallet/transactions",filename:"route",bundlePath:"app/api/wallet/transactions/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\wallet\\transactions\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:p,workUnitAsyncStorage:f,serverHooks:m}=c;function E(){return(0,i.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:f})}},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,7911,925],()=>r(79306));module.exports=s})();