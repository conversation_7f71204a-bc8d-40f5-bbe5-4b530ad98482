import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { verifyTokenEdge } from '@/lib/auth-edge';
import { apiRateLimit, adminRateLimit } from './lib/rateLimiter';

// Define protected and auth routes
const protectedRoutes = ['/dashboard', '/admin'];
const authRoutes = ['/login', '/register'];
const publicRoutes = ['/', '/about', '/contact'];

// Cache for admin settings to avoid repeated API calls
const settingsCache = new Map<string, { value: string; timestamp: number }>();
const CACHE_DURATION = 60000; // 1 minute cache

// Helper function to get admin setting with fallback to defaults
async function getAdminSetting(request: NextRequest, key: string): Promise<string | null> {
  try {
    // Check cache first
    const cached = settingsCache.get(key);
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      return cached.value;
    }

    // Make internal API call with timeout
    const baseUrl = new URL(request.url).origin;
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

    const response = await fetch(`${baseUrl}/api/admin/settings/check?key=${key}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    if (response.ok) {
      const data = await response.json();
      const value = data.value || getDefaultValue(key);

      // Cache the result
      settingsCache.set(key, { value, timestamp: Date.now() });
      return value;
    }

    // Return default values for critical settings if API fails
    return getDefaultValue(key);
  } catch (error) {
    console.error(`Error fetching admin setting ${key}:`, error);
    // Return default values for critical settings
    return getDefaultValue(key);
  }
}

// Get default values for critical settings
function getDefaultValue(key: string): string {
  switch (key) {
    case 'registrationEnabled': return 'true';
    case 'kycRequired': return 'true';
    default: return 'false';
  }
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Apply rate limiting to API routes first
  if (pathname.startsWith('/api/')) {
    // Skip rate limiting for health checks and static endpoints
    if (pathname === '/api/health' || pathname.startsWith('/api/crypto/prices')) {
      return NextResponse.next();
    }

    // Apply admin rate limiting for admin routes
    if (pathname.startsWith('/api/admin/')) {
      const rateLimitResult = adminRateLimit(request);
      if (!rateLimitResult.allowed) {
        const response = NextResponse.json(
          {
            success: false,
            error: 'Too many requests. Please try again later.',
            retryAfter: rateLimitResult.retryAfter
          },
          { status: 429 }
        );

        // Add rate limit headers
        response.headers.set('X-RateLimit-Limit', '30');
        response.headers.set('X-RateLimit-Remaining', rateLimitResult.remaining.toString());
        response.headers.set('X-RateLimit-Reset', new Date(rateLimitResult.resetTime).toISOString());
        if (rateLimitResult.retryAfter) {
          response.headers.set('Retry-After', rateLimitResult.retryAfter.toString());
        }

        return response;
      }
    }
    // Apply general API rate limiting for other API routes (except auth routes which have their own)
    else if (!pathname.startsWith('/api/auth/') && !pathname.startsWith('/api/otp/')) {
      const rateLimitResult = apiRateLimit(request);
      if (!rateLimitResult.allowed) {
        const response = NextResponse.json(
          {
            success: false,
            error: 'Too many requests. Please try again later.',
            retryAfter: rateLimitResult.retryAfter
          },
          { status: 429 }
        );

        // Add rate limit headers
        response.headers.set('X-RateLimit-Limit', '60');
        response.headers.set('X-RateLimit-Remaining', rateLimitResult.remaining.toString());
        response.headers.set('X-RateLimit-Reset', new Date(rateLimitResult.resetTime).toISOString());
        if (rateLimitResult.retryAfter) {
          response.headers.set('Retry-After', rateLimitResult.retryAfter.toString());
        }

        return response;
      }
    }

    // Continue to next middleware for API routes
    return NextResponse.next();
  }

  // Skip middleware for other static paths
  if (pathname.startsWith('/_next') ||
      pathname === '/favicon.ico' ||
      pathname.startsWith('/uploads') ||
      pathname.startsWith('/crypto-icons')) {
    return NextResponse.next();
  }



  // Get token from cookie
  const token = request.cookies.get('auth-token')?.value;

  // Verify token
  let isAuthenticated = false;
  let user = null;

  if (token) {
    try {
      const decoded = await verifyTokenEdge(token);
      if (decoded && decoded.userId && decoded.email) {
        isAuthenticated = true;
        user = decoded;
      }
    } catch (error) {
      // Token is invalid, remove it
      const response = NextResponse.next();
      response.cookies.delete('auth-token');
      return response;
    }
  }

  // Check if the current path is a protected route
  const isProtectedRoute = protectedRoutes.some(route => 
    pathname.startsWith(route)
  );
  
  // Check if the current path is an auth route (login/register)
  const isAuthRoute = authRoutes.some(route =>
    pathname.startsWith(route)
  );

  // Check registration enabled for register route
  if (pathname.startsWith('/register')) {
    try {
      const registrationEnabled = await getAdminSetting(request, 'registrationEnabled');
      if (registrationEnabled === 'false') {
        // Redirect to login with message
        const loginUrl = new URL('/login', request.url);
        loginUrl.searchParams.set('message', 'Registration is currently disabled');
        return NextResponse.redirect(loginUrl);
      }
    } catch (error) {
      console.error('Error checking registration enabled:', error);
      // Continue normally if database check fails
    }
  }

  // If user is not authenticated and trying to access protected route
  if (!isAuthenticated && isProtectedRoute) {
    const loginUrl = new URL('/login', request.url);
    loginUrl.searchParams.set('redirect', pathname);
    return NextResponse.redirect(loginUrl);
  }

  // If user is authenticated and trying to access auth routes
  if (isAuthenticated && isAuthRoute) {
    // Check if there's a redirect parameter
    const redirectUrl = request.nextUrl.searchParams.get('redirect');
    if (redirectUrl && redirectUrl.startsWith('/')) {
      return NextResponse.redirect(new URL(redirectUrl, request.url));
    }
    // Default redirect to dashboard
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }

  // For all other routes, continue normally
  return NextResponse.next();
}

// Configure which routes the middleware should run on
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!api|_next/static|_next/image|favicon.ico|uploads|crypto-icons).*)',
  ],
};
