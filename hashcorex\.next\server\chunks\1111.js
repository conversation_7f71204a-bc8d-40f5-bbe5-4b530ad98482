"use strict";exports.id=1111,exports.ids=[1111],exports.modules={21111:(t,e,r)=>{r.d(e,{X:()=>a,emailService:()=>n});var i=r(49526),o=r(6710);class s{async getEmailConfig(){try{let t=await o.T8.getEmailSettings();if(!t||!t.smtpHost||!t.smtpUser||!t.smtpPassword)return console.warn("Email configuration not found or incomplete"),null;return{host:t.smtpHost,port:t.smtpPort||587,secure:t.smtpSecure||!1,user:t.smtpUser,password:t.smtpPassword,fromName:t.fromName||"HashCoreX",fromEmail:t.fromEmail||t.smtpUser}}catch(t){return console.error("Failed to get email configuration:",t),null}}async initializeTransporter(t=!1){try{if(this.config=await this.getEmailConfig(),!this.config)return console.warn("Email configuration not available - email service disabled"),!1;if(!this.config.host||!this.config.user||!this.config.password)return console.error("Email configuration incomplete - missing host, user, or password"),!1;if(this.transporter=i.createTransport({host:this.config.host,port:this.config.port,secure:this.config.secure,auth:{user:this.config.user,pass:this.config.password},tls:{rejectUnauthorized:!1},connectionTimeout:1e4,greetingTimeout:5e3,socketTimeout:1e4}),t)console.log("Email transporter initialized (verification skipped)");else{let t=this.transporter.verify(),e=new Promise((t,e)=>setTimeout(()=>e(Error("Email verification timeout")),15e3));await Promise.race([t,e]),console.log("Email transporter initialized and verified successfully")}return!0}catch(t){return console.error("Failed to initialize email transporter:",t),this.transporter=null,this.config=null,!1}}async sendEmail(t){try{if((!this.transporter||!this.config)&&(console.log("Email transporter not initialized, attempting to initialize..."),!await this.initializeTransporter(!0)))return console.warn("Email service not configured - skipping email send"),!1;if(!t.to||!t.subject)return console.error("Invalid email data - missing recipient or subject"),!1;let e={from:`"${this.config.fromName}" <${this.config.fromEmail}>`,to:t.to,subject:t.subject,html:t.html,text:t.text},r=this.transporter.sendMail(e),i=new Promise((t,e)=>setTimeout(()=>e(Error("Email send timeout")),3e4)),o=await Promise.race([r,i]);return console.log("Email sent successfully:",o.messageId),!0}catch(t){return console.error("Failed to send email:",t),this.transporter=null,this.config=null,!1}}async sendOTPEmail(t,e,r,i="email_verification"){let o="otp_verification";"password_reset"===i?o="password_reset_otp":"two_factor_auth"===i&&(o="two_factor_otp");let s=await this.getEmailTemplate(o);if(!s)return console.error(`Email template '${o}' not found. Please ensure email templates are seeded.`),!1;let n=s.htmlContent,a=s.textContent||"";return n=(n=n.replace(/{{firstName}}/g,r||"User")).replace(/{{otp}}/g,e),a=(a=a.replace(/{{firstName}}/g,r||"User")).replace(/{{otp}}/g,e),await this.sendEmail({to:t,subject:s.subject,html:n,text:a})}async getEmailTemplate(t){try{return await o.T8.getEmailTemplate(t)}catch(t){return console.error("Failed to get email template:",t),null}}async testConnection(){try{if(this.config=await this.getEmailConfig(),!this.config)return console.error("Email configuration not found or incomplete"),!1;return this.transporter=i.createTransport({host:this.config.host,port:this.config.port,secure:this.config.secure,auth:{user:this.config.user,pass:this.config.password},tls:{rejectUnauthorized:!1}}),await this.transporter.verify(),console.log("Email connection test successful"),!0}catch(t){throw console.error("Email connection test failed:",t),this.transporter=null,t}}constructor(){this.transporter=null,this.config=null}}let n=new s,a=()=>Math.floor(1e5+9e5*Math.random()).toString()}};