import { prisma } from './prisma';
import { miningUnitDb, transactionDb, systemLogDb, adminSettingsDb } from './database';
import { allocateEarningsToUnits, getActiveMiningUnitsFIFO, shouldExpireUnit, expireMiningUnit } from './miningUnitEarnings';

// Note: User active status is now handled dynamically in the referral system
// The isActive field in the database is only used for account access control
// Mining activity status is computed on-demand for binary tree display

// Calculate dynamic ROI based on TH/s amount and admin-configured ranges
export async function calculateDynamicROI(thsAmount: number): Promise<number> {
  try {
    // Get earnings ranges from admin settings
    const earningsRangesStr = await adminSettingsDb.get('earningsRanges');
    let earningsRanges;

    if (earningsRangesStr) {
      try {
        earningsRanges = JSON.parse(earningsRangesStr);
      } catch (parseError) {
        console.error('Error parsing earnings ranges:', parseError);
        earningsRanges = null;
      }
    }

    // Fallback to default ranges if not configured
    if (!earningsRanges || !Array.isArray(earningsRanges)) {
      earningsRanges = [
        { minTHS: 0, maxTHS: 10, dailyReturnMin: 0.3, dailyReturnMax: 0.5, monthlyReturnMin: 10.0, monthlyReturnMax: 15.0 },
        { minTHS: 10, maxTHS: 50, dailyReturnMin: 0.4, dailyReturnMax: 0.6, monthlyReturnMin: 10.0, monthlyReturnMax: 15.0 },
        { minTHS: 50, maxTHS: 999999, dailyReturnMin: 0.5, dailyReturnMax: 0.7, monthlyReturnMin: 10.0, monthlyReturnMax: 15.0 },
      ];
    }

    // Find the appropriate range for the TH/s amount
    const applicableRange = earningsRanges.find((range: any) =>
      thsAmount >= range.minTHS && thsAmount <= range.maxTHS
    );

    let minROI: number;
    let maxROI: number;

    if (applicableRange) {
      minROI = applicableRange.dailyReturnMin;
      maxROI = applicableRange.dailyReturnMax;
    } else {
      // Fallback to highest range if no match found
      const highestRange = earningsRanges[earningsRanges.length - 1];
      minROI = highestRange.dailyReturnMin;
      maxROI = highestRange.dailyReturnMax;
    }

    // Add randomization within the range
    const randomROI = minROI + (Math.random() * (maxROI - minROI));

    // Round to 2 decimal places
    return Math.round(randomROI * 100) / 100;
  } catch (error) {
    console.error('Error calculating dynamic ROI:', error);
    // Fallback to default values based on TH/s amount
    if (thsAmount >= 50) return 0.6;
    if (thsAmount >= 10) return 0.5;
    return 0.4;
  }
}

// Validate monthly return doesn't exceed admin-configured limits for specific TH/s amount
export async function validateMonthlyReturn(dailyROI: number, thsAmount: number): Promise<boolean> {
  try {
    // Get earnings ranges from admin settings
    const earningsRangesStr = await adminSettingsDb.get('earningsRanges');
    let earningsRanges;

    if (earningsRangesStr) {
      try {
        earningsRanges = JSON.parse(earningsRangesStr);
      } catch (parseError) {
        console.error('Error parsing earnings ranges:', parseError);
        earningsRanges = null;
      }
    }

    // Fallback to default ranges if not configured
    if (!earningsRanges || !Array.isArray(earningsRanges)) {
      earningsRanges = [
        { minTHS: 0, maxTHS: 10, dailyReturnMin: 0.3, dailyReturnMax: 0.5, monthlyReturnMin: 10.0, monthlyReturnMax: 15.0 },
        { minTHS: 10, maxTHS: 50, dailyReturnMin: 0.4, dailyReturnMax: 0.6, monthlyReturnMin: 10.0, monthlyReturnMax: 15.0 },
        { minTHS: 50, maxTHS: 999999, dailyReturnMin: 0.5, dailyReturnMax: 0.7, monthlyReturnMin: 10.0, monthlyReturnMax: 15.0 },
      ];
    }

    // Find the appropriate range for the TH/s amount
    const applicableRange = earningsRanges.find((range: any) =>
      thsAmount >= range.minTHS && thsAmount <= range.maxTHS
    );

    let monthlyMin = 10.0;
    let monthlyMax = 15.0;

    if (applicableRange) {
      monthlyMin = applicableRange.monthlyReturnMin || 10.0;
      monthlyMax = applicableRange.monthlyReturnMax || 15.0;
    } else {
      // Fallback to highest range if no match found
      const highestRange = earningsRanges[earningsRanges.length - 1];
      monthlyMin = highestRange.monthlyReturnMin || 10.0;
      monthlyMax = highestRange.monthlyReturnMax || 15.0;
    }

    const monthlyReturn = dailyROI * 30; // Approximate monthly return
    return monthlyReturn >= monthlyMin && monthlyReturn <= monthlyMax;
  } catch (error) {
    console.error('Error validating monthly return:', error);
    // Fallback to default 10-15% range
    const monthlyReturn = dailyROI * 30;
    return monthlyReturn >= 10 && monthlyReturn <= 15;
  }
}

// Get monthly return limits for a specific TH/s amount
export async function getMonthlyReturnLimits(thsAmount: number): Promise<{ min: number; max: number }> {
  try {
    // Get earnings ranges from admin settings
    const earningsRangesStr = await adminSettingsDb.get('earningsRanges');
    let earningsRanges;

    if (earningsRangesStr) {
      try {
        earningsRanges = JSON.parse(earningsRangesStr);
      } catch (parseError) {
        console.error('Error parsing earnings ranges:', parseError);
        earningsRanges = null;
      }
    }

    // Fallback to default ranges if not configured
    if (!earningsRanges || !Array.isArray(earningsRanges)) {
      earningsRanges = [
        { minTHS: 0, maxTHS: 10, dailyReturnMin: 0.3, dailyReturnMax: 0.5, monthlyReturnMin: 10.0, monthlyReturnMax: 15.0 },
        { minTHS: 10, maxTHS: 50, dailyReturnMin: 0.4, dailyReturnMax: 0.6, monthlyReturnMin: 10.0, monthlyReturnMax: 15.0 },
        { minTHS: 50, maxTHS: 999999, dailyReturnMin: 0.5, dailyReturnMax: 0.7, monthlyReturnMin: 10.0, monthlyReturnMax: 15.0 },
      ];
    }

    // Find the appropriate range for the TH/s amount
    const applicableRange = earningsRanges.find((range: any) =>
      thsAmount >= range.minTHS && thsAmount <= range.maxTHS
    );

    if (applicableRange) {
      return {
        min: applicableRange.monthlyReturnMin || 10.0,
        max: applicableRange.monthlyReturnMax || 15.0,
      };
    } else {
      // Fallback to highest range if no match found
      const highestRange = earningsRanges[earningsRanges.length - 1];
      return {
        min: highestRange.monthlyReturnMin || 10.0,
        max: highestRange.monthlyReturnMax || 15.0,
      };
    }
  } catch (error) {
    console.error('Error getting monthly return limits:', error);
    // Fallback to default 10-15% range
    return { min: 10.0, max: 15.0 };
  }
}

// Calculate 7-day average ROI for a specific TH/s amount based on admin configuration
export async function calculate7DayAverageROI(thsAmount: number): Promise<number> {
  try {
    // Get earnings ranges from admin settings
    const earningsRangesStr = await adminSettingsDb.get('earningsRanges');
    let earningsRanges;

    if (earningsRangesStr) {
      try {
        earningsRanges = JSON.parse(earningsRangesStr);
      } catch (parseError) {
        console.error('Error parsing earnings ranges:', parseError);
        earningsRanges = null;
      }
    }

    // Fallback to default ranges if not configured
    if (!earningsRanges || !Array.isArray(earningsRanges)) {
      earningsRanges = [
        { minTHS: 0, maxTHS: 10, dailyReturnMin: 0.3, dailyReturnMax: 0.5, monthlyReturnMin: 10.0, monthlyReturnMax: 15.0 },
        { minTHS: 10, maxTHS: 50, dailyReturnMin: 0.4, dailyReturnMax: 0.6, monthlyReturnMin: 10.0, monthlyReturnMax: 15.0 },
        { minTHS: 50, maxTHS: 999999, dailyReturnMin: 0.5, dailyReturnMax: 0.7, monthlyReturnMin: 10.0, monthlyReturnMax: 15.0 },
      ];
    }

    // Find the appropriate range for the TH/s amount
    const applicableRange = earningsRanges.find((range: any) =>
      thsAmount >= range.minTHS && thsAmount <= range.maxTHS
    );

    let range;
    if (applicableRange) {
      range = applicableRange;
    } else {
      // Fallback to highest range if no match found
      range = earningsRanges[earningsRanges.length - 1];
    }

    // Calculate 7-day average by simulating daily ROI variations
    let totalROI = 0;
    const days = 7;

    for (let day = 0; day < days; day++) {
      // Generate a random ROI within the range for each day
      const dailyROI = range.dailyReturnMin + Math.random() * (range.dailyReturnMax - range.dailyReturnMin);
      totalROI += dailyROI;
    }

    // Return the 7-day average
    return totalROI / days;
  } catch (error) {
    console.error('Error calculating 7-day average ROI:', error);
    return 0.4; // Default fallback
  }
}

// Update existing mining units when earnings configuration changes
export async function updateExistingMiningUnitsROI() {
  try {
    console.log('Updating existing mining units with new ROI configuration...');

    // Get all active mining units
    const activeMiningUnits = await prisma.miningUnit.findMany({
      where: {
        status: 'ACTIVE',
        expiryDate: {
          gt: new Date(),
        },
      },
    });

    console.log(`Found ${activeMiningUnits.length} active mining units to update`);

    const updateResults = [];

    for (const unit of activeMiningUnits) {
      try {
        // Calculate new ROI based on current TH/s amount
        const newROI = await calculateDynamicROI(unit.thsAmount);

        // Update the mining unit with new ROI
        await prisma.miningUnit.update({
          where: { id: unit.id },
          data: { dailyROI: newROI },
        });

        updateResults.push({
          unitId: unit.id,
          userId: unit.userId,
          thsAmount: unit.thsAmount,
          oldROI: unit.dailyROI,
          newROI,
        });

        console.log(`Updated unit ${unit.id}: ${unit.dailyROI}% -> ${newROI}%`);
      } catch (unitError) {
        console.error(`Error updating unit ${unit.id}:`, unitError);
      }
    }

    // Log the update process
    await systemLogDb.create({
      action: 'MINING_UNITS_ROI_UPDATED',
      details: {
        unitsUpdated: updateResults.length,
        totalUnits: activeMiningUnits.length,
        updateResults,
        timestamp: new Date().toISOString(),
      },
    });

    console.log(`Successfully updated ${updateResults.length} mining units with new ROI configuration`);

    return {
      success: true,
      unitsUpdated: updateResults.length,
      totalUnits: activeMiningUnits.length,
      updateResults,
    };
  } catch (error) {
    console.error('Error updating existing mining units ROI:', error);
    throw error;
  }
}



// Calculate daily ROI for all active mining units using FIFO allocation
export async function calculateDailyROI() {
  try {
    console.log('Starting daily ROI calculation with FIFO allocation...');

    // Get all users with active mining units
    const usersWithMiningUnits = await prisma.user.findMany({
      where: {
        miningUnits: {
          some: {
            status: 'ACTIVE',
            expiryDate: {
              gt: new Date(),
            },
          },
        },
      },
      include: {
        miningUnits: {
          where: {
            status: 'ACTIVE',
            expiryDate: {
              gt: new Date(),
            },
          },
        },
      },
    });

    console.log(`Found ${usersWithMiningUnits.length} users with active mining units`);

    const results = [];

    for (const user of usersWithMiningUnits) {
      try {
        // Calculate total daily earnings for this user
        let totalDailyEarnings = 0;
        const unitEarnings = [];

        for (const unit of user.miningUnits) {
          const dailyEarnings = (unit.investmentAmount * unit.dailyROI) / 100;
          totalDailyEarnings += dailyEarnings;
          unitEarnings.push({
            unitId: unit.id,
            thsAmount: unit.thsAmount,
            dailyEarnings,
          });
        }

        if (totalDailyEarnings > 0) {
          // Create transaction first with full amount
          const transaction = await transactionDb.create({
            userId: user.id,
            type: 'MINING_EARNINGS',
            amount: totalDailyEarnings,
            description: `Daily mining earnings - Total: ${unitEarnings.map(u => `${u.thsAmount} TH/s`).join(', ')}`,
            status: 'PENDING',
          });

          // Allocate earnings to mining units using FIFO logic
          const allocationSummary = await allocateEarningsToUnits(
            user.id,
            totalDailyEarnings,
            'MINING_EARNINGS',
            transaction.id,
            'Daily mining ROI earnings'
          );

          // Update transaction with actual allocated amount and add note if there was excess
          if (allocationSummary.totalDiscarded > 0) {
            await prisma.transaction.update({
              where: { id: transaction.id },
              data: {
                amount: allocationSummary.totalAllocated,
                description: `Daily mining earnings - Total: ${unitEarnings.map(u => `${u.thsAmount} TH/s`).join(', ')}. Note: $${allocationSummary.totalDiscarded.toFixed(2)} excess discarded due to 5x mining unit limit cap.`,
              },
            });
          } else {
            // Update transaction amount to match allocated amount (should be the same if no excess)
            await prisma.transaction.update({
              where: { id: transaction.id },
              data: {
                amount: allocationSummary.totalAllocated,
              },
            });
          }

          results.push({
            userId: user.id,
            totalEarnings: allocationSummary.totalAllocated, // Only count allocated amount
            allocations: allocationSummary.allocations,
            unitsProcessed: user.miningUnits.length,
            discardedAmount: allocationSummary.totalDiscarded,
          });

          console.log(`Allocated ${allocationSummary.totalAllocated} of ${totalDailyEarnings} mining earnings to ${allocationSummary.allocations.length} units for user ${user.id}`);

          if (allocationSummary.totalDiscarded > 0) {
            console.log(`Discarded ${allocationSummary.totalDiscarded} excess mining earnings due to capacity limits for user ${user.id}`);
          }
        }

      } catch (userError) {
        console.error(`Error processing mining earnings for user ${user.id}:`, userError);
      }
    }

    // Log the daily ROI calculation
    await systemLogDb.create({
      action: 'DAILY_ROI_CALCULATED',
      details: {
        usersProcessed: results.length,
        totalEarnings: results.reduce((sum, r) => sum + r.totalEarnings, 0),
        totalAllocations: results.reduce((sum, r) => sum + r.allocations.length, 0),
        timestamp: new Date().toISOString(),
      },
    });

    console.log(`Daily ROI calculation completed. Processed ${results.length} users with FIFO allocation.`);
    return results;

  } catch (error) {
    console.error('Daily ROI calculation error:', error);
    throw error;
  }
}

// Process weekly earnings distribution (Saturday 15:00 UTC)
export async function processWeeklyEarnings() {
  try {
    console.log('Starting weekly earnings distribution...');

    // Get all pending mining earnings
    const pendingEarnings = await prisma.transaction.findMany({
      where: {
        type: 'MINING_EARNINGS',
        status: 'PENDING',
      },
      include: {
        user: true,
      },
    });

    console.log(`Found ${pendingEarnings.length} pending earnings transactions`);

    const userEarnings = new Map<string, number>();

    // Group earnings by user
    for (const transaction of pendingEarnings) {
      const currentTotal = userEarnings.get(transaction.userId) || 0;
      userEarnings.set(transaction.userId, currentTotal + transaction.amount);
    }

    const results = [];

    // Process each user's earnings
    for (const [userId, totalEarnings] of userEarnings) {
      try {
        // Mark all pending transactions as completed
        await prisma.transaction.updateMany({
          where: {
            userId,
            type: 'MINING_EARNINGS',
            status: 'PENDING',
          },
          data: {
            status: 'COMPLETED',
          },
        });

        results.push({
          userId,
          totalEarnings,
        });

      } catch (userError) {
        console.error(`Error processing earnings for user ${userId}:`, userError);
      }
    }

    // Log the weekly distribution
    await systemLogDb.create({
      action: 'WEEKLY_EARNINGS_DISTRIBUTED',
      details: {
        usersProcessed: results.length,
        totalDistributed: results.reduce((sum, r) => sum + r.totalEarnings, 0),
        transactionsProcessed: pendingEarnings.length,
        timestamp: new Date().toISOString(),
      },
    });

    console.log(`Weekly earnings distribution completed. Processed ${results.length} users.`);
    return results;

  } catch (error) {
    console.error('Weekly earnings distribution error:', error);
    throw error;
  }
}

// Check and expire mining units that have reached 24 months
export async function expireOldMiningUnits() {
  try {
    console.log('Checking for expired mining units...');

    const expiredUnits = await prisma.miningUnit.findMany({
      where: {
        status: 'ACTIVE',
        expiryDate: {
          lte: new Date(),
        },
      },
      include: {
        user: {
          select: {
            email: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });

    console.log(`Found ${expiredUnits.length} units to expire`);

    for (const unit of expiredUnits) {
      await miningUnitDb.expireUnit(unit.id);

      // Note: User active status is now computed dynamically

      await systemLogDb.create({
        action: 'MINING_UNIT_EXPIRED',
        userId: unit.userId,
        details: {
          miningUnitId: unit.id,
          reason: '24_months_reached',
          totalEarned: unit.totalEarned,
          investmentAmount: unit.investmentAmount,
        },
      });

      // Send email notification
      try {
        const { emailNotificationService } = await import('@/lib/emailNotificationService');
        const totalEarnings = unit.miningEarnings + unit.referralEarnings + unit.binaryEarnings;
        await emailNotificationService.sendMiningUnitExpiryNotification({
          userId: unit.userId,
          email: unit.user.email,
          firstName: unit.user.firstName,
          lastName: unit.user.lastName,
          thsAmount: unit.thsAmount,
          investmentAmount: unit.investmentAmount,
          totalEarned: totalEarnings,
          purchaseDate: unit.createdAt.toISOString(),
          expiryDate: unit.expiryDate.toISOString(),
          expiryReason: 'TIME_LIMIT',
        });
      } catch (emailError) {
        console.error('Failed to send mining unit expiry email:', emailError);
        // Don't fail the expiry if email fails
      }
    }

    return expiredUnits.length;

  } catch (error) {
    console.error('Mining unit expiry check error:', error);
    throw error;
  }
}

// Get mining statistics
export async function getMiningStats() {
  try {
    const stats = await prisma.$transaction([
      // Total TH/s sold
      prisma.miningUnit.aggregate({
        _sum: {
          thsAmount: true,
        },
      }),
      
      // Active TH/s
      prisma.miningUnit.aggregate({
        where: {
          status: 'ACTIVE',
        },
        _sum: {
          thsAmount: true,
        },
      }),
      
      // Total investment
      prisma.miningUnit.aggregate({
        _sum: {
          investmentAmount: true,
        },
      }),
      
      // Total earnings distributed
      prisma.transaction.aggregate({
        where: {
          type: 'MINING_EARNINGS',
          status: 'COMPLETED',
        },
        _sum: {
          amount: true,
        },
      }),
      
      // Active mining units count
      prisma.miningUnit.count({
        where: {
          status: 'ACTIVE',
        },
      }),
      
      // Total mining units count
      prisma.miningUnit.count(),
    ]);

    return {
      totalTHSSold: stats[0]._sum.thsAmount || 0,
      activeTHS: stats[1]._sum.thsAmount || 0,
      totalInvestment: stats[2]._sum.investmentAmount || 0,
      totalEarningsDistributed: stats[3]._sum.amount || 0,
      activeMiningUnits: stats[4],
      totalMiningUnits: stats[5],
    };

  } catch (error) {
    console.error('Mining stats error:', error);
    throw error;
  }
}

// Calculate user's estimated earnings
export async function calculateEstimatedEarnings(userId: string) {
  try {
    const activeMiningUnits = await miningUnitDb.findActiveByUserId(userId);
    
    if (activeMiningUnits.length === 0) {
      return {
        next7Days: 0,
        next30Days: 0,
        next365Days: 0,
        next2Years: 0,
      };
    }

    let totalDaily = 0;
    
    for (const unit of activeMiningUnits) {
      const dailyEarnings = (unit.investmentAmount * unit.dailyROI) / 100;
      const maxEarnings = unit.investmentAmount * 5;
      const remainingEarnings = maxEarnings - unit.totalEarned;
      
      // Use the lower of daily earnings or remaining earnings
      totalDaily += Math.min(dailyEarnings, remainingEarnings);
    }

    return {
      next7Days: totalDaily * 7,
      next30Days: totalDaily * 30,
      next365Days: totalDaily * 365,
      next2Years: totalDaily * 730, // 2 years = 730 days
    };

  } catch (error) {
    console.error('Estimated earnings calculation error:', error);
    throw error;
  }
}
