import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');
  console.log('📝 Creating admin user only...');

  // Create admin user
  const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
  const adminPassword = process.env.ADMIN_PASSWORD || 'Admin123@#';
  const hashedPassword = await bcrypt.hash(adminPassword, 12);

  console.log('👤 Creating admin user...');
  await prisma.user.upsert({
    where: { email: adminEmail },
    update: {
      role: 'ADMIN', // Ensure admin role is set
      kycStatus: 'APPROVED',
      isActive: true,
    },
    create: {
      email: adminEmail,
      password: hashedPassword,
      firstName: 'Admin',
      lastName: 'User',
      referralId: 'ADMIN_REF_001',
      role: 'ADMIN',
      kycStatus: 'APPROVED',
      isActive: true,
    },
  });

  console.log('✅ Database seeding completed successfully!');
  console.log(`📧 Admin email: ${adminEmail}`);
  console.log(`🔑 Admin password: ${adminPassword}`);
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
