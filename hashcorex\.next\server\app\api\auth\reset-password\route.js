"use strict";(()=>{var e={};e.id=6478,e.ids=[6478],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10974:(e,t,r)=>{function s(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}function a(e){let t=[],r=[],s=e.length>=8;r.push({valid:s,message:"At least 8 characters long"}),s||t.push("Password must be at least 8 characters long");let a=/[A-Z]/.test(e);r.push({valid:a,message:"At least one uppercase letter"}),a||t.push("Password must contain at least one uppercase letter");let i=/[a-z]/.test(e);r.push({valid:i,message:"At least one lowercase letter"}),i||t.push("Password must contain at least one lowercase letter");let n=/\d/.test(e);r.push({valid:n,message:"At least one number"}),n||t.push("Password must contain at least one number");let o=/[!@#$%^&*(),.?":{}|<>]/.test(e);return r.push({valid:o,message:"At least one special character"}),o||t.push("Password must contain at least one special character"),{isValid:0===t.length,errors:t,checks:r}}r.d(t,{DT:()=>s,Oj:()=>a})},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37366:e=>{e.exports=require("dns")},39542:(e,t,r)=>{r.d(t,{b9:()=>f,HU:()=>A,qc:()=>I,Lx:()=>w,DY:()=>h,DT:()=>v});var s=r(85663),a=r(43205),i=r.n(a),n=r(6710),o=r(45697);let d=o.z.object({DATABASE_URL:o.z.string().url("DATABASE_URL must be a valid PostgreSQL URL"),DIRECT_URL:o.z.string().url("DIRECT_URL must be a valid PostgreSQL URL"),JWT_SECRET:o.z.string().min(32,"JWT_SECRET must be at least 32 characters long").refine(e=>{let t=/[A-Z]/.test(e),r=/[a-z]/.test(e),s=/\d/.test(e),a=/[!@#$%^&*(),.?":{}|<>]/.test(e);return t&&r&&s&&a},"JWT_SECRET should contain uppercase, lowercase, numbers, and special characters"),JWT_EXPIRES_IN:o.z.string().default("30d"),NODE_ENV:o.z.enum(["development","production","test"]).default("development"),NEXT_PUBLIC_APP_URL:o.z.string().url().optional(),PORT:o.z.string().regex(/^\d+$/).transform(Number).default("3000"),SMTP_HOST:o.z.string().optional(),SMTP_PORT:o.z.string().regex(/^\d+$/).transform(Number).optional(),SMTP_USER:o.z.string().email().optional(),SMTP_PASSWORD:o.z.string().optional(),SMTP_SECURE:o.z.string().transform(e=>"true"===e).optional(),TRON_NETWORK:o.z.enum(["mainnet","testnet"]).default("testnet"),TRON_API_KEY:o.z.string().optional(),USDT_CONTRACT_ADDRESS:o.z.string().optional(),MAX_FILE_SIZE:o.z.string().regex(/^\d+$/).transform(Number).default("10485760"),UPLOAD_DIR:o.z.string().default("./public/uploads"),BCRYPT_ROUNDS:o.z.string().regex(/^\d+$/).transform(Number).default("12"),SESSION_TIMEOUT:o.z.string().regex(/^\d+$/).transform(Number).default("1800"),RATE_LIMIT_WINDOW_MS:o.z.string().regex(/^\d+$/).transform(Number).default("900000"),RATE_LIMIT_MAX_REQUESTS:o.z.string().regex(/^\d+$/).transform(Number).default("100"),COINGECKO_API_URL:o.z.string().url().default("https://api.coingecko.com/api/v3"),LOG_LEVEL:o.z.enum(["error","warn","info","debug"]).default("info"),ENABLE_REQUEST_LOGGING:o.z.string().transform(e=>"true"===e).default("false"),ENABLE_REGISTRATION:o.z.string().transform(e=>"true"===e).default("true"),ENABLE_KYC:o.z.string().transform(e=>"true"===e).default("true"),ENABLE_WITHDRAWALS:o.z.string().transform(e=>"true"===e).default("true")}).refine(e=>{let t=[e.SMTP_HOST,e.SMTP_PORT,e.SMTP_USER,e.SMTP_PASSWORD],r=t.some(e=>void 0!==e),s=t.every(e=>void 0!==e);return!r||!!s},{message:"If SMTP configuration is provided, all SMTP fields (HOST, PORT, USER, PASSWORD) must be provided"});function l(){try{let e=d.safeParse(process.env);if(!e.success){let t=e.error.errors.map(e=>`${e.path.join(".")}: ${e.message}`);return{success:!1,errors:t}}return{success:!0,data:e.data}}catch(e){return{success:!1,errors:["Failed to validate environment variables"]}}}let u=null;function c(){if(!u){let e=l();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),u=e.data,console.log("✅ Environment variables validated successfully")}return u}let p={jwt:{secret:()=>c().JWT_SECRET,expiresIn:()=>c().JWT_EXPIRES_IN},security:{bcryptRounds:()=>c().BCRYPT_ROUNDS,sessionTimeout:()=>c().SESSION_TIMEOUT,maxFileSize:()=>c().MAX_FILE_SIZE,uploadDir:()=>c().UPLOAD_DIR}};if("phase-production-build"!==process.env.NEXT_PHASE){let e=l();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),console.log("✅ Environment variables validated successfully")}let m=async e=>await s.Ay.hash(e,p.security.bcryptRounds()),g=async(e,t)=>await s.Ay.compare(e,t),A=e=>i().sign(e,p.jwt.secret(),{expiresIn:p.jwt.expiresIn()}),S=e=>{try{return i().verify(e,p.jwt.secret())}catch(e){return null}},E=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},f=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=S(t);if(!r)return{authenticated:!1,user:null};let s=await n.Gy.findByEmail(r.email);return s?{authenticated:!0,user:s}:{authenticated:!1,user:null}},h=async e=>{let t,s;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await n.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let a=await m(e.password),i=!1;do s=E(),i=!await n.Gy.findByReferralId(s);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:a,referralId:s});if(t){let{placeUserByReferralType:s}=await r.e(2746).then(r.bind(r,2746)),a="general";"left"===e.placementSide?a="left":"right"===e.placementSide&&(a="right"),await s(t,o.id,a)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},w=async e=>{let t=await n.Gy.findByEmail(e.email);if(!t||!await g(e.password,t.password))throw Error("Invalid email or password");return{token:A({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},v=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),I=async e=>{let t=await n.Gy.findById(e);return t?.role==="ADMIN"}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},82629:(e,t,r)=>{r.d(t,{v5:()=>a});var s=r(6710);class a{static async logError(e){try{let t=e.error instanceof Error?e.error:Error(String(e.error)),r={action:e.action,userId:e.userId,adminId:e.adminId,details:{message:t.message,stack:t.stack,name:t.name,requestUrl:e.requestUrl,requestMethod:e.requestMethod,requestBody:e.requestBody,additionalData:e.additionalData,timestamp:new Date().toISOString()},ipAddress:e.ipAddress,userAgent:e.userAgent};await s.AJ.create(r),console.error(`[${e.action}] Error logged:`,{message:t.message,stack:t.stack,userId:e.userId,adminId:e.adminId,url:e.requestUrl})}catch(t){console.error("Failed to log error to database:",t),console.error("Original error:",e.error)}}static async logApiError(e,t,r,s,a,i){try{let n=null;try{if("GET"!==e.method&&e.headers.get("content-type")?.includes("application/json")){let t=e.clone();(n=await t.json()).password&&(n.password="[REDACTED]"),n.token&&(n.token="[REDACTED]"),n.apiKey&&(n.apiKey="[REDACTED]")}}catch(e){}await this.logError({action:r,error:t,userId:s,adminId:a,ipAddress:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||"unknown",userAgent:e.headers.get("user-agent")||"unknown",requestUrl:e.url,requestMethod:e.method,requestBody:n,additionalData:i})}catch(e){console.error("Failed to log API error:",e),console.error("Original error:",t)}}static async logClientError(e){try{await s.AJ.create({action:"CLIENT_ERROR",userId:e.userId,details:{message:e.message,stack:e.stack,url:e.url,timestamp:e.timestamp,additionalData:e.additionalData},userAgent:e.userAgent})}catch(e){console.error("Failed to log client error:",e)}}static async logAuthError(e,t,r,s){await this.logApiError(e,t,"AUTH_ERROR",void 0,void 0,{email:r,...s})}static async logDatabaseError(e,t,r,a,i){try{await s.AJ.create({action:"DATABASE_ERROR",userId:a,details:{message:e instanceof Error?e.message:String(e),stack:e instanceof Error?e.stack:void 0,operation:t,table:r,timestamp:new Date().toISOString(),additionalData:i}})}catch(t){console.error("Failed to log database error:",t),console.error("Original error:",e)}}static async logBusinessError(e,t,r,s,a){await this.logError({action:"BUSINESS_LOGIC_ERROR",error:e,userId:r,adminId:s,additionalData:{operation:t,...a}})}static async logExternalApiError(e,t,r,s,a){await this.logError({action:"EXTERNAL_API_ERROR",error:e,userId:s,additionalData:{service:t,endpoint:r,...a}})}static async logValidationError(e,t,r,s,a){await this.logError({action:"VALIDATION_ERROR",error:e,userId:s,additionalData:{field:t,value:r,...a}})}}},85163:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>w,routeModule:()=>S,serverHooks:()=>h,workAsyncStorage:()=>E,workUnitAsyncStorage:()=>f});var s={};r.r(s),r.d(s,{POST:()=>A});var a=r(96559),i=r(48088),n=r(37719),o=r(32190),d=r(39542),l=r(10974),u=r(6710),c=r(82629),p=r(31183);class m{static async createSession(e,t,r=2592e6){let s=this.generateSessionId(),a=new Date(Date.now()+r),i=await p.prisma.userSession.findFirst({where:{userId:e,deviceId:t.deviceId,isActive:!0}});if(i)return await p.prisma.userSession.update({where:{id:i.id},data:{lastActivity:new Date,expiresAt:a,ipAddress:t.ipAddress,userAgent:t.userAgent}}),i.id;let n=await p.prisma.userSession.create({data:{id:s,userId:e,deviceId:t.deviceId,deviceName:this.extractDeviceName(t.userAgent),ipAddress:t.ipAddress,userAgent:t.userAgent,isActive:!0,lastActivity:new Date,expiresAt:a}});return await u.AJ.create({action:"SESSION_CREATED",userId:e,details:{sessionId:n.id,deviceId:t.deviceId,deviceName:n.deviceName,ipAddress:t.ipAddress},ipAddress:t.ipAddress,userAgent:t.userAgent}),n.id}static async validateSession(e,t){let r=await p.prisma.userSession.findFirst({where:{id:e,isActive:!0,expiresAt:{gt:new Date}}});return r?r.deviceId!==t.deviceId?(await this.invalidateSession(e,"DEVICE_MISMATCH"),null):(await p.prisma.userSession.update({where:{id:e},data:{lastActivity:new Date,ipAddress:t.ipAddress}}),r):null}static async invalidateSession(e,t){let r=await p.prisma.userSession.findUnique({where:{id:e}});r&&(await p.prisma.userSession.update({where:{id:e},data:{isActive:!1}}),await u.AJ.create({action:"SESSION_INVALIDATED",userId:r.userId,details:{sessionId:e,reason:t,invalidatedAt:new Date().toISOString()},ipAddress:r.ipAddress,userAgent:r.userAgent}))}static async invalidateAllUserSessions(e,t){let r={userId:e,isActive:!0};t&&(r.id={not:t});let s=await p.prisma.userSession.findMany({where:r});s.length>0&&(await p.prisma.userSession.updateMany({where:r,data:{isActive:!1}}),await u.AJ.create({action:"ALL_SESSIONS_INVALIDATED",userId:e,details:{invalidatedSessions:s.length,exceptSessionId:t||null,reason:"SECURITY_EVENT",invalidatedAt:new Date().toISOString()}}))}static async invalidateOnSecurityEvent(e,t,r){await this.invalidateAllUserSessions(e,r),await u.AJ.create({action:"SECURITY_EVENT_SESSION_INVALIDATION",userId:e,details:{event:t,currentSessionPreserved:!!r,timestamp:new Date().toISOString()}})}static async getUserActiveSessions(e){return await p.prisma.userSession.findMany({where:{userId:e,isActive:!0,expiresAt:{gt:new Date}},orderBy:{lastActivity:"desc"}})}static async cleanupExpiredSessions(){return(await p.prisma.userSession.updateMany({where:{OR:[{expiresAt:{lt:new Date}},{lastActivity:{lt:new Date(Date.now()-6048e5)}}],isActive:!0},data:{isActive:!1}})).count}static generateSessionId(){let e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",t="";for(let r=0;r<64;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t}static extractDeviceName(e){return e.includes("Mobile")?"Mobile Device":e.includes("Tablet")?"Tablet":e.includes("Windows")?"Windows PC":e.includes("Macintosh")?"Mac":e.includes("Linux")?"Linux PC":"Unknown Device"}static generateDeviceFingerprint(e,t,r,s){let a=`${e}-${r||""}-${s||""}`;return{userAgent:e,ipAddress:t,acceptLanguage:r,acceptEncoding:s,deviceId:this.hashString(a)}}static hashString(e){let t=0;for(let r=0;r<e.length;r++)t=(t<<5)-t+e.charCodeAt(r),t&=t;return Math.abs(t).toString(36)}static async checkSuspiciousActivity(e){let t=[];return new Set((await this.getUserActiveSessions(e)).map(e=>e.ipAddress)).size>3&&t.push("Multiple IP addresses detected"),(await p.prisma.userSession.findMany({where:{userId:e,createdAt:{gt:new Date(Date.now()-36e5)}}})).length>5&&t.push("Rapid session creation detected"),{suspicious:t.length>0,reasons:t}}}setInterval(async function e(){let e=await m.cleanupExpiredSessions();console.log(`Cleaned up ${e} expired sessions`)},36e5);var g=r(85663);async function A(e){try{let{email:t,newPassword:r,confirmPassword:s}=await e.json();if(!t||!r||!s)return o.NextResponse.json({success:!1,error:"All fields are required"},{status:400});if(!(0,d.DT)(t))return o.NextResponse.json({success:!1,error:"Invalid email format"},{status:400});if(r!==s)return o.NextResponse.json({success:!1,error:"Passwords do not match"},{status:400});let a=(0,l.Oj)(r);if(!a.isValid)return o.NextResponse.json({success:!1,error:a.errors.join(", ")},{status:400});let i=await u.Gy.findByEmail(t);if(!i)return o.NextResponse.json({success:!1,error:"User not found"},{status:400});if(!await u.oV.findVerified(t,"password_reset"))return o.NextResponse.json({success:!1,error:"No verified OTP found. Please complete email verification first."},{status:400});let n=await g.Ay.hash(r,12);await u.Gy.updatePassword(i.id,n),await m.invalidateOnSecurityEvent(i.id,"PASSWORD_CHANGE"),await u.oV.cleanup(),await u.AJ.create({action:"PASSWORD_RESET",userId:i.id,details:{email:i.email,resetTime:new Date().toISOString()},ipAddress:e.headers.get("x-forwarded-for")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"});let c=(0,d.HU)({userId:i.id,email:i.email}),p=o.NextResponse.json({success:!0,message:"Password reset successful",data:{user:{id:i.id,email:i.email,firstName:i.firstName,lastName:i.lastName,referralId:i.referralId,role:i.role,kycStatus:i.kycStatus,isActive:i.isActive,profilePicture:i.profilePicture,createdAt:i.createdAt,updatedAt:i.updatedAt},token:c}});return p.cookies.set("auth-token",c,{httpOnly:!0,secure:!0,sameSite:"strict",maxAge:2592e3,path:"/"}),p}catch(t){return console.error("Password reset error:",t),await c.v5.logApiError(e,t,"PASSWORD_RESET_ERROR"),o.NextResponse.json({success:!1,error:t.message||"Password reset failed"},{status:500})}}let S=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/auth/reset-password/route",pathname:"/api/auth/reset-password",filename:"route",bundlePath:"app/api/auth/reset-password/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\auth\\reset-password\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:E,workUnitAsyncStorage:f,serverHooks:h}=S;function w(){return(0,n.patchFetch)({workAsyncStorage:E,workUnitAsyncStorage:f})}},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,7911,925],()=>r(85163));module.exports=s})();