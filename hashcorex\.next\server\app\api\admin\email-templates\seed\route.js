"use strict";(()=>{var e={};e.id=3392,e.ids=[3392],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37366:e=>{e.exports=require("dns")},39542:(e,t,r)=>{r.d(t,{b9:()=>y,HU:()=>m,qc:()=>T,Lx:()=>v,DY:()=>b,DT:()=>w});var i=r(85663),a=r(43205),o=r.n(a),n=r(6710),s=r(45697);let d=s.z.object({DATABASE_URL:s.z.string().url("DATABASE_URL must be a valid PostgreSQL URL"),DIRECT_URL:s.z.string().url("DIRECT_URL must be a valid PostgreSQL URL"),JWT_SECRET:s.z.string().min(32,"JWT_SECRET must be at least 32 characters long").refine(e=>{let t=/[A-Z]/.test(e),r=/[a-z]/.test(e),i=/\d/.test(e),a=/[!@#$%^&*(),.?":{}|<>]/.test(e);return t&&r&&i&&a},"JWT_SECRET should contain uppercase, lowercase, numbers, and special characters"),JWT_EXPIRES_IN:s.z.string().default("30d"),NODE_ENV:s.z.enum(["development","production","test"]).default("development"),NEXT_PUBLIC_APP_URL:s.z.string().url().optional(),PORT:s.z.string().regex(/^\d+$/).transform(Number).default("3000"),SMTP_HOST:s.z.string().optional(),SMTP_PORT:s.z.string().regex(/^\d+$/).transform(Number).optional(),SMTP_USER:s.z.string().email().optional(),SMTP_PASSWORD:s.z.string().optional(),SMTP_SECURE:s.z.string().transform(e=>"true"===e).optional(),TRON_NETWORK:s.z.enum(["mainnet","testnet"]).default("testnet"),TRON_API_KEY:s.z.string().optional(),USDT_CONTRACT_ADDRESS:s.z.string().optional(),MAX_FILE_SIZE:s.z.string().regex(/^\d+$/).transform(Number).default("10485760"),UPLOAD_DIR:s.z.string().default("./public/uploads"),BCRYPT_ROUNDS:s.z.string().regex(/^\d+$/).transform(Number).default("12"),SESSION_TIMEOUT:s.z.string().regex(/^\d+$/).transform(Number).default("1800"),RATE_LIMIT_WINDOW_MS:s.z.string().regex(/^\d+$/).transform(Number).default("900000"),RATE_LIMIT_MAX_REQUESTS:s.z.string().regex(/^\d+$/).transform(Number).default("100"),COINGECKO_API_URL:s.z.string().url().default("https://api.coingecko.com/api/v3"),LOG_LEVEL:s.z.enum(["error","warn","info","debug"]).default("info"),ENABLE_REQUEST_LOGGING:s.z.string().transform(e=>"true"===e).default("false"),ENABLE_REGISTRATION:s.z.string().transform(e=>"true"===e).default("true"),ENABLE_KYC:s.z.string().transform(e=>"true"===e).default("true"),ENABLE_WITHDRAWALS:s.z.string().transform(e=>"true"===e).default("true")}).refine(e=>{let t=[e.SMTP_HOST,e.SMTP_PORT,e.SMTP_USER,e.SMTP_PASSWORD],r=t.some(e=>void 0!==e),i=t.every(e=>void 0!==e);return!r||!!i},{message:"If SMTP configuration is provided, all SMTP fields (HOST, PORT, USER, PASSWORD) must be provided"});function l(){try{let e=d.safeParse(process.env);if(!e.success){let t=e.error.errors.map(e=>`${e.path.join(".")}: ${e.message}`);return{success:!1,errors:t}}return{success:!0,data:e.data}}catch(e){return{success:!1,errors:["Failed to validate environment variables"]}}}let p=null;function c(){if(!p){let e=l();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),p=e.data,console.log("✅ Environment variables validated successfully")}return p}let u={jwt:{secret:()=>c().JWT_SECRET,expiresIn:()=>c().JWT_EXPIRES_IN},security:{bcryptRounds:()=>c().BCRYPT_ROUNDS,sessionTimeout:()=>c().SESSION_TIMEOUT,maxFileSize:()=>c().MAX_FILE_SIZE,uploadDir:()=>c().UPLOAD_DIR}};if("phase-production-build"!==process.env.NEXT_PHASE){let e=l();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),console.log("✅ Environment variables validated successfully")}let h=async e=>await i.Ay.hash(e,u.security.bcryptRounds()),g=async(e,t)=>await i.Ay.compare(e,t),m=e=>o().sign(e,u.jwt.secret(),{expiresIn:u.jwt.expiresIn()}),f=e=>{try{return o().verify(e,u.jwt.secret())}catch(e){return null}},x=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},y=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=f(t);if(!r)return{authenticated:!1,user:null};let i=await n.Gy.findByEmail(r.email);return i?{authenticated:!0,user:i}:{authenticated:!1,user:null}},b=async e=>{let t,i;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await n.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let a=await h(e.password),o=!1;do i=x(),o=!await n.Gy.findByReferralId(i);while(!o);let s=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:a,referralId:i});if(t){let{placeUserByReferralType:i}=await r.e(2746).then(r.bind(r,2746)),a="general";"left"===e.placementSide?a="left":"right"===e.placementSide&&(a="right"),await i(t,s.id,a)}return{id:s.id,email:s.email,referralId:s.referralId,kycStatus:s.kycStatus}},v=async e=>{let t=await n.Gy.findByEmail(e.email);if(!t||!await g(e.password,t.password))throw Error("Invalid email or password");return{token:m({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},w=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),T=async e=>{let t=await n.Gy.findById(e);return t?.role==="ADMIN"}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},82629:(e,t,r)=>{r.d(t,{v5:()=>a});var i=r(6710);class a{static async logError(e){try{let t=e.error instanceof Error?e.error:Error(String(e.error)),r={action:e.action,userId:e.userId,adminId:e.adminId,details:{message:t.message,stack:t.stack,name:t.name,requestUrl:e.requestUrl,requestMethod:e.requestMethod,requestBody:e.requestBody,additionalData:e.additionalData,timestamp:new Date().toISOString()},ipAddress:e.ipAddress,userAgent:e.userAgent};await i.AJ.create(r),console.error(`[${e.action}] Error logged:`,{message:t.message,stack:t.stack,userId:e.userId,adminId:e.adminId,url:e.requestUrl})}catch(t){console.error("Failed to log error to database:",t),console.error("Original error:",e.error)}}static async logApiError(e,t,r,i,a,o){try{let n=null;try{if("GET"!==e.method&&e.headers.get("content-type")?.includes("application/json")){let t=e.clone();(n=await t.json()).password&&(n.password="[REDACTED]"),n.token&&(n.token="[REDACTED]"),n.apiKey&&(n.apiKey="[REDACTED]")}}catch(e){}await this.logError({action:r,error:t,userId:i,adminId:a,ipAddress:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||"unknown",userAgent:e.headers.get("user-agent")||"unknown",requestUrl:e.url,requestMethod:e.method,requestBody:n,additionalData:o})}catch(e){console.error("Failed to log API error:",e),console.error("Original error:",t)}}static async logClientError(e){try{await i.AJ.create({action:"CLIENT_ERROR",userId:e.userId,details:{message:e.message,stack:e.stack,url:e.url,timestamp:e.timestamp,additionalData:e.additionalData},userAgent:e.userAgent})}catch(e){console.error("Failed to log client error:",e)}}static async logAuthError(e,t,r,i){await this.logApiError(e,t,"AUTH_ERROR",void 0,void 0,{email:r,...i})}static async logDatabaseError(e,t,r,a,o){try{await i.AJ.create({action:"DATABASE_ERROR",userId:a,details:{message:e instanceof Error?e.message:String(e),stack:e instanceof Error?e.stack:void 0,operation:t,table:r,timestamp:new Date().toISOString(),additionalData:o}})}catch(t){console.error("Failed to log database error:",t),console.error("Original error:",e)}}static async logBusinessError(e,t,r,i,a){await this.logError({action:"BUSINESS_LOGIC_ERROR",error:e,userId:r,adminId:i,additionalData:{operation:t,...a}})}static async logExternalApiError(e,t,r,i,a){await this.logError({action:"EXTERNAL_API_ERROR",error:e,userId:i,additionalData:{service:t,endpoint:r,...a}})}static async logValidationError(e,t,r,i,a){await this.logError({action:"VALIDATION_ERROR",error:e,userId:i,additionalData:{field:t,value:r,...a}})}}},88231:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>h,serverHooks:()=>f,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>m});var i={};r.r(i),r.d(i,{POST:()=>u});var a=r(96559),o=r(48088),n=r(37719),s=r(32190),d=r(39542),l=r(6710),p=r(82629);let c=[{name:"otp_verification",subject:"Email Verification - HashCoreX",htmlContent:`
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Verification</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">HashCoreX</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0;">Sustainable Mining Platform</p>
    </div>

    <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;">
        <h2 style="color: #333; margin-top: 0;">Hello {{firstName}}!</h2>

        <p>Thank you for registering with HashCoreX. To complete your registration, please verify your email address using the OTP code below:</p>

        <div style="background: #fff; border: 2px dashed #667eea; padding: 20px; text-align: center; margin: 20px 0; border-radius: 8px;">
            <h3 style="margin: 0; color: #667eea; font-size: 32px; letter-spacing: 5px;">{{otp}}</h3>
            <p style="margin: 10px 0 0 0; color: #666; font-size: 14px;">This code expires in 10 minutes</p>
        </div>

        <p>If you didn't create an account with HashCoreX, please ignore this email.</p>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;">
            <p>Best regards,<br>The HashCoreX Team</p>
            <p style="margin-top: 20px;">
                <a href="#" style="color: #667eea; text-decoration: none;">Visit HashCoreX</a> |
                <a href="#" style="color: #667eea; text-decoration: none;">Support</a>
            </p>
        </div>
    </div>
</body>
</html>`,textContent:`Hello {{firstName}}!

Thank you for registering with HashCoreX. To complete your registration, please verify your email address using the OTP code below:

Your OTP Code: {{otp}}

This code expires in 10 minutes.

If you didn't create an account with HashCoreX, please ignore this email.

Best regards,
The HashCoreX Team`},{name:"welcome_email",subject:"Welcome to HashCoreX - Your Mining Journey Begins!",htmlContent:`
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to HashCoreX</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">Welcome to HashCoreX!</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0;">Your Sustainable Mining Journey Starts Here</p>
    </div>

    <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;">
        <h2 style="color: #333; margin-top: 0;">Hello {{firstName}}!</h2>

        <p>Congratulations! Your HashCoreX account has been successfully created. You're now part of our sustainable mining community.</p>

        <div style="background: #fff; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #667eea;">
            <h3 style="margin-top: 0; color: #667eea;">What's Next?</h3>
            <ul style="margin: 0; padding-left: 20px;">
                <li>Complete your KYC verification</li>
                <li>Explore our mining packages</li>
                <li>Start earning with sustainable mining</li>
                <li>Refer friends and earn bonuses</li>
            </ul>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <a href="#" style="background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">Access Dashboard</a>
        </div>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;">
            <p>Need help? Contact our support team anytime.</p>
            <p>Best regards,<br>The HashCoreX Team</p>
        </div>
    </div>
</body>
</html>`,textContent:`Hello {{firstName}}!

Congratulations! Your HashCoreX account has been successfully created. You're now part of our sustainable mining community.

What's Next?
- Complete your KYC verification
- Explore our mining packages
- Start earning with sustainable mining
- Refer friends and earn bonuses

Visit your dashboard to get started: [Dashboard Link]

Need help? Contact our support team anytime.

Best regards,
The HashCoreX Team`},{name:"deposit_success",subject:"Deposit Confirmed - HashCoreX",htmlContent:`
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Deposit Confirmed</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">HashCoreX</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0;">Deposit Confirmed!</p>
    </div>

    <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;">
        <h2 style="color: #333; margin-top: 0;">Hello {{firstName}}!</h2>

        <p>Great news! Your deposit has been successfully confirmed and credited to your account.</p>

        <div style="background: #fff; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #10b981;">
            <h3 style="margin-top: 0; color: #10b981;">Deposit Details</h3>
            <p><strong>Amount:</strong> {{amount}} {{currency}}</p>
            <p><strong>Transaction ID:</strong> {{transactionId}}</p>
            <p><strong>Status:</strong> <span style="color: #10b981;">Confirmed</span></p>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <a href="#" style="background: #10b981; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">View Dashboard</a>
        </div>

        <p>You can now use these funds to purchase mining packages and start earning!</p>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;">
            <p>Best regards,<br>The HashCoreX Team</p>
            <p style="margin-top: 20px;">
                <a href="#" style="color: #10b981; text-decoration: none;">Visit HashCoreX</a> |
                <a href="#" style="color: #10b981; text-decoration: none;">Support</a>
            </p>
        </div>
    </div>
</body>
</html>`,textContent:`Hello {{firstName}}!

Great news! Your deposit has been successfully confirmed and credited to your account.

Deposit Details:
- Amount: {{amount}} {{currency}}
- Transaction ID: {{transactionId}}
- Status: Confirmed

You can now use these funds to purchase mining packages and start earning!

Visit your dashboard to get started: [Dashboard Link]

Best regards,
The HashCoreX Team`},{name:"kyc_approved",subject:"KYC Verification Approved - HashCoreX",htmlContent:`
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KYC Approved</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">HashCoreX</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0;">KYC Verification Approved!</p>
    </div>

    <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;">
        <h2 style="color: #333; margin-top: 0;">Hello {{firstName}}!</h2>

        <p>Congratulations! Your KYC verification has been approved. Your account is now fully verified and you have access to all HashCoreX features.</p>

        <div style="background: #fff; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #10b981;">
            <h3 style="margin-top: 0; color: #10b981;">What's Now Available?</h3>
            <ul style="margin: 0; padding-left: 20px;">
                <li>Full access to mining packages</li>
                <li>Unlimited deposits and withdrawals</li>
                <li>Access to premium features</li>
                <li>Higher referral commissions</li>
            </ul>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <a href="#" style="background: #10b981; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">Access Dashboard</a>
        </div>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;">
            <p>Best regards,<br>The HashCoreX Team</p>
            <p style="margin-top: 20px;">
                <a href="#" style="color: #10b981; text-decoration: none;">Visit HashCoreX</a> |
                <a href="#" style="color: #10b981; text-decoration: none;">Support</a>
            </p>
        </div>
    </div>
</body>
</html>`,textContent:`Hello {{firstName}}!

Congratulations! Your KYC verification has been approved. Your account is now fully verified and you have access to all HashCoreX features.

What's Now Available?
- Full access to mining packages
- Unlimited deposits and withdrawals
- Access to premium features
- Higher referral commissions

Visit your dashboard to get started: [Dashboard Link]

Best regards,
The HashCoreX Team`},{name:"kyc_rejected",subject:"KYC Verification Update - HashCoreX",htmlContent:`
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KYC Update</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">HashCoreX</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0;">KYC Verification Update</p>
    </div>

    <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;">
        <h2 style="color: #333; margin-top: 0;">Hello {{firstName}}!</h2>

        <p>We have reviewed your KYC verification documents. Unfortunately, we need additional information to complete your verification.</p>

        <div style="background: #fff; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #ef4444;">
            <h3 style="margin-top: 0; color: #ef4444;">Reason for Review</h3>
            <p>{{rejectionReason}}</p>
        </div>

        <div style="background: #fff; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #667eea;">
            <h3 style="margin-top: 0; color: #667eea;">Next Steps</h3>
            <ul style="margin: 0; padding-left: 20px;">
                <li>Review the feedback above</li>
                <li>Prepare updated documents</li>
                <li>Resubmit your KYC application</li>
                <li>Contact support if you need help</li>
            </ul>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <a href="#" style="background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">Resubmit KYC</a>
        </div>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;">
            <p>Best regards,<br>The HashCoreX Team</p>
            <p style="margin-top: 20px;">
                <a href="#" style="color: #667eea; text-decoration: none;">Visit HashCoreX</a> |
                <a href="#" style="color: #667eea; text-decoration: none;">Support</a>
            </p>
        </div>
    </div>
</body>
</html>`,textContent:`Hello {{firstName}}!

We have reviewed your KYC verification documents. Unfortunately, we need additional information to complete your verification.

Reason for Review:
{{rejectionReason}}

Next Steps:
- Review the feedback above
- Prepare updated documents
- Resubmit your KYC application
- Contact support if you need help

Visit your dashboard to resubmit: [Dashboard Link]

Best regards,
The HashCoreX Team`},{name:"withdrawal_approved",subject:"Withdrawal Approved - HashCoreX",htmlContent:`
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Withdrawal Approved</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">HashCoreX</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0;">Withdrawal Approved!</p>
    </div>

    <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;">
        <h2 style="color: #333; margin-top: 0;">Hello {{firstName}}!</h2>

        <p>Good news! Your withdrawal request has been approved and is being processed.</p>

        <div style="background: #fff; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #10b981;">
            <h3 style="margin-top: 0; color: #10b981;">Withdrawal Details</h3>
            <p><strong>Amount:</strong> {{amount}} USDT</p>
            <p><strong>Destination:</strong> {{usdtAddress}}</p>
            <p><strong>Status:</strong> <span style="color: #10b981;">Approved - Processing</span></p>
        </div>

        <p>Your funds will be transferred to your wallet within 24 hours. You will receive another email once the transaction is completed.</p>

        <div style="text-align: center; margin: 30px 0;">
            <a href="#" style="background: #10b981; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">View Transaction</a>
        </div>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;">
            <p>Best regards,<br>The HashCoreX Team</p>
            <p style="margin-top: 20px;">
                <a href="#" style="color: #10b981; text-decoration: none;">Visit HashCoreX</a> |
                <a href="#" style="color: #10b981; text-decoration: none;">Support</a>
            </p>
        </div>
    </div>
</body>
</html>`,textContent:`Hello {{firstName}}!

Good news! Your withdrawal request has been approved and is being processed.

Withdrawal Details:
- Amount: {{amount}} USDT
- Destination: {{usdtAddress}}
- Status: Approved - Processing

Your funds will be transferred to your wallet within 24 hours. You will receive another email once the transaction is completed.

Visit your dashboard: [Dashboard Link]

Best regards,
The HashCoreX Team`},{name:"withdrawal_completed",subject:"Withdrawal Completed - HashCoreX",htmlContent:`
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Withdrawal Completed</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">HashCoreX</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0;">Withdrawal Completed!</p>
    </div>

    <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;">
        <h2 style="color: #333; margin-top: 0;">Hello {{firstName}}!</h2>

        <p>Your withdrawal has been successfully completed! The funds have been transferred to your wallet.</p>

        <div style="background: #fff; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #10b981;">
            <h3 style="margin-top: 0; color: #10b981;">Transaction Details</h3>
            <p><strong>Amount:</strong> {{amount}} USDT</p>
            <p><strong>Destination:</strong> {{usdtAddress}}</p>
            <p><strong>Transaction Hash:</strong> {{transactionHash}}</p>
            <p><strong>Status:</strong> <span style="color: #10b981;">Completed</span></p>
        </div>

        <p>You can verify this transaction on the blockchain using the transaction hash above.</p>

        <div style="text-align: center; margin: 30px 0;">
            <a href="#" style="background: #10b981; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">View on Blockchain</a>
        </div>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;">
            <p>Best regards,<br>The HashCoreX Team</p>
            <p style="margin-top: 20px;">
                <a href="#" style="color: #10b981; text-decoration: none;">Visit HashCoreX</a> |
                <a href="#" style="color: #10b981; text-decoration: none;">Support</a>
            </p>
        </div>
    </div>
</body>
</html>`,textContent:`Hello {{firstName}}!

Your withdrawal has been successfully completed! The funds have been transferred to your wallet.

Transaction Details:
- Amount: {{amount}} USDT
- Destination: {{usdtAddress}}
- Transaction Hash: {{transactionHash}}
- Status: Completed

You can verify this transaction on the blockchain using the transaction hash above.

Visit your dashboard: [Dashboard Link]

Best regards,
The HashCoreX Team`},{name:"withdrawal_rejected",subject:"Withdrawal Update - HashCoreX",htmlContent:`
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Withdrawal Update</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">HashCoreX</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0;">Withdrawal Update</p>
    </div>

    <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;">
        <h2 style="color: #333; margin-top: 0;">Hello {{firstName}}!</h2>

        <p>We regret to inform you that your withdrawal request could not be processed at this time.</p>

        <div style="background: #fff; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #ef4444;">
            <h3 style="margin-top: 0; color: #ef4444;">Withdrawal Details</h3>
            <p><strong>Amount:</strong> {{amount}} USDT</p>
            <p><strong>Reason:</strong> {{rejectionReason}}</p>
        </div>

        <p>The requested amount has been returned to your account balance. Please review the reason above and contact our support team if you need assistance.</p>

        <div style="text-align: center; margin: 30px 0;">
            <a href="#" style="background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">Contact Support</a>
        </div>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;">
            <p>Best regards,<br>The HashCoreX Team</p>
            <p style="margin-top: 20px;">
                <a href="#" style="color: #667eea; text-decoration: none;">Visit HashCoreX</a> |
                <a href="#" style="color: #667eea; text-decoration: none;">Support</a>
            </p>
        </div>
    </div>
</body>
</html>`,textContent:`Hello {{firstName}}!

We regret to inform you that your withdrawal request could not be processed at this time.

Withdrawal Details:
- Amount: {{amount}} USDT
- Reason: {{rejectionReason}}

The requested amount has been returned to your account balance. Please review the reason above and contact our support team if you need assistance.

Visit your dashboard: [Dashboard Link]

Best regards,
The HashCoreX Team`},{name:"test_email",subject:"HashCoreX Email Test",htmlContent:`
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Test</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">HashCoreX</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0;">Email Configuration Test</p>
    </div>

    <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;">
        <h2 style="color: #333; margin-top: 0;">Email Test Successful!</h2>

        <p>This is a test email to verify your SMTP configuration.</p>
        <p>If you received this email, your email settings are working correctly!</p>

        <div style="background: #fff; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #667eea;">
            <h3 style="margin-top: 0; color: #667eea;">Test Details</h3>
            <p><strong>Test Date:</strong> {{testDate}}</p>
            <p><strong>Status:</strong> <span style="color: #10b981;">Success</span></p>
        </div>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;">
            <p>Your email configuration is working properly.</p>
            <p>Best regards,<br>The HashCoreX Team</p>
        </div>
    </div>
</body>
</html>`,textContent:`HashCoreX Email Test

This is a test email to verify your SMTP configuration.
If you received this email, your email settings are working correctly!

Test Date: {{testDate}}
Status: Success

Your email configuration is working properly.

Best regards,
The HashCoreX Team`},{name:"password_reset_otp",subject:"Password Reset - HashCoreX",htmlContent:`
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">HashCoreX</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0;">Password Reset Request</p>
    </div>

    <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;">
        <h2 style="color: #333; margin-top: 0;">Hello {{firstName}}!</h2>

        <p>We received a request to reset your password for your HashCoreX account. If you made this request, please use the verification code below:</p>

        <div style="background: #fff; padding: 30px; margin: 30px 0; border-radius: 10px; text-align: center; border: 2px solid #ef4444;">
            <h2 style="color: #ef4444; margin: 0 0 10px 0; font-size: 36px; letter-spacing: 8px; font-weight: bold;">{{otp}}</h2>
            <p style="color: #666; margin: 0; font-size: 14px;">This code will expire in 10 minutes</p>
        </div>

        <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin: 20px 0;">
            <p style="margin: 0; color: #856404; font-size: 14px;">
                <strong>Security Notice:</strong> If you didn't request this password reset, please ignore this email. Your account remains secure.
            </p>
        </div>

        <p>For your security:</p>
        <ul style="color: #666; font-size: 14px;">
            <li>This code is valid for 10 minutes only</li>
            <li>Don't share this code with anyone</li>
            <li>If you didn't request this reset, your account is still secure</li>
        </ul>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;">
            <p>Need help? Contact our support team.</p>
            <p>Best regards,<br>The HashCoreX Team</p>
        </div>
    </div>
</body>
</html>`,textContent:`HashCoreX Password Reset

Hello {{firstName}}!

We received a request to reset your password for your HashCoreX account. If you made this request, please use the verification code below:

Your verification code: {{otp}}

This code will expire in 10 minutes.

Security Notice: If you didn't request this password reset, please ignore this email. Your account remains secure.

For your security:
- This code is valid for 10 minutes only
- Don't share this code with anyone
- If you didn't request this reset, your account is still secure

Need help? Contact our support team.

Best regards,
The HashCoreX Team`},{name:"mining_unit_purchase",subject:"Mining Unit Purchase Confirmed - HashCoreX",htmlContent:`
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mining Unit Purchase Confirmed</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #ffd60a 0%, #10b981 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">HashCoreX</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0;">Sustainable Mining Platform</p>
    </div>

    <div style="background: white; padding: 40px; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
        <h2 style="color: #10b981; margin: 0 0 20px 0; font-size: 24px;">🎉 Mining Unit Purchase Confirmed!</h2>

        <p style="margin: 0 0 20px 0; font-size: 16px;">Hello <strong>{{firstName}}</strong>,</p>

        <p style="margin: 0 0 25px 0; font-size: 16px;">Congratulations! Your mining unit purchase has been successfully processed and is now active.</p>

        <div style="background: #f8f9fa; padding: 25px; margin: 25px 0; border-radius: 8px; border-left: 4px solid #10b981;">
            <h3 style="margin: 0 0 15px 0; color: #10b981; font-size: 18px;">⚡ Mining Unit Details</h3>
            <table style="width: 100%; border-collapse: collapse;">
                <tr>
                    <td style="padding: 8px 0; font-weight: bold; color: #555;">TH/s Amount:</td>
                    <td style="padding: 8px 0; color: #333;">{{thsAmount}} TH/s</td>
                </tr>
                <tr>
                    <td style="padding: 8px 0; font-weight: bold; color: #555;">Investment Amount:</td>
                    <td style="padding: 8px 0; color: #333;">$\{{investmentAmount}} USDT</td>
                </tr>
                <tr>
                    <td style="padding: 8px 0; font-weight: bold; color: #555;">Daily ROI:</td>
                    <td style="padding: 8px 0; color: #10b981; font-weight: bold;">{{dailyROI}}%</td>
                </tr>
                <tr>
                    <td style="padding: 8px 0; font-weight: bold; color: #555;">Purchase Date:</td>
                    <td style="padding: 8px 0; color: #333;">{{purchaseDate}}</td>
                </tr>
                <tr>
                    <td style="padding: 8px 0; font-weight: bold; color: #555;">Expiry Date:</td>
                    <td style="padding: 8px 0; color: #333;">{{expiryDate}}</td>
                </tr>
            </table>
        </div>

        <div style="background: #e3f2fd; padding: 20px; margin: 25px 0; border-radius: 8px; border-left: 4px solid #2196f3;">
            <h4 style="margin: 0 0 15px 0; color: #1976d2; font-size: 16px;">📋 Important Information</h4>
            <ul style="margin: 0; padding-left: 20px; color: #555;">
                <li style="margin-bottom: 8px;">Your mining unit will start generating daily returns immediately</li>
                <li style="margin-bottom: 8px;">Earnings are paid out weekly on Sundays at 00:00 AM GMT+5:30</li>
                <li style="margin-bottom: 8px;">Mining units expire after 24 months or when they reach 5x return, whichever comes first</li>
                <li style="margin-bottom: 8px;">You can track your earnings in the Mining section of your dashboard</li>
            </ul>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <a href="https://hashcorex.com/dashboard" style="background: #10b981; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">View Dashboard</a>
        </div>

        <p style="margin: 25px 0 0 0; font-size: 16px;">Thank you for choosing HashCoreX for your mining investment!</p>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;">
            <p>Best regards,<br><strong>The HashCoreX Team</strong></p>
        </div>
    </div>
</body>
</html>`,textContent:`Mining Unit Purchase Confirmed!

Hello {{firstName}},

Congratulations! Your mining unit purchase has been successfully processed and is now active.

Mining Unit Details:
- TH/s Amount: {{thsAmount}} TH/s
- Investment Amount: $\{{investmentAmount}} USDT
- Daily ROI: {{dailyROI}}%
- Purchase Date: {{purchaseDate}}
- Expiry Date: {{expiryDate}}

Important Information:
- Your mining unit will start generating daily returns immediately
- Earnings are paid out weekly on Sundays at 00:00 AM GMT+5:30
- Mining units expire after 24 months or when they reach 5x return, whichever comes first
- You can track your earnings in the Mining section of your dashboard

Thank you for choosing HashCoreX for your mining investment!

Best regards,
The HashCoreX Team`},{name:"mining_unit_expiry",subject:"Mining Unit Expired - HashCoreX",htmlContent:`
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mining Unit Expired</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #ffd60a 0%, #ff9800 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">HashCoreX</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0;">Sustainable Mining Platform</p>
    </div>

    <div style="background: white; padding: 40px; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
        <h2 style="color: #ff9800; margin: 0 0 20px 0; font-size: 24px;">⏰ Mining Unit Expired</h2>

        <p style="margin: 0 0 20px 0; font-size: 16px;">Hello <strong>{{firstName}}</strong>,</p>

        <p style="margin: 0 0 25px 0; font-size: 16px;">We're writing to inform you that one of your mining units has reached its expiry condition.</p>

        <div style="background: #fff3e0; padding: 25px; margin: 25px 0; border-radius: 8px; border-left: 4px solid #ff9800;">
            <h3 style="margin: 0 0 15px 0; color: #ff9800; font-size: 18px;">📊 Expired Mining Unit Details</h3>
            <table style="width: 100%; border-collapse: collapse;">
                <tr>
                    <td style="padding: 8px 0; font-weight: bold; color: #555;">TH/s Amount:</td>
                    <td style="padding: 8px 0; color: #333;">{{thsAmount}} TH/s</td>
                </tr>
                <tr>
                    <td style="padding: 8px 0; font-weight: bold; color: #555;">Original Investment:</td>
                    <td style="padding: 8px 0; color: #333;">$\{{investmentAmount}} USDT</td>
                </tr>
                <tr>
                    <td style="padding: 8px 0; font-weight: bold; color: #555;">Total Earned:</td>
                    <td style="padding: 8px 0; color: #10b981; font-weight: bold;">$\{{totalEarned}} USDT</td>
                </tr>
                <tr>
                    <td style="padding: 8px 0; font-weight: bold; color: #555;">Purchase Date:</td>
                    <td style="padding: 8px 0; color: #333;">{{purchaseDate}}</td>
                </tr>
                <tr>
                    <td style="padding: 8px 0; font-weight: bold; color: #555;">Expiry Date:</td>
                    <td style="padding: 8px 0; color: #333;">{{expiryDate}}</td>
                </tr>
                <tr>
                    <td style="padding: 8px 0; font-weight: bold; color: #555;">Expiry Reason:</td>
                    <td style="padding: 8px 0; color: #ff9800; font-weight: bold;">{{expiryReason}}</td>
                </tr>
            </table>
        </div>

        <div style="background: #e8f5e8; padding: 20px; margin: 25px 0; border-radius: 8px; border-left: 4px solid #2e7d32;">
            <h4 style="margin: 0 0 15px 0; color: #2e7d32; font-size: 16px;">✅ What Happens Next?</h4>
            <ul style="margin: 0; padding-left: 20px; color: #555;">
                <li style="margin-bottom: 8px;">All accumulated earnings have been credited to your wallet</li>
                <li style="margin-bottom: 8px;">This mining unit will no longer generate daily returns</li>
                <li style="margin-bottom: 8px;">You can view the complete history in your Mining section</li>
                <li style="margin-bottom: 8px;">Consider purchasing new mining units to continue earning</li>
            </ul>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <a href="https://hashcorex.com/dashboard" style="background: #10b981; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block; margin-right: 10px;">View Dashboard</a>
            <a href="https://hashcorex.com/dashboard" style="background: #ff9800; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">Purchase New Unit</a>
        </div>

        <p style="margin: 25px 0 0 0; font-size: 16px;">Thank you for your continued trust in HashCoreX!</p>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;">
            <p>Best regards,<br><strong>The HashCoreX Team</strong></p>
        </div>
    </div>
</body>
</html>`,textContent:`Mining Unit Expired

Hello {{firstName}},

We're writing to inform you that one of your mining units has reached its expiry condition.

Expired Mining Unit Details:
- TH/s Amount: {{thsAmount}} TH/s
- Original Investment: $\{{investmentAmount}} USDT
- Total Earned: $\{{totalEarned}} USDT
- Purchase Date: {{purchaseDate}}
- Expiry Date: {{expiryDate}}
- Expiry Reason: {{expiryReason}}

What Happens Next?
- All accumulated earnings have been credited to your wallet
- This mining unit will no longer generate daily returns
- You can view the complete history in your Mining section
- Consider purchasing new mining units to continue earning

Thank you for your continued trust in HashCoreX!

Best regards,
The HashCoreX Team`},{name:"two_factor_otp",subject:"Two-Factor Authentication Code - HashCoreX",htmlContent:`
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Two-Factor Authentication</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">HashCoreX</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0;">Two-Factor Authentication</p>
    </div>

    <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;">
        <h2 style="color: #333; margin-top: 0;">Hello {{firstName}}!</h2>

        <p>You are attempting to log in to your HashCoreX account. For your security, please use the verification code below to complete your login:</p>

        <div style="background: #fff; border: 2px dashed #10b981; padding: 20px; text-align: center; margin: 20px 0; border-radius: 8px;">
            <h3 style="margin: 0; color: #10b981; font-size: 32px; letter-spacing: 5px;">{{otp}}</h3>
            <p style="margin: 10px 0 0 0; color: #666; font-size: 14px;">This code expires in 10 minutes</p>
        </div>

        <div style="background: #e7f3ff; border-left: 4px solid #2196F3; padding: 15px; margin: 20px 0;">
            <p style="margin: 0; color: #1976D2;"><strong>Security Notice:</strong></p>
            <p style="margin: 5px 0 0 0; color: #666;">If you did not attempt to log in, please ignore this email and consider changing your password for security.</p>
        </div>

        <p>This verification code is required to complete your login process. Do not share this code with anyone.</p>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;">
            <p>If you have any questions, please contact our support team.</p>
            <p>Best regards,<br>The HashCoreX Team</p>
        </div>
    </div>
</body>
</html>`,textContent:`HashCoreX - Two-Factor Authentication Code

Hello {{firstName}},

You are attempting to log in to your HashCoreX account. For your security, please use the verification code below to complete your login:

Verification Code: {{otp}}

This code expires in 10 minutes.

Security Notice:
If you did not attempt to log in, please ignore this email and consider changing your password for security.

This verification code is required to complete your login process. Do not share this code with anyone.

If you have any questions, please contact our support team.

Best regards,
The HashCoreX Team`}];async function u(e){try{let{authenticated:t,user:r}=await (0,d.b9)(e);if(!t||!r)return s.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});if(!await (0,d.qc)(r.id))return s.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let i=[];for(let e of c)try{if(await l.vo.findByName(e.name)){i.push({name:e.name,status:"exists"});continue}await l.vo.create(e),i.push({name:e.name,status:"created"})}catch(t){i.push({name:e.name,status:"error",error:t.message})}return s.NextResponse.json({success:!0,message:"Default templates seeded successfully",data:i})}catch(t){return console.error("Seed email templates error:",t),await p.v5.logApiError(e,t,"SEED_EMAIL_TEMPLATES_ERROR"),s.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}let h=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/admin/email-templates/seed/route",pathname:"/api/admin/email-templates/seed",filename:"route",bundlePath:"app/api/admin/email-templates/seed/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\email-templates\\seed\\route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:g,workUnitAsyncStorage:m,serverHooks:f}=h;function x(){return(0,n.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:m})}},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[4447,580,7911,925],()=>r(88231));module.exports=i})();