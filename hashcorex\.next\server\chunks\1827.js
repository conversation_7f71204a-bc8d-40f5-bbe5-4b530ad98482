"use strict";exports.id=1827,exports.ids=[1827],exports.modules={1827:(t,e,i)=>{i.d(e,{j:()=>d});var a=i(6710),o=i(31183),n=i(59480),r=i(13161);let s=new Map,c=new Map;class l{static getInstance(){return l.instance||(l.instance=new l),l.instance}async start(){if(this.isRunning)return void console.log("Deposit verification service is already running");this.isRunning=!0,console.log("Starting deposit verification service..."),await this.processPendingVerifications(),await this.processWaitingForConfirmations(),console.log("Deposit verification service started successfully")}stop(){this.isRunning=!1,c.forEach(t=>{clearTimeout(t)}),c.clear(),s.clear(),console.log("Deposit verification service stopped")}async processPendingVerifications(){try{let t=await a.J6.getPendingVerificationDeposits();for(let e of(console.log(`Found ${t.length} deposits pending verification`),t))s.has(e.transactionId)||this.scheduleVerification(e.transactionId,e.tronAddress)}catch(t){console.error("Error processing pending verifications:",t),await a.AJ.create({action:"DEPOSIT_VERIFICATION_ERROR",details:`Error processing pending verifications: ${t instanceof Error?t.message:"Unknown error"}`})}}async processWaitingForConfirmations(){try{let t=await a.J6.getWaitingForConfirmationsDeposits();for(let e of(console.log(`Found ${t.length} deposits waiting for confirmations`),t))c.has(e.transactionId)||this.scheduleConfirmationCheck(e.transactionId,e.tronAddress)}catch(t){console.error("Error processing waiting for confirmations:",t),await a.AJ.create({action:"CONFIRMATION_CHECK_ERROR",details:`Error processing waiting for confirmations: ${t instanceof Error?t.message:"Unknown error"}`})}}scheduleVerification(t,e){s.has(t)||(s.set(t,!0),console.log(`Scheduling verification for transaction: ${t}`),this.verifyTransaction(t,e,!1),setTimeout(()=>{this.verifyTransaction(t,e,!0)},6e4))}async verifyTransaction(t,e,i){try{console.log(`${i?"Retrying":"Attempting"} verification for transaction: ${t}`);let r=await a.rs.get("usdtDepositAddress");r||(r=await a.rs.get("USDT_DEPOSIT_ADDRESS")),r&&(r=r.replace(/['"]/g,"").trim());let c=r||e;console.log(`Using deposit address for verification: ${c} (from admin settings: ${!!r})`);let l=parseInt(await a.rs.get("minConfirmations")||"10"),d=parseFloat(await a.rs.get("minDepositAmount")||"10"),u=parseFloat(await a.rs.get("maxDepositAmount")||"10000"),f=(0,n.gp)(t,c,1),m=new Promise((t,e)=>setTimeout(()=>e(Error("Verification timeout")),3e4)),p=await Promise.race([f,m]);if(!p.isValid&&0===p.confirmations){i&&(await a.J6.updateStatus(t,"FAILED",{failureReason:"Transaction not found or invalid after verification attempts",processedAt:new Date}),await a.AJ.create({action:"DEPOSIT_VERIFICATION_FAILED",details:`Transaction ${t} failed verification after retry`}),s.delete(t));return}if(!(p.toAddress.toLowerCase().includes(c.toLowerCase().slice(1,10))||c.toLowerCase().includes(p.toAddress.toLowerCase().slice(1,10)))){await a.J6.updateStatus(t,"FAILED",{failureReason:"Invalid recipient address",processedAt:new Date}),s.delete(t);return}if(p.amount<d){await a.J6.updateStatus(t,"FAILED",{failureReason:`Deposit amount ${p.amount} USDT is below minimum ${d} USDT`,processedAt:new Date}),s.delete(t);return}if(p.amount>u){await a.J6.updateStatus(t,"FAILED",{failureReason:`Deposit amount ${p.amount} USDT exceeds maximum ${u} USDT`,processedAt:new Date}),s.delete(t);return}await o.prisma.depositTransaction.update({where:{transactionId:t},data:{amount:p.amount,usdtAmount:p.amount,senderAddress:p.fromAddress,blockNumber:p.blockNumber.toString(),blockTimestamp:new Date(p.blockTimestamp),confirmations:p.confirmations,tronAddress:c}}),await a.J6.updateStatus(t,"PENDING",{confirmations:p.confirmations}),console.log(`Transaction ${t} verified with ${p.confirmations} confirmations (required: ${l})`),p.confirmations>=l?await this.completeDeposit(t,p.amount):(await a.J6.updateStatus(t,"WAITING_FOR_CONFIRMATIONS"),this.scheduleConfirmationCheck(t,c)),s.delete(t)}catch(n){console.error(`Error verifying transaction ${t}:`,n);let e=n instanceof Error?n.message:"Unknown error",o=e.includes("timeout")||e.includes("network")||e.includes("ECONNRESET");i||!o?(await a.J6.updateStatus(t,"FAILED",{failureReason:`Verification error: ${e}`,processedAt:new Date}),s.delete(t),await a.AJ.create({action:"DEPOSIT_VERIFICATION_FAILED",details:`Transaction ${t} failed verification: ${e}`})):await a.AJ.create({action:"DEPOSIT_VERIFICATION_NETWORK_ERROR",details:`Network error verifying transaction ${t}: ${e}. Will retry.`})}}scheduleConfirmationCheck(t,e){if(c.has(t))return;console.log(`Starting confirmation checking for transaction: ${t}`);let i=setInterval(async()=>{await this.checkConfirmations(t,e)},6e4);c.set(t,i),this.checkConfirmations(t,e)}async checkConfirmations(t,e){try{console.log(`Checking confirmations for transaction: ${t}`);let i=await a.rs.get("usdtDepositAddress");i||(i=await a.rs.get("USDT_DEPOSIT_ADDRESS")),i&&(i=i.replace(/['"]/g,"").trim());let o=i||e,r=parseInt(await a.rs.get("minConfirmations")||"10"),s=await (0,n.gp)(t,o,1);if(!s.isValid)return void console.log(`Transaction ${t} is no longer valid during confirmation check`);if(await a.J6.updateConfirmations(t,s.confirmations),console.log(`Transaction ${t} has ${s.confirmations} confirmations (required: ${r})`),s.confirmations>=r){await this.completeDeposit(t,s.amount);let e=c.get(t);e&&(clearInterval(e),c.delete(t))}}catch(e){console.error(`Error checking confirmations for transaction ${t}:`,e),await a.AJ.create({action:"CONFIRMATION_CHECK_ERROR",details:`Error checking confirmations for ${t}: ${e instanceof Error?e.message:"Unknown error"}`})}}async completeDeposit(t,e){try{console.log(`Completing deposit for transaction: ${t} with amount: ${e}`);let i=await a.J6.findByTransactionId(t);if(!i)throw Error("Deposit record not found");if("CONFIRMED"===i.status||"COMPLETED"===i.status)return void console.log(`Deposit ${t} already completed, skipping...`);await o.prisma.$transaction(async a=>{await a.depositTransaction.update({where:{transactionId:t},data:{status:"CONFIRMED",verifiedAt:new Date,processedAt:new Date}});let o=await a.walletBalance.findUnique({where:{userId:i.userId}});o?await a.walletBalance.update({where:{userId:i.userId},data:{availableBalance:o.availableBalance+e,totalDeposits:o.totalDeposits+e,lastUpdated:new Date}}):await a.walletBalance.create({data:{userId:i.userId,availableBalance:e,pendingBalance:0,totalDeposits:e,totalWithdrawals:0,totalEarnings:0}});let n=await a.transaction.findFirst({where:{userId:i.userId,type:"DEPOSIT",description:`USDT TRC20 Deposit - TX: ${t}`,status:"PENDING"}});n?await a.transaction.update({where:{id:n.id},data:{status:"COMPLETED",amount:e}}):await a.transaction.create({data:{userId:i.userId,type:"DEPOSIT",amount:e,description:`USDT TRC20 Deposit - TX: ${t}`,status:"COMPLETED"}})}),await a.AJ.create({action:"DEPOSIT_COMPLETED",userId:i.userId,details:`Deposit completed: ${e} USDT from transaction ${t}`});try{let o=await a.Gy.findById(i.userId);o&&await r.emailNotificationService.sendDepositSuccessNotification({userId:o.id,email:o.email,firstName:o.firstName,lastName:o.lastName,amount:e,transactionId:t,currency:"USDT"})}catch(t){console.error("Error sending deposit success email:",t)}console.log(`Deposit completed successfully for transaction: ${t}`)}catch(e){console.error(`Error completing deposit for transaction ${t}:`,e),await a.J6.updateStatus(t,"FAILED",{failureReason:`Completion error: ${e instanceof Error?e.message:"Unknown error"}`,processedAt:new Date}),await a.AJ.create({action:"DEPOSIT_COMPLETION_ERROR",details:`Error completing deposit ${t}: ${e instanceof Error?e.message:"Unknown error"}`})}}async addTransactionForVerification(t,e){this.isRunning||console.log("Deposit verification service is not running, starting verification manually"),this.scheduleVerification(t,e)}getStatus(){return{isRunning:this.isRunning,activeVerifications:s.size,confirmationChecks:c.size}}constructor(){this.isRunning=!1}}let d=l.getInstance()}};