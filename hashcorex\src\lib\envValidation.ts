/**
 * Environment Variable Validation
 * Validates all required environment variables on application startup
 */

import { z } from 'zod';

// Environment validation schema
const envSchema = z.object({
  // Database
  DATABASE_URL: z.string().url('DATABASE_URL must be a valid PostgreSQL URL'),
  DIRECT_URL: z.string().url('DIRECT_URL must be a valid PostgreSQL URL'),
  
  // JWT Configuration
  JWT_SECRET: z.string()
    .min(32, 'JWT_SECRET must be at least 32 characters long')
    .refine((secret) => {
      // Check for strong secret
      const hasUpperCase = /[A-Z]/.test(secret);
      const hasLowerCase = /[a-z]/.test(secret);
      const hasNumbers = /\d/.test(secret);
      const hasSpecialChars = /[!@#$%^&*(),.?":{}|<>]/.test(secret);
      return hasUpperCase && hasLowerCase && hasNumbers && hasSpecialChars;
    }, 'JWT_SECRET should contain uppercase, lowercase, numbers, and special characters'),
  
  JWT_EXPIRES_IN: z.string().default('30d'),
  
  // Node Environment
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  
  // Application Configuration
  NEXT_PUBLIC_APP_URL: z.string().url().optional(),
  PORT: z.string().regex(/^\d+$/).transform(Number).default('3000'),
  
  // Email Configuration (optional but validated if provided)
  SMTP_HOST: z.string().optional(),
  SMTP_PORT: z.string().regex(/^\d+$/).transform(Number).optional(),
  SMTP_USER: z.string().email().optional(),
  SMTP_PASSWORD: z.string().optional(),
  SMTP_SECURE: z.string().transform(val => val === 'true').optional(),
  
  // Tron Network Configuration
  TRON_NETWORK: z.enum(['mainnet', 'testnet']).default('testnet'),
  TRON_API_KEY: z.string().optional(),
  USDT_CONTRACT_ADDRESS: z.string().optional(),
  
  // File Upload Configuration
  MAX_FILE_SIZE: z.string().regex(/^\d+$/).transform(Number).default('10485760'), // 10MB
  UPLOAD_DIR: z.string().default('./public/uploads'),
  
  // Security Configuration
  BCRYPT_ROUNDS: z.string().regex(/^\d+$/).transform(Number).default('12'),
  SESSION_TIMEOUT: z.string().regex(/^\d+$/).transform(Number).default('1800'), // 30 minutes
  
  // Rate Limiting Configuration
  RATE_LIMIT_WINDOW_MS: z.string().regex(/^\d+$/).transform(Number).default('900000'), // 15 minutes
  RATE_LIMIT_MAX_REQUESTS: z.string().regex(/^\d+$/).transform(Number).default('100'),
  
  // External API Configuration
  COINGECKO_API_URL: z.string().url().default('https://api.coingecko.com/api/v3'),
  
  // Monitoring and Logging
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
  ENABLE_REQUEST_LOGGING: z.string().transform(val => val === 'true').default('false'),
  
  // Feature Flags
  ENABLE_REGISTRATION: z.string().transform(val => val === 'true').default('true'),
  ENABLE_KYC: z.string().transform(val => val === 'true').default('true'),
  ENABLE_WITHDRAWALS: z.string().transform(val => val === 'true').default('true'),
});

// Conditional validation for email configuration
const envSchemaWithConditionals = envSchema.refine((data) => {
  // If any SMTP config is provided, all should be provided
  const smtpFields = [data.SMTP_HOST, data.SMTP_PORT, data.SMTP_USER, data.SMTP_PASSWORD];
  const hasAnySmtp = smtpFields.some(field => field !== undefined);
  const hasAllSmtp = smtpFields.every(field => field !== undefined);
  
  if (hasAnySmtp && !hasAllSmtp) {
    return false;
  }
  
  return true;
}, {
  message: 'If SMTP configuration is provided, all SMTP fields (HOST, PORT, USER, PASSWORD) must be provided',
});

// Type for validated environment variables
export type ValidatedEnv = z.infer<typeof envSchema>;

// Validation result
interface EnvValidationResult {
  success: boolean;
  data?: ValidatedEnv;
  errors?: string[];
}

// Validate environment variables
export function validateEnvironment(): EnvValidationResult {
  try {
    const result = envSchemaWithConditionals.safeParse(process.env);
    
    if (!result.success) {
      const errors = result.error.errors.map(err => 
        `${err.path.join('.')}: ${err.message}`
      );
      
      return {
        success: false,
        errors,
      };
    }
    
    return {
      success: true,
      data: result.data,
    };
  } catch (error) {
    return {
      success: false,
      errors: ['Failed to validate environment variables'],
    };
  }
}

// Get validated environment variables
let validatedEnv: ValidatedEnv | null = null;

export function getValidatedEnv(): ValidatedEnv {
  if (!validatedEnv) {
    const validation = validateEnvironment();
    
    if (!validation.success) {
      console.error('❌ Environment validation failed:');
      validation.errors?.forEach(error => console.error(`  - ${error}`));
      process.exit(1);
    }
    
    validatedEnv = validation.data!;
    console.log('✅ Environment variables validated successfully');
  }
  
  return validatedEnv;
}

// Environment-specific configurations
export const config = {
  isDevelopment: () => getValidatedEnv().NODE_ENV === 'development',
  isProduction: () => getValidatedEnv().NODE_ENV === 'production',
  isTest: () => getValidatedEnv().NODE_ENV === 'test',
  
  database: {
    url: () => getValidatedEnv().DATABASE_URL,
    directUrl: () => getValidatedEnv().DIRECT_URL,
  },
  
  jwt: {
    secret: () => getValidatedEnv().JWT_SECRET,
    expiresIn: () => getValidatedEnv().JWT_EXPIRES_IN,
  },
  
  server: {
    port: () => getValidatedEnv().PORT,
    appUrl: () => getValidatedEnv().NEXT_PUBLIC_APP_URL,
  },
  
  email: {
    isConfigured: () => {
      const env = getValidatedEnv();
      return !!(env.SMTP_HOST && env.SMTP_PORT && env.SMTP_USER && env.SMTP_PASSWORD);
    },
    host: () => getValidatedEnv().SMTP_HOST,
    port: () => getValidatedEnv().SMTP_PORT,
    user: () => getValidatedEnv().SMTP_USER,
    password: () => getValidatedEnv().SMTP_PASSWORD,
    secure: () => getValidatedEnv().SMTP_SECURE,
  },
  
  tron: {
    network: () => getValidatedEnv().TRON_NETWORK,
    apiKey: () => getValidatedEnv().TRON_API_KEY,
    usdtContract: () => getValidatedEnv().USDT_CONTRACT_ADDRESS,
  },
  
  security: {
    bcryptRounds: () => getValidatedEnv().BCRYPT_ROUNDS,
    sessionTimeout: () => getValidatedEnv().SESSION_TIMEOUT,
    maxFileSize: () => getValidatedEnv().MAX_FILE_SIZE,
    uploadDir: () => getValidatedEnv().UPLOAD_DIR,
  },
  
  rateLimit: {
    windowMs: () => getValidatedEnv().RATE_LIMIT_WINDOW_MS,
    maxRequests: () => getValidatedEnv().RATE_LIMIT_MAX_REQUESTS,
  },
  
  features: {
    registrationEnabled: () => getValidatedEnv().ENABLE_REGISTRATION,
    kycEnabled: () => getValidatedEnv().ENABLE_KYC,
    withdrawalsEnabled: () => getValidatedEnv().ENABLE_WITHDRAWALS,
  },
  
  logging: {
    level: () => getValidatedEnv().LOG_LEVEL,
    requestLogging: () => getValidatedEnv().ENABLE_REQUEST_LOGGING,
  },
  
  external: {
    coingeckoApiUrl: () => getValidatedEnv().COINGECKO_API_URL,
  },
};

// Validate environment on module load (server-side only)
if (typeof window === 'undefined' && process.env.NODE_ENV !== 'test') {
  // Skip validation during build process
  const isBuilding = process.env.NEXT_PHASE === 'phase-production-build';

  if (!isBuilding) {
    // Only validate in server environment
    const validation = validateEnvironment();

    if (!validation.success) {
      console.error('❌ Environment validation failed:');
      validation.errors?.forEach(error => console.error(`  - ${error}`));

      // In development, show helpful error message
      if (process.env.NODE_ENV === 'development') {
        console.error('\n💡 To fix these errors:');
        console.error('1. Check your .env.local file');
        console.error('2. Ensure all required environment variables are set');
        console.error('3. Verify JWT_SECRET is at least 32 characters with mixed case, numbers, and special characters');
        console.error('4. Ensure database URLs are valid PostgreSQL connection strings');
      }

      process.exit(1);
    }

    console.log('✅ Environment variables validated successfully');
  }
}

// Helper function to check if all critical environment variables are set
export function checkCriticalEnvVars(): { valid: boolean; missing: string[] } {
  const critical = ['DATABASE_URL', 'DIRECT_URL', 'JWT_SECRET'];
  const missing: string[] = [];
  
  for (const key of critical) {
    if (!process.env[key]) {
      missing.push(key);
    }
  }
  
  return {
    valid: missing.length === 0,
    missing,
  };
}

// Helper function to generate a strong JWT secret
export function generateStrongJWTSecret(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()';
  let result = '';
  
  // Ensure at least one of each required character type
  result += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'[Math.floor(Math.random() * 26)]; // Uppercase
  result += 'abcdefghijklmnopqrstuvwxyz'[Math.floor(Math.random() * 26)]; // Lowercase
  result += '0123456789'[Math.floor(Math.random() * 10)]; // Number
  result += '!@#$%^&*()'[Math.floor(Math.random() * 10)]; // Special char
  
  // Fill the rest randomly
  for (let i = 4; i < 64; i++) {
    result += chars[Math.floor(Math.random() * chars.length)];
  }
  
  // Shuffle the string
  return result.split('').sort(() => Math.random() - 0.5).join('');
}

export default config;
