/**
 * 2FA Enable API Endpoint
 * Enable email-based two-factor authentication for user
 */

import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { EmailTwoFactorAuth } from '@/lib/emailTwoFactorAuth';
import { 
  withSecureErrorHandling, 
  createAuthError,
  createSecureResponse 
} from '@/lib/secureErrorHandler';

// POST - Enable 2FA
const enable2FAHandler = async (request: NextRequest) => {
  const { authenticated, user } = await authenticateRequest(request);

  if (!authenticated || !user) {
    const error = createAuthError('Authentication required');
    return createSecureResponse(error);
  }

  // Enable 2FA
  const result = await EmailTwoFactorAuth.enableTwoFactor(
    user.id,
    request.headers.get('x-forwarded-for') || 'unknown',
    request.headers.get('user-agent') || 'unknown'
  );

  if (!result.success) {
    return NextResponse.json(
      { success: false, error: result.error },
      { status: 400 }
    );
  }

  const response = NextResponse.json({
    success: true,
    message: 'Two-factor authentication enabled successfully',
    data: {
      enabled: true,
      method: 'email',
      enabledAt: new Date().toISOString(),
    },
  });

  // Add security headers
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-XSS-Protection', '1; mode=block');

  return response;
};

// Export the handler with secure error handling
export const POST = withSecureErrorHandling(enable2FAHandler, {
  endpoint: '/api/user/2fa/enable',
  requireAuth: true,
});
