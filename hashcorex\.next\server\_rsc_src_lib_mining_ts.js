"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_mining_ts";
exports.ids = ["_rsc_src_lib_mining_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/mining.ts":
/*!***************************!*\
  !*** ./src/lib/mining.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculate7DayAverageROI: () => (/* binding */ calculate7DayAverageROI),\n/* harmony export */   calculateDailyROI: () => (/* binding */ calculateDailyROI),\n/* harmony export */   calculateDynamicROI: () => (/* binding */ calculateDynamicROI),\n/* harmony export */   calculateEstimatedEarnings: () => (/* binding */ calculateEstimatedEarnings),\n/* harmony export */   expireOldMiningUnits: () => (/* binding */ expireOldMiningUnits),\n/* harmony export */   getMiningStats: () => (/* binding */ getMiningStats),\n/* harmony export */   getMonthlyReturnLimits: () => (/* binding */ getMonthlyReturnLimits),\n/* harmony export */   processWeeklyEarnings: () => (/* binding */ processWeeklyEarnings),\n/* harmony export */   updateExistingMiningUnitsROI: () => (/* binding */ updateExistingMiningUnitsROI),\n/* harmony export */   validateMonthlyReturn: () => (/* binding */ validateMonthlyReturn)\n/* harmony export */ });\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./database */ \"(rsc)/./src/lib/database.ts\");\n/* harmony import */ var _miningUnitEarnings__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./miningUnitEarnings */ \"(rsc)/./src/lib/miningUnitEarnings.ts\");\n\n\n\n// Note: User active status is now handled dynamically in the referral system\n// The isActive field in the database is only used for account access control\n// Mining activity status is computed on-demand for binary tree display\n// Calculate dynamic ROI based on TH/s amount and admin-configured ranges\nasync function calculateDynamicROI(thsAmount) {\n    try {\n        // Get earnings ranges from admin settings\n        const earningsRangesStr = await _database__WEBPACK_IMPORTED_MODULE_1__.adminSettingsDb.get('earningsRanges');\n        let earningsRanges;\n        if (earningsRangesStr) {\n            try {\n                earningsRanges = JSON.parse(earningsRangesStr);\n            } catch (parseError) {\n                console.error('Error parsing earnings ranges:', parseError);\n                earningsRanges = null;\n            }\n        }\n        // Fallback to default ranges if not configured\n        if (!earningsRanges || !Array.isArray(earningsRanges)) {\n            earningsRanges = [\n                {\n                    minTHS: 0,\n                    maxTHS: 10,\n                    dailyReturnMin: 0.3,\n                    dailyReturnMax: 0.5,\n                    monthlyReturnMin: 10.0,\n                    monthlyReturnMax: 15.0\n                },\n                {\n                    minTHS: 10,\n                    maxTHS: 50,\n                    dailyReturnMin: 0.4,\n                    dailyReturnMax: 0.6,\n                    monthlyReturnMin: 10.0,\n                    monthlyReturnMax: 15.0\n                },\n                {\n                    minTHS: 50,\n                    maxTHS: 999999,\n                    dailyReturnMin: 0.5,\n                    dailyReturnMax: 0.7,\n                    monthlyReturnMin: 10.0,\n                    monthlyReturnMax: 15.0\n                }\n            ];\n        }\n        // Find the appropriate range for the TH/s amount\n        const applicableRange = earningsRanges.find((range)=>thsAmount >= range.minTHS && thsAmount <= range.maxTHS);\n        let minROI;\n        let maxROI;\n        if (applicableRange) {\n            minROI = applicableRange.dailyReturnMin;\n            maxROI = applicableRange.dailyReturnMax;\n        } else {\n            // Fallback to highest range if no match found\n            const highestRange = earningsRanges[earningsRanges.length - 1];\n            minROI = highestRange.dailyReturnMin;\n            maxROI = highestRange.dailyReturnMax;\n        }\n        // Add randomization within the range\n        const randomROI = minROI + Math.random() * (maxROI - minROI);\n        // Round to 2 decimal places\n        return Math.round(randomROI * 100) / 100;\n    } catch (error) {\n        console.error('Error calculating dynamic ROI:', error);\n        // Fallback to default values based on TH/s amount\n        if (thsAmount >= 50) return 0.6;\n        if (thsAmount >= 10) return 0.5;\n        return 0.4;\n    }\n}\n// Validate monthly return doesn't exceed admin-configured limits for specific TH/s amount\nasync function validateMonthlyReturn(dailyROI, thsAmount) {\n    try {\n        // Get earnings ranges from admin settings\n        const earningsRangesStr = await _database__WEBPACK_IMPORTED_MODULE_1__.adminSettingsDb.get('earningsRanges');\n        let earningsRanges;\n        if (earningsRangesStr) {\n            try {\n                earningsRanges = JSON.parse(earningsRangesStr);\n            } catch (parseError) {\n                console.error('Error parsing earnings ranges:', parseError);\n                earningsRanges = null;\n            }\n        }\n        // Fallback to default ranges if not configured\n        if (!earningsRanges || !Array.isArray(earningsRanges)) {\n            earningsRanges = [\n                {\n                    minTHS: 0,\n                    maxTHS: 10,\n                    dailyReturnMin: 0.3,\n                    dailyReturnMax: 0.5,\n                    monthlyReturnMin: 10.0,\n                    monthlyReturnMax: 15.0\n                },\n                {\n                    minTHS: 10,\n                    maxTHS: 50,\n                    dailyReturnMin: 0.4,\n                    dailyReturnMax: 0.6,\n                    monthlyReturnMin: 10.0,\n                    monthlyReturnMax: 15.0\n                },\n                {\n                    minTHS: 50,\n                    maxTHS: 999999,\n                    dailyReturnMin: 0.5,\n                    dailyReturnMax: 0.7,\n                    monthlyReturnMin: 10.0,\n                    monthlyReturnMax: 15.0\n                }\n            ];\n        }\n        // Find the appropriate range for the TH/s amount\n        const applicableRange = earningsRanges.find((range)=>thsAmount >= range.minTHS && thsAmount <= range.maxTHS);\n        let monthlyMin = 10.0;\n        let monthlyMax = 15.0;\n        if (applicableRange) {\n            monthlyMin = applicableRange.monthlyReturnMin || 10.0;\n            monthlyMax = applicableRange.monthlyReturnMax || 15.0;\n        } else {\n            // Fallback to highest range if no match found\n            const highestRange = earningsRanges[earningsRanges.length - 1];\n            monthlyMin = highestRange.monthlyReturnMin || 10.0;\n            monthlyMax = highestRange.monthlyReturnMax || 15.0;\n        }\n        const monthlyReturn = dailyROI * 30; // Approximate monthly return\n        return monthlyReturn >= monthlyMin && monthlyReturn <= monthlyMax;\n    } catch (error) {\n        console.error('Error validating monthly return:', error);\n        // Fallback to default 10-15% range\n        const monthlyReturn = dailyROI * 30;\n        return monthlyReturn >= 10 && monthlyReturn <= 15;\n    }\n}\n// Get monthly return limits for a specific TH/s amount\nasync function getMonthlyReturnLimits(thsAmount) {\n    try {\n        // Get earnings ranges from admin settings\n        const earningsRangesStr = await _database__WEBPACK_IMPORTED_MODULE_1__.adminSettingsDb.get('earningsRanges');\n        let earningsRanges;\n        if (earningsRangesStr) {\n            try {\n                earningsRanges = JSON.parse(earningsRangesStr);\n            } catch (parseError) {\n                console.error('Error parsing earnings ranges:', parseError);\n                earningsRanges = null;\n            }\n        }\n        // Fallback to default ranges if not configured\n        if (!earningsRanges || !Array.isArray(earningsRanges)) {\n            earningsRanges = [\n                {\n                    minTHS: 0,\n                    maxTHS: 10,\n                    dailyReturnMin: 0.3,\n                    dailyReturnMax: 0.5,\n                    monthlyReturnMin: 10.0,\n                    monthlyReturnMax: 15.0\n                },\n                {\n                    minTHS: 10,\n                    maxTHS: 50,\n                    dailyReturnMin: 0.4,\n                    dailyReturnMax: 0.6,\n                    monthlyReturnMin: 10.0,\n                    monthlyReturnMax: 15.0\n                },\n                {\n                    minTHS: 50,\n                    maxTHS: 999999,\n                    dailyReturnMin: 0.5,\n                    dailyReturnMax: 0.7,\n                    monthlyReturnMin: 10.0,\n                    monthlyReturnMax: 15.0\n                }\n            ];\n        }\n        // Find the appropriate range for the TH/s amount\n        const applicableRange = earningsRanges.find((range)=>thsAmount >= range.minTHS && thsAmount <= range.maxTHS);\n        if (applicableRange) {\n            return {\n                min: applicableRange.monthlyReturnMin || 10.0,\n                max: applicableRange.monthlyReturnMax || 15.0\n            };\n        } else {\n            // Fallback to highest range if no match found\n            const highestRange = earningsRanges[earningsRanges.length - 1];\n            return {\n                min: highestRange.monthlyReturnMin || 10.0,\n                max: highestRange.monthlyReturnMax || 15.0\n            };\n        }\n    } catch (error) {\n        console.error('Error getting monthly return limits:', error);\n        // Fallback to default 10-15% range\n        return {\n            min: 10.0,\n            max: 15.0\n        };\n    }\n}\n// Calculate 7-day average ROI for a specific TH/s amount based on admin configuration\nasync function calculate7DayAverageROI(thsAmount) {\n    try {\n        // Get earnings ranges from admin settings\n        const earningsRangesStr = await _database__WEBPACK_IMPORTED_MODULE_1__.adminSettingsDb.get('earningsRanges');\n        let earningsRanges;\n        if (earningsRangesStr) {\n            try {\n                earningsRanges = JSON.parse(earningsRangesStr);\n            } catch (parseError) {\n                console.error('Error parsing earnings ranges:', parseError);\n                earningsRanges = null;\n            }\n        }\n        // Fallback to default ranges if not configured\n        if (!earningsRanges || !Array.isArray(earningsRanges)) {\n            earningsRanges = [\n                {\n                    minTHS: 0,\n                    maxTHS: 10,\n                    dailyReturnMin: 0.3,\n                    dailyReturnMax: 0.5,\n                    monthlyReturnMin: 10.0,\n                    monthlyReturnMax: 15.0\n                },\n                {\n                    minTHS: 10,\n                    maxTHS: 50,\n                    dailyReturnMin: 0.4,\n                    dailyReturnMax: 0.6,\n                    monthlyReturnMin: 10.0,\n                    monthlyReturnMax: 15.0\n                },\n                {\n                    minTHS: 50,\n                    maxTHS: 999999,\n                    dailyReturnMin: 0.5,\n                    dailyReturnMax: 0.7,\n                    monthlyReturnMin: 10.0,\n                    monthlyReturnMax: 15.0\n                }\n            ];\n        }\n        // Find the appropriate range for the TH/s amount\n        const applicableRange = earningsRanges.find((range)=>thsAmount >= range.minTHS && thsAmount <= range.maxTHS);\n        let range;\n        if (applicableRange) {\n            range = applicableRange;\n        } else {\n            // Fallback to highest range if no match found\n            range = earningsRanges[earningsRanges.length - 1];\n        }\n        // Calculate 7-day average by simulating daily ROI variations\n        let totalROI = 0;\n        const days = 7;\n        for(let day = 0; day < days; day++){\n            // Generate a random ROI within the range for each day\n            const dailyROI = range.dailyReturnMin + Math.random() * (range.dailyReturnMax - range.dailyReturnMin);\n            totalROI += dailyROI;\n        }\n        // Return the 7-day average\n        return totalROI / days;\n    } catch (error) {\n        console.error('Error calculating 7-day average ROI:', error);\n        return 0.4; // Default fallback\n    }\n}\n// Update existing mining units when earnings configuration changes\nasync function updateExistingMiningUnitsROI() {\n    try {\n        console.log('Updating existing mining units with new ROI configuration...');\n        // Get all active mining units\n        const activeMiningUnits = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.findMany({\n            where: {\n                status: 'ACTIVE',\n                expiryDate: {\n                    gt: new Date()\n                }\n            }\n        });\n        console.log(`Found ${activeMiningUnits.length} active mining units to update`);\n        const updateResults = [];\n        for (const unit of activeMiningUnits){\n            try {\n                // Calculate new ROI based on current TH/s amount\n                const newROI = await calculateDynamicROI(unit.thsAmount);\n                // Update the mining unit with new ROI\n                await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.update({\n                    where: {\n                        id: unit.id\n                    },\n                    data: {\n                        dailyROI: newROI\n                    }\n                });\n                updateResults.push({\n                    unitId: unit.id,\n                    userId: unit.userId,\n                    thsAmount: unit.thsAmount,\n                    oldROI: unit.dailyROI,\n                    newROI\n                });\n                console.log(`Updated unit ${unit.id}: ${unit.dailyROI}% -> ${newROI}%`);\n            } catch (unitError) {\n                console.error(`Error updating unit ${unit.id}:`, unitError);\n            }\n        }\n        // Log the update process\n        await _database__WEBPACK_IMPORTED_MODULE_1__.systemLogDb.create({\n            action: 'MINING_UNITS_ROI_UPDATED',\n            details: {\n                unitsUpdated: updateResults.length,\n                totalUnits: activeMiningUnits.length,\n                updateResults,\n                timestamp: new Date().toISOString()\n            }\n        });\n        console.log(`Successfully updated ${updateResults.length} mining units with new ROI configuration`);\n        return {\n            success: true,\n            unitsUpdated: updateResults.length,\n            totalUnits: activeMiningUnits.length,\n            updateResults\n        };\n    } catch (error) {\n        console.error('Error updating existing mining units ROI:', error);\n        throw error;\n    }\n}\n// Calculate daily ROI for all active mining units using FIFO allocation\nasync function calculateDailyROI() {\n    try {\n        console.log('Starting daily ROI calculation with FIFO allocation...');\n        // Get all users with active mining units\n        const usersWithMiningUnits = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findMany({\n            where: {\n                miningUnits: {\n                    some: {\n                        status: 'ACTIVE',\n                        expiryDate: {\n                            gt: new Date()\n                        }\n                    }\n                }\n            },\n            include: {\n                miningUnits: {\n                    where: {\n                        status: 'ACTIVE',\n                        expiryDate: {\n                            gt: new Date()\n                        }\n                    }\n                }\n            }\n        });\n        console.log(`Found ${usersWithMiningUnits.length} users with active mining units`);\n        const results = [];\n        for (const user of usersWithMiningUnits){\n            try {\n                // Calculate total daily earnings for this user\n                let totalDailyEarnings = 0;\n                const unitEarnings = [];\n                for (const unit of user.miningUnits){\n                    const dailyEarnings = unit.investmentAmount * unit.dailyROI / 100;\n                    totalDailyEarnings += dailyEarnings;\n                    unitEarnings.push({\n                        unitId: unit.id,\n                        thsAmount: unit.thsAmount,\n                        dailyEarnings\n                    });\n                }\n                if (totalDailyEarnings > 0) {\n                    // Create transaction first\n                    const transaction = await _database__WEBPACK_IMPORTED_MODULE_1__.transactionDb.create({\n                        userId: user.id,\n                        type: 'MINING_EARNINGS',\n                        amount: totalDailyEarnings,\n                        description: `Daily mining earnings - Total: ${unitEarnings.map((u)=>`${u.thsAmount} TH/s`).join(', ')}`,\n                        status: 'PENDING'\n                    });\n                    // Allocate earnings to mining units using FIFO logic\n                    const allocations = await (0,_miningUnitEarnings__WEBPACK_IMPORTED_MODULE_2__.allocateEarningsToUnits)(user.id, totalDailyEarnings, 'MINING_EARNINGS', transaction.id, 'Daily mining ROI earnings');\n                    results.push({\n                        userId: user.id,\n                        totalEarnings: totalDailyEarnings,\n                        allocations,\n                        unitsProcessed: user.miningUnits.length\n                    });\n                    console.log(`Allocated ${totalDailyEarnings} mining earnings to ${allocations.length} units for user ${user.id}`);\n                }\n            } catch (userError) {\n                console.error(`Error processing mining earnings for user ${user.id}:`, userError);\n            }\n        }\n        // Log the daily ROI calculation\n        await _database__WEBPACK_IMPORTED_MODULE_1__.systemLogDb.create({\n            action: 'DAILY_ROI_CALCULATED',\n            details: {\n                usersProcessed: results.length,\n                totalEarnings: results.reduce((sum, r)=>sum + r.totalEarnings, 0),\n                totalAllocations: results.reduce((sum, r)=>sum + r.allocations.length, 0),\n                timestamp: new Date().toISOString()\n            }\n        });\n        console.log(`Daily ROI calculation completed. Processed ${results.length} users with FIFO allocation.`);\n        return results;\n    } catch (error) {\n        console.error('Daily ROI calculation error:', error);\n        throw error;\n    }\n}\n// Process weekly earnings distribution (Saturday 15:00 UTC)\nasync function processWeeklyEarnings() {\n    try {\n        console.log('Starting weekly earnings distribution...');\n        // Get all pending mining earnings\n        const pendingEarnings = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.findMany({\n            where: {\n                type: 'MINING_EARNINGS',\n                status: 'PENDING'\n            },\n            include: {\n                user: true\n            }\n        });\n        console.log(`Found ${pendingEarnings.length} pending earnings transactions`);\n        const userEarnings = new Map();\n        // Group earnings by user\n        for (const transaction of pendingEarnings){\n            const currentTotal = userEarnings.get(transaction.userId) || 0;\n            userEarnings.set(transaction.userId, currentTotal + transaction.amount);\n        }\n        const results = [];\n        // Process each user's earnings\n        for (const [userId, totalEarnings] of userEarnings){\n            try {\n                // Mark all pending transactions as completed\n                await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.updateMany({\n                    where: {\n                        userId,\n                        type: 'MINING_EARNINGS',\n                        status: 'PENDING'\n                    },\n                    data: {\n                        status: 'COMPLETED'\n                    }\n                });\n                results.push({\n                    userId,\n                    totalEarnings\n                });\n            } catch (userError) {\n                console.error(`Error processing earnings for user ${userId}:`, userError);\n            }\n        }\n        // Log the weekly distribution\n        await _database__WEBPACK_IMPORTED_MODULE_1__.systemLogDb.create({\n            action: 'WEEKLY_EARNINGS_DISTRIBUTED',\n            details: {\n                usersProcessed: results.length,\n                totalDistributed: results.reduce((sum, r)=>sum + r.totalEarnings, 0),\n                transactionsProcessed: pendingEarnings.length,\n                timestamp: new Date().toISOString()\n            }\n        });\n        console.log(`Weekly earnings distribution completed. Processed ${results.length} users.`);\n        return results;\n    } catch (error) {\n        console.error('Weekly earnings distribution error:', error);\n        throw error;\n    }\n}\n// Check and expire mining units that have reached 24 months\nasync function expireOldMiningUnits() {\n    try {\n        console.log('Checking for expired mining units...');\n        const expiredUnits = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.findMany({\n            where: {\n                status: 'ACTIVE',\n                expiryDate: {\n                    lte: new Date()\n                }\n            },\n            include: {\n                user: {\n                    select: {\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n        console.log(`Found ${expiredUnits.length} units to expire`);\n        for (const unit of expiredUnits){\n            await _database__WEBPACK_IMPORTED_MODULE_1__.miningUnitDb.expireUnit(unit.id);\n            // Note: User active status is now computed dynamically\n            await _database__WEBPACK_IMPORTED_MODULE_1__.systemLogDb.create({\n                action: 'MINING_UNIT_EXPIRED',\n                userId: unit.userId,\n                details: {\n                    miningUnitId: unit.id,\n                    reason: '24_months_reached',\n                    totalEarned: unit.totalEarned,\n                    investmentAmount: unit.investmentAmount\n                }\n            });\n            // Send email notification\n            try {\n                const { emailNotificationService } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/nodemailer\"), __webpack_require__.e(\"_rsc_src_lib_emailNotificationService_ts\")]).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/emailNotificationService */ \"(rsc)/./src/lib/emailNotificationService.ts\"));\n                const totalEarnings = unit.miningEarnings + unit.referralEarnings + unit.binaryEarnings;\n                await emailNotificationService.sendMiningUnitExpiryNotification({\n                    userId: unit.userId,\n                    email: unit.user.email,\n                    firstName: unit.user.firstName,\n                    lastName: unit.user.lastName,\n                    thsAmount: unit.thsAmount,\n                    investmentAmount: unit.investmentAmount,\n                    totalEarned: totalEarnings,\n                    purchaseDate: unit.createdAt.toISOString(),\n                    expiryDate: unit.expiryDate.toISOString(),\n                    expiryReason: 'TIME_LIMIT'\n                });\n            } catch (emailError) {\n                console.error('Failed to send mining unit expiry email:', emailError);\n            // Don't fail the expiry if email fails\n            }\n        }\n        return expiredUnits.length;\n    } catch (error) {\n        console.error('Mining unit expiry check error:', error);\n        throw error;\n    }\n}\n// Get mining statistics\nasync function getMiningStats() {\n    try {\n        const stats = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.$transaction([\n            // Total TH/s sold\n            _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.aggregate({\n                _sum: {\n                    thsAmount: true\n                }\n            }),\n            // Active TH/s\n            _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.aggregate({\n                where: {\n                    status: 'ACTIVE'\n                },\n                _sum: {\n                    thsAmount: true\n                }\n            }),\n            // Total investment\n            _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.aggregate({\n                _sum: {\n                    investmentAmount: true\n                }\n            }),\n            // Total earnings distributed\n            _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.aggregate({\n                where: {\n                    type: 'MINING_EARNINGS',\n                    status: 'COMPLETED'\n                },\n                _sum: {\n                    amount: true\n                }\n            }),\n            // Active mining units count\n            _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.count({\n                where: {\n                    status: 'ACTIVE'\n                }\n            }),\n            // Total mining units count\n            _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.count()\n        ]);\n        return {\n            totalTHSSold: stats[0]._sum.thsAmount || 0,\n            activeTHS: stats[1]._sum.thsAmount || 0,\n            totalInvestment: stats[2]._sum.investmentAmount || 0,\n            totalEarningsDistributed: stats[3]._sum.amount || 0,\n            activeMiningUnits: stats[4],\n            totalMiningUnits: stats[5]\n        };\n    } catch (error) {\n        console.error('Mining stats error:', error);\n        throw error;\n    }\n}\n// Calculate user's estimated earnings\nasync function calculateEstimatedEarnings(userId) {\n    try {\n        const activeMiningUnits = await _database__WEBPACK_IMPORTED_MODULE_1__.miningUnitDb.findActiveByUserId(userId);\n        if (activeMiningUnits.length === 0) {\n            return {\n                next7Days: 0,\n                next30Days: 0,\n                next365Days: 0,\n                next2Years: 0\n            };\n        }\n        let totalDaily = 0;\n        for (const unit of activeMiningUnits){\n            const dailyEarnings = unit.investmentAmount * unit.dailyROI / 100;\n            const maxEarnings = unit.investmentAmount * 5;\n            const remainingEarnings = maxEarnings - unit.totalEarned;\n            // Use the lower of daily earnings or remaining earnings\n            totalDaily += Math.min(dailyEarnings, remainingEarnings);\n        }\n        return {\n            next7Days: totalDaily * 7,\n            next30Days: totalDaily * 30,\n            next365Days: totalDaily * 365,\n            next2Years: totalDaily * 730\n        };\n    } catch (error) {\n        console.error('Estimated earnings calculation error:', error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL21pbmluZy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBQWtDO0FBQ3FEO0FBQ3NDO0FBRTdILDZFQUE2RTtBQUM3RSw2RUFBNkU7QUFDN0UsdUVBQXVFO0FBRXZFLHlFQUF5RTtBQUNsRSxlQUFlTSxvQkFBb0JDLFNBQWlCO0lBQ3pELElBQUk7UUFDRiwwQ0FBMEM7UUFDMUMsTUFBTUMsb0JBQW9CLE1BQU1KLHNEQUFlQSxDQUFDSyxHQUFHLENBQUM7UUFDcEQsSUFBSUM7UUFFSixJQUFJRixtQkFBbUI7WUFDckIsSUFBSTtnQkFDRkUsaUJBQWlCQyxLQUFLQyxLQUFLLENBQUNKO1lBQzlCLEVBQUUsT0FBT0ssWUFBWTtnQkFDbkJDLFFBQVFDLEtBQUssQ0FBQyxrQ0FBa0NGO2dCQUNoREgsaUJBQWlCO1lBQ25CO1FBQ0Y7UUFFQSwrQ0FBK0M7UUFDL0MsSUFBSSxDQUFDQSxrQkFBa0IsQ0FBQ00sTUFBTUMsT0FBTyxDQUFDUCxpQkFBaUI7WUFDckRBLGlCQUFpQjtnQkFDZjtvQkFBRVEsUUFBUTtvQkFBR0MsUUFBUTtvQkFBSUMsZ0JBQWdCO29CQUFLQyxnQkFBZ0I7b0JBQUtDLGtCQUFrQjtvQkFBTUMsa0JBQWtCO2dCQUFLO2dCQUNsSDtvQkFBRUwsUUFBUTtvQkFBSUMsUUFBUTtvQkFBSUMsZ0JBQWdCO29CQUFLQyxnQkFBZ0I7b0JBQUtDLGtCQUFrQjtvQkFBTUMsa0JBQWtCO2dCQUFLO2dCQUNuSDtvQkFBRUwsUUFBUTtvQkFBSUMsUUFBUTtvQkFBUUMsZ0JBQWdCO29CQUFLQyxnQkFBZ0I7b0JBQUtDLGtCQUFrQjtvQkFBTUMsa0JBQWtCO2dCQUFLO2FBQ3hIO1FBQ0g7UUFFQSxpREFBaUQ7UUFDakQsTUFBTUMsa0JBQWtCZCxlQUFlZSxJQUFJLENBQUMsQ0FBQ0MsUUFDM0NuQixhQUFhbUIsTUFBTVIsTUFBTSxJQUFJWCxhQUFhbUIsTUFBTVAsTUFBTTtRQUd4RCxJQUFJUTtRQUNKLElBQUlDO1FBRUosSUFBSUosaUJBQWlCO1lBQ25CRyxTQUFTSCxnQkFBZ0JKLGNBQWM7WUFDdkNRLFNBQVNKLGdCQUFnQkgsY0FBYztRQUN6QyxPQUFPO1lBQ0wsOENBQThDO1lBQzlDLE1BQU1RLGVBQWVuQixjQUFjLENBQUNBLGVBQWVvQixNQUFNLEdBQUcsRUFBRTtZQUM5REgsU0FBU0UsYUFBYVQsY0FBYztZQUNwQ1EsU0FBU0MsYUFBYVIsY0FBYztRQUN0QztRQUVBLHFDQUFxQztRQUNyQyxNQUFNVSxZQUFZSixTQUFVSyxLQUFLQyxNQUFNLEtBQU1MLENBQUFBLFNBQVNELE1BQUs7UUFFM0QsNEJBQTRCO1FBQzVCLE9BQU9LLEtBQUtFLEtBQUssQ0FBQ0gsWUFBWSxPQUFPO0lBQ3ZDLEVBQUUsT0FBT2hCLE9BQU87UUFDZEQsUUFBUUMsS0FBSyxDQUFDLGtDQUFrQ0E7UUFDaEQsa0RBQWtEO1FBQ2xELElBQUlSLGFBQWEsSUFBSSxPQUFPO1FBQzVCLElBQUlBLGFBQWEsSUFBSSxPQUFPO1FBQzVCLE9BQU87SUFDVDtBQUNGO0FBRUEsMEZBQTBGO0FBQ25GLGVBQWU0QixzQkFBc0JDLFFBQWdCLEVBQUU3QixTQUFpQjtJQUM3RSxJQUFJO1FBQ0YsMENBQTBDO1FBQzFDLE1BQU1DLG9CQUFvQixNQUFNSixzREFBZUEsQ0FBQ0ssR0FBRyxDQUFDO1FBQ3BELElBQUlDO1FBRUosSUFBSUYsbUJBQW1CO1lBQ3JCLElBQUk7Z0JBQ0ZFLGlCQUFpQkMsS0FBS0MsS0FBSyxDQUFDSjtZQUM5QixFQUFFLE9BQU9LLFlBQVk7Z0JBQ25CQyxRQUFRQyxLQUFLLENBQUMsa0NBQWtDRjtnQkFDaERILGlCQUFpQjtZQUNuQjtRQUNGO1FBRUEsK0NBQStDO1FBQy9DLElBQUksQ0FBQ0Esa0JBQWtCLENBQUNNLE1BQU1DLE9BQU8sQ0FBQ1AsaUJBQWlCO1lBQ3JEQSxpQkFBaUI7Z0JBQ2Y7b0JBQUVRLFFBQVE7b0JBQUdDLFFBQVE7b0JBQUlDLGdCQUFnQjtvQkFBS0MsZ0JBQWdCO29CQUFLQyxrQkFBa0I7b0JBQU1DLGtCQUFrQjtnQkFBSztnQkFDbEg7b0JBQUVMLFFBQVE7b0JBQUlDLFFBQVE7b0JBQUlDLGdCQUFnQjtvQkFBS0MsZ0JBQWdCO29CQUFLQyxrQkFBa0I7b0JBQU1DLGtCQUFrQjtnQkFBSztnQkFDbkg7b0JBQUVMLFFBQVE7b0JBQUlDLFFBQVE7b0JBQVFDLGdCQUFnQjtvQkFBS0MsZ0JBQWdCO29CQUFLQyxrQkFBa0I7b0JBQU1DLGtCQUFrQjtnQkFBSzthQUN4SDtRQUNIO1FBRUEsaURBQWlEO1FBQ2pELE1BQU1DLGtCQUFrQmQsZUFBZWUsSUFBSSxDQUFDLENBQUNDLFFBQzNDbkIsYUFBYW1CLE1BQU1SLE1BQU0sSUFBSVgsYUFBYW1CLE1BQU1QLE1BQU07UUFHeEQsSUFBSWtCLGFBQWE7UUFDakIsSUFBSUMsYUFBYTtRQUVqQixJQUFJZCxpQkFBaUI7WUFDbkJhLGFBQWFiLGdCQUFnQkYsZ0JBQWdCLElBQUk7WUFDakRnQixhQUFhZCxnQkFBZ0JELGdCQUFnQixJQUFJO1FBQ25ELE9BQU87WUFDTCw4Q0FBOEM7WUFDOUMsTUFBTU0sZUFBZW5CLGNBQWMsQ0FBQ0EsZUFBZW9CLE1BQU0sR0FBRyxFQUFFO1lBQzlETyxhQUFhUixhQUFhUCxnQkFBZ0IsSUFBSTtZQUM5Q2dCLGFBQWFULGFBQWFOLGdCQUFnQixJQUFJO1FBQ2hEO1FBRUEsTUFBTWdCLGdCQUFnQkgsV0FBVyxJQUFJLDZCQUE2QjtRQUNsRSxPQUFPRyxpQkFBaUJGLGNBQWNFLGlCQUFpQkQ7SUFDekQsRUFBRSxPQUFPdkIsT0FBTztRQUNkRCxRQUFRQyxLQUFLLENBQUMsb0NBQW9DQTtRQUNsRCxtQ0FBbUM7UUFDbkMsTUFBTXdCLGdCQUFnQkgsV0FBVztRQUNqQyxPQUFPRyxpQkFBaUIsTUFBTUEsaUJBQWlCO0lBQ2pEO0FBQ0Y7QUFFQSx1REFBdUQ7QUFDaEQsZUFBZUMsdUJBQXVCakMsU0FBaUI7SUFDNUQsSUFBSTtRQUNGLDBDQUEwQztRQUMxQyxNQUFNQyxvQkFBb0IsTUFBTUosc0RBQWVBLENBQUNLLEdBQUcsQ0FBQztRQUNwRCxJQUFJQztRQUVKLElBQUlGLG1CQUFtQjtZQUNyQixJQUFJO2dCQUNGRSxpQkFBaUJDLEtBQUtDLEtBQUssQ0FBQ0o7WUFDOUIsRUFBRSxPQUFPSyxZQUFZO2dCQUNuQkMsUUFBUUMsS0FBSyxDQUFDLGtDQUFrQ0Y7Z0JBQ2hESCxpQkFBaUI7WUFDbkI7UUFDRjtRQUVBLCtDQUErQztRQUMvQyxJQUFJLENBQUNBLGtCQUFrQixDQUFDTSxNQUFNQyxPQUFPLENBQUNQLGlCQUFpQjtZQUNyREEsaUJBQWlCO2dCQUNmO29CQUFFUSxRQUFRO29CQUFHQyxRQUFRO29CQUFJQyxnQkFBZ0I7b0JBQUtDLGdCQUFnQjtvQkFBS0Msa0JBQWtCO29CQUFNQyxrQkFBa0I7Z0JBQUs7Z0JBQ2xIO29CQUFFTCxRQUFRO29CQUFJQyxRQUFRO29CQUFJQyxnQkFBZ0I7b0JBQUtDLGdCQUFnQjtvQkFBS0Msa0JBQWtCO29CQUFNQyxrQkFBa0I7Z0JBQUs7Z0JBQ25IO29CQUFFTCxRQUFRO29CQUFJQyxRQUFRO29CQUFRQyxnQkFBZ0I7b0JBQUtDLGdCQUFnQjtvQkFBS0Msa0JBQWtCO29CQUFNQyxrQkFBa0I7Z0JBQUs7YUFDeEg7UUFDSDtRQUVBLGlEQUFpRDtRQUNqRCxNQUFNQyxrQkFBa0JkLGVBQWVlLElBQUksQ0FBQyxDQUFDQyxRQUMzQ25CLGFBQWFtQixNQUFNUixNQUFNLElBQUlYLGFBQWFtQixNQUFNUCxNQUFNO1FBR3hELElBQUlLLGlCQUFpQjtZQUNuQixPQUFPO2dCQUNMaUIsS0FBS2pCLGdCQUFnQkYsZ0JBQWdCLElBQUk7Z0JBQ3pDb0IsS0FBS2xCLGdCQUFnQkQsZ0JBQWdCLElBQUk7WUFDM0M7UUFDRixPQUFPO1lBQ0wsOENBQThDO1lBQzlDLE1BQU1NLGVBQWVuQixjQUFjLENBQUNBLGVBQWVvQixNQUFNLEdBQUcsRUFBRTtZQUM5RCxPQUFPO2dCQUNMVyxLQUFLWixhQUFhUCxnQkFBZ0IsSUFBSTtnQkFDdENvQixLQUFLYixhQUFhTixnQkFBZ0IsSUFBSTtZQUN4QztRQUNGO0lBQ0YsRUFBRSxPQUFPUixPQUFPO1FBQ2RELFFBQVFDLEtBQUssQ0FBQyx3Q0FBd0NBO1FBQ3RELG1DQUFtQztRQUNuQyxPQUFPO1lBQUUwQixLQUFLO1lBQU1DLEtBQUs7UUFBSztJQUNoQztBQUNGO0FBRUEsc0ZBQXNGO0FBQy9FLGVBQWVDLHdCQUF3QnBDLFNBQWlCO0lBQzdELElBQUk7UUFDRiwwQ0FBMEM7UUFDMUMsTUFBTUMsb0JBQW9CLE1BQU1KLHNEQUFlQSxDQUFDSyxHQUFHLENBQUM7UUFDcEQsSUFBSUM7UUFFSixJQUFJRixtQkFBbUI7WUFDckIsSUFBSTtnQkFDRkUsaUJBQWlCQyxLQUFLQyxLQUFLLENBQUNKO1lBQzlCLEVBQUUsT0FBT0ssWUFBWTtnQkFDbkJDLFFBQVFDLEtBQUssQ0FBQyxrQ0FBa0NGO2dCQUNoREgsaUJBQWlCO1lBQ25CO1FBQ0Y7UUFFQSwrQ0FBK0M7UUFDL0MsSUFBSSxDQUFDQSxrQkFBa0IsQ0FBQ00sTUFBTUMsT0FBTyxDQUFDUCxpQkFBaUI7WUFDckRBLGlCQUFpQjtnQkFDZjtvQkFBRVEsUUFBUTtvQkFBR0MsUUFBUTtvQkFBSUMsZ0JBQWdCO29CQUFLQyxnQkFBZ0I7b0JBQUtDLGtCQUFrQjtvQkFBTUMsa0JBQWtCO2dCQUFLO2dCQUNsSDtvQkFBRUwsUUFBUTtvQkFBSUMsUUFBUTtvQkFBSUMsZ0JBQWdCO29CQUFLQyxnQkFBZ0I7b0JBQUtDLGtCQUFrQjtvQkFBTUMsa0JBQWtCO2dCQUFLO2dCQUNuSDtvQkFBRUwsUUFBUTtvQkFBSUMsUUFBUTtvQkFBUUMsZ0JBQWdCO29CQUFLQyxnQkFBZ0I7b0JBQUtDLGtCQUFrQjtvQkFBTUMsa0JBQWtCO2dCQUFLO2FBQ3hIO1FBQ0g7UUFFQSxpREFBaUQ7UUFDakQsTUFBTUMsa0JBQWtCZCxlQUFlZSxJQUFJLENBQUMsQ0FBQ0MsUUFDM0NuQixhQUFhbUIsTUFBTVIsTUFBTSxJQUFJWCxhQUFhbUIsTUFBTVAsTUFBTTtRQUd4RCxJQUFJTztRQUNKLElBQUlGLGlCQUFpQjtZQUNuQkUsUUFBUUY7UUFDVixPQUFPO1lBQ0wsOENBQThDO1lBQzlDRSxRQUFRaEIsY0FBYyxDQUFDQSxlQUFlb0IsTUFBTSxHQUFHLEVBQUU7UUFDbkQ7UUFFQSw2REFBNkQ7UUFDN0QsSUFBSWMsV0FBVztRQUNmLE1BQU1DLE9BQU87UUFFYixJQUFLLElBQUlDLE1BQU0sR0FBR0EsTUFBTUQsTUFBTUMsTUFBTztZQUNuQyxzREFBc0Q7WUFDdEQsTUFBTVYsV0FBV1YsTUFBTU4sY0FBYyxHQUFHWSxLQUFLQyxNQUFNLEtBQU1QLENBQUFBLE1BQU1MLGNBQWMsR0FBR0ssTUFBTU4sY0FBYztZQUNwR3dCLFlBQVlSO1FBQ2Q7UUFFQSwyQkFBMkI7UUFDM0IsT0FBT1EsV0FBV0M7SUFDcEIsRUFBRSxPQUFPOUIsT0FBTztRQUNkRCxRQUFRQyxLQUFLLENBQUMsd0NBQXdDQTtRQUN0RCxPQUFPLEtBQUssbUJBQW1CO0lBQ2pDO0FBQ0Y7QUFFQSxtRUFBbUU7QUFDNUQsZUFBZWdDO0lBQ3BCLElBQUk7UUFDRmpDLFFBQVFrQyxHQUFHLENBQUM7UUFFWiw4QkFBOEI7UUFDOUIsTUFBTUMsb0JBQW9CLE1BQU1qRCwyQ0FBTUEsQ0FBQ2tELFVBQVUsQ0FBQ0MsUUFBUSxDQUFDO1lBQ3pEQyxPQUFPO2dCQUNMQyxRQUFRO2dCQUNSQyxZQUFZO29CQUNWQyxJQUFJLElBQUlDO2dCQUNWO1lBQ0Y7UUFDRjtRQUVBMUMsUUFBUWtDLEdBQUcsQ0FBQyxDQUFDLE1BQU0sRUFBRUMsa0JBQWtCbkIsTUFBTSxDQUFDLDhCQUE4QixDQUFDO1FBRTdFLE1BQU0yQixnQkFBZ0IsRUFBRTtRQUV4QixLQUFLLE1BQU1DLFFBQVFULGtCQUFtQjtZQUNwQyxJQUFJO2dCQUNGLGlEQUFpRDtnQkFDakQsTUFBTVUsU0FBUyxNQUFNckQsb0JBQW9Cb0QsS0FBS25ELFNBQVM7Z0JBRXZELHNDQUFzQztnQkFDdEMsTUFBTVAsMkNBQU1BLENBQUNrRCxVQUFVLENBQUNVLE1BQU0sQ0FBQztvQkFDN0JSLE9BQU87d0JBQUVTLElBQUlILEtBQUtHLEVBQUU7b0JBQUM7b0JBQ3JCQyxNQUFNO3dCQUFFMUIsVUFBVXVCO29CQUFPO2dCQUMzQjtnQkFFQUYsY0FBY00sSUFBSSxDQUFDO29CQUNqQkMsUUFBUU4sS0FBS0csRUFBRTtvQkFDZkksUUFBUVAsS0FBS08sTUFBTTtvQkFDbkIxRCxXQUFXbUQsS0FBS25ELFNBQVM7b0JBQ3pCMkQsUUFBUVIsS0FBS3RCLFFBQVE7b0JBQ3JCdUI7Z0JBQ0Y7Z0JBRUE3QyxRQUFRa0MsR0FBRyxDQUFDLENBQUMsYUFBYSxFQUFFVSxLQUFLRyxFQUFFLENBQUMsRUFBRSxFQUFFSCxLQUFLdEIsUUFBUSxDQUFDLEtBQUssRUFBRXVCLE9BQU8sQ0FBQyxDQUFDO1lBQ3hFLEVBQUUsT0FBT1EsV0FBVztnQkFDbEJyRCxRQUFRQyxLQUFLLENBQUMsQ0FBQyxvQkFBb0IsRUFBRTJDLEtBQUtHLEVBQUUsQ0FBQyxDQUFDLENBQUMsRUFBRU07WUFDbkQ7UUFDRjtRQUVBLHlCQUF5QjtRQUN6QixNQUFNaEUsa0RBQVdBLENBQUNpRSxNQUFNLENBQUM7WUFDdkJDLFFBQVE7WUFDUkMsU0FBUztnQkFDUEMsY0FBY2QsY0FBYzNCLE1BQU07Z0JBQ2xDMEMsWUFBWXZCLGtCQUFrQm5CLE1BQU07Z0JBQ3BDMkI7Z0JBQ0FnQixXQUFXLElBQUlqQixPQUFPa0IsV0FBVztZQUNuQztRQUNGO1FBRUE1RCxRQUFRa0MsR0FBRyxDQUFDLENBQUMscUJBQXFCLEVBQUVTLGNBQWMzQixNQUFNLENBQUMsd0NBQXdDLENBQUM7UUFFbEcsT0FBTztZQUNMNkMsU0FBUztZQUNUSixjQUFjZCxjQUFjM0IsTUFBTTtZQUNsQzBDLFlBQVl2QixrQkFBa0JuQixNQUFNO1lBQ3BDMkI7UUFDRjtJQUNGLEVBQUUsT0FBTzFDLE9BQU87UUFDZEQsUUFBUUMsS0FBSyxDQUFDLDZDQUE2Q0E7UUFDM0QsTUFBTUE7SUFDUjtBQUNGO0FBSUEsd0VBQXdFO0FBQ2pFLGVBQWU2RDtJQUNwQixJQUFJO1FBQ0Y5RCxRQUFRa0MsR0FBRyxDQUFDO1FBRVoseUNBQXlDO1FBQ3pDLE1BQU02Qix1QkFBdUIsTUFBTTdFLDJDQUFNQSxDQUFDOEUsSUFBSSxDQUFDM0IsUUFBUSxDQUFDO1lBQ3REQyxPQUFPO2dCQUNMMkIsYUFBYTtvQkFDWEMsTUFBTTt3QkFDSjNCLFFBQVE7d0JBQ1JDLFlBQVk7NEJBQ1ZDLElBQUksSUFBSUM7d0JBQ1Y7b0JBQ0Y7Z0JBQ0Y7WUFDRjtZQUNBeUIsU0FBUztnQkFDUEYsYUFBYTtvQkFDWDNCLE9BQU87d0JBQ0xDLFFBQVE7d0JBQ1JDLFlBQVk7NEJBQ1ZDLElBQUksSUFBSUM7d0JBQ1Y7b0JBQ0Y7Z0JBQ0Y7WUFDRjtRQUNGO1FBRUExQyxRQUFRa0MsR0FBRyxDQUFDLENBQUMsTUFBTSxFQUFFNkIscUJBQXFCL0MsTUFBTSxDQUFDLCtCQUErQixDQUFDO1FBRWpGLE1BQU1vRCxVQUFVLEVBQUU7UUFFbEIsS0FBSyxNQUFNSixRQUFRRCxxQkFBc0I7WUFDdkMsSUFBSTtnQkFDRiwrQ0FBK0M7Z0JBQy9DLElBQUlNLHFCQUFxQjtnQkFDekIsTUFBTUMsZUFBZSxFQUFFO2dCQUV2QixLQUFLLE1BQU0xQixRQUFRb0IsS0FBS0MsV0FBVyxDQUFFO29CQUNuQyxNQUFNTSxnQkFBZ0IsS0FBTUMsZ0JBQWdCLEdBQUc1QixLQUFLdEIsUUFBUSxHQUFJO29CQUNoRStDLHNCQUFzQkU7b0JBQ3RCRCxhQUFhckIsSUFBSSxDQUFDO3dCQUNoQkMsUUFBUU4sS0FBS0csRUFBRTt3QkFDZnRELFdBQVdtRCxLQUFLbkQsU0FBUzt3QkFDekI4RTtvQkFDRjtnQkFDRjtnQkFFQSxJQUFJRixxQkFBcUIsR0FBRztvQkFDMUIsMkJBQTJCO29CQUMzQixNQUFNSSxjQUFjLE1BQU1yRixvREFBYUEsQ0FBQ2tFLE1BQU0sQ0FBQzt3QkFDN0NILFFBQVFhLEtBQUtqQixFQUFFO3dCQUNmMkIsTUFBTTt3QkFDTkMsUUFBUU47d0JBQ1JPLGFBQWEsQ0FBQywrQkFBK0IsRUFBRU4sYUFBYU8sR0FBRyxDQUFDQyxDQUFBQSxJQUFLLEdBQUdBLEVBQUVyRixTQUFTLENBQUMsS0FBSyxDQUFDLEVBQUVzRixJQUFJLENBQUMsT0FBTzt3QkFDeEd4QyxRQUFRO29CQUNWO29CQUVBLHFEQUFxRDtvQkFDckQsTUFBTXlDLGNBQWMsTUFBTXpGLDRFQUF1QkEsQ0FDL0N5RSxLQUFLakIsRUFBRSxFQUNQc0Isb0JBQ0EsbUJBQ0FJLFlBQVkxQixFQUFFLEVBQ2Q7b0JBR0ZxQixRQUFRbkIsSUFBSSxDQUFDO3dCQUNYRSxRQUFRYSxLQUFLakIsRUFBRTt3QkFDZmtDLGVBQWVaO3dCQUNmVzt3QkFDQUUsZ0JBQWdCbEIsS0FBS0MsV0FBVyxDQUFDakQsTUFBTTtvQkFDekM7b0JBRUFoQixRQUFRa0MsR0FBRyxDQUFDLENBQUMsVUFBVSxFQUFFbUMsbUJBQW1CLG9CQUFvQixFQUFFVyxZQUFZaEUsTUFBTSxDQUFDLGdCQUFnQixFQUFFZ0QsS0FBS2pCLEVBQUUsRUFBRTtnQkFDbEg7WUFFRixFQUFFLE9BQU9vQyxXQUFXO2dCQUNsQm5GLFFBQVFDLEtBQUssQ0FBQyxDQUFDLDBDQUEwQyxFQUFFK0QsS0FBS2pCLEVBQUUsQ0FBQyxDQUFDLENBQUMsRUFBRW9DO1lBQ3pFO1FBQ0Y7UUFFQSxnQ0FBZ0M7UUFDaEMsTUFBTTlGLGtEQUFXQSxDQUFDaUUsTUFBTSxDQUFDO1lBQ3ZCQyxRQUFRO1lBQ1JDLFNBQVM7Z0JBQ1A0QixnQkFBZ0JoQixRQUFRcEQsTUFBTTtnQkFDOUJpRSxlQUFlYixRQUFRaUIsTUFBTSxDQUFDLENBQUNDLEtBQUtDLElBQU1ELE1BQU1DLEVBQUVOLGFBQWEsRUFBRTtnQkFDakVPLGtCQUFrQnBCLFFBQVFpQixNQUFNLENBQUMsQ0FBQ0MsS0FBS0MsSUFBTUQsTUFBTUMsRUFBRVAsV0FBVyxDQUFDaEUsTUFBTSxFQUFFO2dCQUN6RTJDLFdBQVcsSUFBSWpCLE9BQU9rQixXQUFXO1lBQ25DO1FBQ0Y7UUFFQTVELFFBQVFrQyxHQUFHLENBQUMsQ0FBQywyQ0FBMkMsRUFBRWtDLFFBQVFwRCxNQUFNLENBQUMsNEJBQTRCLENBQUM7UUFDdEcsT0FBT29EO0lBRVQsRUFBRSxPQUFPbkUsT0FBTztRQUNkRCxRQUFRQyxLQUFLLENBQUMsZ0NBQWdDQTtRQUM5QyxNQUFNQTtJQUNSO0FBQ0Y7QUFFQSw0REFBNEQ7QUFDckQsZUFBZXdGO0lBQ3BCLElBQUk7UUFDRnpGLFFBQVFrQyxHQUFHLENBQUM7UUFFWixrQ0FBa0M7UUFDbEMsTUFBTXdELGtCQUFrQixNQUFNeEcsMkNBQU1BLENBQUN1RixXQUFXLENBQUNwQyxRQUFRLENBQUM7WUFDeERDLE9BQU87Z0JBQ0xvQyxNQUFNO2dCQUNObkMsUUFBUTtZQUNWO1lBQ0E0QixTQUFTO2dCQUNQSCxNQUFNO1lBQ1I7UUFDRjtRQUVBaEUsUUFBUWtDLEdBQUcsQ0FBQyxDQUFDLE1BQU0sRUFBRXdELGdCQUFnQjFFLE1BQU0sQ0FBQyw4QkFBOEIsQ0FBQztRQUUzRSxNQUFNMkUsZUFBZSxJQUFJQztRQUV6Qix5QkFBeUI7UUFDekIsS0FBSyxNQUFNbkIsZUFBZWlCLGdCQUFpQjtZQUN6QyxNQUFNRyxlQUFlRixhQUFhaEcsR0FBRyxDQUFDOEUsWUFBWXRCLE1BQU0sS0FBSztZQUM3RHdDLGFBQWFHLEdBQUcsQ0FBQ3JCLFlBQVl0QixNQUFNLEVBQUUwQyxlQUFlcEIsWUFBWUUsTUFBTTtRQUN4RTtRQUVBLE1BQU1QLFVBQVUsRUFBRTtRQUVsQiwrQkFBK0I7UUFDL0IsS0FBSyxNQUFNLENBQUNqQixRQUFROEIsY0FBYyxJQUFJVSxhQUFjO1lBQ2xELElBQUk7Z0JBQ0YsNkNBQTZDO2dCQUM3QyxNQUFNekcsMkNBQU1BLENBQUN1RixXQUFXLENBQUNzQixVQUFVLENBQUM7b0JBQ2xDekQsT0FBTzt3QkFDTGE7d0JBQ0F1QixNQUFNO3dCQUNObkMsUUFBUTtvQkFDVjtvQkFDQVMsTUFBTTt3QkFDSlQsUUFBUTtvQkFDVjtnQkFDRjtnQkFFQTZCLFFBQVFuQixJQUFJLENBQUM7b0JBQ1hFO29CQUNBOEI7Z0JBQ0Y7WUFFRixFQUFFLE9BQU9FLFdBQVc7Z0JBQ2xCbkYsUUFBUUMsS0FBSyxDQUFDLENBQUMsbUNBQW1DLEVBQUVrRCxPQUFPLENBQUMsQ0FBQyxFQUFFZ0M7WUFDakU7UUFDRjtRQUVBLDhCQUE4QjtRQUM5QixNQUFNOUYsa0RBQVdBLENBQUNpRSxNQUFNLENBQUM7WUFDdkJDLFFBQVE7WUFDUkMsU0FBUztnQkFDUDRCLGdCQUFnQmhCLFFBQVFwRCxNQUFNO2dCQUM5QmdGLGtCQUFrQjVCLFFBQVFpQixNQUFNLENBQUMsQ0FBQ0MsS0FBS0MsSUFBTUQsTUFBTUMsRUFBRU4sYUFBYSxFQUFFO2dCQUNwRWdCLHVCQUF1QlAsZ0JBQWdCMUUsTUFBTTtnQkFDN0MyQyxXQUFXLElBQUlqQixPQUFPa0IsV0FBVztZQUNuQztRQUNGO1FBRUE1RCxRQUFRa0MsR0FBRyxDQUFDLENBQUMsa0RBQWtELEVBQUVrQyxRQUFRcEQsTUFBTSxDQUFDLE9BQU8sQ0FBQztRQUN4RixPQUFPb0Q7SUFFVCxFQUFFLE9BQU9uRSxPQUFPO1FBQ2RELFFBQVFDLEtBQUssQ0FBQyx1Q0FBdUNBO1FBQ3JELE1BQU1BO0lBQ1I7QUFDRjtBQUVBLDREQUE0RDtBQUNyRCxlQUFlaUc7SUFDcEIsSUFBSTtRQUNGbEcsUUFBUWtDLEdBQUcsQ0FBQztRQUVaLE1BQU1pRSxlQUFlLE1BQU1qSCwyQ0FBTUEsQ0FBQ2tELFVBQVUsQ0FBQ0MsUUFBUSxDQUFDO1lBQ3BEQyxPQUFPO2dCQUNMQyxRQUFRO2dCQUNSQyxZQUFZO29CQUNWNEQsS0FBSyxJQUFJMUQ7Z0JBQ1g7WUFDRjtZQUNBeUIsU0FBUztnQkFDUEgsTUFBTTtvQkFDSnFDLFFBQVE7d0JBQ05DLE9BQU87d0JBQ1BDLFdBQVc7d0JBQ1hDLFVBQVU7b0JBQ1o7Z0JBQ0Y7WUFDRjtRQUNGO1FBRUF4RyxRQUFRa0MsR0FBRyxDQUFDLENBQUMsTUFBTSxFQUFFaUUsYUFBYW5GLE1BQU0sQ0FBQyxnQkFBZ0IsQ0FBQztRQUUxRCxLQUFLLE1BQU00QixRQUFRdUQsYUFBYztZQUMvQixNQUFNaEgsbURBQVlBLENBQUNzSCxVQUFVLENBQUM3RCxLQUFLRyxFQUFFO1lBRXJDLHVEQUF1RDtZQUV2RCxNQUFNMUQsa0RBQVdBLENBQUNpRSxNQUFNLENBQUM7Z0JBQ3ZCQyxRQUFRO2dCQUNSSixRQUFRUCxLQUFLTyxNQUFNO2dCQUNuQkssU0FBUztvQkFDUGtELGNBQWM5RCxLQUFLRyxFQUFFO29CQUNyQjRELFFBQVE7b0JBQ1JDLGFBQWFoRSxLQUFLZ0UsV0FBVztvQkFDN0JwQyxrQkFBa0I1QixLQUFLNEIsZ0JBQWdCO2dCQUN6QztZQUNGO1lBRUEsMEJBQTBCO1lBQzFCLElBQUk7Z0JBQ0YsTUFBTSxFQUFFcUMsd0JBQXdCLEVBQUUsR0FBRyxNQUFNLDJSQUF3QztnQkFDbkYsTUFBTTVCLGdCQUFnQnJDLEtBQUtrRSxjQUFjLEdBQUdsRSxLQUFLbUUsZ0JBQWdCLEdBQUduRSxLQUFLb0UsY0FBYztnQkFDdkYsTUFBTUgseUJBQXlCSSxnQ0FBZ0MsQ0FBQztvQkFDOUQ5RCxRQUFRUCxLQUFLTyxNQUFNO29CQUNuQm1ELE9BQU8xRCxLQUFLb0IsSUFBSSxDQUFDc0MsS0FBSztvQkFDdEJDLFdBQVczRCxLQUFLb0IsSUFBSSxDQUFDdUMsU0FBUztvQkFDOUJDLFVBQVU1RCxLQUFLb0IsSUFBSSxDQUFDd0MsUUFBUTtvQkFDNUIvRyxXQUFXbUQsS0FBS25ELFNBQVM7b0JBQ3pCK0Usa0JBQWtCNUIsS0FBSzRCLGdCQUFnQjtvQkFDdkNvQyxhQUFhM0I7b0JBQ2JpQyxjQUFjdEUsS0FBS3VFLFNBQVMsQ0FBQ3ZELFdBQVc7b0JBQ3hDcEIsWUFBWUksS0FBS0osVUFBVSxDQUFDb0IsV0FBVztvQkFDdkN3RCxjQUFjO2dCQUNoQjtZQUNGLEVBQUUsT0FBT0MsWUFBWTtnQkFDbkJySCxRQUFRQyxLQUFLLENBQUMsNENBQTRDb0g7WUFDMUQsdUNBQXVDO1lBQ3pDO1FBQ0Y7UUFFQSxPQUFPbEIsYUFBYW5GLE1BQU07SUFFNUIsRUFBRSxPQUFPZixPQUFPO1FBQ2RELFFBQVFDLEtBQUssQ0FBQyxtQ0FBbUNBO1FBQ2pELE1BQU1BO0lBQ1I7QUFDRjtBQUVBLHdCQUF3QjtBQUNqQixlQUFlcUg7SUFDcEIsSUFBSTtRQUNGLE1BQU1DLFFBQVEsTUFBTXJJLDJDQUFNQSxDQUFDc0ksWUFBWSxDQUFDO1lBQ3RDLGtCQUFrQjtZQUNsQnRJLDJDQUFNQSxDQUFDa0QsVUFBVSxDQUFDcUYsU0FBUyxDQUFDO2dCQUMxQkMsTUFBTTtvQkFDSmpJLFdBQVc7Z0JBQ2I7WUFDRjtZQUVBLGNBQWM7WUFDZFAsMkNBQU1BLENBQUNrRCxVQUFVLENBQUNxRixTQUFTLENBQUM7Z0JBQzFCbkYsT0FBTztvQkFDTEMsUUFBUTtnQkFDVjtnQkFDQW1GLE1BQU07b0JBQ0pqSSxXQUFXO2dCQUNiO1lBQ0Y7WUFFQSxtQkFBbUI7WUFDbkJQLDJDQUFNQSxDQUFDa0QsVUFBVSxDQUFDcUYsU0FBUyxDQUFDO2dCQUMxQkMsTUFBTTtvQkFDSmxELGtCQUFrQjtnQkFDcEI7WUFDRjtZQUVBLDZCQUE2QjtZQUM3QnRGLDJDQUFNQSxDQUFDdUYsV0FBVyxDQUFDZ0QsU0FBUyxDQUFDO2dCQUMzQm5GLE9BQU87b0JBQ0xvQyxNQUFNO29CQUNObkMsUUFBUTtnQkFDVjtnQkFDQW1GLE1BQU07b0JBQ0ovQyxRQUFRO2dCQUNWO1lBQ0Y7WUFFQSw0QkFBNEI7WUFDNUJ6RiwyQ0FBTUEsQ0FBQ2tELFVBQVUsQ0FBQ3VGLEtBQUssQ0FBQztnQkFDdEJyRixPQUFPO29CQUNMQyxRQUFRO2dCQUNWO1lBQ0Y7WUFFQSwyQkFBMkI7WUFDM0JyRCwyQ0FBTUEsQ0FBQ2tELFVBQVUsQ0FBQ3VGLEtBQUs7U0FDeEI7UUFFRCxPQUFPO1lBQ0xDLGNBQWNMLEtBQUssQ0FBQyxFQUFFLENBQUNHLElBQUksQ0FBQ2pJLFNBQVMsSUFBSTtZQUN6Q29JLFdBQVdOLEtBQUssQ0FBQyxFQUFFLENBQUNHLElBQUksQ0FBQ2pJLFNBQVMsSUFBSTtZQUN0Q3FJLGlCQUFpQlAsS0FBSyxDQUFDLEVBQUUsQ0FBQ0csSUFBSSxDQUFDbEQsZ0JBQWdCLElBQUk7WUFDbkR1RCwwQkFBMEJSLEtBQUssQ0FBQyxFQUFFLENBQUNHLElBQUksQ0FBQy9DLE1BQU0sSUFBSTtZQUNsRHhDLG1CQUFtQm9GLEtBQUssQ0FBQyxFQUFFO1lBQzNCUyxrQkFBa0JULEtBQUssQ0FBQyxFQUFFO1FBQzVCO0lBRUYsRUFBRSxPQUFPdEgsT0FBTztRQUNkRCxRQUFRQyxLQUFLLENBQUMsdUJBQXVCQTtRQUNyQyxNQUFNQTtJQUNSO0FBQ0Y7QUFFQSxzQ0FBc0M7QUFDL0IsZUFBZWdJLDJCQUEyQjlFLE1BQWM7SUFDN0QsSUFBSTtRQUNGLE1BQU1oQixvQkFBb0IsTUFBTWhELG1EQUFZQSxDQUFDK0ksa0JBQWtCLENBQUMvRTtRQUVoRSxJQUFJaEIsa0JBQWtCbkIsTUFBTSxLQUFLLEdBQUc7WUFDbEMsT0FBTztnQkFDTG1ILFdBQVc7Z0JBQ1hDLFlBQVk7Z0JBQ1pDLGFBQWE7Z0JBQ2JDLFlBQVk7WUFDZDtRQUNGO1FBRUEsSUFBSUMsYUFBYTtRQUVqQixLQUFLLE1BQU0zRixRQUFRVCxrQkFBbUI7WUFDcEMsTUFBTW9DLGdCQUFnQixLQUFNQyxnQkFBZ0IsR0FBRzVCLEtBQUt0QixRQUFRLEdBQUk7WUFDaEUsTUFBTWtILGNBQWM1RixLQUFLNEIsZ0JBQWdCLEdBQUc7WUFDNUMsTUFBTWlFLG9CQUFvQkQsY0FBYzVGLEtBQUtnRSxXQUFXO1lBRXhELHdEQUF3RDtZQUN4RDJCLGNBQWNySCxLQUFLUyxHQUFHLENBQUM0QyxlQUFla0U7UUFDeEM7UUFFQSxPQUFPO1lBQ0xOLFdBQVdJLGFBQWE7WUFDeEJILFlBQVlHLGFBQWE7WUFDekJGLGFBQWFFLGFBQWE7WUFDMUJELFlBQVlDLGFBQWE7UUFDM0I7SUFFRixFQUFFLE9BQU90SSxPQUFPO1FBQ2RELFFBQVFDLEtBQUssQ0FBQyx5Q0FBeUNBO1FBQ3ZELE1BQU1BO0lBQ1I7QUFDRiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxkcmVhbVxcRGVza3RvcFxcSGFzaF9NaW5pbmdzXFxoYXNoY29yZXhcXHNyY1xcbGliXFxtaW5pbmcudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgcHJpc21hIH0gZnJvbSAnLi9wcmlzbWEnO1xuaW1wb3J0IHsgbWluaW5nVW5pdERiLCB0cmFuc2FjdGlvbkRiLCBzeXN0ZW1Mb2dEYiwgYWRtaW5TZXR0aW5nc0RiIH0gZnJvbSAnLi9kYXRhYmFzZSc7XG5pbXBvcnQgeyBhbGxvY2F0ZUVhcm5pbmdzVG9Vbml0cywgZ2V0QWN0aXZlTWluaW5nVW5pdHNGSUZPLCBzaG91bGRFeHBpcmVVbml0LCBleHBpcmVNaW5pbmdVbml0IH0gZnJvbSAnLi9taW5pbmdVbml0RWFybmluZ3MnO1xuXG4vLyBOb3RlOiBVc2VyIGFjdGl2ZSBzdGF0dXMgaXMgbm93IGhhbmRsZWQgZHluYW1pY2FsbHkgaW4gdGhlIHJlZmVycmFsIHN5c3RlbVxuLy8gVGhlIGlzQWN0aXZlIGZpZWxkIGluIHRoZSBkYXRhYmFzZSBpcyBvbmx5IHVzZWQgZm9yIGFjY291bnQgYWNjZXNzIGNvbnRyb2xcbi8vIE1pbmluZyBhY3Rpdml0eSBzdGF0dXMgaXMgY29tcHV0ZWQgb24tZGVtYW5kIGZvciBiaW5hcnkgdHJlZSBkaXNwbGF5XG5cbi8vIENhbGN1bGF0ZSBkeW5hbWljIFJPSSBiYXNlZCBvbiBUSC9zIGFtb3VudCBhbmQgYWRtaW4tY29uZmlndXJlZCByYW5nZXNcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBjYWxjdWxhdGVEeW5hbWljUk9JKHRoc0Ftb3VudDogbnVtYmVyKTogUHJvbWlzZTxudW1iZXI+IHtcbiAgdHJ5IHtcbiAgICAvLyBHZXQgZWFybmluZ3MgcmFuZ2VzIGZyb20gYWRtaW4gc2V0dGluZ3NcbiAgICBjb25zdCBlYXJuaW5nc1Jhbmdlc1N0ciA9IGF3YWl0IGFkbWluU2V0dGluZ3NEYi5nZXQoJ2Vhcm5pbmdzUmFuZ2VzJyk7XG4gICAgbGV0IGVhcm5pbmdzUmFuZ2VzO1xuXG4gICAgaWYgKGVhcm5pbmdzUmFuZ2VzU3RyKSB7XG4gICAgICB0cnkge1xuICAgICAgICBlYXJuaW5nc1JhbmdlcyA9IEpTT04ucGFyc2UoZWFybmluZ3NSYW5nZXNTdHIpO1xuICAgICAgfSBjYXRjaCAocGFyc2VFcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBwYXJzaW5nIGVhcm5pbmdzIHJhbmdlczonLCBwYXJzZUVycm9yKTtcbiAgICAgICAgZWFybmluZ3NSYW5nZXMgPSBudWxsO1xuICAgICAgfVxuICAgIH1cblxuICAgIC8vIEZhbGxiYWNrIHRvIGRlZmF1bHQgcmFuZ2VzIGlmIG5vdCBjb25maWd1cmVkXG4gICAgaWYgKCFlYXJuaW5nc1JhbmdlcyB8fCAhQXJyYXkuaXNBcnJheShlYXJuaW5nc1JhbmdlcykpIHtcbiAgICAgIGVhcm5pbmdzUmFuZ2VzID0gW1xuICAgICAgICB7IG1pblRIUzogMCwgbWF4VEhTOiAxMCwgZGFpbHlSZXR1cm5NaW46IDAuMywgZGFpbHlSZXR1cm5NYXg6IDAuNSwgbW9udGhseVJldHVybk1pbjogMTAuMCwgbW9udGhseVJldHVybk1heDogMTUuMCB9LFxuICAgICAgICB7IG1pblRIUzogMTAsIG1heFRIUzogNTAsIGRhaWx5UmV0dXJuTWluOiAwLjQsIGRhaWx5UmV0dXJuTWF4OiAwLjYsIG1vbnRobHlSZXR1cm5NaW46IDEwLjAsIG1vbnRobHlSZXR1cm5NYXg6IDE1LjAgfSxcbiAgICAgICAgeyBtaW5USFM6IDUwLCBtYXhUSFM6IDk5OTk5OSwgZGFpbHlSZXR1cm5NaW46IDAuNSwgZGFpbHlSZXR1cm5NYXg6IDAuNywgbW9udGhseVJldHVybk1pbjogMTAuMCwgbW9udGhseVJldHVybk1heDogMTUuMCB9LFxuICAgICAgXTtcbiAgICB9XG5cbiAgICAvLyBGaW5kIHRoZSBhcHByb3ByaWF0ZSByYW5nZSBmb3IgdGhlIFRIL3MgYW1vdW50XG4gICAgY29uc3QgYXBwbGljYWJsZVJhbmdlID0gZWFybmluZ3NSYW5nZXMuZmluZCgocmFuZ2U6IGFueSkgPT5cbiAgICAgIHRoc0Ftb3VudCA+PSByYW5nZS5taW5USFMgJiYgdGhzQW1vdW50IDw9IHJhbmdlLm1heFRIU1xuICAgICk7XG5cbiAgICBsZXQgbWluUk9JOiBudW1iZXI7XG4gICAgbGV0IG1heFJPSTogbnVtYmVyO1xuXG4gICAgaWYgKGFwcGxpY2FibGVSYW5nZSkge1xuICAgICAgbWluUk9JID0gYXBwbGljYWJsZVJhbmdlLmRhaWx5UmV0dXJuTWluO1xuICAgICAgbWF4Uk9JID0gYXBwbGljYWJsZVJhbmdlLmRhaWx5UmV0dXJuTWF4O1xuICAgIH0gZWxzZSB7XG4gICAgICAvLyBGYWxsYmFjayB0byBoaWdoZXN0IHJhbmdlIGlmIG5vIG1hdGNoIGZvdW5kXG4gICAgICBjb25zdCBoaWdoZXN0UmFuZ2UgPSBlYXJuaW5nc1Jhbmdlc1tlYXJuaW5nc1Jhbmdlcy5sZW5ndGggLSAxXTtcbiAgICAgIG1pblJPSSA9IGhpZ2hlc3RSYW5nZS5kYWlseVJldHVybk1pbjtcbiAgICAgIG1heFJPSSA9IGhpZ2hlc3RSYW5nZS5kYWlseVJldHVybk1heDtcbiAgICB9XG5cbiAgICAvLyBBZGQgcmFuZG9taXphdGlvbiB3aXRoaW4gdGhlIHJhbmdlXG4gICAgY29uc3QgcmFuZG9tUk9JID0gbWluUk9JICsgKE1hdGgucmFuZG9tKCkgKiAobWF4Uk9JIC0gbWluUk9JKSk7XG5cbiAgICAvLyBSb3VuZCB0byAyIGRlY2ltYWwgcGxhY2VzXG4gICAgcmV0dXJuIE1hdGgucm91bmQocmFuZG9tUk9JICogMTAwKSAvIDEwMDtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjYWxjdWxhdGluZyBkeW5hbWljIFJPSTonLCBlcnJvcik7XG4gICAgLy8gRmFsbGJhY2sgdG8gZGVmYXVsdCB2YWx1ZXMgYmFzZWQgb24gVEgvcyBhbW91bnRcbiAgICBpZiAodGhzQW1vdW50ID49IDUwKSByZXR1cm4gMC42O1xuICAgIGlmICh0aHNBbW91bnQgPj0gMTApIHJldHVybiAwLjU7XG4gICAgcmV0dXJuIDAuNDtcbiAgfVxufVxuXG4vLyBWYWxpZGF0ZSBtb250aGx5IHJldHVybiBkb2Vzbid0IGV4Y2VlZCBhZG1pbi1jb25maWd1cmVkIGxpbWl0cyBmb3Igc3BlY2lmaWMgVEgvcyBhbW91bnRcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiB2YWxpZGF0ZU1vbnRobHlSZXR1cm4oZGFpbHlST0k6IG51bWJlciwgdGhzQW1vdW50OiBudW1iZXIpOiBQcm9taXNlPGJvb2xlYW4+IHtcbiAgdHJ5IHtcbiAgICAvLyBHZXQgZWFybmluZ3MgcmFuZ2VzIGZyb20gYWRtaW4gc2V0dGluZ3NcbiAgICBjb25zdCBlYXJuaW5nc1Jhbmdlc1N0ciA9IGF3YWl0IGFkbWluU2V0dGluZ3NEYi5nZXQoJ2Vhcm5pbmdzUmFuZ2VzJyk7XG4gICAgbGV0IGVhcm5pbmdzUmFuZ2VzO1xuXG4gICAgaWYgKGVhcm5pbmdzUmFuZ2VzU3RyKSB7XG4gICAgICB0cnkge1xuICAgICAgICBlYXJuaW5nc1JhbmdlcyA9IEpTT04ucGFyc2UoZWFybmluZ3NSYW5nZXNTdHIpO1xuICAgICAgfSBjYXRjaCAocGFyc2VFcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBwYXJzaW5nIGVhcm5pbmdzIHJhbmdlczonLCBwYXJzZUVycm9yKTtcbiAgICAgICAgZWFybmluZ3NSYW5nZXMgPSBudWxsO1xuICAgICAgfVxuICAgIH1cblxuICAgIC8vIEZhbGxiYWNrIHRvIGRlZmF1bHQgcmFuZ2VzIGlmIG5vdCBjb25maWd1cmVkXG4gICAgaWYgKCFlYXJuaW5nc1JhbmdlcyB8fCAhQXJyYXkuaXNBcnJheShlYXJuaW5nc1JhbmdlcykpIHtcbiAgICAgIGVhcm5pbmdzUmFuZ2VzID0gW1xuICAgICAgICB7IG1pblRIUzogMCwgbWF4VEhTOiAxMCwgZGFpbHlSZXR1cm5NaW46IDAuMywgZGFpbHlSZXR1cm5NYXg6IDAuNSwgbW9udGhseVJldHVybk1pbjogMTAuMCwgbW9udGhseVJldHVybk1heDogMTUuMCB9LFxuICAgICAgICB7IG1pblRIUzogMTAsIG1heFRIUzogNTAsIGRhaWx5UmV0dXJuTWluOiAwLjQsIGRhaWx5UmV0dXJuTWF4OiAwLjYsIG1vbnRobHlSZXR1cm5NaW46IDEwLjAsIG1vbnRobHlSZXR1cm5NYXg6IDE1LjAgfSxcbiAgICAgICAgeyBtaW5USFM6IDUwLCBtYXhUSFM6IDk5OTk5OSwgZGFpbHlSZXR1cm5NaW46IDAuNSwgZGFpbHlSZXR1cm5NYXg6IDAuNywgbW9udGhseVJldHVybk1pbjogMTAuMCwgbW9udGhseVJldHVybk1heDogMTUuMCB9LFxuICAgICAgXTtcbiAgICB9XG5cbiAgICAvLyBGaW5kIHRoZSBhcHByb3ByaWF0ZSByYW5nZSBmb3IgdGhlIFRIL3MgYW1vdW50XG4gICAgY29uc3QgYXBwbGljYWJsZVJhbmdlID0gZWFybmluZ3NSYW5nZXMuZmluZCgocmFuZ2U6IGFueSkgPT5cbiAgICAgIHRoc0Ftb3VudCA+PSByYW5nZS5taW5USFMgJiYgdGhzQW1vdW50IDw9IHJhbmdlLm1heFRIU1xuICAgICk7XG5cbiAgICBsZXQgbW9udGhseU1pbiA9IDEwLjA7XG4gICAgbGV0IG1vbnRobHlNYXggPSAxNS4wO1xuXG4gICAgaWYgKGFwcGxpY2FibGVSYW5nZSkge1xuICAgICAgbW9udGhseU1pbiA9IGFwcGxpY2FibGVSYW5nZS5tb250aGx5UmV0dXJuTWluIHx8IDEwLjA7XG4gICAgICBtb250aGx5TWF4ID0gYXBwbGljYWJsZVJhbmdlLm1vbnRobHlSZXR1cm5NYXggfHwgMTUuMDtcbiAgICB9IGVsc2Uge1xuICAgICAgLy8gRmFsbGJhY2sgdG8gaGlnaGVzdCByYW5nZSBpZiBubyBtYXRjaCBmb3VuZFxuICAgICAgY29uc3QgaGlnaGVzdFJhbmdlID0gZWFybmluZ3NSYW5nZXNbZWFybmluZ3NSYW5nZXMubGVuZ3RoIC0gMV07XG4gICAgICBtb250aGx5TWluID0gaGlnaGVzdFJhbmdlLm1vbnRobHlSZXR1cm5NaW4gfHwgMTAuMDtcbiAgICAgIG1vbnRobHlNYXggPSBoaWdoZXN0UmFuZ2UubW9udGhseVJldHVybk1heCB8fCAxNS4wO1xuICAgIH1cblxuICAgIGNvbnN0IG1vbnRobHlSZXR1cm4gPSBkYWlseVJPSSAqIDMwOyAvLyBBcHByb3hpbWF0ZSBtb250aGx5IHJldHVyblxuICAgIHJldHVybiBtb250aGx5UmV0dXJuID49IG1vbnRobHlNaW4gJiYgbW9udGhseVJldHVybiA8PSBtb250aGx5TWF4O1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHZhbGlkYXRpbmcgbW9udGhseSByZXR1cm46JywgZXJyb3IpO1xuICAgIC8vIEZhbGxiYWNrIHRvIGRlZmF1bHQgMTAtMTUlIHJhbmdlXG4gICAgY29uc3QgbW9udGhseVJldHVybiA9IGRhaWx5Uk9JICogMzA7XG4gICAgcmV0dXJuIG1vbnRobHlSZXR1cm4gPj0gMTAgJiYgbW9udGhseVJldHVybiA8PSAxNTtcbiAgfVxufVxuXG4vLyBHZXQgbW9udGhseSByZXR1cm4gbGltaXRzIGZvciBhIHNwZWNpZmljIFRIL3MgYW1vdW50XG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2V0TW9udGhseVJldHVybkxpbWl0cyh0aHNBbW91bnQ6IG51bWJlcik6IFByb21pc2U8eyBtaW46IG51bWJlcjsgbWF4OiBudW1iZXIgfT4ge1xuICB0cnkge1xuICAgIC8vIEdldCBlYXJuaW5ncyByYW5nZXMgZnJvbSBhZG1pbiBzZXR0aW5nc1xuICAgIGNvbnN0IGVhcm5pbmdzUmFuZ2VzU3RyID0gYXdhaXQgYWRtaW5TZXR0aW5nc0RiLmdldCgnZWFybmluZ3NSYW5nZXMnKTtcbiAgICBsZXQgZWFybmluZ3NSYW5nZXM7XG5cbiAgICBpZiAoZWFybmluZ3NSYW5nZXNTdHIpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGVhcm5pbmdzUmFuZ2VzID0gSlNPTi5wYXJzZShlYXJuaW5nc1Jhbmdlc1N0cik7XG4gICAgICB9IGNhdGNoIChwYXJzZUVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHBhcnNpbmcgZWFybmluZ3MgcmFuZ2VzOicsIHBhcnNlRXJyb3IpO1xuICAgICAgICBlYXJuaW5nc1JhbmdlcyA9IG51bGw7XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gRmFsbGJhY2sgdG8gZGVmYXVsdCByYW5nZXMgaWYgbm90IGNvbmZpZ3VyZWRcbiAgICBpZiAoIWVhcm5pbmdzUmFuZ2VzIHx8ICFBcnJheS5pc0FycmF5KGVhcm5pbmdzUmFuZ2VzKSkge1xuICAgICAgZWFybmluZ3NSYW5nZXMgPSBbXG4gICAgICAgIHsgbWluVEhTOiAwLCBtYXhUSFM6IDEwLCBkYWlseVJldHVybk1pbjogMC4zLCBkYWlseVJldHVybk1heDogMC41LCBtb250aGx5UmV0dXJuTWluOiAxMC4wLCBtb250aGx5UmV0dXJuTWF4OiAxNS4wIH0sXG4gICAgICAgIHsgbWluVEhTOiAxMCwgbWF4VEhTOiA1MCwgZGFpbHlSZXR1cm5NaW46IDAuNCwgZGFpbHlSZXR1cm5NYXg6IDAuNiwgbW9udGhseVJldHVybk1pbjogMTAuMCwgbW9udGhseVJldHVybk1heDogMTUuMCB9LFxuICAgICAgICB7IG1pblRIUzogNTAsIG1heFRIUzogOTk5OTk5LCBkYWlseVJldHVybk1pbjogMC41LCBkYWlseVJldHVybk1heDogMC43LCBtb250aGx5UmV0dXJuTWluOiAxMC4wLCBtb250aGx5UmV0dXJuTWF4OiAxNS4wIH0sXG4gICAgICBdO1xuICAgIH1cblxuICAgIC8vIEZpbmQgdGhlIGFwcHJvcHJpYXRlIHJhbmdlIGZvciB0aGUgVEgvcyBhbW91bnRcbiAgICBjb25zdCBhcHBsaWNhYmxlUmFuZ2UgPSBlYXJuaW5nc1Jhbmdlcy5maW5kKChyYW5nZTogYW55KSA9PlxuICAgICAgdGhzQW1vdW50ID49IHJhbmdlLm1pblRIUyAmJiB0aHNBbW91bnQgPD0gcmFuZ2UubWF4VEhTXG4gICAgKTtcblxuICAgIGlmIChhcHBsaWNhYmxlUmFuZ2UpIHtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIG1pbjogYXBwbGljYWJsZVJhbmdlLm1vbnRobHlSZXR1cm5NaW4gfHwgMTAuMCxcbiAgICAgICAgbWF4OiBhcHBsaWNhYmxlUmFuZ2UubW9udGhseVJldHVybk1heCB8fCAxNS4wLFxuICAgICAgfTtcbiAgICB9IGVsc2Uge1xuICAgICAgLy8gRmFsbGJhY2sgdG8gaGlnaGVzdCByYW5nZSBpZiBubyBtYXRjaCBmb3VuZFxuICAgICAgY29uc3QgaGlnaGVzdFJhbmdlID0gZWFybmluZ3NSYW5nZXNbZWFybmluZ3NSYW5nZXMubGVuZ3RoIC0gMV07XG4gICAgICByZXR1cm4ge1xuICAgICAgICBtaW46IGhpZ2hlc3RSYW5nZS5tb250aGx5UmV0dXJuTWluIHx8IDEwLjAsXG4gICAgICAgIG1heDogaGlnaGVzdFJhbmdlLm1vbnRobHlSZXR1cm5NYXggfHwgMTUuMCxcbiAgICAgIH07XG4gICAgfVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGdldHRpbmcgbW9udGhseSByZXR1cm4gbGltaXRzOicsIGVycm9yKTtcbiAgICAvLyBGYWxsYmFjayB0byBkZWZhdWx0IDEwLTE1JSByYW5nZVxuICAgIHJldHVybiB7IG1pbjogMTAuMCwgbWF4OiAxNS4wIH07XG4gIH1cbn1cblxuLy8gQ2FsY3VsYXRlIDctZGF5IGF2ZXJhZ2UgUk9JIGZvciBhIHNwZWNpZmljIFRIL3MgYW1vdW50IGJhc2VkIG9uIGFkbWluIGNvbmZpZ3VyYXRpb25cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBjYWxjdWxhdGU3RGF5QXZlcmFnZVJPSSh0aHNBbW91bnQ6IG51bWJlcik6IFByb21pc2U8bnVtYmVyPiB7XG4gIHRyeSB7XG4gICAgLy8gR2V0IGVhcm5pbmdzIHJhbmdlcyBmcm9tIGFkbWluIHNldHRpbmdzXG4gICAgY29uc3QgZWFybmluZ3NSYW5nZXNTdHIgPSBhd2FpdCBhZG1pblNldHRpbmdzRGIuZ2V0KCdlYXJuaW5nc1JhbmdlcycpO1xuICAgIGxldCBlYXJuaW5nc1JhbmdlcztcblxuICAgIGlmIChlYXJuaW5nc1Jhbmdlc1N0cikge1xuICAgICAgdHJ5IHtcbiAgICAgICAgZWFybmluZ3NSYW5nZXMgPSBKU09OLnBhcnNlKGVhcm5pbmdzUmFuZ2VzU3RyKTtcbiAgICAgIH0gY2F0Y2ggKHBhcnNlRXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgcGFyc2luZyBlYXJuaW5ncyByYW5nZXM6JywgcGFyc2VFcnJvcik7XG4gICAgICAgIGVhcm5pbmdzUmFuZ2VzID0gbnVsbDtcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBGYWxsYmFjayB0byBkZWZhdWx0IHJhbmdlcyBpZiBub3QgY29uZmlndXJlZFxuICAgIGlmICghZWFybmluZ3NSYW5nZXMgfHwgIUFycmF5LmlzQXJyYXkoZWFybmluZ3NSYW5nZXMpKSB7XG4gICAgICBlYXJuaW5nc1JhbmdlcyA9IFtcbiAgICAgICAgeyBtaW5USFM6IDAsIG1heFRIUzogMTAsIGRhaWx5UmV0dXJuTWluOiAwLjMsIGRhaWx5UmV0dXJuTWF4OiAwLjUsIG1vbnRobHlSZXR1cm5NaW46IDEwLjAsIG1vbnRobHlSZXR1cm5NYXg6IDE1LjAgfSxcbiAgICAgICAgeyBtaW5USFM6IDEwLCBtYXhUSFM6IDUwLCBkYWlseVJldHVybk1pbjogMC40LCBkYWlseVJldHVybk1heDogMC42LCBtb250aGx5UmV0dXJuTWluOiAxMC4wLCBtb250aGx5UmV0dXJuTWF4OiAxNS4wIH0sXG4gICAgICAgIHsgbWluVEhTOiA1MCwgbWF4VEhTOiA5OTk5OTksIGRhaWx5UmV0dXJuTWluOiAwLjUsIGRhaWx5UmV0dXJuTWF4OiAwLjcsIG1vbnRobHlSZXR1cm5NaW46IDEwLjAsIG1vbnRobHlSZXR1cm5NYXg6IDE1LjAgfSxcbiAgICAgIF07XG4gICAgfVxuXG4gICAgLy8gRmluZCB0aGUgYXBwcm9wcmlhdGUgcmFuZ2UgZm9yIHRoZSBUSC9zIGFtb3VudFxuICAgIGNvbnN0IGFwcGxpY2FibGVSYW5nZSA9IGVhcm5pbmdzUmFuZ2VzLmZpbmQoKHJhbmdlOiBhbnkpID0+XG4gICAgICB0aHNBbW91bnQgPj0gcmFuZ2UubWluVEhTICYmIHRoc0Ftb3VudCA8PSByYW5nZS5tYXhUSFNcbiAgICApO1xuXG4gICAgbGV0IHJhbmdlO1xuICAgIGlmIChhcHBsaWNhYmxlUmFuZ2UpIHtcbiAgICAgIHJhbmdlID0gYXBwbGljYWJsZVJhbmdlO1xuICAgIH0gZWxzZSB7XG4gICAgICAvLyBGYWxsYmFjayB0byBoaWdoZXN0IHJhbmdlIGlmIG5vIG1hdGNoIGZvdW5kXG4gICAgICByYW5nZSA9IGVhcm5pbmdzUmFuZ2VzW2Vhcm5pbmdzUmFuZ2VzLmxlbmd0aCAtIDFdO1xuICAgIH1cblxuICAgIC8vIENhbGN1bGF0ZSA3LWRheSBhdmVyYWdlIGJ5IHNpbXVsYXRpbmcgZGFpbHkgUk9JIHZhcmlhdGlvbnNcbiAgICBsZXQgdG90YWxST0kgPSAwO1xuICAgIGNvbnN0IGRheXMgPSA3O1xuXG4gICAgZm9yIChsZXQgZGF5ID0gMDsgZGF5IDwgZGF5czsgZGF5KyspIHtcbiAgICAgIC8vIEdlbmVyYXRlIGEgcmFuZG9tIFJPSSB3aXRoaW4gdGhlIHJhbmdlIGZvciBlYWNoIGRheVxuICAgICAgY29uc3QgZGFpbHlST0kgPSByYW5nZS5kYWlseVJldHVybk1pbiArIE1hdGgucmFuZG9tKCkgKiAocmFuZ2UuZGFpbHlSZXR1cm5NYXggLSByYW5nZS5kYWlseVJldHVybk1pbik7XG4gICAgICB0b3RhbFJPSSArPSBkYWlseVJPSTtcbiAgICB9XG5cbiAgICAvLyBSZXR1cm4gdGhlIDctZGF5IGF2ZXJhZ2VcbiAgICByZXR1cm4gdG90YWxST0kgLyBkYXlzO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNhbGN1bGF0aW5nIDctZGF5IGF2ZXJhZ2UgUk9JOicsIGVycm9yKTtcbiAgICByZXR1cm4gMC40OyAvLyBEZWZhdWx0IGZhbGxiYWNrXG4gIH1cbn1cblxuLy8gVXBkYXRlIGV4aXN0aW5nIG1pbmluZyB1bml0cyB3aGVuIGVhcm5pbmdzIGNvbmZpZ3VyYXRpb24gY2hhbmdlc1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHVwZGF0ZUV4aXN0aW5nTWluaW5nVW5pdHNST0koKSB7XG4gIHRyeSB7XG4gICAgY29uc29sZS5sb2coJ1VwZGF0aW5nIGV4aXN0aW5nIG1pbmluZyB1bml0cyB3aXRoIG5ldyBST0kgY29uZmlndXJhdGlvbi4uLicpO1xuXG4gICAgLy8gR2V0IGFsbCBhY3RpdmUgbWluaW5nIHVuaXRzXG4gICAgY29uc3QgYWN0aXZlTWluaW5nVW5pdHMgPSBhd2FpdCBwcmlzbWEubWluaW5nVW5pdC5maW5kTWFueSh7XG4gICAgICB3aGVyZToge1xuICAgICAgICBzdGF0dXM6ICdBQ1RJVkUnLFxuICAgICAgICBleHBpcnlEYXRlOiB7XG4gICAgICAgICAgZ3Q6IG5ldyBEYXRlKCksXG4gICAgICAgIH0sXG4gICAgICB9LFxuICAgIH0pO1xuXG4gICAgY29uc29sZS5sb2coYEZvdW5kICR7YWN0aXZlTWluaW5nVW5pdHMubGVuZ3RofSBhY3RpdmUgbWluaW5nIHVuaXRzIHRvIHVwZGF0ZWApO1xuXG4gICAgY29uc3QgdXBkYXRlUmVzdWx0cyA9IFtdO1xuXG4gICAgZm9yIChjb25zdCB1bml0IG9mIGFjdGl2ZU1pbmluZ1VuaXRzKSB7XG4gICAgICB0cnkge1xuICAgICAgICAvLyBDYWxjdWxhdGUgbmV3IFJPSSBiYXNlZCBvbiBjdXJyZW50IFRIL3MgYW1vdW50XG4gICAgICAgIGNvbnN0IG5ld1JPSSA9IGF3YWl0IGNhbGN1bGF0ZUR5bmFtaWNST0kodW5pdC50aHNBbW91bnQpO1xuXG4gICAgICAgIC8vIFVwZGF0ZSB0aGUgbWluaW5nIHVuaXQgd2l0aCBuZXcgUk9JXG4gICAgICAgIGF3YWl0IHByaXNtYS5taW5pbmdVbml0LnVwZGF0ZSh7XG4gICAgICAgICAgd2hlcmU6IHsgaWQ6IHVuaXQuaWQgfSxcbiAgICAgICAgICBkYXRhOiB7IGRhaWx5Uk9JOiBuZXdST0kgfSxcbiAgICAgICAgfSk7XG5cbiAgICAgICAgdXBkYXRlUmVzdWx0cy5wdXNoKHtcbiAgICAgICAgICB1bml0SWQ6IHVuaXQuaWQsXG4gICAgICAgICAgdXNlcklkOiB1bml0LnVzZXJJZCxcbiAgICAgICAgICB0aHNBbW91bnQ6IHVuaXQudGhzQW1vdW50LFxuICAgICAgICAgIG9sZFJPSTogdW5pdC5kYWlseVJPSSxcbiAgICAgICAgICBuZXdST0ksXG4gICAgICAgIH0pO1xuXG4gICAgICAgIGNvbnNvbGUubG9nKGBVcGRhdGVkIHVuaXQgJHt1bml0LmlkfTogJHt1bml0LmRhaWx5Uk9JfSUgLT4gJHtuZXdST0l9JWApO1xuICAgICAgfSBjYXRjaCAodW5pdEVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoYEVycm9yIHVwZGF0aW5nIHVuaXQgJHt1bml0LmlkfTpgLCB1bml0RXJyb3IpO1xuICAgICAgfVxuICAgIH1cblxuICAgIC8vIExvZyB0aGUgdXBkYXRlIHByb2Nlc3NcbiAgICBhd2FpdCBzeXN0ZW1Mb2dEYi5jcmVhdGUoe1xuICAgICAgYWN0aW9uOiAnTUlOSU5HX1VOSVRTX1JPSV9VUERBVEVEJyxcbiAgICAgIGRldGFpbHM6IHtcbiAgICAgICAgdW5pdHNVcGRhdGVkOiB1cGRhdGVSZXN1bHRzLmxlbmd0aCxcbiAgICAgICAgdG90YWxVbml0czogYWN0aXZlTWluaW5nVW5pdHMubGVuZ3RoLFxuICAgICAgICB1cGRhdGVSZXN1bHRzLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgIH0sXG4gICAgfSk7XG5cbiAgICBjb25zb2xlLmxvZyhgU3VjY2Vzc2Z1bGx5IHVwZGF0ZWQgJHt1cGRhdGVSZXN1bHRzLmxlbmd0aH0gbWluaW5nIHVuaXRzIHdpdGggbmV3IFJPSSBjb25maWd1cmF0aW9uYCk7XG5cbiAgICByZXR1cm4ge1xuICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgIHVuaXRzVXBkYXRlZDogdXBkYXRlUmVzdWx0cy5sZW5ndGgsXG4gICAgICB0b3RhbFVuaXRzOiBhY3RpdmVNaW5pbmdVbml0cy5sZW5ndGgsXG4gICAgICB1cGRhdGVSZXN1bHRzLFxuICAgIH07XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgdXBkYXRpbmcgZXhpc3RpbmcgbWluaW5nIHVuaXRzIFJPSTonLCBlcnJvcik7XG4gICAgdGhyb3cgZXJyb3I7XG4gIH1cbn1cblxuXG5cbi8vIENhbGN1bGF0ZSBkYWlseSBST0kgZm9yIGFsbCBhY3RpdmUgbWluaW5nIHVuaXRzIHVzaW5nIEZJRk8gYWxsb2NhdGlvblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGNhbGN1bGF0ZURhaWx5Uk9JKCkge1xuICB0cnkge1xuICAgIGNvbnNvbGUubG9nKCdTdGFydGluZyBkYWlseSBST0kgY2FsY3VsYXRpb24gd2l0aCBGSUZPIGFsbG9jYXRpb24uLi4nKTtcblxuICAgIC8vIEdldCBhbGwgdXNlcnMgd2l0aCBhY3RpdmUgbWluaW5nIHVuaXRzXG4gICAgY29uc3QgdXNlcnNXaXRoTWluaW5nVW5pdHMgPSBhd2FpdCBwcmlzbWEudXNlci5maW5kTWFueSh7XG4gICAgICB3aGVyZToge1xuICAgICAgICBtaW5pbmdVbml0czoge1xuICAgICAgICAgIHNvbWU6IHtcbiAgICAgICAgICAgIHN0YXR1czogJ0FDVElWRScsXG4gICAgICAgICAgICBleHBpcnlEYXRlOiB7XG4gICAgICAgICAgICAgIGd0OiBuZXcgRGF0ZSgpLFxuICAgICAgICAgICAgfSxcbiAgICAgICAgICB9LFxuICAgICAgICB9LFxuICAgICAgfSxcbiAgICAgIGluY2x1ZGU6IHtcbiAgICAgICAgbWluaW5nVW5pdHM6IHtcbiAgICAgICAgICB3aGVyZToge1xuICAgICAgICAgICAgc3RhdHVzOiAnQUNUSVZFJyxcbiAgICAgICAgICAgIGV4cGlyeURhdGU6IHtcbiAgICAgICAgICAgICAgZ3Q6IG5ldyBEYXRlKCksXG4gICAgICAgICAgICB9LFxuICAgICAgICAgIH0sXG4gICAgICAgIH0sXG4gICAgICB9LFxuICAgIH0pO1xuXG4gICAgY29uc29sZS5sb2coYEZvdW5kICR7dXNlcnNXaXRoTWluaW5nVW5pdHMubGVuZ3RofSB1c2VycyB3aXRoIGFjdGl2ZSBtaW5pbmcgdW5pdHNgKTtcblxuICAgIGNvbnN0IHJlc3VsdHMgPSBbXTtcblxuICAgIGZvciAoY29uc3QgdXNlciBvZiB1c2Vyc1dpdGhNaW5pbmdVbml0cykge1xuICAgICAgdHJ5IHtcbiAgICAgICAgLy8gQ2FsY3VsYXRlIHRvdGFsIGRhaWx5IGVhcm5pbmdzIGZvciB0aGlzIHVzZXJcbiAgICAgICAgbGV0IHRvdGFsRGFpbHlFYXJuaW5ncyA9IDA7XG4gICAgICAgIGNvbnN0IHVuaXRFYXJuaW5ncyA9IFtdO1xuXG4gICAgICAgIGZvciAoY29uc3QgdW5pdCBvZiB1c2VyLm1pbmluZ1VuaXRzKSB7XG4gICAgICAgICAgY29uc3QgZGFpbHlFYXJuaW5ncyA9ICh1bml0LmludmVzdG1lbnRBbW91bnQgKiB1bml0LmRhaWx5Uk9JKSAvIDEwMDtcbiAgICAgICAgICB0b3RhbERhaWx5RWFybmluZ3MgKz0gZGFpbHlFYXJuaW5ncztcbiAgICAgICAgICB1bml0RWFybmluZ3MucHVzaCh7XG4gICAgICAgICAgICB1bml0SWQ6IHVuaXQuaWQsXG4gICAgICAgICAgICB0aHNBbW91bnQ6IHVuaXQudGhzQW1vdW50LFxuICAgICAgICAgICAgZGFpbHlFYXJuaW5ncyxcbiAgICAgICAgICB9KTtcbiAgICAgICAgfVxuXG4gICAgICAgIGlmICh0b3RhbERhaWx5RWFybmluZ3MgPiAwKSB7XG4gICAgICAgICAgLy8gQ3JlYXRlIHRyYW5zYWN0aW9uIGZpcnN0XG4gICAgICAgICAgY29uc3QgdHJhbnNhY3Rpb24gPSBhd2FpdCB0cmFuc2FjdGlvbkRiLmNyZWF0ZSh7XG4gICAgICAgICAgICB1c2VySWQ6IHVzZXIuaWQsXG4gICAgICAgICAgICB0eXBlOiAnTUlOSU5HX0VBUk5JTkdTJyxcbiAgICAgICAgICAgIGFtb3VudDogdG90YWxEYWlseUVhcm5pbmdzLFxuICAgICAgICAgICAgZGVzY3JpcHRpb246IGBEYWlseSBtaW5pbmcgZWFybmluZ3MgLSBUb3RhbDogJHt1bml0RWFybmluZ3MubWFwKHUgPT4gYCR7dS50aHNBbW91bnR9IFRIL3NgKS5qb2luKCcsICcpfWAsXG4gICAgICAgICAgICBzdGF0dXM6ICdQRU5ESU5HJyxcbiAgICAgICAgICB9KTtcblxuICAgICAgICAgIC8vIEFsbG9jYXRlIGVhcm5pbmdzIHRvIG1pbmluZyB1bml0cyB1c2luZyBGSUZPIGxvZ2ljXG4gICAgICAgICAgY29uc3QgYWxsb2NhdGlvbnMgPSBhd2FpdCBhbGxvY2F0ZUVhcm5pbmdzVG9Vbml0cyhcbiAgICAgICAgICAgIHVzZXIuaWQsXG4gICAgICAgICAgICB0b3RhbERhaWx5RWFybmluZ3MsXG4gICAgICAgICAgICAnTUlOSU5HX0VBUk5JTkdTJyxcbiAgICAgICAgICAgIHRyYW5zYWN0aW9uLmlkLFxuICAgICAgICAgICAgJ0RhaWx5IG1pbmluZyBST0kgZWFybmluZ3MnXG4gICAgICAgICAgKTtcblxuICAgICAgICAgIHJlc3VsdHMucHVzaCh7XG4gICAgICAgICAgICB1c2VySWQ6IHVzZXIuaWQsXG4gICAgICAgICAgICB0b3RhbEVhcm5pbmdzOiB0b3RhbERhaWx5RWFybmluZ3MsXG4gICAgICAgICAgICBhbGxvY2F0aW9ucyxcbiAgICAgICAgICAgIHVuaXRzUHJvY2Vzc2VkOiB1c2VyLm1pbmluZ1VuaXRzLmxlbmd0aCxcbiAgICAgICAgICB9KTtcblxuICAgICAgICAgIGNvbnNvbGUubG9nKGBBbGxvY2F0ZWQgJHt0b3RhbERhaWx5RWFybmluZ3N9IG1pbmluZyBlYXJuaW5ncyB0byAke2FsbG9jYXRpb25zLmxlbmd0aH0gdW5pdHMgZm9yIHVzZXIgJHt1c2VyLmlkfWApO1xuICAgICAgICB9XG5cbiAgICAgIH0gY2F0Y2ggKHVzZXJFcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKGBFcnJvciBwcm9jZXNzaW5nIG1pbmluZyBlYXJuaW5ncyBmb3IgdXNlciAke3VzZXIuaWR9OmAsIHVzZXJFcnJvcik7XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gTG9nIHRoZSBkYWlseSBST0kgY2FsY3VsYXRpb25cbiAgICBhd2FpdCBzeXN0ZW1Mb2dEYi5jcmVhdGUoe1xuICAgICAgYWN0aW9uOiAnREFJTFlfUk9JX0NBTENVTEFURUQnLFxuICAgICAgZGV0YWlsczoge1xuICAgICAgICB1c2Vyc1Byb2Nlc3NlZDogcmVzdWx0cy5sZW5ndGgsXG4gICAgICAgIHRvdGFsRWFybmluZ3M6IHJlc3VsdHMucmVkdWNlKChzdW0sIHIpID0+IHN1bSArIHIudG90YWxFYXJuaW5ncywgMCksXG4gICAgICAgIHRvdGFsQWxsb2NhdGlvbnM6IHJlc3VsdHMucmVkdWNlKChzdW0sIHIpID0+IHN1bSArIHIuYWxsb2NhdGlvbnMubGVuZ3RoLCAwKSxcbiAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICB9LFxuICAgIH0pO1xuXG4gICAgY29uc29sZS5sb2coYERhaWx5IFJPSSBjYWxjdWxhdGlvbiBjb21wbGV0ZWQuIFByb2Nlc3NlZCAke3Jlc3VsdHMubGVuZ3RofSB1c2VycyB3aXRoIEZJRk8gYWxsb2NhdGlvbi5gKTtcbiAgICByZXR1cm4gcmVzdWx0cztcblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0RhaWx5IFJPSSBjYWxjdWxhdGlvbiBlcnJvcjonLCBlcnJvcik7XG4gICAgdGhyb3cgZXJyb3I7XG4gIH1cbn1cblxuLy8gUHJvY2VzcyB3ZWVrbHkgZWFybmluZ3MgZGlzdHJpYnV0aW9uIChTYXR1cmRheSAxNTowMCBVVEMpXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gcHJvY2Vzc1dlZWtseUVhcm5pbmdzKCkge1xuICB0cnkge1xuICAgIGNvbnNvbGUubG9nKCdTdGFydGluZyB3ZWVrbHkgZWFybmluZ3MgZGlzdHJpYnV0aW9uLi4uJyk7XG5cbiAgICAvLyBHZXQgYWxsIHBlbmRpbmcgbWluaW5nIGVhcm5pbmdzXG4gICAgY29uc3QgcGVuZGluZ0Vhcm5pbmdzID0gYXdhaXQgcHJpc21hLnRyYW5zYWN0aW9uLmZpbmRNYW55KHtcbiAgICAgIHdoZXJlOiB7XG4gICAgICAgIHR5cGU6ICdNSU5JTkdfRUFSTklOR1MnLFxuICAgICAgICBzdGF0dXM6ICdQRU5ESU5HJyxcbiAgICAgIH0sXG4gICAgICBpbmNsdWRlOiB7XG4gICAgICAgIHVzZXI6IHRydWUsXG4gICAgICB9LFxuICAgIH0pO1xuXG4gICAgY29uc29sZS5sb2coYEZvdW5kICR7cGVuZGluZ0Vhcm5pbmdzLmxlbmd0aH0gcGVuZGluZyBlYXJuaW5ncyB0cmFuc2FjdGlvbnNgKTtcblxuICAgIGNvbnN0IHVzZXJFYXJuaW5ncyA9IG5ldyBNYXA8c3RyaW5nLCBudW1iZXI+KCk7XG5cbiAgICAvLyBHcm91cCBlYXJuaW5ncyBieSB1c2VyXG4gICAgZm9yIChjb25zdCB0cmFuc2FjdGlvbiBvZiBwZW5kaW5nRWFybmluZ3MpIHtcbiAgICAgIGNvbnN0IGN1cnJlbnRUb3RhbCA9IHVzZXJFYXJuaW5ncy5nZXQodHJhbnNhY3Rpb24udXNlcklkKSB8fCAwO1xuICAgICAgdXNlckVhcm5pbmdzLnNldCh0cmFuc2FjdGlvbi51c2VySWQsIGN1cnJlbnRUb3RhbCArIHRyYW5zYWN0aW9uLmFtb3VudCk7XG4gICAgfVxuXG4gICAgY29uc3QgcmVzdWx0cyA9IFtdO1xuXG4gICAgLy8gUHJvY2VzcyBlYWNoIHVzZXIncyBlYXJuaW5nc1xuICAgIGZvciAoY29uc3QgW3VzZXJJZCwgdG90YWxFYXJuaW5nc10gb2YgdXNlckVhcm5pbmdzKSB7XG4gICAgICB0cnkge1xuICAgICAgICAvLyBNYXJrIGFsbCBwZW5kaW5nIHRyYW5zYWN0aW9ucyBhcyBjb21wbGV0ZWRcbiAgICAgICAgYXdhaXQgcHJpc21hLnRyYW5zYWN0aW9uLnVwZGF0ZU1hbnkoe1xuICAgICAgICAgIHdoZXJlOiB7XG4gICAgICAgICAgICB1c2VySWQsXG4gICAgICAgICAgICB0eXBlOiAnTUlOSU5HX0VBUk5JTkdTJyxcbiAgICAgICAgICAgIHN0YXR1czogJ1BFTkRJTkcnLFxuICAgICAgICAgIH0sXG4gICAgICAgICAgZGF0YToge1xuICAgICAgICAgICAgc3RhdHVzOiAnQ09NUExFVEVEJyxcbiAgICAgICAgICB9LFxuICAgICAgICB9KTtcblxuICAgICAgICByZXN1bHRzLnB1c2goe1xuICAgICAgICAgIHVzZXJJZCxcbiAgICAgICAgICB0b3RhbEVhcm5pbmdzLFxuICAgICAgICB9KTtcblxuICAgICAgfSBjYXRjaCAodXNlckVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoYEVycm9yIHByb2Nlc3NpbmcgZWFybmluZ3MgZm9yIHVzZXIgJHt1c2VySWR9OmAsIHVzZXJFcnJvcik7XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gTG9nIHRoZSB3ZWVrbHkgZGlzdHJpYnV0aW9uXG4gICAgYXdhaXQgc3lzdGVtTG9nRGIuY3JlYXRlKHtcbiAgICAgIGFjdGlvbjogJ1dFRUtMWV9FQVJOSU5HU19ESVNUUklCVVRFRCcsXG4gICAgICBkZXRhaWxzOiB7XG4gICAgICAgIHVzZXJzUHJvY2Vzc2VkOiByZXN1bHRzLmxlbmd0aCxcbiAgICAgICAgdG90YWxEaXN0cmlidXRlZDogcmVzdWx0cy5yZWR1Y2UoKHN1bSwgcikgPT4gc3VtICsgci50b3RhbEVhcm5pbmdzLCAwKSxcbiAgICAgICAgdHJhbnNhY3Rpb25zUHJvY2Vzc2VkOiBwZW5kaW5nRWFybmluZ3MubGVuZ3RoLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgIH0sXG4gICAgfSk7XG5cbiAgICBjb25zb2xlLmxvZyhgV2Vla2x5IGVhcm5pbmdzIGRpc3RyaWJ1dGlvbiBjb21wbGV0ZWQuIFByb2Nlc3NlZCAke3Jlc3VsdHMubGVuZ3RofSB1c2Vycy5gKTtcbiAgICByZXR1cm4gcmVzdWx0cztcblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ1dlZWtseSBlYXJuaW5ncyBkaXN0cmlidXRpb24gZXJyb3I6JywgZXJyb3IpO1xuICAgIHRocm93IGVycm9yO1xuICB9XG59XG5cbi8vIENoZWNrIGFuZCBleHBpcmUgbWluaW5nIHVuaXRzIHRoYXQgaGF2ZSByZWFjaGVkIDI0IG1vbnRoc1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGV4cGlyZU9sZE1pbmluZ1VuaXRzKCkge1xuICB0cnkge1xuICAgIGNvbnNvbGUubG9nKCdDaGVja2luZyBmb3IgZXhwaXJlZCBtaW5pbmcgdW5pdHMuLi4nKTtcblxuICAgIGNvbnN0IGV4cGlyZWRVbml0cyA9IGF3YWl0IHByaXNtYS5taW5pbmdVbml0LmZpbmRNYW55KHtcbiAgICAgIHdoZXJlOiB7XG4gICAgICAgIHN0YXR1czogJ0FDVElWRScsXG4gICAgICAgIGV4cGlyeURhdGU6IHtcbiAgICAgICAgICBsdGU6IG5ldyBEYXRlKCksXG4gICAgICAgIH0sXG4gICAgICB9LFxuICAgICAgaW5jbHVkZToge1xuICAgICAgICB1c2VyOiB7XG4gICAgICAgICAgc2VsZWN0OiB7XG4gICAgICAgICAgICBlbWFpbDogdHJ1ZSxcbiAgICAgICAgICAgIGZpcnN0TmFtZTogdHJ1ZSxcbiAgICAgICAgICAgIGxhc3ROYW1lOiB0cnVlLFxuICAgICAgICAgIH0sXG4gICAgICAgIH0sXG4gICAgICB9LFxuICAgIH0pO1xuXG4gICAgY29uc29sZS5sb2coYEZvdW5kICR7ZXhwaXJlZFVuaXRzLmxlbmd0aH0gdW5pdHMgdG8gZXhwaXJlYCk7XG5cbiAgICBmb3IgKGNvbnN0IHVuaXQgb2YgZXhwaXJlZFVuaXRzKSB7XG4gICAgICBhd2FpdCBtaW5pbmdVbml0RGIuZXhwaXJlVW5pdCh1bml0LmlkKTtcblxuICAgICAgLy8gTm90ZTogVXNlciBhY3RpdmUgc3RhdHVzIGlzIG5vdyBjb21wdXRlZCBkeW5hbWljYWxseVxuXG4gICAgICBhd2FpdCBzeXN0ZW1Mb2dEYi5jcmVhdGUoe1xuICAgICAgICBhY3Rpb246ICdNSU5JTkdfVU5JVF9FWFBJUkVEJyxcbiAgICAgICAgdXNlcklkOiB1bml0LnVzZXJJZCxcbiAgICAgICAgZGV0YWlsczoge1xuICAgICAgICAgIG1pbmluZ1VuaXRJZDogdW5pdC5pZCxcbiAgICAgICAgICByZWFzb246ICcyNF9tb250aHNfcmVhY2hlZCcsXG4gICAgICAgICAgdG90YWxFYXJuZWQ6IHVuaXQudG90YWxFYXJuZWQsXG4gICAgICAgICAgaW52ZXN0bWVudEFtb3VudDogdW5pdC5pbnZlc3RtZW50QW1vdW50LFxuICAgICAgICB9LFxuICAgICAgfSk7XG5cbiAgICAgIC8vIFNlbmQgZW1haWwgbm90aWZpY2F0aW9uXG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCB7IGVtYWlsTm90aWZpY2F0aW9uU2VydmljZSB9ID0gYXdhaXQgaW1wb3J0KCdAL2xpYi9lbWFpbE5vdGlmaWNhdGlvblNlcnZpY2UnKTtcbiAgICAgICAgY29uc3QgdG90YWxFYXJuaW5ncyA9IHVuaXQubWluaW5nRWFybmluZ3MgKyB1bml0LnJlZmVycmFsRWFybmluZ3MgKyB1bml0LmJpbmFyeUVhcm5pbmdzO1xuICAgICAgICBhd2FpdCBlbWFpbE5vdGlmaWNhdGlvblNlcnZpY2Uuc2VuZE1pbmluZ1VuaXRFeHBpcnlOb3RpZmljYXRpb24oe1xuICAgICAgICAgIHVzZXJJZDogdW5pdC51c2VySWQsXG4gICAgICAgICAgZW1haWw6IHVuaXQudXNlci5lbWFpbCxcbiAgICAgICAgICBmaXJzdE5hbWU6IHVuaXQudXNlci5maXJzdE5hbWUsXG4gICAgICAgICAgbGFzdE5hbWU6IHVuaXQudXNlci5sYXN0TmFtZSxcbiAgICAgICAgICB0aHNBbW91bnQ6IHVuaXQudGhzQW1vdW50LFxuICAgICAgICAgIGludmVzdG1lbnRBbW91bnQ6IHVuaXQuaW52ZXN0bWVudEFtb3VudCxcbiAgICAgICAgICB0b3RhbEVhcm5lZDogdG90YWxFYXJuaW5ncyxcbiAgICAgICAgICBwdXJjaGFzZURhdGU6IHVuaXQuY3JlYXRlZEF0LnRvSVNPU3RyaW5nKCksXG4gICAgICAgICAgZXhwaXJ5RGF0ZTogdW5pdC5leHBpcnlEYXRlLnRvSVNPU3RyaW5nKCksXG4gICAgICAgICAgZXhwaXJ5UmVhc29uOiAnVElNRV9MSU1JVCcsXG4gICAgICAgIH0pO1xuICAgICAgfSBjYXRjaCAoZW1haWxFcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gc2VuZCBtaW5pbmcgdW5pdCBleHBpcnkgZW1haWw6JywgZW1haWxFcnJvcik7XG4gICAgICAgIC8vIERvbid0IGZhaWwgdGhlIGV4cGlyeSBpZiBlbWFpbCBmYWlsc1xuICAgICAgfVxuICAgIH1cblxuICAgIHJldHVybiBleHBpcmVkVW5pdHMubGVuZ3RoO1xuXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignTWluaW5nIHVuaXQgZXhwaXJ5IGNoZWNrIGVycm9yOicsIGVycm9yKTtcbiAgICB0aHJvdyBlcnJvcjtcbiAgfVxufVxuXG4vLyBHZXQgbWluaW5nIHN0YXRpc3RpY3NcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBnZXRNaW5pbmdTdGF0cygpIHtcbiAgdHJ5IHtcbiAgICBjb25zdCBzdGF0cyA9IGF3YWl0IHByaXNtYS4kdHJhbnNhY3Rpb24oW1xuICAgICAgLy8gVG90YWwgVEgvcyBzb2xkXG4gICAgICBwcmlzbWEubWluaW5nVW5pdC5hZ2dyZWdhdGUoe1xuICAgICAgICBfc3VtOiB7XG4gICAgICAgICAgdGhzQW1vdW50OiB0cnVlLFxuICAgICAgICB9LFxuICAgICAgfSksXG4gICAgICBcbiAgICAgIC8vIEFjdGl2ZSBUSC9zXG4gICAgICBwcmlzbWEubWluaW5nVW5pdC5hZ2dyZWdhdGUoe1xuICAgICAgICB3aGVyZToge1xuICAgICAgICAgIHN0YXR1czogJ0FDVElWRScsXG4gICAgICAgIH0sXG4gICAgICAgIF9zdW06IHtcbiAgICAgICAgICB0aHNBbW91bnQ6IHRydWUsXG4gICAgICAgIH0sXG4gICAgICB9KSxcbiAgICAgIFxuICAgICAgLy8gVG90YWwgaW52ZXN0bWVudFxuICAgICAgcHJpc21hLm1pbmluZ1VuaXQuYWdncmVnYXRlKHtcbiAgICAgICAgX3N1bToge1xuICAgICAgICAgIGludmVzdG1lbnRBbW91bnQ6IHRydWUsXG4gICAgICAgIH0sXG4gICAgICB9KSxcbiAgICAgIFxuICAgICAgLy8gVG90YWwgZWFybmluZ3MgZGlzdHJpYnV0ZWRcbiAgICAgIHByaXNtYS50cmFuc2FjdGlvbi5hZ2dyZWdhdGUoe1xuICAgICAgICB3aGVyZToge1xuICAgICAgICAgIHR5cGU6ICdNSU5JTkdfRUFSTklOR1MnLFxuICAgICAgICAgIHN0YXR1czogJ0NPTVBMRVRFRCcsXG4gICAgICAgIH0sXG4gICAgICAgIF9zdW06IHtcbiAgICAgICAgICBhbW91bnQ6IHRydWUsXG4gICAgICAgIH0sXG4gICAgICB9KSxcbiAgICAgIFxuICAgICAgLy8gQWN0aXZlIG1pbmluZyB1bml0cyBjb3VudFxuICAgICAgcHJpc21hLm1pbmluZ1VuaXQuY291bnQoe1xuICAgICAgICB3aGVyZToge1xuICAgICAgICAgIHN0YXR1czogJ0FDVElWRScsXG4gICAgICAgIH0sXG4gICAgICB9KSxcbiAgICAgIFxuICAgICAgLy8gVG90YWwgbWluaW5nIHVuaXRzIGNvdW50XG4gICAgICBwcmlzbWEubWluaW5nVW5pdC5jb3VudCgpLFxuICAgIF0pO1xuXG4gICAgcmV0dXJuIHtcbiAgICAgIHRvdGFsVEhTU29sZDogc3RhdHNbMF0uX3N1bS50aHNBbW91bnQgfHwgMCxcbiAgICAgIGFjdGl2ZVRIUzogc3RhdHNbMV0uX3N1bS50aHNBbW91bnQgfHwgMCxcbiAgICAgIHRvdGFsSW52ZXN0bWVudDogc3RhdHNbMl0uX3N1bS5pbnZlc3RtZW50QW1vdW50IHx8IDAsXG4gICAgICB0b3RhbEVhcm5pbmdzRGlzdHJpYnV0ZWQ6IHN0YXRzWzNdLl9zdW0uYW1vdW50IHx8IDAsXG4gICAgICBhY3RpdmVNaW5pbmdVbml0czogc3RhdHNbNF0sXG4gICAgICB0b3RhbE1pbmluZ1VuaXRzOiBzdGF0c1s1XSxcbiAgICB9O1xuXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignTWluaW5nIHN0YXRzIGVycm9yOicsIGVycm9yKTtcbiAgICB0aHJvdyBlcnJvcjtcbiAgfVxufVxuXG4vLyBDYWxjdWxhdGUgdXNlcidzIGVzdGltYXRlZCBlYXJuaW5nc1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGNhbGN1bGF0ZUVzdGltYXRlZEVhcm5pbmdzKHVzZXJJZDogc3RyaW5nKSB7XG4gIHRyeSB7XG4gICAgY29uc3QgYWN0aXZlTWluaW5nVW5pdHMgPSBhd2FpdCBtaW5pbmdVbml0RGIuZmluZEFjdGl2ZUJ5VXNlcklkKHVzZXJJZCk7XG4gICAgXG4gICAgaWYgKGFjdGl2ZU1pbmluZ1VuaXRzLmxlbmd0aCA9PT0gMCkge1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgbmV4dDdEYXlzOiAwLFxuICAgICAgICBuZXh0MzBEYXlzOiAwLFxuICAgICAgICBuZXh0MzY1RGF5czogMCxcbiAgICAgICAgbmV4dDJZZWFyczogMCxcbiAgICAgIH07XG4gICAgfVxuXG4gICAgbGV0IHRvdGFsRGFpbHkgPSAwO1xuICAgIFxuICAgIGZvciAoY29uc3QgdW5pdCBvZiBhY3RpdmVNaW5pbmdVbml0cykge1xuICAgICAgY29uc3QgZGFpbHlFYXJuaW5ncyA9ICh1bml0LmludmVzdG1lbnRBbW91bnQgKiB1bml0LmRhaWx5Uk9JKSAvIDEwMDtcbiAgICAgIGNvbnN0IG1heEVhcm5pbmdzID0gdW5pdC5pbnZlc3RtZW50QW1vdW50ICogNTtcbiAgICAgIGNvbnN0IHJlbWFpbmluZ0Vhcm5pbmdzID0gbWF4RWFybmluZ3MgLSB1bml0LnRvdGFsRWFybmVkO1xuICAgICAgXG4gICAgICAvLyBVc2UgdGhlIGxvd2VyIG9mIGRhaWx5IGVhcm5pbmdzIG9yIHJlbWFpbmluZyBlYXJuaW5nc1xuICAgICAgdG90YWxEYWlseSArPSBNYXRoLm1pbihkYWlseUVhcm5pbmdzLCByZW1haW5pbmdFYXJuaW5ncyk7XG4gICAgfVxuXG4gICAgcmV0dXJuIHtcbiAgICAgIG5leHQ3RGF5czogdG90YWxEYWlseSAqIDcsXG4gICAgICBuZXh0MzBEYXlzOiB0b3RhbERhaWx5ICogMzAsXG4gICAgICBuZXh0MzY1RGF5czogdG90YWxEYWlseSAqIDM2NSxcbiAgICAgIG5leHQyWWVhcnM6IHRvdGFsRGFpbHkgKiA3MzAsIC8vIDIgeWVhcnMgPSA3MzAgZGF5c1xuICAgIH07XG5cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFc3RpbWF0ZWQgZWFybmluZ3MgY2FsY3VsYXRpb24gZXJyb3I6JywgZXJyb3IpO1xuICAgIHRocm93IGVycm9yO1xuICB9XG59XG4iXSwibmFtZXMiOlsicHJpc21hIiwibWluaW5nVW5pdERiIiwidHJhbnNhY3Rpb25EYiIsInN5c3RlbUxvZ0RiIiwiYWRtaW5TZXR0aW5nc0RiIiwiYWxsb2NhdGVFYXJuaW5nc1RvVW5pdHMiLCJjYWxjdWxhdGVEeW5hbWljUk9JIiwidGhzQW1vdW50IiwiZWFybmluZ3NSYW5nZXNTdHIiLCJnZXQiLCJlYXJuaW5nc1JhbmdlcyIsIkpTT04iLCJwYXJzZSIsInBhcnNlRXJyb3IiLCJjb25zb2xlIiwiZXJyb3IiLCJBcnJheSIsImlzQXJyYXkiLCJtaW5USFMiLCJtYXhUSFMiLCJkYWlseVJldHVybk1pbiIsImRhaWx5UmV0dXJuTWF4IiwibW9udGhseVJldHVybk1pbiIsIm1vbnRobHlSZXR1cm5NYXgiLCJhcHBsaWNhYmxlUmFuZ2UiLCJmaW5kIiwicmFuZ2UiLCJtaW5ST0kiLCJtYXhST0kiLCJoaWdoZXN0UmFuZ2UiLCJsZW5ndGgiLCJyYW5kb21ST0kiLCJNYXRoIiwicmFuZG9tIiwicm91bmQiLCJ2YWxpZGF0ZU1vbnRobHlSZXR1cm4iLCJkYWlseVJPSSIsIm1vbnRobHlNaW4iLCJtb250aGx5TWF4IiwibW9udGhseVJldHVybiIsImdldE1vbnRobHlSZXR1cm5MaW1pdHMiLCJtaW4iLCJtYXgiLCJjYWxjdWxhdGU3RGF5QXZlcmFnZVJPSSIsInRvdGFsUk9JIiwiZGF5cyIsImRheSIsInVwZGF0ZUV4aXN0aW5nTWluaW5nVW5pdHNST0kiLCJsb2ciLCJhY3RpdmVNaW5pbmdVbml0cyIsIm1pbmluZ1VuaXQiLCJmaW5kTWFueSIsIndoZXJlIiwic3RhdHVzIiwiZXhwaXJ5RGF0ZSIsImd0IiwiRGF0ZSIsInVwZGF0ZVJlc3VsdHMiLCJ1bml0IiwibmV3Uk9JIiwidXBkYXRlIiwiaWQiLCJkYXRhIiwicHVzaCIsInVuaXRJZCIsInVzZXJJZCIsIm9sZFJPSSIsInVuaXRFcnJvciIsImNyZWF0ZSIsImFjdGlvbiIsImRldGFpbHMiLCJ1bml0c1VwZGF0ZWQiLCJ0b3RhbFVuaXRzIiwidGltZXN0YW1wIiwidG9JU09TdHJpbmciLCJzdWNjZXNzIiwiY2FsY3VsYXRlRGFpbHlST0kiLCJ1c2Vyc1dpdGhNaW5pbmdVbml0cyIsInVzZXIiLCJtaW5pbmdVbml0cyIsInNvbWUiLCJpbmNsdWRlIiwicmVzdWx0cyIsInRvdGFsRGFpbHlFYXJuaW5ncyIsInVuaXRFYXJuaW5ncyIsImRhaWx5RWFybmluZ3MiLCJpbnZlc3RtZW50QW1vdW50IiwidHJhbnNhY3Rpb24iLCJ0eXBlIiwiYW1vdW50IiwiZGVzY3JpcHRpb24iLCJtYXAiLCJ1Iiwiam9pbiIsImFsbG9jYXRpb25zIiwidG90YWxFYXJuaW5ncyIsInVuaXRzUHJvY2Vzc2VkIiwidXNlckVycm9yIiwidXNlcnNQcm9jZXNzZWQiLCJyZWR1Y2UiLCJzdW0iLCJyIiwidG90YWxBbGxvY2F0aW9ucyIsInByb2Nlc3NXZWVrbHlFYXJuaW5ncyIsInBlbmRpbmdFYXJuaW5ncyIsInVzZXJFYXJuaW5ncyIsIk1hcCIsImN1cnJlbnRUb3RhbCIsInNldCIsInVwZGF0ZU1hbnkiLCJ0b3RhbERpc3RyaWJ1dGVkIiwidHJhbnNhY3Rpb25zUHJvY2Vzc2VkIiwiZXhwaXJlT2xkTWluaW5nVW5pdHMiLCJleHBpcmVkVW5pdHMiLCJsdGUiLCJzZWxlY3QiLCJlbWFpbCIsImZpcnN0TmFtZSIsImxhc3ROYW1lIiwiZXhwaXJlVW5pdCIsIm1pbmluZ1VuaXRJZCIsInJlYXNvbiIsInRvdGFsRWFybmVkIiwiZW1haWxOb3RpZmljYXRpb25TZXJ2aWNlIiwibWluaW5nRWFybmluZ3MiLCJyZWZlcnJhbEVhcm5pbmdzIiwiYmluYXJ5RWFybmluZ3MiLCJzZW5kTWluaW5nVW5pdEV4cGlyeU5vdGlmaWNhdGlvbiIsInB1cmNoYXNlRGF0ZSIsImNyZWF0ZWRBdCIsImV4cGlyeVJlYXNvbiIsImVtYWlsRXJyb3IiLCJnZXRNaW5pbmdTdGF0cyIsInN0YXRzIiwiJHRyYW5zYWN0aW9uIiwiYWdncmVnYXRlIiwiX3N1bSIsImNvdW50IiwidG90YWxUSFNTb2xkIiwiYWN0aXZlVEhTIiwidG90YWxJbnZlc3RtZW50IiwidG90YWxFYXJuaW5nc0Rpc3RyaWJ1dGVkIiwidG90YWxNaW5pbmdVbml0cyIsImNhbGN1bGF0ZUVzdGltYXRlZEVhcm5pbmdzIiwiZmluZEFjdGl2ZUJ5VXNlcklkIiwibmV4dDdEYXlzIiwibmV4dDMwRGF5cyIsIm5leHQzNjVEYXlzIiwibmV4dDJZZWFycyIsInRvdGFsRGFpbHkiLCJtYXhFYXJuaW5ncyIsInJlbWFpbmluZ0Vhcm5pbmdzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mining.ts\n");

/***/ })

};
;