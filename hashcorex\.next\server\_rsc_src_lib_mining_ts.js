"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_mining_ts";
exports.ids = ["_rsc_src_lib_mining_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/mining.ts":
/*!***************************!*\
  !*** ./src/lib/mining.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculate7DayAverageROI: () => (/* binding */ calculate7DayAverageROI),\n/* harmony export */   calculateDailyROI: () => (/* binding */ calculateDailyROI),\n/* harmony export */   calculateDynamicROI: () => (/* binding */ calculateDynamicROI),\n/* harmony export */   calculateEstimatedEarnings: () => (/* binding */ calculateEstimatedEarnings),\n/* harmony export */   expireOldMiningUnits: () => (/* binding */ expireOldMiningUnits),\n/* harmony export */   getMiningStats: () => (/* binding */ getMiningStats),\n/* harmony export */   getMonthlyReturnLimits: () => (/* binding */ getMonthlyReturnLimits),\n/* harmony export */   processWeeklyEarnings: () => (/* binding */ processWeeklyEarnings),\n/* harmony export */   updateExistingMiningUnitsROI: () => (/* binding */ updateExistingMiningUnitsROI),\n/* harmony export */   validateMonthlyReturn: () => (/* binding */ validateMonthlyReturn)\n/* harmony export */ });\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./database */ \"(rsc)/./src/lib/database.ts\");\n/* harmony import */ var _miningUnitEarnings__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./miningUnitEarnings */ \"(rsc)/./src/lib/miningUnitEarnings.ts\");\n\n\n\n// Note: User active status is now handled dynamically in the referral system\n// The isActive field in the database is only used for account access control\n// Mining activity status is computed on-demand for binary tree display\n// Calculate dynamic ROI based on TH/s amount and admin-configured ranges\nasync function calculateDynamicROI(thsAmount) {\n    try {\n        // Get earnings ranges from admin settings\n        const earningsRangesStr = await _database__WEBPACK_IMPORTED_MODULE_1__.adminSettingsDb.get('earningsRanges');\n        let earningsRanges;\n        if (earningsRangesStr) {\n            try {\n                earningsRanges = JSON.parse(earningsRangesStr);\n            } catch (parseError) {\n                console.error('Error parsing earnings ranges:', parseError);\n                earningsRanges = null;\n            }\n        }\n        // Fallback to default ranges if not configured\n        if (!earningsRanges || !Array.isArray(earningsRanges)) {\n            earningsRanges = [\n                {\n                    minTHS: 0,\n                    maxTHS: 10,\n                    dailyReturnMin: 0.3,\n                    dailyReturnMax: 0.5,\n                    monthlyReturnMin: 10.0,\n                    monthlyReturnMax: 15.0\n                },\n                {\n                    minTHS: 10,\n                    maxTHS: 50,\n                    dailyReturnMin: 0.4,\n                    dailyReturnMax: 0.6,\n                    monthlyReturnMin: 10.0,\n                    monthlyReturnMax: 15.0\n                },\n                {\n                    minTHS: 50,\n                    maxTHS: 999999,\n                    dailyReturnMin: 0.5,\n                    dailyReturnMax: 0.7,\n                    monthlyReturnMin: 10.0,\n                    monthlyReturnMax: 15.0\n                }\n            ];\n        }\n        // Find the appropriate range for the TH/s amount\n        const applicableRange = earningsRanges.find((range)=>thsAmount >= range.minTHS && thsAmount <= range.maxTHS);\n        let minROI;\n        let maxROI;\n        if (applicableRange) {\n            minROI = applicableRange.dailyReturnMin;\n            maxROI = applicableRange.dailyReturnMax;\n        } else {\n            // Fallback to highest range if no match found\n            const highestRange = earningsRanges[earningsRanges.length - 1];\n            minROI = highestRange.dailyReturnMin;\n            maxROI = highestRange.dailyReturnMax;\n        }\n        // Add randomization within the range\n        const randomROI = minROI + Math.random() * (maxROI - minROI);\n        // Round to 2 decimal places\n        return Math.round(randomROI * 100) / 100;\n    } catch (error) {\n        console.error('Error calculating dynamic ROI:', error);\n        // Fallback to default values based on TH/s amount\n        if (thsAmount >= 50) return 0.6;\n        if (thsAmount >= 10) return 0.5;\n        return 0.4;\n    }\n}\n// Validate monthly return doesn't exceed admin-configured limits for specific TH/s amount\nasync function validateMonthlyReturn(dailyROI, thsAmount) {\n    try {\n        // Get earnings ranges from admin settings\n        const earningsRangesStr = await _database__WEBPACK_IMPORTED_MODULE_1__.adminSettingsDb.get('earningsRanges');\n        let earningsRanges;\n        if (earningsRangesStr) {\n            try {\n                earningsRanges = JSON.parse(earningsRangesStr);\n            } catch (parseError) {\n                console.error('Error parsing earnings ranges:', parseError);\n                earningsRanges = null;\n            }\n        }\n        // Fallback to default ranges if not configured\n        if (!earningsRanges || !Array.isArray(earningsRanges)) {\n            earningsRanges = [\n                {\n                    minTHS: 0,\n                    maxTHS: 10,\n                    dailyReturnMin: 0.3,\n                    dailyReturnMax: 0.5,\n                    monthlyReturnMin: 10.0,\n                    monthlyReturnMax: 15.0\n                },\n                {\n                    minTHS: 10,\n                    maxTHS: 50,\n                    dailyReturnMin: 0.4,\n                    dailyReturnMax: 0.6,\n                    monthlyReturnMin: 10.0,\n                    monthlyReturnMax: 15.0\n                },\n                {\n                    minTHS: 50,\n                    maxTHS: 999999,\n                    dailyReturnMin: 0.5,\n                    dailyReturnMax: 0.7,\n                    monthlyReturnMin: 10.0,\n                    monthlyReturnMax: 15.0\n                }\n            ];\n        }\n        // Find the appropriate range for the TH/s amount\n        const applicableRange = earningsRanges.find((range)=>thsAmount >= range.minTHS && thsAmount <= range.maxTHS);\n        let monthlyMin = 10.0;\n        let monthlyMax = 15.0;\n        if (applicableRange) {\n            monthlyMin = applicableRange.monthlyReturnMin || 10.0;\n            monthlyMax = applicableRange.monthlyReturnMax || 15.0;\n        } else {\n            // Fallback to highest range if no match found\n            const highestRange = earningsRanges[earningsRanges.length - 1];\n            monthlyMin = highestRange.monthlyReturnMin || 10.0;\n            monthlyMax = highestRange.monthlyReturnMax || 15.0;\n        }\n        const monthlyReturn = dailyROI * 30; // Approximate monthly return\n        return monthlyReturn >= monthlyMin && monthlyReturn <= monthlyMax;\n    } catch (error) {\n        console.error('Error validating monthly return:', error);\n        // Fallback to default 10-15% range\n        const monthlyReturn = dailyROI * 30;\n        return monthlyReturn >= 10 && monthlyReturn <= 15;\n    }\n}\n// Get monthly return limits for a specific TH/s amount\nasync function getMonthlyReturnLimits(thsAmount) {\n    try {\n        // Get earnings ranges from admin settings\n        const earningsRangesStr = await _database__WEBPACK_IMPORTED_MODULE_1__.adminSettingsDb.get('earningsRanges');\n        let earningsRanges;\n        if (earningsRangesStr) {\n            try {\n                earningsRanges = JSON.parse(earningsRangesStr);\n            } catch (parseError) {\n                console.error('Error parsing earnings ranges:', parseError);\n                earningsRanges = null;\n            }\n        }\n        // Fallback to default ranges if not configured\n        if (!earningsRanges || !Array.isArray(earningsRanges)) {\n            earningsRanges = [\n                {\n                    minTHS: 0,\n                    maxTHS: 10,\n                    dailyReturnMin: 0.3,\n                    dailyReturnMax: 0.5,\n                    monthlyReturnMin: 10.0,\n                    monthlyReturnMax: 15.0\n                },\n                {\n                    minTHS: 10,\n                    maxTHS: 50,\n                    dailyReturnMin: 0.4,\n                    dailyReturnMax: 0.6,\n                    monthlyReturnMin: 10.0,\n                    monthlyReturnMax: 15.0\n                },\n                {\n                    minTHS: 50,\n                    maxTHS: 999999,\n                    dailyReturnMin: 0.5,\n                    dailyReturnMax: 0.7,\n                    monthlyReturnMin: 10.0,\n                    monthlyReturnMax: 15.0\n                }\n            ];\n        }\n        // Find the appropriate range for the TH/s amount\n        const applicableRange = earningsRanges.find((range)=>thsAmount >= range.minTHS && thsAmount <= range.maxTHS);\n        if (applicableRange) {\n            return {\n                min: applicableRange.monthlyReturnMin || 10.0,\n                max: applicableRange.monthlyReturnMax || 15.0\n            };\n        } else {\n            // Fallback to highest range if no match found\n            const highestRange = earningsRanges[earningsRanges.length - 1];\n            return {\n                min: highestRange.monthlyReturnMin || 10.0,\n                max: highestRange.monthlyReturnMax || 15.0\n            };\n        }\n    } catch (error) {\n        console.error('Error getting monthly return limits:', error);\n        // Fallback to default 10-15% range\n        return {\n            min: 10.0,\n            max: 15.0\n        };\n    }\n}\n// Calculate 7-day average ROI for a specific TH/s amount based on admin configuration\nasync function calculate7DayAverageROI(thsAmount) {\n    try {\n        // Get earnings ranges from admin settings\n        const earningsRangesStr = await _database__WEBPACK_IMPORTED_MODULE_1__.adminSettingsDb.get('earningsRanges');\n        let earningsRanges;\n        if (earningsRangesStr) {\n            try {\n                earningsRanges = JSON.parse(earningsRangesStr);\n            } catch (parseError) {\n                console.error('Error parsing earnings ranges:', parseError);\n                earningsRanges = null;\n            }\n        }\n        // Fallback to default ranges if not configured\n        if (!earningsRanges || !Array.isArray(earningsRanges)) {\n            earningsRanges = [\n                {\n                    minTHS: 0,\n                    maxTHS: 10,\n                    dailyReturnMin: 0.3,\n                    dailyReturnMax: 0.5,\n                    monthlyReturnMin: 10.0,\n                    monthlyReturnMax: 15.0\n                },\n                {\n                    minTHS: 10,\n                    maxTHS: 50,\n                    dailyReturnMin: 0.4,\n                    dailyReturnMax: 0.6,\n                    monthlyReturnMin: 10.0,\n                    monthlyReturnMax: 15.0\n                },\n                {\n                    minTHS: 50,\n                    maxTHS: 999999,\n                    dailyReturnMin: 0.5,\n                    dailyReturnMax: 0.7,\n                    monthlyReturnMin: 10.0,\n                    monthlyReturnMax: 15.0\n                }\n            ];\n        }\n        // Find the appropriate range for the TH/s amount\n        const applicableRange = earningsRanges.find((range)=>thsAmount >= range.minTHS && thsAmount <= range.maxTHS);\n        let range;\n        if (applicableRange) {\n            range = applicableRange;\n        } else {\n            // Fallback to highest range if no match found\n            range = earningsRanges[earningsRanges.length - 1];\n        }\n        // Calculate 7-day average by simulating daily ROI variations\n        let totalROI = 0;\n        const days = 7;\n        for(let day = 0; day < days; day++){\n            // Generate a random ROI within the range for each day\n            const dailyROI = range.dailyReturnMin + Math.random() * (range.dailyReturnMax - range.dailyReturnMin);\n            totalROI += dailyROI;\n        }\n        // Return the 7-day average\n        return totalROI / days;\n    } catch (error) {\n        console.error('Error calculating 7-day average ROI:', error);\n        return 0.4; // Default fallback\n    }\n}\n// Update existing mining units when earnings configuration changes\nasync function updateExistingMiningUnitsROI() {\n    try {\n        console.log('Updating existing mining units with new ROI configuration...');\n        // Get all active mining units\n        const activeMiningUnits = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.findMany({\n            where: {\n                status: 'ACTIVE',\n                expiryDate: {\n                    gt: new Date()\n                }\n            }\n        });\n        console.log(`Found ${activeMiningUnits.length} active mining units to update`);\n        const updateResults = [];\n        for (const unit of activeMiningUnits){\n            try {\n                // Calculate new ROI based on current TH/s amount\n                const newROI = await calculateDynamicROI(unit.thsAmount);\n                // Update the mining unit with new ROI\n                await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.update({\n                    where: {\n                        id: unit.id\n                    },\n                    data: {\n                        dailyROI: newROI\n                    }\n                });\n                updateResults.push({\n                    unitId: unit.id,\n                    userId: unit.userId,\n                    thsAmount: unit.thsAmount,\n                    oldROI: unit.dailyROI,\n                    newROI\n                });\n                console.log(`Updated unit ${unit.id}: ${unit.dailyROI}% -> ${newROI}%`);\n            } catch (unitError) {\n                console.error(`Error updating unit ${unit.id}:`, unitError);\n            }\n        }\n        // Log the update process\n        await _database__WEBPACK_IMPORTED_MODULE_1__.systemLogDb.create({\n            action: 'MINING_UNITS_ROI_UPDATED',\n            details: {\n                unitsUpdated: updateResults.length,\n                totalUnits: activeMiningUnits.length,\n                updateResults,\n                timestamp: new Date().toISOString()\n            }\n        });\n        console.log(`Successfully updated ${updateResults.length} mining units with new ROI configuration`);\n        return {\n            success: true,\n            unitsUpdated: updateResults.length,\n            totalUnits: activeMiningUnits.length,\n            updateResults\n        };\n    } catch (error) {\n        console.error('Error updating existing mining units ROI:', error);\n        throw error;\n    }\n}\n// Calculate daily ROI for all active mining units using FIFO allocation\nasync function calculateDailyROI() {\n    try {\n        console.log('Starting daily ROI calculation with FIFO allocation...');\n        // Get all users with active mining units\n        const usersWithMiningUnits = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findMany({\n            where: {\n                miningUnits: {\n                    some: {\n                        status: 'ACTIVE',\n                        expiryDate: {\n                            gt: new Date()\n                        }\n                    }\n                }\n            },\n            include: {\n                miningUnits: {\n                    where: {\n                        status: 'ACTIVE',\n                        expiryDate: {\n                            gt: new Date()\n                        }\n                    }\n                }\n            }\n        });\n        console.log(`Found ${usersWithMiningUnits.length} users with active mining units`);\n        const results = [];\n        for (const user of usersWithMiningUnits){\n            try {\n                // Calculate total daily earnings for this user\n                let totalDailyEarnings = 0;\n                const unitEarnings = [];\n                for (const unit of user.miningUnits){\n                    const dailyEarnings = unit.investmentAmount * unit.dailyROI / 100;\n                    totalDailyEarnings += dailyEarnings;\n                    unitEarnings.push({\n                        unitId: unit.id,\n                        thsAmount: unit.thsAmount,\n                        dailyEarnings\n                    });\n                }\n                if (totalDailyEarnings > 0) {\n                    // Create transaction first with full amount\n                    const transaction = await _database__WEBPACK_IMPORTED_MODULE_1__.transactionDb.create({\n                        userId: user.id,\n                        type: 'MINING_EARNINGS',\n                        amount: totalDailyEarnings,\n                        description: `Daily mining earnings - Total: ${unitEarnings.map((u)=>`${u.thsAmount} TH/s`).join(', ')}`,\n                        status: 'PENDING'\n                    });\n                    // Allocate earnings to mining units using FIFO logic\n                    const allocationSummary = await (0,_miningUnitEarnings__WEBPACK_IMPORTED_MODULE_2__.allocateEarningsToUnits)(user.id, totalDailyEarnings, 'MINING_EARNINGS', transaction.id, 'Daily mining ROI earnings');\n                    // Update transaction amount to reflect only what was actually allocated\n                    if (allocationSummary.totalAllocated !== totalDailyEarnings) {\n                        await _database__WEBPACK_IMPORTED_MODULE_1__.transactionDb.updateStatus(transaction.id, 'PENDING', {\n                            amount: allocationSummary.totalAllocated,\n                            description: `Daily mining earnings - Allocated: ${allocationSummary.totalAllocated}, Discarded: ${allocationSummary.totalDiscarded} (capacity limits)`\n                        });\n                    }\n                    results.push({\n                        userId: user.id,\n                        totalEarnings: allocationSummary.totalAllocated,\n                        allocations: allocationSummary.allocations,\n                        unitsProcessed: user.miningUnits.length,\n                        discardedAmount: allocationSummary.totalDiscarded\n                    });\n                    console.log(`Allocated ${allocationSummary.totalAllocated} of ${totalDailyEarnings} mining earnings to ${allocationSummary.allocations.length} units for user ${user.id}`);\n                    if (allocationSummary.totalDiscarded > 0) {\n                        console.log(`Discarded ${allocationSummary.totalDiscarded} excess mining earnings due to capacity limits for user ${user.id}`);\n                    }\n                }\n            } catch (userError) {\n                console.error(`Error processing mining earnings for user ${user.id}:`, userError);\n            }\n        }\n        // Log the daily ROI calculation\n        await _database__WEBPACK_IMPORTED_MODULE_1__.systemLogDb.create({\n            action: 'DAILY_ROI_CALCULATED',\n            details: {\n                usersProcessed: results.length,\n                totalEarnings: results.reduce((sum, r)=>sum + r.totalEarnings, 0),\n                totalAllocations: results.reduce((sum, r)=>sum + r.allocations.length, 0),\n                timestamp: new Date().toISOString()\n            }\n        });\n        console.log(`Daily ROI calculation completed. Processed ${results.length} users with FIFO allocation.`);\n        return results;\n    } catch (error) {\n        console.error('Daily ROI calculation error:', error);\n        throw error;\n    }\n}\n// Process weekly earnings distribution (Saturday 15:00 UTC)\nasync function processWeeklyEarnings() {\n    try {\n        console.log('Starting weekly earnings distribution...');\n        // Get all pending mining earnings\n        const pendingEarnings = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.findMany({\n            where: {\n                type: 'MINING_EARNINGS',\n                status: 'PENDING'\n            },\n            include: {\n                user: true\n            }\n        });\n        console.log(`Found ${pendingEarnings.length} pending earnings transactions`);\n        const userEarnings = new Map();\n        // Group earnings by user\n        for (const transaction of pendingEarnings){\n            const currentTotal = userEarnings.get(transaction.userId) || 0;\n            userEarnings.set(transaction.userId, currentTotal + transaction.amount);\n        }\n        const results = [];\n        // Process each user's earnings\n        for (const [userId, totalEarnings] of userEarnings){\n            try {\n                // Mark all pending transactions as completed\n                await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.updateMany({\n                    where: {\n                        userId,\n                        type: 'MINING_EARNINGS',\n                        status: 'PENDING'\n                    },\n                    data: {\n                        status: 'COMPLETED'\n                    }\n                });\n                results.push({\n                    userId,\n                    totalEarnings\n                });\n            } catch (userError) {\n                console.error(`Error processing earnings for user ${userId}:`, userError);\n            }\n        }\n        // Log the weekly distribution\n        await _database__WEBPACK_IMPORTED_MODULE_1__.systemLogDb.create({\n            action: 'WEEKLY_EARNINGS_DISTRIBUTED',\n            details: {\n                usersProcessed: results.length,\n                totalDistributed: results.reduce((sum, r)=>sum + r.totalEarnings, 0),\n                transactionsProcessed: pendingEarnings.length,\n                timestamp: new Date().toISOString()\n            }\n        });\n        console.log(`Weekly earnings distribution completed. Processed ${results.length} users.`);\n        return results;\n    } catch (error) {\n        console.error('Weekly earnings distribution error:', error);\n        throw error;\n    }\n}\n// Check and expire mining units that have reached 24 months\nasync function expireOldMiningUnits() {\n    try {\n        console.log('Checking for expired mining units...');\n        const expiredUnits = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.findMany({\n            where: {\n                status: 'ACTIVE',\n                expiryDate: {\n                    lte: new Date()\n                }\n            },\n            include: {\n                user: {\n                    select: {\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n        console.log(`Found ${expiredUnits.length} units to expire`);\n        for (const unit of expiredUnits){\n            await _database__WEBPACK_IMPORTED_MODULE_1__.miningUnitDb.expireUnit(unit.id);\n            // Note: User active status is now computed dynamically\n            await _database__WEBPACK_IMPORTED_MODULE_1__.systemLogDb.create({\n                action: 'MINING_UNIT_EXPIRED',\n                userId: unit.userId,\n                details: {\n                    miningUnitId: unit.id,\n                    reason: '24_months_reached',\n                    totalEarned: unit.totalEarned,\n                    investmentAmount: unit.investmentAmount\n                }\n            });\n            // Send email notification\n            try {\n                const { emailNotificationService } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/nodemailer\"), __webpack_require__.e(\"_rsc_src_lib_emailNotificationService_ts\")]).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/emailNotificationService */ \"(rsc)/./src/lib/emailNotificationService.ts\"));\n                const totalEarnings = unit.miningEarnings + unit.referralEarnings + unit.binaryEarnings;\n                await emailNotificationService.sendMiningUnitExpiryNotification({\n                    userId: unit.userId,\n                    email: unit.user.email,\n                    firstName: unit.user.firstName,\n                    lastName: unit.user.lastName,\n                    thsAmount: unit.thsAmount,\n                    investmentAmount: unit.investmentAmount,\n                    totalEarned: totalEarnings,\n                    purchaseDate: unit.createdAt.toISOString(),\n                    expiryDate: unit.expiryDate.toISOString(),\n                    expiryReason: 'TIME_LIMIT'\n                });\n            } catch (emailError) {\n                console.error('Failed to send mining unit expiry email:', emailError);\n            // Don't fail the expiry if email fails\n            }\n        }\n        return expiredUnits.length;\n    } catch (error) {\n        console.error('Mining unit expiry check error:', error);\n        throw error;\n    }\n}\n// Get mining statistics\nasync function getMiningStats() {\n    try {\n        const stats = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.$transaction([\n            // Total TH/s sold\n            _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.aggregate({\n                _sum: {\n                    thsAmount: true\n                }\n            }),\n            // Active TH/s\n            _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.aggregate({\n                where: {\n                    status: 'ACTIVE'\n                },\n                _sum: {\n                    thsAmount: true\n                }\n            }),\n            // Total investment\n            _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.aggregate({\n                _sum: {\n                    investmentAmount: true\n                }\n            }),\n            // Total earnings distributed\n            _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.aggregate({\n                where: {\n                    type: 'MINING_EARNINGS',\n                    status: 'COMPLETED'\n                },\n                _sum: {\n                    amount: true\n                }\n            }),\n            // Active mining units count\n            _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.count({\n                where: {\n                    status: 'ACTIVE'\n                }\n            }),\n            // Total mining units count\n            _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.count()\n        ]);\n        return {\n            totalTHSSold: stats[0]._sum.thsAmount || 0,\n            activeTHS: stats[1]._sum.thsAmount || 0,\n            totalInvestment: stats[2]._sum.investmentAmount || 0,\n            totalEarningsDistributed: stats[3]._sum.amount || 0,\n            activeMiningUnits: stats[4],\n            totalMiningUnits: stats[5]\n        };\n    } catch (error) {\n        console.error('Mining stats error:', error);\n        throw error;\n    }\n}\n// Calculate user's estimated earnings\nasync function calculateEstimatedEarnings(userId) {\n    try {\n        const activeMiningUnits = await _database__WEBPACK_IMPORTED_MODULE_1__.miningUnitDb.findActiveByUserId(userId);\n        if (activeMiningUnits.length === 0) {\n            return {\n                next7Days: 0,\n                next30Days: 0,\n                next365Days: 0,\n                next2Years: 0\n            };\n        }\n        let totalDaily = 0;\n        for (const unit of activeMiningUnits){\n            const dailyEarnings = unit.investmentAmount * unit.dailyROI / 100;\n            const maxEarnings = unit.investmentAmount * 5;\n            const remainingEarnings = maxEarnings - unit.totalEarned;\n            // Use the lower of daily earnings or remaining earnings\n            totalDaily += Math.min(dailyEarnings, remainingEarnings);\n        }\n        return {\n            next7Days: totalDaily * 7,\n            next30Days: totalDaily * 30,\n            next365Days: totalDaily * 365,\n            next2Years: totalDaily * 730\n        };\n    } catch (error) {\n        console.error('Estimated earnings calculation error:', error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mining.ts\n");

/***/ })

};
;