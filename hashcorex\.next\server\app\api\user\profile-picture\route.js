"use strict";(()=>{var e={};e.id=1956,e.ids=[1956],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},21820:e=>{e.exports=require("os")},23870:(e,r,t)=>{t.d(r,{A:()=>u});var s=t(55511);let i={randomUUID:s.randomUUID},a=new Uint8Array(256),o=a.length,n=[];for(let e=0;e<256;++e)n.push((e+256).toString(16).slice(1));let u=function(e,r,t){if(i.randomUUID&&!r&&!e)return i.randomUUID();let u=(e=e||{}).random??e.rng?.()??(o>a.length-16&&((0,s.randomFillSync)(a),o=0),a.slice(o,o+=16));if(u.length<16)throw Error("Random bytes length must be >= 16");if(u[6]=15&u[6]|64,u[8]=63&u[8]|128,r){if((t=t||0)<0||t+16>r.length)throw RangeError(`UUID byte range ${t}:${t+15} is out of buffer bounds`);for(let e=0;e<16;++e)r[t+e]=u[e];return r}return function(e,r=0){return(n[e[r+0]]+n[e[r+1]]+n[e[r+2]]+n[e[r+3]]+"-"+n[e[r+4]]+n[e[r+5]]+"-"+n[e[r+6]]+n[e[r+7]]+"-"+n[e[r+8]]+n[e[r+9]]+"-"+n[e[r+10]]+n[e[r+11]]+n[e[r+12]]+n[e[r+13]]+n[e[r+14]]+n[e[r+15]]).toLowerCase()}(u)}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37366:e=>{e.exports=require("dns")},39542:(e,r,t)=>{t.d(r,{b9:()=>R,HU:()=>E,qc:()=>T,Lx:()=>x,DY:()=>y,DT:()=>h});var s=t(85663),i=t(43205),a=t.n(i),o=t(6710),n=t(45697);let u=n.z.object({DATABASE_URL:n.z.string().url("DATABASE_URL must be a valid PostgreSQL URL"),DIRECT_URL:n.z.string().url("DIRECT_URL must be a valid PostgreSQL URL"),JWT_SECRET:n.z.string().min(32,"JWT_SECRET must be at least 32 characters long").refine(e=>{let r=/[A-Z]/.test(e),t=/[a-z]/.test(e),s=/\d/.test(e),i=/[!@#$%^&*(),.?":{}|<>]/.test(e);return r&&t&&s&&i},"JWT_SECRET should contain uppercase, lowercase, numbers, and special characters"),JWT_EXPIRES_IN:n.z.string().default("30d"),NODE_ENV:n.z.enum(["development","production","test"]).default("development"),NEXT_PUBLIC_APP_URL:n.z.string().url().optional(),PORT:n.z.string().regex(/^\d+$/).transform(Number).default("3000"),SMTP_HOST:n.z.string().optional(),SMTP_PORT:n.z.string().regex(/^\d+$/).transform(Number).optional(),SMTP_USER:n.z.string().email().optional(),SMTP_PASSWORD:n.z.string().optional(),SMTP_SECURE:n.z.string().transform(e=>"true"===e).optional(),TRON_NETWORK:n.z.enum(["mainnet","testnet"]).default("testnet"),TRON_API_KEY:n.z.string().optional(),USDT_CONTRACT_ADDRESS:n.z.string().optional(),MAX_FILE_SIZE:n.z.string().regex(/^\d+$/).transform(Number).default("10485760"),UPLOAD_DIR:n.z.string().default("./public/uploads"),BCRYPT_ROUNDS:n.z.string().regex(/^\d+$/).transform(Number).default("12"),SESSION_TIMEOUT:n.z.string().regex(/^\d+$/).transform(Number).default("1800"),RATE_LIMIT_WINDOW_MS:n.z.string().regex(/^\d+$/).transform(Number).default("900000"),RATE_LIMIT_MAX_REQUESTS:n.z.string().regex(/^\d+$/).transform(Number).default("100"),COINGECKO_API_URL:n.z.string().url().default("https://api.coingecko.com/api/v3"),LOG_LEVEL:n.z.enum(["error","warn","info","debug"]).default("info"),ENABLE_REQUEST_LOGGING:n.z.string().transform(e=>"true"===e).default("false"),ENABLE_REGISTRATION:n.z.string().transform(e=>"true"===e).default("true"),ENABLE_KYC:n.z.string().transform(e=>"true"===e).default("true"),ENABLE_WITHDRAWALS:n.z.string().transform(e=>"true"===e).default("true")}).refine(e=>{let r=[e.SMTP_HOST,e.SMTP_PORT,e.SMTP_USER,e.SMTP_PASSWORD],t=r.some(e=>void 0!==e),s=r.every(e=>void 0!==e);return!t||!!s},{message:"If SMTP configuration is provided, all SMTP fields (HOST, PORT, USER, PASSWORD) must be provided"});function l(){try{let e=u.safeParse(process.env);if(!e.success){let r=e.error.errors.map(e=>`${e.path.join(".")}: ${e.message}`);return{success:!1,errors:r}}return{success:!0,data:e.data}}catch(e){return{success:!1,errors:["Failed to validate environment variables"]}}}let c=null;function d(){if(!c){let e=l();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),c=e.data,console.log("✅ Environment variables validated successfully")}return c}let p={jwt:{secret:()=>d().JWT_SECRET,expiresIn:()=>d().JWT_EXPIRES_IN},security:{bcryptRounds:()=>d().BCRYPT_ROUNDS,sessionTimeout:()=>d().SESSION_TIMEOUT,maxFileSize:()=>d().MAX_FILE_SIZE,uploadDir:()=>d().UPLOAD_DIR}};if("phase-production-build"!==process.env.NEXT_PHASE){let e=l();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),console.log("✅ Environment variables validated successfully")}let f=async e=>await s.Ay.hash(e,p.security.bcryptRounds()),m=async(e,r)=>await s.Ay.compare(e,r),E=e=>a().sign(e,p.jwt.secret(),{expiresIn:p.jwt.expiresIn()}),g=e=>{try{return a().verify(e,p.jwt.secret())}catch(e){return null}},S=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",r="HC";for(let t=0;t<8;t++)r+=e.charAt(Math.floor(Math.random()*e.length));return r},R=async e=>{let r=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!r)return{authenticated:!1,user:null};let t=g(r);if(!t)return{authenticated:!1,user:null};let s=await o.Gy.findByEmail(t.email);return s?{authenticated:!0,user:s}:{authenticated:!1,user:null}},y=async e=>{let r,s;if(await o.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let t=await o.Gy.findByReferralId(e.referralCode);if(!t)throw Error("Invalid referral code");r=t.id}let i=await f(e.password),a=!1;do s=S(),a=!await o.Gy.findByReferralId(s);while(!a);let n=await o.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:i,referralId:s});if(r){let{placeUserByReferralType:s}=await t.e(2746).then(t.bind(t,2746)),i="general";"left"===e.placementSide?i="left":"right"===e.placementSide&&(i="right"),await s(r,n.id,i)}return{id:n.id,email:n.email,referralId:n.referralId,kycStatus:n.kycStatus}},x=async e=>{let r=await o.Gy.findByEmail(e.email);if(!r||!await m(e.password,r.password))throw Error("Invalid email or password");return{token:E({userId:r.id,email:r.email}),user:{id:r.id,email:r.email,referralId:r.referralId,kycStatus:r.kycStatus}}},h=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),T=async e=>{let r=await o.Gy.findById(e);return r?.role==="ADMIN"}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71055:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>g,serverHooks:()=>y,workAsyncStorage:()=>S,workUnitAsyncStorage:()=>R});var s={};t.r(s),t.d(s,{DELETE:()=>E,POST:()=>m});var i=t(96559),a=t(48088),o=t(37719),n=t(32190),u=t(39542),l=t(6710),c=t(79748),d=t(33873),p=t(23870),f=t(29021);async function m(e){try{let{authenticated:r,user:t}=await (0,u.b9)(e);if(!r||!t)return n.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});let s=(await e.formData()).get("profilePicture");if(!s)return n.NextResponse.json({success:!1,error:"No file provided"},{status:400});if(!s.type.startsWith("image/"))return n.NextResponse.json({success:!1,error:"File must be an image"},{status:400});if(s.size>5242880)return n.NextResponse.json({success:!1,error:"File size must be less than 5MB"},{status:400});let i=(0,d.join)(process.cwd(),"public","uploads","profile-pictures");(0,f.existsSync)(i)||await (0,c.mkdir)(i,{recursive:!0});let a=s.name.split(".").pop(),o=`${t.id}_${(0,p.A)()}.${a}`,m=(0,d.join)(i,o),E=`/uploads/profile-pictures/${o}`,g=await l.Gy.findById(t.id);if(g?.profilePicture){let e=(0,d.join)(process.cwd(),"public",g.profilePicture);try{(0,f.existsSync)(e)&&await (0,c.unlink)(e)}catch(e){console.warn("Failed to delete old profile picture:",e)}}let S=await s.arrayBuffer(),R=Buffer.from(S);await (0,c.writeFile)(m,R);let y=await l.Gy.updateProfilePicture(t.id,E);return n.NextResponse.json({success:!0,message:"Profile picture updated successfully",data:{profilePicture:E,user:y}})}catch(e){return console.error("Profile picture upload error:",e),n.NextResponse.json({success:!1,error:"Failed to upload profile picture"},{status:500})}}async function E(e){try{let{authenticated:r,user:t}=await (0,u.b9)(e);if(!r||!t)return n.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});let s=await l.Gy.findById(t.id);if(!s?.profilePicture)return n.NextResponse.json({success:!1,error:"No profile picture to remove"},{status:400});let i=(0,d.join)(process.cwd(),"public",s.profilePicture);try{(0,f.existsSync)(i)&&await (0,c.unlink)(i)}catch(e){console.warn("Failed to delete profile picture file:",e)}let a=await l.Gy.updateProfilePicture(t.id,null);return n.NextResponse.json({success:!0,message:"Profile picture removed successfully",data:{user:a}})}catch(e){return console.error("Profile picture removal error:",e),n.NextResponse.json({success:!1,error:"Failed to remove profile picture"},{status:500})}}let g=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/user/profile-picture/route",pathname:"/api/user/profile-picture",filename:"route",bundlePath:"app/api/user/profile-picture/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\user\\profile-picture\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:S,workUnitAsyncStorage:R,serverHooks:y}=g;function x(){return(0,o.patchFetch)({workAsyncStorage:S,workUnitAsyncStorage:R})}},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},79748:e=>{e.exports=require("fs/promises")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,7911,925],()=>t(71055));module.exports=s})();