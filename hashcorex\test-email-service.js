const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testEmailService() {
  try {
    console.log('Testing email service configuration...');
    
    // Get email settings
    const emailKeys = [
      'smtpHost',
      'smtpPort', 
      'smtpSecure',
      'smtpUser',
      'smtpPassword',
      'fromName',
      'fromEmail',
      'emailEnabled'
    ];
    
    const settings = await prisma.adminSettings.findMany({
      where: {
        key: {
          in: emailKeys
        }
      }
    });
    
    const settingsObject = {};
    settings.forEach(setting => {
      settingsObject[setting.key] = setting.value;
    });
    
    console.log('Email settings found:');
    console.log('smtpHost:', settingsObject.smtpHost);
    console.log('smtpPort:', settingsObject.smtpPort);
    console.log('smtpSecure:', settingsObject.smtpSecure);
    console.log('smtpUser:', settingsObject.smtpUser);
    console.log('smtpPassword:', settingsObject.smtpPassword ? '***' : 'NOT SET');
    console.log('fromName:', settingsObject.fromName);
    console.log('fromEmail:', settingsObject.fromEmail);
    console.log('emailEnabled:', settingsObject.emailEnabled);
    
    // Check if all required fields are present
    const required = ['smtpHost', 'smtpUser', 'smtpPassword'];
    const missing = required.filter(key => !settingsObject[key]);
    
    if (missing.length > 0) {
      console.log('❌ Missing required email settings:', missing);
    } else {
      console.log('✅ All required email settings are present');
    }
    
    // Check email templates
    console.log('\nChecking email templates...');
    const templates = await prisma.emailTemplate.findMany();
    console.log(`Found ${templates.length} email templates:`);
    templates.forEach(template => {
      console.log(`- ${template.name}: ${template.subject} (Active: ${template.isActive})`);
    });
    
    // Check for OTP template specifically
    const otpTemplate = templates.find(t => t.name === 'otp_verification');
    if (otpTemplate) {
      console.log('✅ OTP verification template found');
    } else {
      console.log('❌ OTP verification template NOT found');
    }
    
  } catch (error) {
    console.error('Error testing email service:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testEmailService();
