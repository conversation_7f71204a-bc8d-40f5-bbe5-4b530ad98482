import { NextRequest, NextResponse } from 'next/server';
import { adminSettingsDb } from '@/lib/database';

// GET - Check specific admin setting (for middleware use)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const key = searchParams.get('key');

    if (!key) {
      return NextResponse.json(
        { success: false, error: 'Setting key is required' },
        { status: 400 }
      );
    }

    // Get the setting value
    const value = await adminSettingsDb.get(key);

    return NextResponse.json({
      success: true,
      key,
      value: value || null,
    });

  } catch (error: any) {
    console.error('Admin setting check error:', error);
    
    // Return default values for critical settings
    const key = new URL(request.url).searchParams.get('key');
    let defaultValue = null;

    if (key === 'registrationEnabled') defaultValue = 'true';
    if (key === 'kycRequired') defaultValue = 'true';
    
    return NextResponse.json({
      success: true,
      key,
      value: defaultValue,
      fallback: true,
    });
  }
}
