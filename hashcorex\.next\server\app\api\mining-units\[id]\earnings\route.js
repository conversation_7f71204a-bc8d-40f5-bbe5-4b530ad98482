"use strict";(()=>{var e={};e.id=5276,e.ids=[5276],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37366:e=>{e.exports=require("dns")},39542:(e,t,r)=>{r.d(t,{b9:()=>y,HU:()=>f,qc:()=>R,Lx:()=>I,DY:()=>A,DT:()=>_});var n=r(85663),i=r(43205),a=r.n(i),s=r(6710),o=r(45697);let u=o.z.object({DATABASE_URL:o.z.string().url("DATABASE_URL must be a valid PostgreSQL URL"),DIRECT_URL:o.z.string().url("DIRECT_URL must be a valid PostgreSQL URL"),JWT_SECRET:o.z.string().min(32,"JWT_SECRET must be at least 32 characters long").refine(e=>{let t=/[A-Z]/.test(e),r=/[a-z]/.test(e),n=/\d/.test(e),i=/[!@#$%^&*(),.?":{}|<>]/.test(e);return t&&r&&n&&i},"JWT_SECRET should contain uppercase, lowercase, numbers, and special characters"),JWT_EXPIRES_IN:o.z.string().default("30d"),NODE_ENV:o.z.enum(["development","production","test"]).default("development"),NEXT_PUBLIC_APP_URL:o.z.string().url().optional(),PORT:o.z.string().regex(/^\d+$/).transform(Number).default("3000"),SMTP_HOST:o.z.string().optional(),SMTP_PORT:o.z.string().regex(/^\d+$/).transform(Number).optional(),SMTP_USER:o.z.string().email().optional(),SMTP_PASSWORD:o.z.string().optional(),SMTP_SECURE:o.z.string().transform(e=>"true"===e).optional(),TRON_NETWORK:o.z.enum(["mainnet","testnet"]).default("testnet"),TRON_API_KEY:o.z.string().optional(),USDT_CONTRACT_ADDRESS:o.z.string().optional(),MAX_FILE_SIZE:o.z.string().regex(/^\d+$/).transform(Number).default("10485760"),UPLOAD_DIR:o.z.string().default("./public/uploads"),BCRYPT_ROUNDS:o.z.string().regex(/^\d+$/).transform(Number).default("12"),SESSION_TIMEOUT:o.z.string().regex(/^\d+$/).transform(Number).default("1800"),RATE_LIMIT_WINDOW_MS:o.z.string().regex(/^\d+$/).transform(Number).default("900000"),RATE_LIMIT_MAX_REQUESTS:o.z.string().regex(/^\d+$/).transform(Number).default("100"),COINGECKO_API_URL:o.z.string().url().default("https://api.coingecko.com/api/v3"),LOG_LEVEL:o.z.enum(["error","warn","info","debug"]).default("info"),ENABLE_REQUEST_LOGGING:o.z.string().transform(e=>"true"===e).default("false"),ENABLE_REGISTRATION:o.z.string().transform(e=>"true"===e).default("true"),ENABLE_KYC:o.z.string().transform(e=>"true"===e).default("true"),ENABLE_WITHDRAWALS:o.z.string().transform(e=>"true"===e).default("true")}).refine(e=>{let t=[e.SMTP_HOST,e.SMTP_PORT,e.SMTP_USER,e.SMTP_PASSWORD],r=t.some(e=>void 0!==e),n=t.every(e=>void 0!==e);return!r||!!n},{message:"If SMTP configuration is provided, all SMTP fields (HOST, PORT, USER, PASSWORD) must be provided"});function l(){try{let e=u.safeParse(process.env);if(!e.success){let t=e.error.errors.map(e=>`${e.path.join(".")}: ${e.message}`);return{success:!1,errors:t}}return{success:!0,data:e.data}}catch(e){return{success:!1,errors:["Failed to validate environment variables"]}}}let d=null;function c(){if(!d){let e=l();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),d=e.data,console.log("✅ Environment variables validated successfully")}return d}let m={jwt:{secret:()=>c().JWT_SECRET,expiresIn:()=>c().JWT_EXPIRES_IN},security:{bcryptRounds:()=>c().BCRYPT_ROUNDS,sessionTimeout:()=>c().SESSION_TIMEOUT,maxFileSize:()=>c().MAX_FILE_SIZE,uploadDir:()=>c().UPLOAD_DIR}};if("phase-production-build"!==process.env.NEXT_PHASE){let e=l();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),console.log("✅ Environment variables validated successfully")}let p=async e=>await n.Ay.hash(e,m.security.bcryptRounds()),g=async(e,t)=>await n.Ay.compare(e,t),f=e=>a().sign(e,m.jwt.secret(),{expiresIn:m.jwt.expiresIn()}),E=e=>{try{return a().verify(e,m.jwt.secret())}catch(e){return null}},h=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},y=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=E(t);if(!r)return{authenticated:!1,user:null};let n=await s.Gy.findByEmail(r.email);return n?{authenticated:!0,user:n}:{authenticated:!1,user:null}},A=async e=>{let t,n;if(await s.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await s.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let i=await p(e.password),a=!1;do n=h(),a=!await s.Gy.findByReferralId(n);while(!a);let o=await s.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:i,referralId:n});if(t){let{placeUserByReferralType:n}=await r.e(2746).then(r.bind(r,2746)),i="general";"left"===e.placementSide?i="left":"right"===e.placementSide&&(i="right"),await n(t,o.id,i)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},I=async e=>{let t=await s.Gy.findByEmail(e.email);if(!t||!await g(e.password,t.password))throw Error("Invalid email or password");return{token:f({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},_=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),R=async e=>{let t=await s.Gy.findById(e);return t?.role==="ADMIN"}},39794:(e,t,r)=>{r.d(t,{Py:()=>s,k8:()=>l,kp:()=>u});var n=r(31183),i=r(6710);async function a(e){return await n.prisma.miningUnit.findMany({where:{userId:e,status:"ACTIVE",expiryDate:{gt:new Date}},orderBy:{createdAt:"asc"}})}async function s(e,t,r,s,u){let l=await a(e);if(0===l.length)throw Error("No active mining units found for earnings allocation");let d=[],c=t;for(let e of l){var m;if(c<=0)break;let t=Math.max(0,5*(m=e).investmentAmount-(m.miningEarnings+m.referralEarnings+m.binaryEarnings));if(t<=0)continue;let i=Math.min(c,t);if(i>0){let a={};switch(r){case"MINING_EARNINGS":a.miningEarnings={increment:i};break;case"DIRECT_REFERRAL":a.referralEarnings={increment:i};break;case"BINARY_BONUS":a.binaryEarnings={increment:i}}a.totalEarned={increment:i},await n.prisma.miningUnit.update({where:{id:e.id},data:a}),await n.prisma.miningUnitEarningsAllocation.create({data:{miningUnitId:e.id,transactionId:s,earningType:r,amount:i,description:u}}),d.push({miningUnitId:e.id,amount:i,remainingCapacity:t-i}),c-=i;let l=await n.prisma.miningUnit.findUnique({where:{id:e.id}});l&&function(e){let t=5*e.investmentAmount;return e.miningEarnings+e.referralEarnings+e.binaryEarnings>=t}(l)&&await o(e.id,"5x_investment_reached")}}let p=t-c,g=c;return c>0&&(console.warn(`Unable to allocate ${c} to mining units - all units at capacity. Amount discarded.`),await i.AJ.create({action:"EARNINGS_ALLOCATION_OVERFLOW",userId:e,details:{totalAmount:t,allocatedAmount:p,overflowAmount:g,earningType:r,reason:"all_units_at_capacity",note:"Excess amount discarded as per mining unit capacity limits"}})),{allocations:d,totalAllocated:p,totalDiscarded:g,allocationSuccess:0===g}}async function o(e,t){let a=await n.prisma.miningUnit.findUnique({where:{id:e},include:{user:{select:{email:!0,firstName:!0,lastName:!0}}}});if(!a)throw Error(`Mining unit ${e} not found`);await n.prisma.miningUnit.update({where:{id:e},data:{status:"EXPIRED"}});let s=a.miningEarnings+a.referralEarnings+a.binaryEarnings;await i.AJ.create({action:"MINING_UNIT_EXPIRED",userId:a.userId,details:{miningUnitId:e,reason:t,totalEarned:s,miningEarnings:a.miningEarnings,referralEarnings:a.referralEarnings,binaryEarnings:a.binaryEarnings,investmentAmount:a.investmentAmount,multiplier:s/a.investmentAmount}});try{let{emailNotificationService:e}=await Promise.all([r.e(9526),r.e(3161)]).then(r.bind(r,13161));await e.sendMiningUnitExpiryNotification({userId:a.userId,email:a.user.email,firstName:a.user.firstName,lastName:a.user.lastName,thsAmount:a.thsAmount,investmentAmount:a.investmentAmount,totalEarned:s,purchaseDate:a.createdAt.toISOString(),expiryDate:a.expiryDate.toISOString(),expiryReason:"24_months_reached"===t?"TIME_LIMIT":"RETURN_LIMIT"})}catch(e){console.error("Failed to send mining unit expiry email:",e)}console.log(`Mining unit ${e} expired due to ${t}. Total earnings: ${s}`)}async function u(e){return await n.prisma.miningUnit.findMany({where:{userId:e},orderBy:{createdAt:"asc"}})}async function l(e){return await n.prisma.miningUnitEarningsAllocation.findMany({where:{miningUnitId:e},include:{transaction:!0},orderBy:{allocatedAt:"desc"}})}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},87646:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>E,routeModule:()=>m,serverHooks:()=>f,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>g});var n={};r.r(n),r.d(n,{GET:()=>c});var i=r(96559),a=r(48088),s=r(37719),o=r(32190),u=r(39542),l=r(39794),d=r(31183);async function c(e,{params:t}){try{let{authenticated:r,user:n}=await (0,u.b9)(e);if(!r||!n)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});let{id:i}=await t,a=await d.prisma.miningUnit.findUnique({where:{id:i},select:{userId:!0,investmentAmount:!0,thsAmount:!0,status:!0}});if(!a)return o.NextResponse.json({success:!1,error:"Mining unit not found"},{status:404});if(a.userId!==n.id)return o.NextResponse.json({success:!1,error:"Access denied"},{status:403});let s=await (0,l.k8)(i),c=s.reduce((e,t)=>{let r=t.earningType;return e[r]||(e[r]={totalAmount:0,count:0,allocations:[]}),e[r].totalAmount+=t.amount,e[r].count+=1,e[r].allocations.push({amount:t.amount,date:t.allocatedAt,description:t.description,transactionId:t.transactionId}),e},{}),m=s.reduce((e,t)=>e+t.amount,0),p=5*a.investmentAmount,g=Math.max(0,p-m),f=m/p*100;return o.NextResponse.json({success:!0,data:{miningUnit:{id:i,thsAmount:a.thsAmount,investmentAmount:a.investmentAmount,status:a.status,maxEarnings:p},earnings:{totalAllocated:m,remainingCapacity:g,progressPercentage:Math.min(f,100),summary:c},history:s.map(e=>({id:e.id,earningType:e.earningType,amount:e.amount,description:e.description,allocatedAt:e.allocatedAt,transaction:{id:e.transaction.id,type:e.transaction.type,status:e.transaction.status,createdAt:e.transaction.createdAt}}))}})}catch(e){return console.error("Mining unit earnings history fetch error:",e),o.NextResponse.json({success:!1,error:"Failed to fetch earnings history"},{status:500})}}let m=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/mining-units/[id]/earnings/route",pathname:"/api/mining-units/[id]/earnings",filename:"route",bundlePath:"app/api/mining-units/[id]/earnings/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\mining-units\\[id]\\earnings\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:p,workUnitAsyncStorage:g,serverHooks:f}=m;function E(){return(0,s.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:g})}},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[4447,580,7911,925],()=>r(87646));module.exports=n})();