"use strict";(()=>{var e={};e.id=8225,e.ids=[8225],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37366:e=>{e.exports=require("dns")},39542:(e,t,r)=>{r.d(t,{b9:()=>R,HU:()=>S,qc:()=>f,Lx:()=>I,DY:()=>P,DT:()=>g});var a=r(85663),n=r(43205),s=r.n(n),i=r(6710),o=r(45697);let l=o.z.object({DATABASE_URL:o.z.string().url("DATABASE_URL must be a valid PostgreSQL URL"),DIRECT_URL:o.z.string().url("DIRECT_URL must be a valid PostgreSQL URL"),JWT_SECRET:o.z.string().min(32,"JWT_SECRET must be at least 32 characters long").refine(e=>{let t=/[A-Z]/.test(e),r=/[a-z]/.test(e),a=/\d/.test(e),n=/[!@#$%^&*(),.?":{}|<>]/.test(e);return t&&r&&a&&n},"JWT_SECRET should contain uppercase, lowercase, numbers, and special characters"),JWT_EXPIRES_IN:o.z.string().default("30d"),NODE_ENV:o.z.enum(["development","production","test"]).default("development"),NEXT_PUBLIC_APP_URL:o.z.string().url().optional(),PORT:o.z.string().regex(/^\d+$/).transform(Number).default("3000"),SMTP_HOST:o.z.string().optional(),SMTP_PORT:o.z.string().regex(/^\d+$/).transform(Number).optional(),SMTP_USER:o.z.string().email().optional(),SMTP_PASSWORD:o.z.string().optional(),SMTP_SECURE:o.z.string().transform(e=>"true"===e).optional(),TRON_NETWORK:o.z.enum(["mainnet","testnet"]).default("testnet"),TRON_API_KEY:o.z.string().optional(),USDT_CONTRACT_ADDRESS:o.z.string().optional(),MAX_FILE_SIZE:o.z.string().regex(/^\d+$/).transform(Number).default("10485760"),UPLOAD_DIR:o.z.string().default("./public/uploads"),BCRYPT_ROUNDS:o.z.string().regex(/^\d+$/).transform(Number).default("12"),SESSION_TIMEOUT:o.z.string().regex(/^\d+$/).transform(Number).default("1800"),RATE_LIMIT_WINDOW_MS:o.z.string().regex(/^\d+$/).transform(Number).default("900000"),RATE_LIMIT_MAX_REQUESTS:o.z.string().regex(/^\d+$/).transform(Number).default("100"),COINGECKO_API_URL:o.z.string().url().default("https://api.coingecko.com/api/v3"),LOG_LEVEL:o.z.enum(["error","warn","info","debug"]).default("info"),ENABLE_REQUEST_LOGGING:o.z.string().transform(e=>"true"===e).default("false"),ENABLE_REGISTRATION:o.z.string().transform(e=>"true"===e).default("true"),ENABLE_KYC:o.z.string().transform(e=>"true"===e).default("true"),ENABLE_WITHDRAWALS:o.z.string().transform(e=>"true"===e).default("true")}).refine(e=>{let t=[e.SMTP_HOST,e.SMTP_PORT,e.SMTP_USER,e.SMTP_PASSWORD],r=t.some(e=>void 0!==e),a=t.every(e=>void 0!==e);return!r||!!a},{message:"If SMTP configuration is provided, all SMTP fields (HOST, PORT, USER, PASSWORD) must be provided"});function u(){try{let e=l.safeParse(process.env);if(!e.success){let t=e.error.errors.map(e=>`${e.path.join(".")}: ${e.message}`);return{success:!1,errors:t}}return{success:!0,data:e.data}}catch(e){return{success:!1,errors:["Failed to validate environment variables"]}}}let d=null;function c(){if(!d){let e=u();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),d=e.data,console.log("✅ Environment variables validated successfully")}return d}let p={jwt:{secret:()=>c().JWT_SECRET,expiresIn:()=>c().JWT_EXPIRES_IN},security:{bcryptRounds:()=>c().BCRYPT_ROUNDS,sessionTimeout:()=>c().SESSION_TIMEOUT,maxFileSize:()=>c().MAX_FILE_SIZE,uploadDir:()=>c().UPLOAD_DIR}};if("phase-production-build"!==process.env.NEXT_PHASE){let e=u();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),console.log("✅ Environment variables validated successfully")}let m=async e=>await a.Ay.hash(e,p.security.bcryptRounds()),E=async(e,t)=>await a.Ay.compare(e,t),S=e=>s().sign(e,p.jwt.secret(),{expiresIn:p.jwt.expiresIn()}),_=e=>{try{return s().verify(e,p.jwt.secret())}catch(e){return null}},A=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},R=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=_(t);if(!r)return{authenticated:!1,user:null};let a=await i.Gy.findByEmail(r.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},P=async e=>{let t,a;if(await i.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await i.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let n=await m(e.password),s=!1;do a=A(),s=!await i.Gy.findByReferralId(a);while(!s);let o=await i.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:n,referralId:a});if(t){let{placeUserByReferralType:a}=await r.e(2746).then(r.bind(r,2746)),n="general";"left"===e.placementSide?n="left":"right"===e.placementSide&&(n="right"),await a(t,o.id,n)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},I=async e=>{let t=await i.Gy.findByEmail(e.email);if(!t||!await E(e.password,t.password))throw Error("Invalid email or password");return{token:S({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},g=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),f=async e=>{let t=await i.Gy.findById(e);return t?.role==="ADMIN"}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},60864:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>_,routeModule:()=>p,serverHooks:()=>S,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>E});var a={};r.r(a),r.d(a,{GET:()=>d,PUT:()=>c});var n=r(96559),s=r(48088),i=r(37719),o=r(32190),l=r(39542),u=r(6710);async function d(e){try{let{authenticated:t,user:r}=await (0,l.b9)(e);if(!t||!r)return o.NextResponse.json({error:"Not authenticated"},{status:401});if(!await (0,l.qc)(r.id))return o.NextResponse.json({error:"Admin access required"},{status:403});let a=await u.rs.getAll(),n={};a.forEach(e=>{try{n[e.key]=JSON.parse(e.value)}catch{n[e.key]=e.value}}),n.MAX_BINARY_POINTS_PER_SIDE&&(n.maxBinaryPointsPerSide=parseFloat(n.MAX_BINARY_POINTS_PER_SIDE),console.log(`Mapped MAX_BINARY_POINTS_PER_SIDE: ${n.MAX_BINARY_POINTS_PER_SIDE} → maxBinaryPointsPerSide: ${n.maxBinaryPointsPerSide}`),delete n.MAX_BINARY_POINTS_PER_SIDE),n.BINARY_POINT_VALUE&&(n.binaryPointValue=parseFloat(n.BINARY_POINT_VALUE),delete n.BINARY_POINT_VALUE),n.BINARY_MATCHING_ENABLED&&(n.binaryMatchingEnabled="true"===n.BINARY_MATCHING_ENABLED,delete n.BINARY_MATCHING_ENABLED),n.BINARY_MATCHING_SCHEDULE&&(n.binaryMatchingSchedule=n.BINARY_MATCHING_SCHEDULE,delete n.BINARY_MATCHING_SCHEDULE),n.THS_PRICE&&(n.thsPriceUSD=parseFloat(n.THS_PRICE),delete n.THS_PRICE),n.MINIMUM_PURCHASE&&(n.minPurchaseAmount=parseFloat(n.MINIMUM_PURCHASE),delete n.MINIMUM_PURCHASE),n.MAXIMUM_PURCHASE&&(n.maxPurchaseAmount=parseFloat(n.MAXIMUM_PURCHASE),delete n.MAXIMUM_PURCHASE),console.log("Settings object after mapping:",{maxBinaryPointsPerSide:n.maxBinaryPointsPerSide});let s={thsPriceUSD:50,minPurchaseAmount:100,maxPurchaseAmount:1e4,earningsRanges:[{minTHS:0,maxTHS:10,dailyReturnMin:.3,dailyReturnMax:.5,monthlyReturnMin:10,monthlyReturnMax:15},{minTHS:10,maxTHS:50,dailyReturnMin:.4,dailyReturnMax:.6,monthlyReturnMin:10,monthlyReturnMax:15},{minTHS:50,maxTHS:999999,dailyReturnMin:.5,dailyReturnMax:.7,monthlyReturnMin:10,monthlyReturnMax:15}],binaryBonusPercentage:10,referralBonusPercentage:5,maxBinaryPointsPerSide:10,binaryPointValue:10,binaryMatchingEnabled:!0,binaryMatchingSchedule:"Weekly at 15:00 UTC",usdtDepositAddress:"",minDepositAmount:10,maxDepositAmount:1e4,depositEnabled:!0,minConfirmations:1,depositFeePercentage:0,tronNetwork:"testnet",tronMainnetApiUrl:"https://api.trongrid.io",tronTestnetApiUrl:"https://api.shasta.trongrid.io",usdtMainnetContract:"TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t",usdtTestnetContract:"TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs",minWithdrawalAmount:50,withdrawalFeeFixed:3,withdrawalFeePercentage:1,withdrawalProcessingDays:3,platformFeePercentage:1,registrationEnabled:!0,kycRequired:!0,...n};return o.NextResponse.json({success:!0,data:s})}catch(e){return console.error("Admin settings fetch error:",e),o.NextResponse.json({success:!1,error:"Failed to fetch settings"},{status:500})}}async function c(e){try{let{authenticated:t,user:r}=await (0,l.b9)(e);if(!t||!r)return o.NextResponse.json({error:"Not authenticated"},{status:401});if(!await (0,l.qc)(r.id))return o.NextResponse.json({error:"Admin access required"},{status:403});let a=await e.json();console.log("Received settings for update:",a),console.log("Received maxBinaryPointsPerSide:",a.maxBinaryPointsPerSide,typeof a.maxBinaryPointsPerSide);let n={...a};["MAX_BINARY_POINTS_PER_SIDE","BINARY_POINT_VALUE","BINARY_MATCHING_ENABLED","BINARY_MATCHING_SCHEDULE","THS_PRICE","MINIMUM_PURCHASE","MAXIMUM_PURCHASE"].forEach(e=>{n[e]&&(console.log(`Removing conflicting database key: ${e} = ${n[e]}`),delete n[e])}),console.log("Cleaned settings for processing:",{maxBinaryPointsPerSide:n.maxBinaryPointsPerSide,binaryPointValue:n.binaryPointValue,binaryMatchingEnabled:n.binaryMatchingEnabled,binaryMatchingSchedule:n.binaryMatchingSchedule});let s={maxBinaryPointsPerSide:"MAX_BINARY_POINTS_PER_SIDE",binaryPointValue:"BINARY_POINT_VALUE",binaryMatchingEnabled:"BINARY_MATCHING_ENABLED",binaryMatchingSchedule:"BINARY_MATCHING_SCHEDULE",thsPriceUSD:"THS_PRICE",minPurchaseAmount:"MINIMUM_PURCHASE",maxPurchaseAmount:"MAXIMUM_PURCHASE"},i=Object.entries(n).map(async([e,t])=>{let a=s[e]||e;if(!s[e])return u.rs.set(a,JSON.stringify(t),r.id);{console.log(`Mapping ${e} (${t}) → ${a} (${String(t)})`);let n=await u.rs.set(a,String(t),r.id);return console.log(`Database update result for ${a}:`,n),n}});return await Promise.all(i),console.log("All settings updates completed"),await u.AJ.create({action:"SYSTEM_SETTINGS_UPDATED",userId:r.id,details:{updatedSettings:Object.keys(n)},ipAddress:e.headers.get("x-forwarded-for")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"}),o.NextResponse.json({success:!0,message:"Settings updated successfully"})}catch(e){return console.error("Admin settings update error:",e),o.NextResponse.json({success:!1,error:"Failed to update settings"},{status:500})}}let p=new n.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/admin/settings/route",pathname:"/api/admin/settings",filename:"route",bundlePath:"app/api/admin/settings/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\settings\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:m,workUnitAsyncStorage:E,serverHooks:S}=p;function _(){return(0,i.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:E})}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580,7911,925],()=>r(60864));module.exports=a})();