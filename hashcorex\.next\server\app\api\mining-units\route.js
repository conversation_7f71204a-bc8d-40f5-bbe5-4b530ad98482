/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/mining-units/route";
exports.ids = ["app/api/mining-units/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmining-units%2Froute&page=%2Fapi%2Fmining-units%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmining-units%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmining-units%2Froute&page=%2Fapi%2Fmining-units%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmining-units%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_dream_Desktop_Hash_Minings_hashcorex_src_app_api_mining_units_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/mining-units/route.ts */ \"(rsc)/./src/app/api/mining-units/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/mining-units/route\",\n        pathname: \"/api/mining-units\",\n        filename: \"route\",\n        bundlePath: \"app/api/mining-units/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\api\\\\mining-units\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_dream_Desktop_Hash_Minings_hashcorex_src_app_api_mining_units_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmining-units%2Froute&page=%2Fapi%2Fmining-units%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmining-units%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/mining-units/route.ts":
/*!*******************************************!*\
  !*** ./src/app/api/mining-units/route.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/database */ \"(rsc)/./src/lib/database.ts\");\n/* harmony import */ var _lib_miningUnitEarnings__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/miningUnitEarnings */ \"(rsc)/./src/lib/miningUnitEarnings.ts\");\n/* harmony import */ var _lib_referral__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/referral */ \"(rsc)/./src/lib/referral.ts\");\n\n\n\n\n\n// GET - Fetch user's mining units with detailed earnings breakdown\nasync function GET(request) {\n    try {\n        const { authenticated, user } = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.authenticateRequest)(request);\n        if (!authenticated || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Not authenticated'\n            }, {\n                status: 401\n            });\n        }\n        // Get all mining units with detailed earnings breakdown\n        const miningUnits = await (0,_lib_miningUnitEarnings__WEBPACK_IMPORTED_MODULE_3__.getUserMiningUnitsWithEarnings)(user.id);\n        // Calculate additional information for each unit\n        const enrichedMiningUnits = miningUnits.map((unit)=>{\n            const totalEarnings = unit.miningEarnings + unit.referralEarnings + unit.binaryEarnings;\n            const maxEarnings = unit.investmentAmount * 5;\n            const progress = totalEarnings / maxEarnings * 100;\n            const remainingCapacity = Math.max(0, maxEarnings - totalEarnings);\n            return {\n                ...unit,\n                totalEarningsCalculated: totalEarnings,\n                progressPercentage: Math.min(progress, 100),\n                remainingCapacity,\n                maxEarnings,\n                willExpireSoon: progress >= 95\n            };\n        });\n        // Sort by creation date for FIFO order\n        const sortedUnits = enrichedMiningUnits.sort((a, b)=>new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: sortedUnits,\n            summary: {\n                totalUnits: sortedUnits.length,\n                activeUnits: sortedUnits.filter((unit)=>unit.status === 'ACTIVE').length,\n                expiredUnits: sortedUnits.filter((unit)=>unit.status === 'EXPIRED').length,\n                totalMiningEarnings: sortedUnits.reduce((sum, unit)=>sum + unit.miningEarnings, 0),\n                totalReferralEarnings: sortedUnits.reduce((sum, unit)=>sum + unit.referralEarnings, 0),\n                totalBinaryEarnings: sortedUnits.reduce((sum, unit)=>sum + unit.binaryEarnings, 0),\n                totalEarnings: sortedUnits.reduce((sum, unit)=>sum + (unit.miningEarnings + unit.referralEarnings + unit.binaryEarnings), 0),\n                totalInvestment: sortedUnits.reduce((sum, unit)=>sum + unit.investmentAmount, 0),\n                totalMiningPower: sortedUnits.reduce((sum, unit)=>sum + unit.thsAmount, 0)\n            }\n        });\n    } catch (error) {\n        console.error('Mining units fetch error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to fetch mining units'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - Purchase new mining unit\nasync function POST(request) {\n    try {\n        const { authenticated, user } = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.authenticateRequest)(request);\n        if (!authenticated || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Not authenticated'\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const { thsAmount, investmentAmount } = body;\n        // Validation\n        if (!thsAmount || !investmentAmount || thsAmount <= 0 || investmentAmount <= 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Invalid TH/s amount or investment amount'\n            }, {\n                status: 400\n            });\n        }\n        // Get minimum purchase amount from admin settings\n        const minPurchase = parseFloat(await _lib_database__WEBPACK_IMPORTED_MODULE_2__.adminSettingsDb.get('MINIMUM_PURCHASE') || '50');\n        if (investmentAmount < minPurchase) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: `Minimum purchase amount is $${minPurchase}`\n            }, {\n                status: 400\n            });\n        }\n        // Get TH/s price from admin settings\n        const thsPrice = parseFloat(await _lib_database__WEBPACK_IMPORTED_MODULE_2__.adminSettingsDb.get('THS_PRICE') || '50');\n        const expectedAmount = thsAmount * thsPrice;\n        // Allow small rounding differences (within 1%)\n        if (Math.abs(investmentAmount - expectedAmount) > expectedAmount * 0.01) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Investment amount does not match TH/s price'\n            }, {\n                status: 400\n            });\n        }\n        // Calculate dynamic ROI based on unit size\n        const { calculateDynamicROI } = await __webpack_require__.e(/*! import() */ \"_rsc_src_lib_mining_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/mining */ \"(rsc)/./src/lib/mining.ts\"));\n        const dynamicROI = await calculateDynamicROI(thsAmount);\n        // Check wallet balance before purchase\n        const walletBalance = await _lib_database__WEBPACK_IMPORTED_MODULE_2__.walletBalanceDb.getOrCreate(user.id);\n        if (walletBalance.availableBalance < investmentAmount) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: `Insufficient balance. Available: $${walletBalance.availableBalance.toFixed(2)}, Required: $${investmentAmount.toFixed(2)}`\n            }, {\n                status: 400\n            });\n        }\n        // Use the calculated dynamic ROI\n        const dailyROI = dynamicROI;\n        // Create mining unit\n        const miningUnit = await _lib_database__WEBPACK_IMPORTED_MODULE_2__.miningUnitDb.create({\n            userId: user.id,\n            thsAmount,\n            investmentAmount,\n            dailyROI\n        });\n        // Deduct amount from wallet balance\n        await _lib_database__WEBPACK_IMPORTED_MODULE_2__.walletBalanceDb.updateBalance(user.id, {\n            availableBalance: walletBalance.availableBalance - investmentAmount\n        });\n        // Create purchase transaction\n        await _lib_database__WEBPACK_IMPORTED_MODULE_2__.transactionDb.create({\n            userId: user.id,\n            type: 'PURCHASE',\n            amount: investmentAmount,\n            description: `Mining unit purchase - ${thsAmount} TH/s`,\n            status: 'COMPLETED'\n        });\n        // Note: User active status is now computed dynamically for binary tree display\n        // Process referral commissions and binary points\n        const sponsorInfo = await (0,_lib_referral__WEBPACK_IMPORTED_MODULE_4__.getSponsorInfo)(user.id);\n        if (sponsorInfo) {\n            try {\n                // Process direct referral bonus (10% to sponsor's wallet)\n                const bonusAmount = await (0,_lib_referral__WEBPACK_IMPORTED_MODULE_4__.processDirectReferralBonus)(sponsorInfo.id, investmentAmount, user.id);\n                console.log(`Direct referral bonus of $${bonusAmount} added to sponsor ${sponsorInfo.id}`);\n                // Add binary points to active upliners ($100 = 1 point)\n                await (0,_lib_referral__WEBPACK_IMPORTED_MODULE_4__.addBinaryPoints)(user.id, investmentAmount);\n                console.log(`Binary points added for investment of $${investmentAmount}`);\n            } catch (commissionError) {\n                console.error('Error processing commissions:', commissionError);\n            // Don't fail the purchase if commission processing fails\n            }\n        }\n        // Log the purchase\n        await _lib_database__WEBPACK_IMPORTED_MODULE_2__.systemLogDb.create({\n            action: 'MINING_UNIT_PURCHASED',\n            userId: user.id,\n            details: {\n                miningUnitId: miningUnit.id,\n                thsAmount,\n                investmentAmount,\n                dailyROI,\n                sponsorId: sponsorInfo?.id\n            },\n            ipAddress: request.headers.get('x-forwarded-for') || 'unknown',\n            userAgent: request.headers.get('user-agent') || 'unknown'\n        });\n        // Send email notification\n        try {\n            const { emailNotificationService } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/nodemailer\"), __webpack_require__.e(\"_rsc_src_lib_emailNotificationService_ts\")]).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/emailNotificationService */ \"(rsc)/./src/lib/emailNotificationService.ts\"));\n            await emailNotificationService.sendMiningUnitPurchaseNotification({\n                userId: user.id,\n                email: user.email,\n                firstName: user.firstName,\n                lastName: user.lastName,\n                thsAmount,\n                investmentAmount,\n                dailyROI,\n                purchaseDate: miningUnit.createdAt,\n                expiryDate: miningUnit.expiryDate\n            });\n        } catch (emailError) {\n            console.error('Failed to send mining unit purchase email:', emailError);\n        // Don't fail the purchase if email fails\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Mining unit purchased successfully',\n            data: miningUnit\n        });\n    } catch (error) {\n        console.error('Mining unit purchase error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to purchase mining unit'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9taW5pbmctdW5pdHMvcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUF3RDtBQUNQO0FBQzJEO0FBQ2xDO0FBQ21CO0FBRTdGLG1FQUFtRTtBQUM1RCxlQUFlVyxJQUFJQyxPQUFvQjtJQUM1QyxJQUFJO1FBQ0YsTUFBTSxFQUFFQyxhQUFhLEVBQUVDLElBQUksRUFBRSxHQUFHLE1BQU1iLDhEQUFtQkEsQ0FBQ1c7UUFFMUQsSUFBSSxDQUFDQyxpQkFBaUIsQ0FBQ0MsTUFBTTtZQUMzQixPQUFPZCxxREFBWUEsQ0FBQ2UsSUFBSSxDQUN0QjtnQkFBRUMsU0FBUztnQkFBT0MsT0FBTztZQUFvQixHQUM3QztnQkFBRUMsUUFBUTtZQUFJO1FBRWxCO1FBRUEsd0RBQXdEO1FBQ3hELE1BQU1DLGNBQWMsTUFBTVosdUZBQThCQSxDQUFDTyxLQUFLTSxFQUFFO1FBRWhFLGlEQUFpRDtRQUNqRCxNQUFNQyxzQkFBc0JGLFlBQVlHLEdBQUcsQ0FBQ0MsQ0FBQUE7WUFDMUMsTUFBTUMsZ0JBQWdCRCxLQUFLRSxjQUFjLEdBQUdGLEtBQUtHLGdCQUFnQixHQUFHSCxLQUFLSSxjQUFjO1lBQ3ZGLE1BQU1DLGNBQWNMLEtBQUtNLGdCQUFnQixHQUFHO1lBQzVDLE1BQU1DLFdBQVcsZ0JBQWlCRixjQUFlO1lBQ2pELE1BQU1HLG9CQUFvQkMsS0FBS0MsR0FBRyxDQUFDLEdBQUdMLGNBQWNKO1lBRXBELE9BQU87Z0JBQ0wsR0FBR0QsSUFBSTtnQkFDUFcseUJBQXlCVjtnQkFDekJXLG9CQUFvQkgsS0FBS0ksR0FBRyxDQUFDTixVQUFVO2dCQUN2Q0M7Z0JBQ0FIO2dCQUNBUyxnQkFBZ0JQLFlBQVk7WUFDOUI7UUFDRjtRQUVBLHVDQUF1QztRQUN2QyxNQUFNUSxjQUFjakIsb0JBQW9Ca0IsSUFBSSxDQUFDLENBQUNDLEdBQUdDLElBQy9DLElBQUlDLEtBQUtGLEVBQUVHLFNBQVMsRUFBRUMsT0FBTyxLQUFLLElBQUlGLEtBQUtELEVBQUVFLFNBQVMsRUFBRUMsT0FBTztRQUdqRSxPQUFPNUMscURBQVlBLENBQUNlLElBQUksQ0FBQztZQUN2QkMsU0FBUztZQUNUNkIsTUFBTVA7WUFDTlEsU0FBUztnQkFDUEMsWUFBWVQsWUFBWVUsTUFBTTtnQkFDOUJDLGFBQWFYLFlBQVlZLE1BQU0sQ0FBQzNCLENBQUFBLE9BQVFBLEtBQUtMLE1BQU0sS0FBSyxVQUFVOEIsTUFBTTtnQkFDeEVHLGNBQWNiLFlBQVlZLE1BQU0sQ0FBQzNCLENBQUFBLE9BQVFBLEtBQUtMLE1BQU0sS0FBSyxXQUFXOEIsTUFBTTtnQkFDMUVJLHFCQUFxQmQsWUFBWWUsTUFBTSxDQUFDLENBQUNDLEtBQUsvQixPQUFTK0IsTUFBTS9CLEtBQUtFLGNBQWMsRUFBRTtnQkFDbEY4Qix1QkFBdUJqQixZQUFZZSxNQUFNLENBQUMsQ0FBQ0MsS0FBSy9CLE9BQVMrQixNQUFNL0IsS0FBS0csZ0JBQWdCLEVBQUU7Z0JBQ3RGOEIscUJBQXFCbEIsWUFBWWUsTUFBTSxDQUFDLENBQUNDLEtBQUsvQixPQUFTK0IsTUFBTS9CLEtBQUtJLGNBQWMsRUFBRTtnQkFDbEZILGVBQWVjLFlBQVllLE1BQU0sQ0FBQyxDQUFDQyxLQUFLL0IsT0FBUytCLE1BQU8vQixDQUFBQSxLQUFLRSxjQUFjLEdBQUdGLEtBQUtHLGdCQUFnQixHQUFHSCxLQUFLSSxjQUFjLEdBQUc7Z0JBQzVIOEIsaUJBQWlCbkIsWUFBWWUsTUFBTSxDQUFDLENBQUNDLEtBQUsvQixPQUFTK0IsTUFBTS9CLEtBQUtNLGdCQUFnQixFQUFFO2dCQUNoRjZCLGtCQUFrQnBCLFlBQVllLE1BQU0sQ0FBQyxDQUFDQyxLQUFLL0IsT0FBUytCLE1BQU0vQixLQUFLb0MsU0FBUyxFQUFFO1lBQzVFO1FBQ0Y7SUFFRixFQUFFLE9BQU8xQyxPQUFZO1FBQ25CMkMsUUFBUTNDLEtBQUssQ0FBQyw2QkFBNkJBO1FBRTNDLE9BQU9qQixxREFBWUEsQ0FBQ2UsSUFBSSxDQUN0QjtZQUFFQyxTQUFTO1lBQU9DLE9BQU87UUFBK0IsR0FDeEQ7WUFBRUMsUUFBUTtRQUFJO0lBRWxCO0FBQ0Y7QUFFQSxrQ0FBa0M7QUFDM0IsZUFBZTJDLEtBQUtqRCxPQUFvQjtJQUM3QyxJQUFJO1FBQ0YsTUFBTSxFQUFFQyxhQUFhLEVBQUVDLElBQUksRUFBRSxHQUFHLE1BQU1iLDhEQUFtQkEsQ0FBQ1c7UUFFMUQsSUFBSSxDQUFDQyxpQkFBaUIsQ0FBQ0MsTUFBTTtZQUMzQixPQUFPZCxxREFBWUEsQ0FBQ2UsSUFBSSxDQUN0QjtnQkFBRUMsU0FBUztnQkFBT0MsT0FBTztZQUFvQixHQUM3QztnQkFBRUMsUUFBUTtZQUFJO1FBRWxCO1FBRUEsTUFBTTRDLE9BQU8sTUFBTWxELFFBQVFHLElBQUk7UUFDL0IsTUFBTSxFQUFFNEMsU0FBUyxFQUFFOUIsZ0JBQWdCLEVBQUUsR0FBR2lDO1FBRXhDLGFBQWE7UUFDYixJQUFJLENBQUNILGFBQWEsQ0FBQzlCLG9CQUFvQjhCLGFBQWEsS0FBSzlCLG9CQUFvQixHQUFHO1lBQzlFLE9BQU83QixxREFBWUEsQ0FBQ2UsSUFBSSxDQUN0QjtnQkFBRUMsU0FBUztnQkFBT0MsT0FBTztZQUEyQyxHQUNwRTtnQkFBRUMsUUFBUTtZQUFJO1FBRWxCO1FBRUEsa0RBQWtEO1FBQ2xELE1BQU02QyxjQUFjQyxXQUFXLE1BQU01RCwwREFBZUEsQ0FBQzZELEdBQUcsQ0FBQyx1QkFBdUI7UUFDaEYsSUFBSXBDLG1CQUFtQmtDLGFBQWE7WUFDbEMsT0FBTy9ELHFEQUFZQSxDQUFDZSxJQUFJLENBQ3RCO2dCQUFFQyxTQUFTO2dCQUFPQyxPQUFPLENBQUMsNEJBQTRCLEVBQUU4QyxhQUFhO1lBQUMsR0FDdEU7Z0JBQUU3QyxRQUFRO1lBQUk7UUFFbEI7UUFFQSxxQ0FBcUM7UUFDckMsTUFBTWdELFdBQVdGLFdBQVcsTUFBTTVELDBEQUFlQSxDQUFDNkQsR0FBRyxDQUFDLGdCQUFnQjtRQUN0RSxNQUFNRSxpQkFBaUJSLFlBQVlPO1FBRW5DLCtDQUErQztRQUMvQyxJQUFJbEMsS0FBS29DLEdBQUcsQ0FBQ3ZDLG1CQUFtQnNDLGtCQUFrQkEsaUJBQWlCLE1BQU07WUFDdkUsT0FBT25FLHFEQUFZQSxDQUFDZSxJQUFJLENBQ3RCO2dCQUFFQyxTQUFTO2dCQUFPQyxPQUFPO1lBQThDLEdBQ3ZFO2dCQUFFQyxRQUFRO1lBQUk7UUFFbEI7UUFFQSwyQ0FBMkM7UUFDM0MsTUFBTSxFQUFFbUQsbUJBQW1CLEVBQUUsR0FBRyxNQUFNLG9LQUFzQjtRQUM1RCxNQUFNQyxhQUFhLE1BQU1ELG9CQUFvQlY7UUFFN0MsdUNBQXVDO1FBQ3ZDLE1BQU1ZLGdCQUFnQixNQUFNakUsMERBQWVBLENBQUNrRSxXQUFXLENBQUMxRCxLQUFLTSxFQUFFO1FBRS9ELElBQUltRCxjQUFjRSxnQkFBZ0IsR0FBRzVDLGtCQUFrQjtZQUNyRCxPQUFPN0IscURBQVlBLENBQUNlLElBQUksQ0FDdEI7Z0JBQ0VDLFNBQVM7Z0JBQ1RDLE9BQU8sQ0FBQyxrQ0FBa0MsRUFBRXNELGNBQWNFLGdCQUFnQixDQUFDQyxPQUFPLENBQUMsR0FBRyxhQUFhLEVBQUU3QyxpQkFBaUI2QyxPQUFPLENBQUMsSUFBSTtZQUNwSSxHQUNBO2dCQUFFeEQsUUFBUTtZQUFJO1FBRWxCO1FBRUEsaUNBQWlDO1FBQ2pDLE1BQU15RCxXQUFXTDtRQUVqQixxQkFBcUI7UUFDckIsTUFBTU0sYUFBYSxNQUFNMUUsdURBQVlBLENBQUMyRSxNQUFNLENBQUM7WUFDM0NDLFFBQVFoRSxLQUFLTSxFQUFFO1lBQ2Z1QztZQUNBOUI7WUFDQThDO1FBQ0Y7UUFFQSxvQ0FBb0M7UUFDcEMsTUFBTXJFLDBEQUFlQSxDQUFDeUUsYUFBYSxDQUFDakUsS0FBS00sRUFBRSxFQUFFO1lBQzNDcUQsa0JBQWtCRixjQUFjRSxnQkFBZ0IsR0FBRzVDO1FBQ3JEO1FBRUEsOEJBQThCO1FBQzlCLE1BQU0xQix3REFBYUEsQ0FBQzBFLE1BQU0sQ0FBQztZQUN6QkMsUUFBUWhFLEtBQUtNLEVBQUU7WUFDZjRELE1BQU07WUFDTkMsUUFBUXBEO1lBQ1JxRCxhQUFhLENBQUMsdUJBQXVCLEVBQUV2QixVQUFVLEtBQUssQ0FBQztZQUN2RHpDLFFBQVE7UUFDVjtRQUVBLCtFQUErRTtRQUUvRSxpREFBaUQ7UUFDakQsTUFBTWlFLGNBQWMsTUFBTXpFLDZEQUFjQSxDQUFDSSxLQUFLTSxFQUFFO1FBQ2hELElBQUkrRCxhQUFhO1lBQ2YsSUFBSTtnQkFDRiwwREFBMEQ7Z0JBQzFELE1BQU1DLGNBQWMsTUFBTTVFLHlFQUEwQkEsQ0FBQzJFLFlBQVkvRCxFQUFFLEVBQUVTLGtCQUFrQmYsS0FBS00sRUFBRTtnQkFDOUZ3QyxRQUFReUIsR0FBRyxDQUFDLENBQUMsMEJBQTBCLEVBQUVELFlBQVksa0JBQWtCLEVBQUVELFlBQVkvRCxFQUFFLEVBQUU7Z0JBRXpGLHdEQUF3RDtnQkFDeEQsTUFBTVgsOERBQWVBLENBQUNLLEtBQUtNLEVBQUUsRUFBRVM7Z0JBQy9CK0IsUUFBUXlCLEdBQUcsQ0FBQyxDQUFDLHVDQUF1QyxFQUFFeEQsa0JBQWtCO1lBQzFFLEVBQUUsT0FBT3lELGlCQUFpQjtnQkFDeEIxQixRQUFRM0MsS0FBSyxDQUFDLGlDQUFpQ3FFO1lBQy9DLHlEQUF5RDtZQUMzRDtRQUNGO1FBRUEsbUJBQW1CO1FBQ25CLE1BQU1qRixzREFBV0EsQ0FBQ3dFLE1BQU0sQ0FBQztZQUN2QlUsUUFBUTtZQUNSVCxRQUFRaEUsS0FBS00sRUFBRTtZQUNmb0UsU0FBUztnQkFDUEMsY0FBY2IsV0FBV3hELEVBQUU7Z0JBQzNCdUM7Z0JBQ0E5QjtnQkFDQThDO2dCQUNBZSxXQUFXUCxhQUFhL0Q7WUFDMUI7WUFDQXVFLFdBQVcvRSxRQUFRZ0YsT0FBTyxDQUFDM0IsR0FBRyxDQUFDLHNCQUFzQjtZQUNyRDRCLFdBQVdqRixRQUFRZ0YsT0FBTyxDQUFDM0IsR0FBRyxDQUFDLGlCQUFpQjtRQUNsRDtRQUVBLDBCQUEwQjtRQUMxQixJQUFJO1lBQ0YsTUFBTSxFQUFFNkIsd0JBQXdCLEVBQUUsR0FBRyxNQUFNLDJSQUF3QztZQUNuRixNQUFNQSx5QkFBeUJDLGtDQUFrQyxDQUFDO2dCQUNoRWpCLFFBQVFoRSxLQUFLTSxFQUFFO2dCQUNmNEUsT0FBT2xGLEtBQUtrRixLQUFLO2dCQUNqQkMsV0FBV25GLEtBQUttRixTQUFTO2dCQUN6QkMsVUFBVXBGLEtBQUtvRixRQUFRO2dCQUN2QnZDO2dCQUNBOUI7Z0JBQ0E4QztnQkFDQXdCLGNBQWN2QixXQUFXakMsU0FBUztnQkFDbEN5RCxZQUFZeEIsV0FBV3dCLFVBQVU7WUFDbkM7UUFDRixFQUFFLE9BQU9DLFlBQVk7WUFDbkJ6QyxRQUFRM0MsS0FBSyxDQUFDLDhDQUE4Q29GO1FBQzVELHlDQUF5QztRQUMzQztRQUVBLE9BQU9yRyxxREFBWUEsQ0FBQ2UsSUFBSSxDQUFDO1lBQ3ZCQyxTQUFTO1lBQ1RzRixTQUFTO1lBQ1R6RCxNQUFNK0I7UUFDUjtJQUVGLEVBQUUsT0FBTzNELE9BQVk7UUFDbkIyQyxRQUFRM0MsS0FBSyxDQUFDLCtCQUErQkE7UUFFN0MsT0FBT2pCLHFEQUFZQSxDQUFDZSxJQUFJLENBQ3RCO1lBQUVDLFNBQVM7WUFBT0MsT0FBTztRQUFpQyxHQUMxRDtZQUFFQyxRQUFRO1FBQUk7SUFFbEI7QUFDRiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxkcmVhbVxcRGVza3RvcFxcSGFzaF9NaW5pbmdzXFxoYXNoY29yZXhcXHNyY1xcYXBwXFxhcGlcXG1pbmluZy11bml0c1xccm91dGUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTmV4dFJlcXVlc3QsIE5leHRSZXNwb25zZSB9IGZyb20gJ25leHQvc2VydmVyJztcbmltcG9ydCB7IGF1dGhlbnRpY2F0ZVJlcXVlc3QgfSBmcm9tICdAL2xpYi9hdXRoJztcbmltcG9ydCB7IG1pbmluZ1VuaXREYiwgdHJhbnNhY3Rpb25EYiwgYWRtaW5TZXR0aW5nc0RiLCBzeXN0ZW1Mb2dEYiwgd2FsbGV0QmFsYW5jZURiIH0gZnJvbSAnQC9saWIvZGF0YWJhc2UnO1xuaW1wb3J0IHsgZ2V0VXNlck1pbmluZ1VuaXRzV2l0aEVhcm5pbmdzIH0gZnJvbSAnQC9saWIvbWluaW5nVW5pdEVhcm5pbmdzJztcbmltcG9ydCB7IHByb2Nlc3NEaXJlY3RSZWZlcnJhbEJvbnVzLCBhZGRCaW5hcnlQb2ludHMsIGdldFNwb25zb3JJbmZvIH0gZnJvbSAnQC9saWIvcmVmZXJyYWwnO1xuXG4vLyBHRVQgLSBGZXRjaCB1c2VyJ3MgbWluaW5nIHVuaXRzIHdpdGggZGV0YWlsZWQgZWFybmluZ3MgYnJlYWtkb3duXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gR0VUKHJlcXVlc3Q6IE5leHRSZXF1ZXN0KSB7XG4gIHRyeSB7XG4gICAgY29uc3QgeyBhdXRoZW50aWNhdGVkLCB1c2VyIH0gPSBhd2FpdCBhdXRoZW50aWNhdGVSZXF1ZXN0KHJlcXVlc3QpO1xuXG4gICAgaWYgKCFhdXRoZW50aWNhdGVkIHx8ICF1c2VyKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiAnTm90IGF1dGhlbnRpY2F0ZWQnIH0sXG4gICAgICAgIHsgc3RhdHVzOiA0MDEgfVxuICAgICAgKTtcbiAgICB9XG5cbiAgICAvLyBHZXQgYWxsIG1pbmluZyB1bml0cyB3aXRoIGRldGFpbGVkIGVhcm5pbmdzIGJyZWFrZG93blxuICAgIGNvbnN0IG1pbmluZ1VuaXRzID0gYXdhaXQgZ2V0VXNlck1pbmluZ1VuaXRzV2l0aEVhcm5pbmdzKHVzZXIuaWQpO1xuXG4gICAgLy8gQ2FsY3VsYXRlIGFkZGl0aW9uYWwgaW5mb3JtYXRpb24gZm9yIGVhY2ggdW5pdFxuICAgIGNvbnN0IGVucmljaGVkTWluaW5nVW5pdHMgPSBtaW5pbmdVbml0cy5tYXAodW5pdCA9PiB7XG4gICAgICBjb25zdCB0b3RhbEVhcm5pbmdzID0gdW5pdC5taW5pbmdFYXJuaW5ncyArIHVuaXQucmVmZXJyYWxFYXJuaW5ncyArIHVuaXQuYmluYXJ5RWFybmluZ3M7XG4gICAgICBjb25zdCBtYXhFYXJuaW5ncyA9IHVuaXQuaW52ZXN0bWVudEFtb3VudCAqIDU7XG4gICAgICBjb25zdCBwcm9ncmVzcyA9ICh0b3RhbEVhcm5pbmdzIC8gbWF4RWFybmluZ3MpICogMTAwO1xuICAgICAgY29uc3QgcmVtYWluaW5nQ2FwYWNpdHkgPSBNYXRoLm1heCgwLCBtYXhFYXJuaW5ncyAtIHRvdGFsRWFybmluZ3MpO1xuXG4gICAgICByZXR1cm4ge1xuICAgICAgICAuLi51bml0LFxuICAgICAgICB0b3RhbEVhcm5pbmdzQ2FsY3VsYXRlZDogdG90YWxFYXJuaW5ncyxcbiAgICAgICAgcHJvZ3Jlc3NQZXJjZW50YWdlOiBNYXRoLm1pbihwcm9ncmVzcywgMTAwKSxcbiAgICAgICAgcmVtYWluaW5nQ2FwYWNpdHksXG4gICAgICAgIG1heEVhcm5pbmdzLFxuICAgICAgICB3aWxsRXhwaXJlU29vbjogcHJvZ3Jlc3MgPj0gOTUsIC8vIEZsYWcgdW5pdHMgY2xvc2UgdG8gZXhwaXJhdGlvblxuICAgICAgfTtcbiAgICB9KTtcblxuICAgIC8vIFNvcnQgYnkgY3JlYXRpb24gZGF0ZSBmb3IgRklGTyBvcmRlclxuICAgIGNvbnN0IHNvcnRlZFVuaXRzID0gZW5yaWNoZWRNaW5pbmdVbml0cy5zb3J0KChhLCBiKSA9PlxuICAgICAgbmV3IERhdGUoYS5jcmVhdGVkQXQpLmdldFRpbWUoKSAtIG5ldyBEYXRlKGIuY3JlYXRlZEF0KS5nZXRUaW1lKClcbiAgICApO1xuXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICBkYXRhOiBzb3J0ZWRVbml0cyxcbiAgICAgIHN1bW1hcnk6IHtcbiAgICAgICAgdG90YWxVbml0czogc29ydGVkVW5pdHMubGVuZ3RoLFxuICAgICAgICBhY3RpdmVVbml0czogc29ydGVkVW5pdHMuZmlsdGVyKHVuaXQgPT4gdW5pdC5zdGF0dXMgPT09ICdBQ1RJVkUnKS5sZW5ndGgsXG4gICAgICAgIGV4cGlyZWRVbml0czogc29ydGVkVW5pdHMuZmlsdGVyKHVuaXQgPT4gdW5pdC5zdGF0dXMgPT09ICdFWFBJUkVEJykubGVuZ3RoLFxuICAgICAgICB0b3RhbE1pbmluZ0Vhcm5pbmdzOiBzb3J0ZWRVbml0cy5yZWR1Y2UoKHN1bSwgdW5pdCkgPT4gc3VtICsgdW5pdC5taW5pbmdFYXJuaW5ncywgMCksXG4gICAgICAgIHRvdGFsUmVmZXJyYWxFYXJuaW5nczogc29ydGVkVW5pdHMucmVkdWNlKChzdW0sIHVuaXQpID0+IHN1bSArIHVuaXQucmVmZXJyYWxFYXJuaW5ncywgMCksXG4gICAgICAgIHRvdGFsQmluYXJ5RWFybmluZ3M6IHNvcnRlZFVuaXRzLnJlZHVjZSgoc3VtLCB1bml0KSA9PiBzdW0gKyB1bml0LmJpbmFyeUVhcm5pbmdzLCAwKSxcbiAgICAgICAgdG90YWxFYXJuaW5nczogc29ydGVkVW5pdHMucmVkdWNlKChzdW0sIHVuaXQpID0+IHN1bSArICh1bml0Lm1pbmluZ0Vhcm5pbmdzICsgdW5pdC5yZWZlcnJhbEVhcm5pbmdzICsgdW5pdC5iaW5hcnlFYXJuaW5ncyksIDApLFxuICAgICAgICB0b3RhbEludmVzdG1lbnQ6IHNvcnRlZFVuaXRzLnJlZHVjZSgoc3VtLCB1bml0KSA9PiBzdW0gKyB1bml0LmludmVzdG1lbnRBbW91bnQsIDApLFxuICAgICAgICB0b3RhbE1pbmluZ1Bvd2VyOiBzb3J0ZWRVbml0cy5yZWR1Y2UoKHN1bSwgdW5pdCkgPT4gc3VtICsgdW5pdC50aHNBbW91bnQsIDApLFxuICAgICAgfSxcbiAgICB9KTtcblxuICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgY29uc29sZS5lcnJvcignTWluaW5nIHVuaXRzIGZldGNoIGVycm9yOicsIGVycm9yKTtcblxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiAnRmFpbGVkIHRvIGZldGNoIG1pbmluZyB1bml0cycgfSxcbiAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgICk7XG4gIH1cbn1cblxuLy8gUE9TVCAtIFB1cmNoYXNlIG5ldyBtaW5pbmcgdW5pdFxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIFBPU1QocmVxdWVzdDogTmV4dFJlcXVlc3QpIHtcbiAgdHJ5IHtcbiAgICBjb25zdCB7IGF1dGhlbnRpY2F0ZWQsIHVzZXIgfSA9IGF3YWl0IGF1dGhlbnRpY2F0ZVJlcXVlc3QocmVxdWVzdCk7XG5cbiAgICBpZiAoIWF1dGhlbnRpY2F0ZWQgfHwgIXVzZXIpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgeyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICdOb3QgYXV0aGVudGljYXRlZCcgfSxcbiAgICAgICAgeyBzdGF0dXM6IDQwMSB9XG4gICAgICApO1xuICAgIH1cblxuICAgIGNvbnN0IGJvZHkgPSBhd2FpdCByZXF1ZXN0Lmpzb24oKTtcbiAgICBjb25zdCB7IHRoc0Ftb3VudCwgaW52ZXN0bWVudEFtb3VudCB9ID0gYm9keTtcblxuICAgIC8vIFZhbGlkYXRpb25cbiAgICBpZiAoIXRoc0Ftb3VudCB8fCAhaW52ZXN0bWVudEFtb3VudCB8fCB0aHNBbW91bnQgPD0gMCB8fCBpbnZlc3RtZW50QW1vdW50IDw9IDApIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgeyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICdJbnZhbGlkIFRIL3MgYW1vdW50IG9yIGludmVzdG1lbnQgYW1vdW50JyB9LFxuICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgICk7XG4gICAgfVxuXG4gICAgLy8gR2V0IG1pbmltdW0gcHVyY2hhc2UgYW1vdW50IGZyb20gYWRtaW4gc2V0dGluZ3NcbiAgICBjb25zdCBtaW5QdXJjaGFzZSA9IHBhcnNlRmxvYXQoYXdhaXQgYWRtaW5TZXR0aW5nc0RiLmdldCgnTUlOSU1VTV9QVVJDSEFTRScpIHx8ICc1MCcpO1xuICAgIGlmIChpbnZlc3RtZW50QW1vdW50IDwgbWluUHVyY2hhc2UpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgeyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6IGBNaW5pbXVtIHB1cmNoYXNlIGFtb3VudCBpcyAkJHttaW5QdXJjaGFzZX1gIH0sXG4gICAgICAgIHsgc3RhdHVzOiA0MDAgfVxuICAgICAgKTtcbiAgICB9XG5cbiAgICAvLyBHZXQgVEgvcyBwcmljZSBmcm9tIGFkbWluIHNldHRpbmdzXG4gICAgY29uc3QgdGhzUHJpY2UgPSBwYXJzZUZsb2F0KGF3YWl0IGFkbWluU2V0dGluZ3NEYi5nZXQoJ1RIU19QUklDRScpIHx8ICc1MCcpO1xuICAgIGNvbnN0IGV4cGVjdGVkQW1vdW50ID0gdGhzQW1vdW50ICogdGhzUHJpY2U7XG4gICAgXG4gICAgLy8gQWxsb3cgc21hbGwgcm91bmRpbmcgZGlmZmVyZW5jZXMgKHdpdGhpbiAxJSlcbiAgICBpZiAoTWF0aC5hYnMoaW52ZXN0bWVudEFtb3VudCAtIGV4cGVjdGVkQW1vdW50KSA+IGV4cGVjdGVkQW1vdW50ICogMC4wMSkge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ0ludmVzdG1lbnQgYW1vdW50IGRvZXMgbm90IG1hdGNoIFRIL3MgcHJpY2UnIH0sXG4gICAgICAgIHsgc3RhdHVzOiA0MDAgfVxuICAgICAgKTtcbiAgICB9XG5cbiAgICAvLyBDYWxjdWxhdGUgZHluYW1pYyBST0kgYmFzZWQgb24gdW5pdCBzaXplXG4gICAgY29uc3QgeyBjYWxjdWxhdGVEeW5hbWljUk9JIH0gPSBhd2FpdCBpbXBvcnQoJ0AvbGliL21pbmluZycpO1xuICAgIGNvbnN0IGR5bmFtaWNST0kgPSBhd2FpdCBjYWxjdWxhdGVEeW5hbWljUk9JKHRoc0Ftb3VudCk7XG4gICAgXG4gICAgLy8gQ2hlY2sgd2FsbGV0IGJhbGFuY2UgYmVmb3JlIHB1cmNoYXNlXG4gICAgY29uc3Qgd2FsbGV0QmFsYW5jZSA9IGF3YWl0IHdhbGxldEJhbGFuY2VEYi5nZXRPckNyZWF0ZSh1c2VyLmlkKTtcblxuICAgIGlmICh3YWxsZXRCYWxhbmNlLmF2YWlsYWJsZUJhbGFuY2UgPCBpbnZlc3RtZW50QW1vdW50KSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgIHtcbiAgICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgICBlcnJvcjogYEluc3VmZmljaWVudCBiYWxhbmNlLiBBdmFpbGFibGU6ICQke3dhbGxldEJhbGFuY2UuYXZhaWxhYmxlQmFsYW5jZS50b0ZpeGVkKDIpfSwgUmVxdWlyZWQ6ICQke2ludmVzdG1lbnRBbW91bnQudG9GaXhlZCgyKX1gXG4gICAgICAgIH0sXG4gICAgICAgIHsgc3RhdHVzOiA0MDAgfVxuICAgICAgKTtcbiAgICB9XG5cbiAgICAvLyBVc2UgdGhlIGNhbGN1bGF0ZWQgZHluYW1pYyBST0lcbiAgICBjb25zdCBkYWlseVJPSSA9IGR5bmFtaWNST0k7XG5cbiAgICAvLyBDcmVhdGUgbWluaW5nIHVuaXRcbiAgICBjb25zdCBtaW5pbmdVbml0ID0gYXdhaXQgbWluaW5nVW5pdERiLmNyZWF0ZSh7XG4gICAgICB1c2VySWQ6IHVzZXIuaWQsXG4gICAgICB0aHNBbW91bnQsXG4gICAgICBpbnZlc3RtZW50QW1vdW50LFxuICAgICAgZGFpbHlST0ksXG4gICAgfSk7XG5cbiAgICAvLyBEZWR1Y3QgYW1vdW50IGZyb20gd2FsbGV0IGJhbGFuY2VcbiAgICBhd2FpdCB3YWxsZXRCYWxhbmNlRGIudXBkYXRlQmFsYW5jZSh1c2VyLmlkLCB7XG4gICAgICBhdmFpbGFibGVCYWxhbmNlOiB3YWxsZXRCYWxhbmNlLmF2YWlsYWJsZUJhbGFuY2UgLSBpbnZlc3RtZW50QW1vdW50LFxuICAgIH0pO1xuXG4gICAgLy8gQ3JlYXRlIHB1cmNoYXNlIHRyYW5zYWN0aW9uXG4gICAgYXdhaXQgdHJhbnNhY3Rpb25EYi5jcmVhdGUoe1xuICAgICAgdXNlcklkOiB1c2VyLmlkLFxuICAgICAgdHlwZTogJ1BVUkNIQVNFJyxcbiAgICAgIGFtb3VudDogaW52ZXN0bWVudEFtb3VudCxcbiAgICAgIGRlc2NyaXB0aW9uOiBgTWluaW5nIHVuaXQgcHVyY2hhc2UgLSAke3Roc0Ftb3VudH0gVEgvc2AsXG4gICAgICBzdGF0dXM6ICdDT01QTEVURUQnLFxuICAgIH0pO1xuXG4gICAgLy8gTm90ZTogVXNlciBhY3RpdmUgc3RhdHVzIGlzIG5vdyBjb21wdXRlZCBkeW5hbWljYWxseSBmb3IgYmluYXJ5IHRyZWUgZGlzcGxheVxuXG4gICAgLy8gUHJvY2VzcyByZWZlcnJhbCBjb21taXNzaW9ucyBhbmQgYmluYXJ5IHBvaW50c1xuICAgIGNvbnN0IHNwb25zb3JJbmZvID0gYXdhaXQgZ2V0U3BvbnNvckluZm8odXNlci5pZCk7XG4gICAgaWYgKHNwb25zb3JJbmZvKSB7XG4gICAgICB0cnkge1xuICAgICAgICAvLyBQcm9jZXNzIGRpcmVjdCByZWZlcnJhbCBib251cyAoMTAlIHRvIHNwb25zb3IncyB3YWxsZXQpXG4gICAgICAgIGNvbnN0IGJvbnVzQW1vdW50ID0gYXdhaXQgcHJvY2Vzc0RpcmVjdFJlZmVycmFsQm9udXMoc3BvbnNvckluZm8uaWQsIGludmVzdG1lbnRBbW91bnQsIHVzZXIuaWQpO1xuICAgICAgICBjb25zb2xlLmxvZyhgRGlyZWN0IHJlZmVycmFsIGJvbnVzIG9mICQke2JvbnVzQW1vdW50fSBhZGRlZCB0byBzcG9uc29yICR7c3BvbnNvckluZm8uaWR9YCk7XG5cbiAgICAgICAgLy8gQWRkIGJpbmFyeSBwb2ludHMgdG8gYWN0aXZlIHVwbGluZXJzICgkMTAwID0gMSBwb2ludClcbiAgICAgICAgYXdhaXQgYWRkQmluYXJ5UG9pbnRzKHVzZXIuaWQsIGludmVzdG1lbnRBbW91bnQpO1xuICAgICAgICBjb25zb2xlLmxvZyhgQmluYXJ5IHBvaW50cyBhZGRlZCBmb3IgaW52ZXN0bWVudCBvZiAkJHtpbnZlc3RtZW50QW1vdW50fWApO1xuICAgICAgfSBjYXRjaCAoY29tbWlzc2lvbkVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHByb2Nlc3NpbmcgY29tbWlzc2lvbnM6JywgY29tbWlzc2lvbkVycm9yKTtcbiAgICAgICAgLy8gRG9uJ3QgZmFpbCB0aGUgcHVyY2hhc2UgaWYgY29tbWlzc2lvbiBwcm9jZXNzaW5nIGZhaWxzXG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gTG9nIHRoZSBwdXJjaGFzZVxuICAgIGF3YWl0IHN5c3RlbUxvZ0RiLmNyZWF0ZSh7XG4gICAgICBhY3Rpb246ICdNSU5JTkdfVU5JVF9QVVJDSEFTRUQnLFxuICAgICAgdXNlcklkOiB1c2VyLmlkLFxuICAgICAgZGV0YWlsczoge1xuICAgICAgICBtaW5pbmdVbml0SWQ6IG1pbmluZ1VuaXQuaWQsXG4gICAgICAgIHRoc0Ftb3VudCxcbiAgICAgICAgaW52ZXN0bWVudEFtb3VudCxcbiAgICAgICAgZGFpbHlST0ksXG4gICAgICAgIHNwb25zb3JJZDogc3BvbnNvckluZm8/LmlkLFxuICAgICAgfSxcbiAgICAgIGlwQWRkcmVzczogcmVxdWVzdC5oZWFkZXJzLmdldCgneC1mb3J3YXJkZWQtZm9yJykgfHwgJ3Vua25vd24nLFxuICAgICAgdXNlckFnZW50OiByZXF1ZXN0LmhlYWRlcnMuZ2V0KCd1c2VyLWFnZW50JykgfHwgJ3Vua25vd24nLFxuICAgIH0pO1xuXG4gICAgLy8gU2VuZCBlbWFpbCBub3RpZmljYXRpb25cbiAgICB0cnkge1xuICAgICAgY29uc3QgeyBlbWFpbE5vdGlmaWNhdGlvblNlcnZpY2UgfSA9IGF3YWl0IGltcG9ydCgnQC9saWIvZW1haWxOb3RpZmljYXRpb25TZXJ2aWNlJyk7XG4gICAgICBhd2FpdCBlbWFpbE5vdGlmaWNhdGlvblNlcnZpY2Uuc2VuZE1pbmluZ1VuaXRQdXJjaGFzZU5vdGlmaWNhdGlvbih7XG4gICAgICAgIHVzZXJJZDogdXNlci5pZCxcbiAgICAgICAgZW1haWw6IHVzZXIuZW1haWwsXG4gICAgICAgIGZpcnN0TmFtZTogdXNlci5maXJzdE5hbWUsXG4gICAgICAgIGxhc3ROYW1lOiB1c2VyLmxhc3ROYW1lLFxuICAgICAgICB0aHNBbW91bnQsXG4gICAgICAgIGludmVzdG1lbnRBbW91bnQsXG4gICAgICAgIGRhaWx5Uk9JLFxuICAgICAgICBwdXJjaGFzZURhdGU6IG1pbmluZ1VuaXQuY3JlYXRlZEF0LFxuICAgICAgICBleHBpcnlEYXRlOiBtaW5pbmdVbml0LmV4cGlyeURhdGUsXG4gICAgICB9KTtcbiAgICB9IGNhdGNoIChlbWFpbEVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gc2VuZCBtaW5pbmcgdW5pdCBwdXJjaGFzZSBlbWFpbDonLCBlbWFpbEVycm9yKTtcbiAgICAgIC8vIERvbid0IGZhaWwgdGhlIHB1cmNoYXNlIGlmIGVtYWlsIGZhaWxzXG4gICAgfVxuXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICBtZXNzYWdlOiAnTWluaW5nIHVuaXQgcHVyY2hhc2VkIHN1Y2Nlc3NmdWxseScsXG4gICAgICBkYXRhOiBtaW5pbmdVbml0LFxuICAgIH0pO1xuXG4gIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICBjb25zb2xlLmVycm9yKCdNaW5pbmcgdW5pdCBwdXJjaGFzZSBlcnJvcjonLCBlcnJvcik7XG4gICAgXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgeyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICdGYWlsZWQgdG8gcHVyY2hhc2UgbWluaW5nIHVuaXQnIH0sXG4gICAgICB7IHN0YXR1czogNTAwIH1cbiAgICApO1xuICB9XG59XG4iXSwibmFtZXMiOlsiTmV4dFJlc3BvbnNlIiwiYXV0aGVudGljYXRlUmVxdWVzdCIsIm1pbmluZ1VuaXREYiIsInRyYW5zYWN0aW9uRGIiLCJhZG1pblNldHRpbmdzRGIiLCJzeXN0ZW1Mb2dEYiIsIndhbGxldEJhbGFuY2VEYiIsImdldFVzZXJNaW5pbmdVbml0c1dpdGhFYXJuaW5ncyIsInByb2Nlc3NEaXJlY3RSZWZlcnJhbEJvbnVzIiwiYWRkQmluYXJ5UG9pbnRzIiwiZ2V0U3BvbnNvckluZm8iLCJHRVQiLCJyZXF1ZXN0IiwiYXV0aGVudGljYXRlZCIsInVzZXIiLCJqc29uIiwic3VjY2VzcyIsImVycm9yIiwic3RhdHVzIiwibWluaW5nVW5pdHMiLCJpZCIsImVucmljaGVkTWluaW5nVW5pdHMiLCJtYXAiLCJ1bml0IiwidG90YWxFYXJuaW5ncyIsIm1pbmluZ0Vhcm5pbmdzIiwicmVmZXJyYWxFYXJuaW5ncyIsImJpbmFyeUVhcm5pbmdzIiwibWF4RWFybmluZ3MiLCJpbnZlc3RtZW50QW1vdW50IiwicHJvZ3Jlc3MiLCJyZW1haW5pbmdDYXBhY2l0eSIsIk1hdGgiLCJtYXgiLCJ0b3RhbEVhcm5pbmdzQ2FsY3VsYXRlZCIsInByb2dyZXNzUGVyY2VudGFnZSIsIm1pbiIsIndpbGxFeHBpcmVTb29uIiwic29ydGVkVW5pdHMiLCJzb3J0IiwiYSIsImIiLCJEYXRlIiwiY3JlYXRlZEF0IiwiZ2V0VGltZSIsImRhdGEiLCJzdW1tYXJ5IiwidG90YWxVbml0cyIsImxlbmd0aCIsImFjdGl2ZVVuaXRzIiwiZmlsdGVyIiwiZXhwaXJlZFVuaXRzIiwidG90YWxNaW5pbmdFYXJuaW5ncyIsInJlZHVjZSIsInN1bSIsInRvdGFsUmVmZXJyYWxFYXJuaW5ncyIsInRvdGFsQmluYXJ5RWFybmluZ3MiLCJ0b3RhbEludmVzdG1lbnQiLCJ0b3RhbE1pbmluZ1Bvd2VyIiwidGhzQW1vdW50IiwiY29uc29sZSIsIlBPU1QiLCJib2R5IiwibWluUHVyY2hhc2UiLCJwYXJzZUZsb2F0IiwiZ2V0IiwidGhzUHJpY2UiLCJleHBlY3RlZEFtb3VudCIsImFicyIsImNhbGN1bGF0ZUR5bmFtaWNST0kiLCJkeW5hbWljUk9JIiwid2FsbGV0QmFsYW5jZSIsImdldE9yQ3JlYXRlIiwiYXZhaWxhYmxlQmFsYW5jZSIsInRvRml4ZWQiLCJkYWlseVJPSSIsIm1pbmluZ1VuaXQiLCJjcmVhdGUiLCJ1c2VySWQiLCJ1cGRhdGVCYWxhbmNlIiwidHlwZSIsImFtb3VudCIsImRlc2NyaXB0aW9uIiwic3BvbnNvckluZm8iLCJib251c0Ftb3VudCIsImxvZyIsImNvbW1pc3Npb25FcnJvciIsImFjdGlvbiIsImRldGFpbHMiLCJtaW5pbmdVbml0SWQiLCJzcG9uc29ySWQiLCJpcEFkZHJlc3MiLCJoZWFkZXJzIiwidXNlckFnZW50IiwiZW1haWxOb3RpZmljYXRpb25TZXJ2aWNlIiwic2VuZE1pbmluZ1VuaXRQdXJjaGFzZU5vdGlmaWNhdGlvbiIsImVtYWlsIiwiZmlyc3ROYW1lIiwibGFzdE5hbWUiLCJwdXJjaGFzZURhdGUiLCJleHBpcnlEYXRlIiwiZW1haWxFcnJvciIsIm1lc3NhZ2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/mining-units/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authenticateRequest: () => (/* binding */ authenticateRequest),\n/* harmony export */   createSession: () => (/* binding */ createSession),\n/* harmony export */   generateReferralId: () => (/* binding */ generateReferralId),\n/* harmony export */   generateToken: () => (/* binding */ generateToken),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   isAdmin: () => (/* binding */ isAdmin),\n/* harmony export */   loginUser: () => (/* binding */ loginUser),\n/* harmony export */   registerUser: () => (/* binding */ registerUser),\n/* harmony export */   validateEmail: () => (/* binding */ validateEmail),\n/* harmony export */   validatePassword: () => (/* binding */ validatePassword),\n/* harmony export */   validateSession: () => (/* binding */ validateSession),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./database */ \"(rsc)/./src/lib/database.ts\");\n/* harmony import */ var _envValidation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./envValidation */ \"(rsc)/./src/lib/envValidation.ts\");\n\n\n\n\n// Password utilities\nconst hashPassword = async (password)=>{\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].hash(password, _envValidation__WEBPACK_IMPORTED_MODULE_3__.config.security.bcryptRounds());\n};\nconst verifyPassword = async (password, hashedPassword)=>{\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].compare(password, hashedPassword);\n};\n// JWT utilities\nconst generateToken = (payload)=>{\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, _envValidation__WEBPACK_IMPORTED_MODULE_3__.config.jwt.secret(), {\n        expiresIn: _envValidation__WEBPACK_IMPORTED_MODULE_3__.config.jwt.expiresIn()\n    });\n};\nconst verifyToken = (token)=>{\n    try {\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, _envValidation__WEBPACK_IMPORTED_MODULE_3__.config.jwt.secret());\n        return decoded;\n    } catch (error) {\n        return null;\n    }\n};\n// Generate unique referral ID\nconst generateReferralId = ()=>{\n    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\n    let result = 'HC'; // HashCoreX prefix\n    for(let i = 0; i < 8; i++){\n        result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n};\n// Authentication middleware\nconst authenticateRequest = async (request)=>{\n    const token = request.headers.get('authorization')?.replace('Bearer ', '') || request.cookies.get('auth-token')?.value;\n    if (!token) {\n        return {\n            authenticated: false,\n            user: null\n        };\n    }\n    const decoded = verifyToken(token);\n    if (!decoded) {\n        return {\n            authenticated: false,\n            user: null\n        };\n    }\n    const user = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByEmail(decoded.email);\n    if (!user) {\n        return {\n            authenticated: false,\n            user: null\n        };\n    }\n    return {\n        authenticated: true,\n        user\n    };\n};\n// User registration\nconst registerUser = async (data)=>{\n    // Check if user already exists\n    const existingUser = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByEmail(data.email);\n    if (existingUser) {\n        throw new Error('User already exists with this email');\n    }\n    // Validate referral code if provided\n    let referrerId;\n    if (data.referralCode) {\n        const referrer = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByReferralId(data.referralCode);\n        if (!referrer) {\n            throw new Error('Invalid referral code');\n        }\n        referrerId = referrer.id;\n    }\n    // Hash password\n    const passwordHash = await hashPassword(data.password);\n    // Generate unique referral ID\n    let referralId;\n    let isUnique = false;\n    do {\n        referralId = generateReferralId();\n        const existing = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByReferralId(referralId);\n        isUnique = !existing;\n    }while (!isUnique);\n    // Create user in PostgreSQL\n    const user = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.create({\n        email: data.email,\n        firstName: data.firstName,\n        lastName: data.lastName,\n        password: passwordHash,\n        referralId\n    });\n    // Create referral relationship if referrer exists\n    if (referrerId) {\n        const { placeUserByReferralType } = await __webpack_require__.e(/*! import() */ \"_rsc_src_lib_referral_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./referral */ \"(rsc)/./src/lib/referral.ts\"));\n        // Determine referral type based on placementSide parameter\n        let referralType = 'general';\n        if (data.placementSide === 'left') {\n            referralType = 'left';\n        } else if (data.placementSide === 'right') {\n            referralType = 'right';\n        }\n        // Place user using the new unified placement function\n        await placeUserByReferralType(referrerId, user.id, referralType);\n    }\n    return {\n        id: user.id,\n        email: user.email,\n        referralId: user.referralId,\n        kycStatus: user.kycStatus\n    };\n};\n// User login\nconst loginUser = async (data)=>{\n    const user = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByEmail(data.email);\n    if (!user) {\n        throw new Error('Invalid email or password');\n    }\n    const isValidPassword = await verifyPassword(data.password, user.password);\n    if (!isValidPassword) {\n        throw new Error('Invalid email or password');\n    }\n    const token = generateToken({\n        userId: user.id,\n        email: user.email\n    });\n    return {\n        token,\n        user: {\n            id: user.id,\n            email: user.email,\n            referralId: user.referralId,\n            kycStatus: user.kycStatus\n        }\n    };\n};\n// Password validation\nconst validatePassword = (password)=>{\n    const errors = [];\n    if (password.length < 8) {\n        errors.push('Password must be at least 8 characters long');\n    }\n    if (!/[A-Z]/.test(password)) {\n        errors.push('Password must contain at least one uppercase letter');\n    }\n    if (!/[a-z]/.test(password)) {\n        errors.push('Password must contain at least one lowercase letter');\n    }\n    if (!/\\d/.test(password)) {\n        errors.push('Password must contain at least one number');\n    }\n    if (!/[!@#$%^&*(),.?\":{}|<>]/.test(password)) {\n        errors.push('Password must contain at least one special character');\n    }\n    return {\n        valid: errors.length === 0,\n        errors\n    };\n};\n// Email validation\nconst validateEmail = (email)=>{\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n};\n// Session management\nconst createSession = (userId, email)=>{\n    return generateToken({\n        userId,\n        email\n    });\n};\nconst validateSession = (token)=>{\n    return verifyToken(token);\n};\n// Admin authentication\nconst isAdmin = async (userId)=>{\n    const user = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findById(userId);\n    return user?.role === 'ADMIN';\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   adminSettingsDb: () => (/* binding */ adminSettingsDb),\n/* harmony export */   binaryPointsDb: () => (/* binding */ binaryPointsDb),\n/* harmony export */   depositTransactionDb: () => (/* binding */ depositTransactionDb),\n/* harmony export */   emailLogDb: () => (/* binding */ emailLogDb),\n/* harmony export */   emailTemplateDb: () => (/* binding */ emailTemplateDb),\n/* harmony export */   miningUnitDb: () => (/* binding */ miningUnitDb),\n/* harmony export */   otpDb: () => (/* binding */ otpDb),\n/* harmony export */   referralDb: () => (/* binding */ referralDb),\n/* harmony export */   supportTicketDb: () => (/* binding */ supportTicketDb),\n/* harmony export */   systemLogDb: () => (/* binding */ systemLogDb),\n/* harmony export */   systemSettingsDb: () => (/* binding */ systemSettingsDb),\n/* harmony export */   ticketResponseDb: () => (/* binding */ ticketResponseDb),\n/* harmony export */   transactionDb: () => (/* binding */ transactionDb),\n/* harmony export */   userDb: () => (/* binding */ userDb),\n/* harmony export */   walletBalanceDb: () => (/* binding */ walletBalanceDb),\n/* harmony export */   withdrawalDb: () => (/* binding */ withdrawalDb)\n/* harmony export */ });\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n// User Database Operations\nconst userDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.create({\n            data: {\n                email: data.email,\n                firstName: data.firstName,\n                lastName: data.lastName,\n                password: data.password,\n                referralId: data.referralId || undefined\n            }\n        });\n    },\n    async findByEmail (email) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                email\n            },\n            include: {\n                miningUnits: true,\n                transactions: true,\n                binaryPoints: true\n            }\n        });\n    },\n    async findById (id) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                id\n            },\n            include: {\n                miningUnits: true,\n                transactions: true,\n                binaryPoints: true\n            }\n        });\n    },\n    async findByReferralId (referralId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                referralId\n            }\n        });\n    },\n    async update (id, data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id\n            },\n            data\n        });\n    },\n    async updateKYCStatus (userId, status) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id: userId\n            },\n            data: {\n                kycStatus: status\n            }\n        });\n    },\n    async updateWithdrawalAddress (email, withdrawalAddress) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                email\n            },\n            data: {\n                withdrawalAddress\n            }\n        });\n    },\n    async updateProfilePicture (id, profilePicture) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id\n            },\n            data: {\n                profilePicture\n            },\n            select: {\n                id: true,\n                email: true,\n                firstName: true,\n                lastName: true,\n                referralId: true,\n                role: true,\n                kycStatus: true,\n                profilePicture: true,\n                createdAt: true,\n                updatedAt: true\n            }\n        });\n    },\n    async updatePassword (id, hashedPassword) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id\n            },\n            data: {\n                password: hashedPassword\n            }\n        });\n    }\n};\n// Mining Unit Database Operations\nconst miningUnitDb = {\n    async create (data) {\n        const expiryDate = new Date();\n        expiryDate.setFullYear(expiryDate.getFullYear() + 2); // 24 months from now\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.create({\n            data: {\n                userId: data.userId,\n                thsAmount: data.thsAmount,\n                investmentAmount: data.investmentAmount,\n                dailyROI: data.dailyROI,\n                expiryDate\n            }\n        });\n    },\n    async findActiveByUserId (userId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.findMany({\n            where: {\n                userId,\n                status: 'ACTIVE',\n                expiryDate: {\n                    gt: new Date()\n                }\n            }\n        });\n    },\n    async updateTotalEarned (unitId, amount) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.update({\n            where: {\n                id: unitId\n            },\n            data: {\n                totalEarned: {\n                    increment: amount\n                }\n            }\n        });\n    },\n    async expireUnit (unitId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.update({\n            where: {\n                id: unitId\n            },\n            data: {\n                status: 'EXPIRED'\n            }\n        });\n    },\n    async findAllActive () {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.findMany({\n            where: {\n                status: 'ACTIVE',\n                expiryDate: {\n                    gt: new Date()\n                }\n            },\n            include: {\n                user: true\n            }\n        });\n    },\n    async updateEarnings (unitId, earningType, amount) {\n        const updateData = {\n            totalEarned: {\n                increment: amount\n            }\n        };\n        switch(earningType){\n            case 'mining':\n                updateData.miningEarnings = {\n                    increment: amount\n                };\n                break;\n            case 'referral':\n                updateData.referralEarnings = {\n                    increment: amount\n                };\n                break;\n            case 'binary':\n                updateData.binaryEarnings = {\n                    increment: amount\n                };\n                break;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.update({\n            where: {\n                id: unitId\n            },\n            data: updateData\n        });\n    }\n};\n// Transaction Database Operations\nconst transactionDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.create({\n            data: {\n                userId: data.userId,\n                type: data.type,\n                amount: data.amount,\n                description: data.description,\n                reference: data.reference,\n                status: data.status || 'PENDING'\n            }\n        });\n    },\n    async findByUserId (userId, filters) {\n        const where = {\n            userId\n        };\n        if (filters?.types && filters.types.length > 0) {\n            where.type = {\n                in: filters.types\n            };\n        }\n        if (filters?.status) {\n            where.status = filters.status;\n        }\n        if (filters?.search) {\n            where.OR = [\n                {\n                    description: {\n                        contains: filters.search,\n                        mode: 'insensitive'\n                    }\n                },\n                {\n                    type: {\n                        contains: filters.search,\n                        mode: 'insensitive'\n                    }\n                },\n                {\n                    reference: {\n                        contains: filters.search,\n                        mode: 'insensitive'\n                    }\n                }\n            ];\n        }\n        const include = filters?.includeUser ? {\n            user: {\n                select: {\n                    id: true,\n                    email: true,\n                    firstName: true,\n                    lastName: true\n                }\n            }\n        } : undefined;\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.findMany({\n            where,\n            include,\n            orderBy: {\n                createdAt: 'desc'\n            },\n            take: filters?.limit || 50,\n            skip: filters?.offset\n        });\n    },\n    async updateStatus (transactionId, status, additionalData) {\n        const updateData = {\n            status\n        };\n        if (additionalData?.amount !== undefined) {\n            updateData.amount = additionalData.amount;\n        }\n        if (additionalData?.description) {\n            updateData.description = additionalData.description;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.update({\n            where: {\n                id: transactionId\n            },\n            data: updateData\n        });\n    },\n    async findPendingByTypeAndDescription (userId, type, descriptionPattern) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.findFirst({\n            where: {\n                userId,\n                type,\n                description: {\n                    contains: descriptionPattern\n                },\n                status: 'PENDING'\n            }\n        });\n    },\n    async updateByReference (reference, type, status, additionalData) {\n        const updateData = {\n            status\n        };\n        if (additionalData?.amount !== undefined) {\n            updateData.amount = additionalData.amount;\n        }\n        if (additionalData?.description) {\n            updateData.description = additionalData.description;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.updateMany({\n            where: {\n                reference,\n                type,\n                status: 'PENDING'\n            },\n            data: updateData\n        });\n    }\n};\n// Referral Database Operations\nconst referralDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.create({\n            data: {\n                referrerId: data.referrerId,\n                referredId: data.referredId,\n                placementSide: data.placementSide\n            }\n        });\n    },\n    async findByReferrerId (referrerId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findMany({\n            where: {\n                referrerId\n            },\n            include: {\n                referred: {\n                    select: {\n                        id: true,\n                        email: true,\n                        createdAt: true\n                    }\n                }\n            }\n        });\n    }\n};\n// Binary Points Database Operations\nconst binaryPointsDb = {\n    async upsert (data) {\n        // Round to 2 decimal places to ensure precision\n        const leftPoints = data.leftPoints !== undefined ? Math.round(data.leftPoints * 100) / 100 : undefined;\n        const rightPoints = data.rightPoints !== undefined ? Math.round(data.rightPoints * 100) / 100 : undefined;\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.binaryPoints.upsert({\n            where: {\n                userId: data.userId\n            },\n            update: {\n                leftPoints: leftPoints !== undefined ? {\n                    increment: leftPoints\n                } : undefined,\n                rightPoints: rightPoints !== undefined ? {\n                    increment: rightPoints\n                } : undefined\n            },\n            create: {\n                userId: data.userId,\n                leftPoints: leftPoints || 0,\n                rightPoints: rightPoints || 0\n            }\n        });\n    },\n    async findByUserId (userId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.binaryPoints.findUnique({\n            where: {\n                userId\n            }\n        });\n    },\n    async resetPoints (userId, leftPoints, rightPoints) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.binaryPoints.update({\n            where: {\n                userId\n            },\n            data: {\n                leftPoints,\n                rightPoints,\n                flushDate: new Date()\n            }\n        });\n    }\n};\n// Withdrawal Database Operations\nconst withdrawalDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.withdrawalRequest.create({\n            data: {\n                userId: data.userId,\n                amount: data.amount,\n                usdtAddress: data.usdtAddress\n            }\n        });\n    },\n    async findPending () {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.withdrawalRequest.findMany({\n            where: {\n                status: 'PENDING'\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        kycStatus: true\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'asc'\n            }\n        });\n    },\n    async updateStatus (requestId, status, processedBy, txid, rejectionReason) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.withdrawalRequest.update({\n            where: {\n                id: requestId\n            },\n            data: {\n                status,\n                processedBy,\n                txid,\n                rejectionReason,\n                processedAt: new Date()\n            }\n        });\n    }\n};\n// Admin Settings Database Operations\nconst adminSettingsDb = {\n    async get (key) {\n        const setting = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.findUnique({\n            where: {\n                key\n            }\n        });\n        return setting?.value;\n    },\n    async set (key, value, updatedBy) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.upsert({\n            where: {\n                key\n            },\n            update: {\n                value\n            },\n            create: {\n                key,\n                value\n            }\n        });\n    },\n    async getAll () {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.findMany();\n    }\n};\n// System Logs\nconst systemLogDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.systemLog.create({\n            data: {\n                action: data.action,\n                userId: data.userId,\n                adminId: data.adminId,\n                details: data.details ? JSON.stringify(data.details) : null,\n                ipAddress: data.ipAddress,\n                userAgent: data.userAgent\n            }\n        });\n    }\n};\n// Wallet Balance Database Operations\nconst walletBalanceDb = {\n    async getOrCreate (userId) {\n        let walletBalance = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.findUnique({\n            where: {\n                userId\n            }\n        });\n        if (!walletBalance) {\n            walletBalance = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.create({\n                data: {\n                    userId,\n                    availableBalance: 0,\n                    pendingBalance: 0,\n                    totalDeposits: 0,\n                    totalWithdrawals: 0,\n                    totalEarnings: 0\n                }\n            });\n        }\n        return walletBalance;\n    },\n    async updateBalance (userId, updates) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.update({\n            where: {\n                userId\n            },\n            data: {\n                ...updates,\n                lastUpdated: new Date()\n            }\n        });\n    },\n    async addDeposit (userId, amount) {\n        const wallet = await this.getOrCreate(userId);\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.update({\n            where: {\n                userId\n            },\n            data: {\n                availableBalance: wallet.availableBalance + amount,\n                totalDeposits: wallet.totalDeposits + amount,\n                lastUpdated: new Date()\n            }\n        });\n    },\n    async addEarnings (userId, amount) {\n        const wallet = await this.getOrCreate(userId);\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.update({\n            where: {\n                userId\n            },\n            data: {\n                availableBalance: wallet.availableBalance + amount,\n                totalEarnings: wallet.totalEarnings + amount,\n                lastUpdated: new Date()\n            }\n        });\n    },\n    async deductWithdrawal (userId, amount) {\n        const wallet = await this.getOrCreate(userId);\n        if (wallet.availableBalance < amount) {\n            throw new Error('Insufficient balance');\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.update({\n            where: {\n                userId\n            },\n            data: {\n                availableBalance: wallet.availableBalance - amount,\n                totalWithdrawals: wallet.totalWithdrawals + amount,\n                lastUpdated: new Date()\n            }\n        });\n    },\n    async findByUserId (userId) {\n        return await this.getOrCreate(userId);\n    }\n};\n// Deposit Transaction Database Operations\nconst depositTransactionDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.create({\n            data: {\n                userId: data.userId,\n                transactionId: data.transactionId,\n                amount: data.amount,\n                usdtAmount: data.usdtAmount,\n                tronAddress: data.tronAddress,\n                senderAddress: data.senderAddress,\n                blockNumber: data.blockNumber,\n                blockTimestamp: data.blockTimestamp,\n                confirmations: data.confirmations || 0,\n                status: 'PENDING'\n            }\n        });\n    },\n    async findByTransactionId (transactionId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.findUnique({\n            where: {\n                transactionId\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    async findByUserId (userId, filters) {\n        const where = {\n            userId\n        };\n        if (filters?.status) {\n            where.status = filters.status;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.findMany({\n            where,\n            orderBy: {\n                createdAt: 'desc'\n            },\n            take: filters?.limit || 50,\n            skip: filters?.offset,\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    async findAll (filters) {\n        const where = {};\n        if (filters?.status) {\n            where.status = filters.status;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.findMany({\n            where,\n            orderBy: {\n                createdAt: 'desc'\n            },\n            take: filters?.limit || 100,\n            skip: filters?.offset,\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    async updateStatus (transactionId, status, updates) {\n        const updateData = {\n            status\n        };\n        if (updates?.verifiedAt) updateData.verifiedAt = updates.verifiedAt;\n        if (updates?.processedAt) updateData.processedAt = updates.processedAt;\n        if (updates?.failureReason) updateData.failureReason = updates.failureReason;\n        if (updates?.confirmations !== undefined) updateData.confirmations = updates.confirmations;\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.update({\n            where: {\n                transactionId\n            },\n            data: updateData\n        });\n    },\n    async markAsCompleted (transactionId) {\n        return await this.updateStatus(transactionId, 'COMPLETED', {\n            processedAt: new Date()\n        });\n    },\n    async markAsFailed (transactionId, reason) {\n        return await this.updateStatus(transactionId, 'FAILED', {\n            failureReason: reason,\n            processedAt: new Date()\n        });\n    },\n    async getPendingDeposits () {\n        return await this.findAll({\n            status: 'PENDING'\n        });\n    },\n    async getPendingVerificationDeposits () {\n        return await this.findAll({\n            status: 'PENDING_VERIFICATION'\n        });\n    },\n    async getWaitingForConfirmationsDeposits () {\n        return await this.findAll({\n            status: 'WAITING_FOR_CONFIRMATIONS'\n        });\n    },\n    async findByStatus (status) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.findMany({\n            where: {\n                status\n            },\n            orderBy: {\n                createdAt: 'desc'\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    async updateConfirmations (transactionId, confirmations) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.update({\n            where: {\n                transactionId\n            },\n            data: {\n                confirmations\n            }\n        });\n    },\n    async getDepositStats () {\n        const stats = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.aggregate({\n            _count: {\n                id: true\n            },\n            _sum: {\n                usdtAmount: true\n            },\n            where: {\n                status: {\n                    in: [\n                        'COMPLETED',\n                        'CONFIRMED'\n                    ]\n                }\n            }\n        });\n        const pendingCount = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.count({\n            where: {\n                status: {\n                    in: [\n                        'PENDING',\n                        'PENDING_VERIFICATION',\n                        'WAITING_FOR_CONFIRMATIONS'\n                    ]\n                }\n            }\n        });\n        return {\n            totalDeposits: stats._count.id || 0,\n            totalAmount: stats._sum.usdtAmount || 0,\n            pendingDeposits: pendingCount\n        };\n    }\n};\n// Support Ticket Database Operations\nconst supportTicketDb = {\n    create: async (data)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.create({\n            data,\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            }\n        });\n    },\n    findByUserId: async (userId)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.findMany({\n            where: {\n                userId\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    },\n    findById: async (id)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.findUnique({\n            where: {\n                id\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            }\n        });\n    },\n    findAll: async ()=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.findMany({\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    },\n    updateStatus: async (id, status)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.update({\n            where: {\n                id\n            },\n            data: {\n                status,\n                updatedAt: new Date()\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            }\n        });\n    }\n};\n// Ticket Response Database Operations\nconst ticketResponseDb = {\n    create: async (data)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.ticketResponse.create({\n            data,\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    findByTicketId: async (ticketId)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.ticketResponse.findMany({\n            where: {\n                ticketId\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'asc'\n            }\n        });\n    }\n};\n// System Settings Database Operations\nconst systemSettingsDb = {\n    async getSetting (key) {\n        const setting = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.findUnique({\n            where: {\n                key\n            }\n        });\n        return setting?.value || null;\n    },\n    async getSettings (keys) {\n        const settings = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.findMany({\n            where: {\n                key: {\n                    in: keys\n                }\n            }\n        });\n        const result = {};\n        settings.forEach((setting)=>{\n            result[setting.key] = setting.value;\n        });\n        return result;\n    },\n    async updateSettings (settings) {\n        const updates = Object.entries(settings).map(([key, value])=>({\n                key,\n                value: typeof value === 'string' ? value : JSON.stringify(value)\n            }));\n        // Use transaction to update multiple settings\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.$transaction(updates.map(({ key, value })=>_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.upsert({\n                where: {\n                    key\n                },\n                update: {\n                    value\n                },\n                create: {\n                    key,\n                    value\n                }\n            })));\n    },\n    async getEmailSettings () {\n        const settings = await this.getSettings([\n            'smtpHost',\n            'smtpPort',\n            'smtpSecure',\n            'smtpUser',\n            'smtpPassword',\n            'fromName',\n            'fromEmail',\n            'emailEnabled'\n        ]);\n        return {\n            smtpHost: settings.smtpHost,\n            smtpPort: settings.smtpPort ? parseInt(settings.smtpPort) : 587,\n            smtpSecure: settings.smtpSecure === 'true',\n            smtpUser: settings.smtpUser,\n            smtpPassword: settings.smtpPassword,\n            fromName: settings.fromName || 'HashCoreX',\n            fromEmail: settings.fromEmail,\n            emailEnabled: settings.emailEnabled !== 'false'\n        };\n    },\n    async updateEmailSettings (emailSettings) {\n        const settings = {};\n        if (emailSettings.smtpHost !== undefined) settings.smtpHost = emailSettings.smtpHost;\n        if (emailSettings.smtpPort !== undefined) settings.smtpPort = emailSettings.smtpPort.toString();\n        if (emailSettings.smtpSecure !== undefined) settings.smtpSecure = emailSettings.smtpSecure.toString();\n        if (emailSettings.smtpUser !== undefined) settings.smtpUser = emailSettings.smtpUser;\n        if (emailSettings.smtpPassword !== undefined) settings.smtpPassword = emailSettings.smtpPassword;\n        if (emailSettings.fromName !== undefined) settings.fromName = emailSettings.fromName;\n        if (emailSettings.fromEmail !== undefined) settings.fromEmail = emailSettings.fromEmail;\n        if (emailSettings.emailEnabled !== undefined) settings.emailEnabled = emailSettings.emailEnabled.toString();\n        await this.updateSettings(settings);\n    },\n    async getEmailTemplate (name) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.findUnique({\n            where: {\n                name,\n                isActive: true\n            }\n        });\n    }\n};\n// OTP Verification Database Operations\nconst otpDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.oTPVerification.create({\n            data\n        });\n    },\n    async findValid (email, purpose) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.oTPVerification.findFirst({\n            where: {\n                email,\n                purpose,\n                verified: false,\n                expiresAt: {\n                    gt: new Date()\n                }\n            },\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    },\n    async verify (id) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.oTPVerification.update({\n            where: {\n                id\n            },\n            data: {\n                verified: true\n            }\n        });\n    },\n    async findVerified (email, purpose) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.oTPVerification.findFirst({\n            where: {\n                email,\n                purpose,\n                verified: true,\n                expiresAt: {\n                    gt: new Date()\n                }\n            },\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    },\n    async cleanup () {\n        // Remove expired OTPs\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.oTPVerification.deleteMany({\n            where: {\n                expiresAt: {\n                    lt: new Date()\n                }\n            }\n        });\n    }\n};\n// Email Template Database Operations\nconst emailTemplateDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.create({\n            data\n        });\n    },\n    async findAll () {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.findMany({\n            orderBy: {\n                name: 'asc'\n            }\n        });\n    },\n    async findByName (name) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.findUnique({\n            where: {\n                name\n            }\n        });\n    },\n    async update (name, data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.update({\n            where: {\n                name\n            },\n            data\n        });\n    },\n    async delete (name) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.delete({\n            where: {\n                name\n            }\n        });\n    }\n};\n// Email Log Database Operations\nconst emailLogDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailLog.create({\n            data\n        });\n    },\n    async updateStatus (id, status, error) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailLog.update({\n            where: {\n                id\n            },\n            data: {\n                status,\n                error,\n                sentAt: status === 'SENT' ? new Date() : undefined\n            }\n        });\n    },\n    async findRecent (limit = 50) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailLog.findMany({\n            take: limit,\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/envValidation.ts":
/*!**********************************!*\
  !*** ./src/lib/envValidation.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkCriticalEnvVars: () => (/* binding */ checkCriticalEnvVars),\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   generateStrongJWTSecret: () => (/* binding */ generateStrongJWTSecret),\n/* harmony export */   getValidatedEnv: () => (/* binding */ getValidatedEnv),\n/* harmony export */   validateEnvironment: () => (/* binding */ validateEnvironment)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n/**\n * Environment Variable Validation\n * Validates all required environment variables on application startup\n */ \n// Environment validation schema\nconst envSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    // Database\n    DATABASE_URL: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().url('DATABASE_URL must be a valid PostgreSQL URL'),\n    DIRECT_URL: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().url('DIRECT_URL must be a valid PostgreSQL URL'),\n    // JWT Configuration\n    JWT_SECRET: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(32, 'JWT_SECRET must be at least 32 characters long').refine((secret)=>{\n        // Check for strong secret\n        const hasUpperCase = /[A-Z]/.test(secret);\n        const hasLowerCase = /[a-z]/.test(secret);\n        const hasNumbers = /\\d/.test(secret);\n        const hasSpecialChars = /[!@#$%^&*(),.?\":{}|<>]/.test(secret);\n        return hasUpperCase && hasLowerCase && hasNumbers && hasSpecialChars;\n    }, 'JWT_SECRET should contain uppercase, lowercase, numbers, and special characters'),\n    JWT_EXPIRES_IN: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().default('30d'),\n    // Node Environment\n    NODE_ENV: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        'development',\n        'production',\n        'test'\n    ]).default('development'),\n    // Application Configuration\n    NEXT_PUBLIC_APP_URL: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().url().optional(),\n    PORT: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().regex(/^\\d+$/).transform(Number).default('3000'),\n    // Email Configuration (optional but validated if provided)\n    SMTP_HOST: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    SMTP_PORT: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().regex(/^\\d+$/).transform(Number).optional(),\n    SMTP_USER: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().email().optional(),\n    SMTP_PASSWORD: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    SMTP_SECURE: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().transform((val)=>val === 'true').optional(),\n    // Tron Network Configuration\n    TRON_NETWORK: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        'mainnet',\n        'testnet'\n    ]).default('testnet'),\n    TRON_API_KEY: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    USDT_CONTRACT_ADDRESS: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    // File Upload Configuration\n    MAX_FILE_SIZE: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().regex(/^\\d+$/).transform(Number).default('10485760'),\n    UPLOAD_DIR: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().default('./public/uploads'),\n    // Security Configuration\n    BCRYPT_ROUNDS: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().regex(/^\\d+$/).transform(Number).default('12'),\n    SESSION_TIMEOUT: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().regex(/^\\d+$/).transform(Number).default('1800'),\n    // Rate Limiting Configuration\n    RATE_LIMIT_WINDOW_MS: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().regex(/^\\d+$/).transform(Number).default('900000'),\n    RATE_LIMIT_MAX_REQUESTS: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().regex(/^\\d+$/).transform(Number).default('100'),\n    // External API Configuration\n    COINGECKO_API_URL: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().url().default('https://api.coingecko.com/api/v3'),\n    // Monitoring and Logging\n    LOG_LEVEL: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        'error',\n        'warn',\n        'info',\n        'debug'\n    ]).default('info'),\n    ENABLE_REQUEST_LOGGING: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().transform((val)=>val === 'true').default('false'),\n    // Feature Flags\n    ENABLE_REGISTRATION: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().transform((val)=>val === 'true').default('true'),\n    ENABLE_KYC: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().transform((val)=>val === 'true').default('true'),\n    ENABLE_WITHDRAWALS: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().transform((val)=>val === 'true').default('true'),\n    MAINTENANCE_MODE: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().transform((val)=>val === 'true').default('false')\n});\n// Conditional validation for email configuration\nconst envSchemaWithConditionals = envSchema.refine((data)=>{\n    // If any SMTP config is provided, all should be provided\n    const smtpFields = [\n        data.SMTP_HOST,\n        data.SMTP_PORT,\n        data.SMTP_USER,\n        data.SMTP_PASSWORD\n    ];\n    const hasAnySmtp = smtpFields.some((field)=>field !== undefined);\n    const hasAllSmtp = smtpFields.every((field)=>field !== undefined);\n    if (hasAnySmtp && !hasAllSmtp) {\n        return false;\n    }\n    return true;\n}, {\n    message: 'If SMTP configuration is provided, all SMTP fields (HOST, PORT, USER, PASSWORD) must be provided'\n});\n// Validate environment variables\nfunction validateEnvironment() {\n    try {\n        const result = envSchemaWithConditionals.safeParse(process.env);\n        if (!result.success) {\n            const errors = result.error.errors.map((err)=>`${err.path.join('.')}: ${err.message}`);\n            return {\n                success: false,\n                errors\n            };\n        }\n        return {\n            success: true,\n            data: result.data\n        };\n    } catch (error) {\n        return {\n            success: false,\n            errors: [\n                'Failed to validate environment variables'\n            ]\n        };\n    }\n}\n// Get validated environment variables\nlet validatedEnv = null;\nfunction getValidatedEnv() {\n    if (!validatedEnv) {\n        const validation = validateEnvironment();\n        if (!validation.success) {\n            console.error('❌ Environment validation failed:');\n            validation.errors?.forEach((error)=>console.error(`  - ${error}`));\n            process.exit(1);\n        }\n        validatedEnv = validation.data;\n        console.log('✅ Environment variables validated successfully');\n    }\n    return validatedEnv;\n}\n// Environment-specific configurations\nconst config = {\n    isDevelopment: ()=>getValidatedEnv().NODE_ENV === 'development',\n    isProduction: ()=>getValidatedEnv().NODE_ENV === 'production',\n    isTest: ()=>getValidatedEnv().NODE_ENV === 'test',\n    database: {\n        url: ()=>getValidatedEnv().DATABASE_URL,\n        directUrl: ()=>getValidatedEnv().DIRECT_URL\n    },\n    jwt: {\n        secret: ()=>getValidatedEnv().JWT_SECRET,\n        expiresIn: ()=>getValidatedEnv().JWT_EXPIRES_IN\n    },\n    server: {\n        port: ()=>getValidatedEnv().PORT,\n        appUrl: ()=>getValidatedEnv().NEXT_PUBLIC_APP_URL\n    },\n    email: {\n        isConfigured: ()=>{\n            const env = getValidatedEnv();\n            return !!(env.SMTP_HOST && env.SMTP_PORT && env.SMTP_USER && env.SMTP_PASSWORD);\n        },\n        host: ()=>getValidatedEnv().SMTP_HOST,\n        port: ()=>getValidatedEnv().SMTP_PORT,\n        user: ()=>getValidatedEnv().SMTP_USER,\n        password: ()=>getValidatedEnv().SMTP_PASSWORD,\n        secure: ()=>getValidatedEnv().SMTP_SECURE\n    },\n    tron: {\n        network: ()=>getValidatedEnv().TRON_NETWORK,\n        apiKey: ()=>getValidatedEnv().TRON_API_KEY,\n        usdtContract: ()=>getValidatedEnv().USDT_CONTRACT_ADDRESS\n    },\n    security: {\n        bcryptRounds: ()=>getValidatedEnv().BCRYPT_ROUNDS,\n        sessionTimeout: ()=>getValidatedEnv().SESSION_TIMEOUT,\n        maxFileSize: ()=>getValidatedEnv().MAX_FILE_SIZE,\n        uploadDir: ()=>getValidatedEnv().UPLOAD_DIR\n    },\n    rateLimit: {\n        windowMs: ()=>getValidatedEnv().RATE_LIMIT_WINDOW_MS,\n        maxRequests: ()=>getValidatedEnv().RATE_LIMIT_MAX_REQUESTS\n    },\n    features: {\n        registrationEnabled: ()=>getValidatedEnv().ENABLE_REGISTRATION,\n        kycEnabled: ()=>getValidatedEnv().ENABLE_KYC,\n        withdrawalsEnabled: ()=>getValidatedEnv().ENABLE_WITHDRAWALS,\n        maintenanceMode: ()=>getValidatedEnv().MAINTENANCE_MODE\n    },\n    logging: {\n        level: ()=>getValidatedEnv().LOG_LEVEL,\n        requestLogging: ()=>getValidatedEnv().ENABLE_REQUEST_LOGGING\n    },\n    external: {\n        coingeckoApiUrl: ()=>getValidatedEnv().COINGECKO_API_URL\n    }\n};\n// Validate environment on module load (server-side only)\nif (true) {\n    // Skip validation during build process\n    const isBuilding = process.env.NEXT_PHASE === 'phase-production-build';\n    if (!isBuilding) {\n        // Only validate in server environment\n        const validation = validateEnvironment();\n        if (!validation.success) {\n            console.error('❌ Environment validation failed:');\n            validation.errors?.forEach((error)=>console.error(`  - ${error}`));\n            // In development, show helpful error message\n            if (true) {\n                console.error('\\n💡 To fix these errors:');\n                console.error('1. Check your .env.local file');\n                console.error('2. Ensure all required environment variables are set');\n                console.error('3. Verify JWT_SECRET is at least 32 characters with mixed case, numbers, and special characters');\n                console.error('4. Ensure database URLs are valid PostgreSQL connection strings');\n            }\n            process.exit(1);\n        }\n        console.log('✅ Environment variables validated successfully');\n    }\n}\n// Helper function to check if all critical environment variables are set\nfunction checkCriticalEnvVars() {\n    const critical = [\n        'DATABASE_URL',\n        'DIRECT_URL',\n        'JWT_SECRET'\n    ];\n    const missing = [];\n    for (const key of critical){\n        if (!process.env[key]) {\n            missing.push(key);\n        }\n    }\n    return {\n        valid: missing.length === 0,\n        missing\n    };\n}\n// Helper function to generate a strong JWT secret\nfunction generateStrongJWTSecret() {\n    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()';\n    let result = '';\n    // Ensure at least one of each required character type\n    result += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'[Math.floor(Math.random() * 26)]; // Uppercase\n    result += 'abcdefghijklmnopqrstuvwxyz'[Math.floor(Math.random() * 26)]; // Lowercase\n    result += '0123456789'[Math.floor(Math.random() * 10)]; // Number\n    result += '!@#$%^&*()'[Math.floor(Math.random() * 10)]; // Special char\n    // Fill the rest randomly\n    for(let i = 4; i < 64; i++){\n        result += chars[Math.floor(Math.random() * chars.length)];\n    }\n    // Shuffle the string\n    return result.split('').sort(()=>Math.random() - 0.5).join('');\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (config);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/envValidation.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/miningUnitEarnings.ts":
/*!***************************************!*\
  !*** ./src/lib/miningUnitEarnings.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   allocateEarningsToUnits: () => (/* binding */ allocateEarningsToUnits),\n/* harmony export */   calculateRemainingCapacity: () => (/* binding */ calculateRemainingCapacity),\n/* harmony export */   expireMiningUnit: () => (/* binding */ expireMiningUnit),\n/* harmony export */   getActiveMiningUnitsFIFO: () => (/* binding */ getActiveMiningUnitsFIFO),\n/* harmony export */   getMiningUnitEarningsHistory: () => (/* binding */ getMiningUnitEarningsHistory),\n/* harmony export */   getUserMiningUnitsWithEarnings: () => (/* binding */ getUserMiningUnitsWithEarnings),\n/* harmony export */   shouldExpireUnit: () => (/* binding */ shouldExpireUnit)\n/* harmony export */ });\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/database */ \"(rsc)/./src/lib/database.ts\");\n\n\n/**\n * Get active mining units for a user ordered by creation date (FIFO)\n */ async function getActiveMiningUnitsFIFO(userId) {\n    return await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.findMany({\n        where: {\n            userId,\n            status: 'ACTIVE',\n            expiryDate: {\n                gt: new Date()\n            }\n        },\n        orderBy: {\n            createdAt: 'asc'\n        }\n    });\n}\n/**\n * Calculate remaining earning capacity for a mining unit (5x - current earnings)\n */ function calculateRemainingCapacity(unit) {\n    const maxEarnings = unit.investmentAmount * 5;\n    const currentTotalEarnings = unit.miningEarnings + unit.referralEarnings + unit.binaryEarnings;\n    return Math.max(0, maxEarnings - currentTotalEarnings);\n}\n/**\n * Check if a mining unit should expire based on 5x earnings\n */ function shouldExpireUnit(unit) {\n    const maxEarnings = unit.investmentAmount * 5;\n    const currentTotalEarnings = unit.miningEarnings + unit.referralEarnings + unit.binaryEarnings;\n    return currentTotalEarnings >= maxEarnings;\n}\n/**\n * Allocate earnings to mining units using FIFO logic\n * Returns summary showing allocations and any discarded excess amounts\n */ async function allocateEarningsToUnits(userId, totalAmount, earningType, transactionId, description) {\n    const activeMiningUnits = await getActiveMiningUnitsFIFO(userId);\n    if (activeMiningUnits.length === 0) {\n        throw new Error('No active mining units found for earnings allocation');\n    }\n    const allocations = [];\n    let remainingAmount = totalAmount;\n    for (const unit of activeMiningUnits){\n        if (remainingAmount <= 0) break;\n        const remainingCapacity = calculateRemainingCapacity(unit);\n        if (remainingCapacity <= 0) {\n            continue;\n        }\n        // Allocate the minimum of remaining amount or remaining capacity\n        const allocationAmount = Math.min(remainingAmount, remainingCapacity);\n        if (allocationAmount > 0) {\n            // Update the mining unit earnings based on type\n            const updateData = {};\n            switch(earningType){\n                case 'MINING_EARNINGS':\n                    updateData.miningEarnings = {\n                        increment: allocationAmount\n                    };\n                    break;\n                case 'DIRECT_REFERRAL':\n                    updateData.referralEarnings = {\n                        increment: allocationAmount\n                    };\n                    break;\n                case 'BINARY_BONUS':\n                    updateData.binaryEarnings = {\n                        increment: allocationAmount\n                    };\n                    break;\n            }\n            // Update total earned for legacy compatibility\n            updateData.totalEarned = {\n                increment: allocationAmount\n            };\n            await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.update({\n                where: {\n                    id: unit.id\n                },\n                data: updateData\n            });\n            // Create earnings allocation record\n            await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnitEarningsAllocation.create({\n                data: {\n                    miningUnitId: unit.id,\n                    transactionId,\n                    earningType,\n                    amount: allocationAmount,\n                    description\n                }\n            });\n            allocations.push({\n                miningUnitId: unit.id,\n                amount: allocationAmount,\n                remainingCapacity: remainingCapacity - allocationAmount\n            });\n            remainingAmount -= allocationAmount;\n            // Check if unit should expire after this allocation\n            const updatedUnit = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.findUnique({\n                where: {\n                    id: unit.id\n                }\n            });\n            if (updatedUnit && shouldExpireUnit(updatedUnit)) {\n                await expireMiningUnit(unit.id, '5x_investment_reached');\n            }\n        }\n    }\n    const totalAllocated = totalAmount - remainingAmount;\n    const totalDiscarded = remainingAmount;\n    // If there's still remaining amount, it means all units are at capacity\n    if (remainingAmount > 0) {\n        console.warn(`Unable to allocate ${remainingAmount} to mining units - all units at capacity. Amount discarded.`);\n        // Log this situation\n        await _lib_database__WEBPACK_IMPORTED_MODULE_1__.systemLogDb.create({\n            action: 'EARNINGS_ALLOCATION_OVERFLOW',\n            userId,\n            details: {\n                totalAmount,\n                allocatedAmount: totalAllocated,\n                overflowAmount: totalDiscarded,\n                earningType,\n                reason: 'all_units_at_capacity',\n                note: 'Excess amount discarded as per mining unit capacity limits'\n            }\n        });\n    }\n    return {\n        allocations,\n        totalAllocated,\n        totalDiscarded,\n        allocationSuccess: totalDiscarded === 0\n    };\n}\n/**\n * Expire a mining unit and log the action\n */ async function expireMiningUnit(unitId, reason) {\n    const unit = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.findUnique({\n        where: {\n            id: unitId\n        },\n        include: {\n            user: {\n                select: {\n                    email: true,\n                    firstName: true,\n                    lastName: true\n                }\n            }\n        }\n    });\n    if (!unit) {\n        throw new Error(`Mining unit ${unitId} not found`);\n    }\n    await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.update({\n        where: {\n            id: unitId\n        },\n        data: {\n            status: 'EXPIRED'\n        }\n    });\n    const totalEarnings = unit.miningEarnings + unit.referralEarnings + unit.binaryEarnings;\n    await _lib_database__WEBPACK_IMPORTED_MODULE_1__.systemLogDb.create({\n        action: 'MINING_UNIT_EXPIRED',\n        userId: unit.userId,\n        details: {\n            miningUnitId: unitId,\n            reason,\n            totalEarned: totalEarnings,\n            miningEarnings: unit.miningEarnings,\n            referralEarnings: unit.referralEarnings,\n            binaryEarnings: unit.binaryEarnings,\n            investmentAmount: unit.investmentAmount,\n            multiplier: totalEarnings / unit.investmentAmount\n        }\n    });\n    // Send email notification\n    try {\n        const { emailNotificationService } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/nodemailer\"), __webpack_require__.e(\"_rsc_src_lib_emailNotificationService_ts\")]).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/emailNotificationService */ \"(rsc)/./src/lib/emailNotificationService.ts\"));\n        await emailNotificationService.sendMiningUnitExpiryNotification({\n            userId: unit.userId,\n            email: unit.user.email,\n            firstName: unit.user.firstName,\n            lastName: unit.user.lastName,\n            thsAmount: unit.thsAmount,\n            investmentAmount: unit.investmentAmount,\n            totalEarned: totalEarnings,\n            purchaseDate: unit.createdAt.toISOString(),\n            expiryDate: unit.expiryDate.toISOString(),\n            expiryReason: reason === '24_months_reached' ? 'TIME_LIMIT' : 'RETURN_LIMIT'\n        });\n    } catch (emailError) {\n        console.error('Failed to send mining unit expiry email:', emailError);\n    // Don't fail the expiry if email fails\n    }\n    console.log(`Mining unit ${unitId} expired due to ${reason}. Total earnings: ${totalEarnings}`);\n}\n/**\n * Get detailed earnings breakdown for a user's mining units\n */ async function getUserMiningUnitsWithEarnings(userId) {\n    return await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.findMany({\n        where: {\n            userId\n        },\n        orderBy: {\n            createdAt: 'asc'\n        }\n    });\n}\n/**\n * Get earnings allocation history for a mining unit\n */ async function getMiningUnitEarningsHistory(miningUnitId) {\n    return await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnitEarningsAllocation.findMany({\n        where: {\n            miningUnitId\n        },\n        include: {\n            transaction: true\n        },\n        orderBy: {\n            allocatedAt: 'desc'\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/miningUnitEarnings.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFFOUMsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsR0FBRztBQUVuRSxJQUFJSSxJQUFxQyxFQUFFSCxnQkFBZ0JFLE1BQU0sR0FBR0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZHJlYW1cXERlc2t0b3BcXEhhc2hfTWluaW5nc1xcaGFzaGNvcmV4XFxzcmNcXGxpYlxccHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50JztcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XG59O1xuXG5leHBvcnQgY29uc3QgcHJpc21hID0gZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/PyBuZXcgUHJpc21hQ2xpZW50KCk7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hO1xuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJwcm9jZXNzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/referral.ts":
/*!*****************************!*\
  !*** ./src/lib/referral.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addBinaryPoints: () => (/* binding */ addBinaryPoints),\n/* harmony export */   bulkUpdateTreeCounts: () => (/* binding */ bulkUpdateTreeCounts),\n/* harmony export */   calculateDownlineCount: () => (/* binding */ calculateDownlineCount),\n/* harmony export */   getBinaryTreeStructure: () => (/* binding */ getBinaryTreeStructure),\n/* harmony export */   getCachedDownlineCounts: () => (/* binding */ getCachedDownlineCounts),\n/* harmony export */   getDetailedTeamStats: () => (/* binding */ getDetailedTeamStats),\n/* harmony export */   getDirectReferralCount: () => (/* binding */ getDirectReferralCount),\n/* harmony export */   getSponsorInfo: () => (/* binding */ getSponsorInfo),\n/* harmony export */   getTotalTeamCount: () => (/* binding */ getTotalTeamCount),\n/* harmony export */   getTreeHealthStats: () => (/* binding */ getTreeHealthStats),\n/* harmony export */   getUsersByGeneration: () => (/* binding */ getUsersByGeneration),\n/* harmony export */   hasActiveMiningUnits: () => (/* binding */ hasActiveMiningUnits),\n/* harmony export */   loadNodeChildren: () => (/* binding */ loadNodeChildren),\n/* harmony export */   placeUserByReferralType: () => (/* binding */ placeUserByReferralType),\n/* harmony export */   placeUserInBinaryTree: () => (/* binding */ placeUserInBinaryTree),\n/* harmony export */   placeUserInLeftSideOnly: () => (/* binding */ placeUserInLeftSideOnly),\n/* harmony export */   placeUserInRightSideOnly: () => (/* binding */ placeUserInRightSideOnly),\n/* harmony export */   placeUserInSpecificSide: () => (/* binding */ placeUserInSpecificSide),\n/* harmony export */   processBinaryMatching: () => (/* binding */ processBinaryMatching),\n/* harmony export */   processDirectReferralBonus: () => (/* binding */ processDirectReferralBonus),\n/* harmony export */   searchUsersInTree: () => (/* binding */ searchUsersInTree),\n/* harmony export */   updateCachedDownlineCounts: () => (/* binding */ updateCachedDownlineCounts)\n/* harmony export */ });\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./database */ \"(rsc)/./src/lib/database.ts\");\n/* harmony import */ var _miningUnitEarnings__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./miningUnitEarnings */ \"(rsc)/./src/lib/miningUnitEarnings.ts\");\n\n\n\n// Check if user has active mining units (for binary tree display)\nasync function hasActiveMiningUnits(userId) {\n    try {\n        const activeMiningUnits = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.count({\n            where: {\n                userId,\n                status: 'ACTIVE',\n                expiryDate: {\n                    gt: new Date()\n                }\n            }\n        });\n        return activeMiningUnits > 0;\n    } catch (error) {\n        console.error('Error checking active mining units:', error);\n        return false;\n    }\n}\n// Calculate total downline count for a specific side\nasync function calculateDownlineCount(userId, side) {\n    try {\n        const downlineUsers = await getDownlineUsers(userId, side);\n        return downlineUsers.length;\n    } catch (error) {\n        console.error('Downline count calculation error:', error);\n        return 0;\n    }\n}\n// Find the optimal placement position in the weaker leg\nasync function findOptimalPlacementPosition(referrerId) {\n    try {\n        // Calculate total downline counts for both sides\n        const leftDownlineCount = await calculateDownlineCount(referrerId, 'LEFT');\n        const rightDownlineCount = await calculateDownlineCount(referrerId, 'RIGHT');\n        // Determine weaker leg based on total downline count\n        const weakerSide = leftDownlineCount <= rightDownlineCount ? 'LEFT' : 'RIGHT';\n        // Find the next available spot in the weaker leg\n        const availableSpot = await findNextAvailableSpotInLeg(referrerId, weakerSide);\n        if (availableSpot) {\n            return availableSpot;\n        }\n        // Fallback: if no spot found in weaker leg, try the other side\n        const strongerSide = weakerSide === 'LEFT' ? 'RIGHT' : 'LEFT';\n        const fallbackSpot = await findNextAvailableSpotInLeg(referrerId, strongerSide);\n        if (fallbackSpot) {\n            return fallbackSpot;\n        }\n        // Final fallback: place directly under referrer\n        const existingReferrals = await _database__WEBPACK_IMPORTED_MODULE_1__.referralDb.findByReferrerId(referrerId);\n        const hasLeft = existingReferrals.some((r)=>r.placementSide === 'LEFT');\n        const hasRight = existingReferrals.some((r)=>r.placementSide === 'RIGHT');\n        if (!hasLeft) {\n            return {\n                userId: referrerId,\n                side: 'LEFT'\n            };\n        } else if (!hasRight) {\n            return {\n                userId: referrerId,\n                side: 'RIGHT'\n            };\n        }\n        // If both sides are occupied, place in the weaker side\n        return {\n            userId: referrerId,\n            side: weakerSide\n        };\n    } catch (error) {\n        console.error('Optimal placement position error:', error);\n        // Fallback to left side\n        return {\n            userId: referrerId,\n            side: 'LEFT'\n        };\n    }\n}\n// Enhanced place new user in binary tree with weaker leg algorithm\nasync function placeUserInBinaryTree(referrerId, newUserId) {\n    try {\n        // Find optimal placement position using advanced weaker leg algorithm\n        const optimalPosition = await findOptimalPlacementPosition(referrerId);\n        // Create referral relationship with the optimal parent\n        await _database__WEBPACK_IMPORTED_MODULE_1__.referralDb.create({\n            referrerId: optimalPosition.userId,\n            referredId: newUserId,\n            placementSide: optimalPosition.side\n        });\n        // Update the parent's left/right referral IDs\n        const updateData = optimalPosition.side === 'LEFT' ? {\n            leftReferralId: newUserId\n        } : {\n            rightReferralId: newUserId\n        };\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id: optimalPosition.userId\n            },\n            data: updateData\n        });\n        // Create sponsor relationship (separate from binary placement)\n        // The sponsor is always the original referrer, regardless of binary placement\n        await createSponsorRelationship(referrerId, newUserId);\n        // Update cached tree counts for affected users\n        await updateTreeCountsAfterPlacement(referrerId, optimalPosition.userId);\n        return optimalPosition.side;\n    } catch (error) {\n        console.error('Binary tree placement error:', error);\n        throw error;\n    }\n}\n// Create sponsor relationship (separate from binary placement)\nasync function createSponsorRelationship(sponsorId, newUserId) {\n    try {\n        // Update the new user's referrerId field to track sponsor\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id: newUserId\n            },\n            data: {\n                referrerId: sponsorId\n            }\n        });\n        // Update sponsor's direct referral count\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id: sponsorId\n            },\n            data: {\n                directReferralCount: {\n                    increment: 1\n                },\n                updatedAt: new Date()\n            }\n        });\n        // Mark referral as direct sponsor if the binary placement parent is the same as sponsor\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.updateMany({\n            where: {\n                referrerId: sponsorId,\n                referredId: newUserId\n            },\n            data: {\n                isDirectSponsor: true\n            }\n        });\n    } catch (error) {\n        console.error('Sponsor relationship creation error:', error);\n    // Don't throw error as this is supplementary to binary placement\n    }\n}\n// Update cached downline counts for a user\nasync function updateCachedDownlineCounts(userId) {\n    try {\n        const leftCount = await calculateDownlineCount(userId, 'LEFT');\n        const rightCount = await calculateDownlineCount(userId, 'RIGHT');\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id: userId\n            },\n            data: {\n                totalLeftDownline: leftCount,\n                totalRightDownline: rightCount,\n                lastTreeUpdate: new Date()\n            }\n        });\n    } catch (error) {\n        console.error('Update cached downline counts error:', error);\n    }\n}\n// Get cached downline counts (with fallback to real-time calculation)\nasync function getCachedDownlineCounts(userId) {\n    try {\n        const user = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                id: userId\n            },\n            select: {\n                totalLeftDownline: true,\n                totalRightDownline: true,\n                lastTreeUpdate: true\n            }\n        });\n        if (!user) {\n            return {\n                left: 0,\n                right: 0,\n                total: 0\n            };\n        }\n        // Check if cache is recent (within last 30 minutes for more accurate counts)\n        const cacheAge = user.lastTreeUpdate ? Date.now() - user.lastTreeUpdate.getTime() : Infinity;\n        const cacheValidDuration = 30 * 60 * 1000; // 30 minutes\n        if (cacheAge < cacheValidDuration && user.totalLeftDownline !== null && user.totalRightDownline !== null) {\n            // Use cached values\n            return {\n                left: user.totalLeftDownline,\n                right: user.totalRightDownline,\n                total: user.totalLeftDownline + user.totalRightDownline\n            };\n        } else {\n            // Cache is stale or missing, recalculate and update\n            const leftCount = await calculateDownlineCount(userId, 'LEFT');\n            const rightCount = await calculateDownlineCount(userId, 'RIGHT');\n            // Update cache asynchronously\n            updateCachedDownlineCounts(userId).catch(console.error);\n            return {\n                left: leftCount,\n                right: rightCount,\n                total: leftCount + rightCount\n            };\n        }\n    } catch (error) {\n        console.error('Get cached downline counts error:', error);\n        return {\n            left: 0,\n            right: 0,\n            total: 0\n        };\n    }\n}\n// Find optimal placement in specific side with weaker leg logic (LEGACY - for backward compatibility)\nasync function findOptimalPlacementInSide(referrerId, targetSide) {\n    try {\n        // First, try to find the next available spot in the target side\n        const availableSpot = await findNextAvailableSpotInLeg(referrerId, targetSide);\n        if (availableSpot) {\n            return availableSpot;\n        }\n        // If no spot available, find the position with smallest downline in that side\n        const sideUsers = await getDownlineUsers(referrerId, targetSide);\n        // Find the user with the smallest total downline in the target side\n        let optimalUser = referrerId;\n        let minDownlineCount = Infinity;\n        for (const sideUser of sideUsers){\n            const leftCount = await calculateDownlineCount(sideUser.id, 'LEFT');\n            const rightCount = await calculateDownlineCount(sideUser.id, 'RIGHT');\n            const totalDownline = leftCount + rightCount;\n            if (totalDownline < minDownlineCount) {\n                // Check if this user has available spots\n                const userReferrals = await _database__WEBPACK_IMPORTED_MODULE_1__.referralDb.findByReferrerId(sideUser.id);\n                const hasLeft = userReferrals.some((r)=>r.placementSide === 'LEFT');\n                const hasRight = userReferrals.some((r)=>r.placementSide === 'RIGHT');\n                if (!hasLeft || !hasRight) {\n                    minDownlineCount = totalDownline;\n                    optimalUser = sideUser.id;\n                }\n            }\n        }\n        // Determine which side to place in for the optimal user\n        const optimalUserReferrals = await _database__WEBPACK_IMPORTED_MODULE_1__.referralDb.findByReferrerId(optimalUser);\n        const hasLeft = optimalUserReferrals.some((r)=>r.placementSide === 'LEFT');\n        const hasRight = optimalUserReferrals.some((r)=>r.placementSide === 'RIGHT');\n        if (!hasLeft) {\n            return {\n                userId: optimalUser,\n                side: 'LEFT'\n            };\n        } else if (!hasRight) {\n            return {\n                userId: optimalUser,\n                side: 'RIGHT'\n            };\n        }\n        // If both sides occupied, use weaker leg logic\n        const leftCount = await calculateDownlineCount(optimalUser, 'LEFT');\n        const rightCount = await calculateDownlineCount(optimalUser, 'RIGHT');\n        const weakerSide = leftCount <= rightCount ? 'LEFT' : 'RIGHT';\n        return {\n            userId: optimalUser,\n            side: weakerSide\n        };\n    } catch (error) {\n        console.error('Optimal placement in side error:', error);\n        return {\n            userId: referrerId,\n            side: targetSide\n        };\n    }\n}\n// NEW: Find deepest available position in LEFT side only (strict left-side placement)\nasync function findDeepestLeftPosition(referrerId) {\n    try {\n        // Start from the referrer and traverse down the LEFT side only\n        let currentUserId = referrerId;\n        let currentLevel = 0;\n        const maxDepth = 20; // Prevent infinite loops\n        while(currentLevel < maxDepth){\n            // Verify current user exists\n            const userExists = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n                where: {\n                    id: currentUserId\n                },\n                select: {\n                    id: true\n                }\n            });\n            if (!userExists) {\n                // User doesn't exist, fallback to referrer\n                return {\n                    userId: referrerId,\n                    side: 'LEFT'\n                };\n            }\n            // Check if current user has a LEFT spot available\n            const currentReferrals = await _database__WEBPACK_IMPORTED_MODULE_1__.referralDb.findByReferrerId(currentUserId);\n            const hasLeft = currentReferrals.some((r)=>r.placementSide === 'LEFT');\n            if (!hasLeft) {\n                // Found an available LEFT spot\n                return {\n                    userId: currentUserId,\n                    side: 'LEFT'\n                };\n            }\n            // Move to the LEFT child and continue traversing\n            const leftChild = currentReferrals.find((r)=>r.placementSide === 'LEFT');\n            if (!leftChild) {\n                // This shouldn't happen if hasLeft is true, but safety check\n                return {\n                    userId: currentUserId,\n                    side: 'LEFT'\n                };\n            }\n            currentUserId = leftChild.referredId;\n            currentLevel++;\n        }\n        // If we've reached max depth, place at the last position\n        return {\n            userId: currentUserId,\n            side: 'LEFT'\n        };\n    } catch (error) {\n        console.error('Find deepest left position error:', error);\n        return {\n            userId: referrerId,\n            side: 'LEFT'\n        };\n    }\n}\n// NEW: Find deepest available position in RIGHT side only (strict right-side placement)\nasync function findDeepestRightPosition(referrerId) {\n    try {\n        // Start from the referrer and traverse down the RIGHT side only\n        let currentUserId = referrerId;\n        let currentLevel = 0;\n        const maxDepth = 20; // Prevent infinite loops\n        while(currentLevel < maxDepth){\n            // Verify current user exists\n            const userExists = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n                where: {\n                    id: currentUserId\n                },\n                select: {\n                    id: true\n                }\n            });\n            if (!userExists) {\n                // User doesn't exist, fallback to referrer\n                return {\n                    userId: referrerId,\n                    side: 'RIGHT'\n                };\n            }\n            // Check if current user has a RIGHT spot available\n            const currentReferrals = await _database__WEBPACK_IMPORTED_MODULE_1__.referralDb.findByReferrerId(currentUserId);\n            const hasRight = currentReferrals.some((r)=>r.placementSide === 'RIGHT');\n            if (!hasRight) {\n                // Found an available RIGHT spot\n                return {\n                    userId: currentUserId,\n                    side: 'RIGHT'\n                };\n            }\n            // Move to the RIGHT child and continue traversing\n            const rightChild = currentReferrals.find((r)=>r.placementSide === 'RIGHT');\n            if (!rightChild) {\n                // This shouldn't happen if hasRight is true, but safety check\n                return {\n                    userId: currentUserId,\n                    side: 'RIGHT'\n                };\n            }\n            currentUserId = rightChild.referredId;\n            currentLevel++;\n        }\n        // If we've reached max depth, place at the last position\n        return {\n            userId: currentUserId,\n            side: 'RIGHT'\n        };\n    } catch (error) {\n        console.error('Find deepest right position error:', error);\n        return {\n            userId: referrerId,\n            side: 'RIGHT'\n        };\n    }\n}\n// Enhanced place user in specific side with weaker leg algorithm (LEGACY - for backward compatibility)\nasync function placeUserInSpecificSide(referrerId, newUserId, side) {\n    try {\n        // Find optimal placement position within the specified side\n        const optimalPosition = await findOptimalPlacementInSide(referrerId, side);\n        // Create referral relationship with the optimal parent\n        await _database__WEBPACK_IMPORTED_MODULE_1__.referralDb.create({\n            referrerId: optimalPosition.userId,\n            referredId: newUserId,\n            placementSide: optimalPosition.side\n        });\n        // Update the parent's referral ID\n        const updateData = optimalPosition.side === 'LEFT' ? {\n            leftReferralId: newUserId\n        } : {\n            rightReferralId: newUserId\n        };\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id: optimalPosition.userId\n            },\n            data: updateData\n        });\n        // Create sponsor relationship (separate from binary placement)\n        await createSponsorRelationship(referrerId, newUserId);\n        // Update cached tree counts for affected users\n        await updateTreeCountsAfterPlacement(referrerId, optimalPosition.userId);\n        return optimalPosition.side;\n    } catch (error) {\n        console.error('Specific side placement error:', error);\n        throw error;\n    }\n}\n// NEW: Place user strictly in LEFT side only (deepest available left position)\nasync function placeUserInLeftSideOnly(referrerId, newUserId) {\n    try {\n        // Find the deepest available position in the LEFT side\n        const optimalPosition = await findDeepestLeftPosition(referrerId);\n        // Create referral relationship with the optimal parent\n        await _database__WEBPACK_IMPORTED_MODULE_1__.referralDb.create({\n            referrerId: optimalPosition.userId,\n            referredId: newUserId,\n            placementSide: optimalPosition.side\n        });\n        // Update the parent's left referral ID\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id: optimalPosition.userId\n            },\n            data: {\n                leftReferralId: newUserId\n            }\n        });\n        // Create sponsor relationship (separate from binary placement)\n        await createSponsorRelationship(referrerId, newUserId);\n        // Update cached tree counts for affected users\n        await updateTreeCountsAfterPlacement(referrerId, optimalPosition.userId);\n        return optimalPosition.side;\n    } catch (error) {\n        console.error('Left side only placement error:', error);\n        throw error;\n    }\n}\n// NEW: Place user strictly in RIGHT side only (deepest available right position)\nasync function placeUserInRightSideOnly(referrerId, newUserId) {\n    try {\n        // Find the deepest available position in the RIGHT side\n        const optimalPosition = await findDeepestRightPosition(referrerId);\n        // Create referral relationship with the optimal parent\n        await _database__WEBPACK_IMPORTED_MODULE_1__.referralDb.create({\n            referrerId: optimalPosition.userId,\n            referredId: newUserId,\n            placementSide: optimalPosition.side\n        });\n        // Update the parent's right referral ID\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id: optimalPosition.userId\n            },\n            data: {\n                rightReferralId: newUserId\n            }\n        });\n        // Create sponsor relationship (separate from binary placement)\n        await createSponsorRelationship(referrerId, newUserId);\n        // Update cached tree counts for affected users\n        await updateTreeCountsAfterPlacement(referrerId, optimalPosition.userId);\n        return optimalPosition.side;\n    } catch (error) {\n        console.error('Right side only placement error:', error);\n        throw error;\n    }\n}\n// NEW: Main placement function that routes to appropriate algorithm based on referral link type\nasync function placeUserByReferralType(referrerId, newUserId, referralType) {\n    try {\n        switch(referralType){\n            case 'left':\n                // Strict left-side placement: find deepest available left position\n                return await placeUserInLeftSideOnly(referrerId, newUserId);\n            case 'right':\n                // Strict right-side placement: find deepest available right position\n                return await placeUserInRightSideOnly(referrerId, newUserId);\n            case 'general':\n            default:\n                // Default weaker leg placement\n                return await placeUserInBinaryTree(referrerId, newUserId);\n        }\n    } catch (error) {\n        console.error('Placement by referral type error:', error);\n        throw error;\n    }\n}\n// Find next available spot in a specific leg\nasync function findNextAvailableSpotInLeg(rootUserId, targetSide) {\n    try {\n        // Get the first user in the target leg\n        const rootReferrals = await _database__WEBPACK_IMPORTED_MODULE_1__.referralDb.findByReferrerId(rootUserId);\n        const firstInLeg = rootReferrals.find((r)=>r.placementSide === targetSide);\n        if (!firstInLeg) {\n            // The target side is completely empty\n            return {\n                userId: rootUserId,\n                side: targetSide\n            };\n        }\n        // Traverse down the leg to find the first available spot\n        const queue = [\n            firstInLeg.referredId\n        ];\n        while(queue.length > 0){\n            const currentUserId = queue.shift();\n            const currentReferrals = await _database__WEBPACK_IMPORTED_MODULE_1__.referralDb.findByReferrerId(currentUserId);\n            // Check if this user has any empty spots\n            const hasLeft = currentReferrals.some((r)=>r.placementSide === 'LEFT');\n            const hasRight = currentReferrals.some((r)=>r.placementSide === 'RIGHT');\n            if (!hasLeft) {\n                return {\n                    userId: currentUserId,\n                    side: 'LEFT'\n                };\n            }\n            if (!hasRight) {\n                return {\n                    userId: currentUserId,\n                    side: 'RIGHT'\n                };\n            }\n            // Add children to queue for further traversal\n            currentReferrals.forEach((r)=>{\n                queue.push(r.referredId);\n            });\n        }\n        return null; // No available spot found\n    } catch (error) {\n        console.error('Find available spot error:', error);\n        return null;\n    }\n}\n// Process direct referral bonus (10% of investment) - Added directly to sponsor's wallet\n// ONLY active sponsors (with active mining units) receive commissions\n// ONLY paid ONCE per user (first purchase only)\nasync function processDirectReferralBonus(referrerId, investmentAmount, purchaserId) {\n    try {\n        // Check if sponsor is active (has active mining units)\n        const isActive = await hasActiveMiningUnits(referrerId);\n        if (!isActive) {\n            console.log(`Skipping direct referral bonus for inactive sponsor ${referrerId} - no active mining units`);\n            return 0; // Return 0 commission for inactive sponsors\n        }\n        // Check if this user has already received first commission from this purchaser\n        if (purchaserId) {\n            const purchaser = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n                where: {\n                    id: purchaserId\n                },\n                select: {\n                    hasReceivedFirstCommission: true,\n                    firstName: true,\n                    lastName: true\n                }\n            });\n            if (purchaser?.hasReceivedFirstCommission) {\n                console.log(`Skipping direct referral bonus - sponsor ${referrerId} already received first commission from user ${purchaserId} (${purchaser.firstName} ${purchaser.lastName})`);\n                return 0; // Return 0 commission for subsequent purchases\n            }\n        }\n        const bonusPercentage = parseFloat(await _database__WEBPACK_IMPORTED_MODULE_1__.adminSettingsDb.get('DIRECT_REFERRAL_BONUS') || '10');\n        const bonusAmount = investmentAmount * bonusPercentage / 100;\n        // Create direct referral transaction first\n        const transaction = await _database__WEBPACK_IMPORTED_MODULE_1__.transactionDb.create({\n            userId: referrerId,\n            type: 'DIRECT_REFERRAL',\n            amount: bonusAmount,\n            description: `Direct referral bonus (${bonusPercentage}% of $${investmentAmount}) - First purchase`,\n            reference: purchaserId ? `from_user:${purchaserId}` : 'direct_referral',\n            status: 'COMPLETED'\n        });\n        // Allocate earnings to sponsor's mining units using FIFO logic\n        try {\n            const allocationSummary = await (0,_miningUnitEarnings__WEBPACK_IMPORTED_MODULE_2__.allocateEarningsToUnits)(referrerId, bonusAmount, 'DIRECT_REFERRAL', transaction.id, `Direct referral commission from ${purchaserId ? 'user purchase' : 'referral'}`);\n            console.log(`Allocated ${allocationSummary.totalAllocated} of ${bonusAmount} referral bonus to ${allocationSummary.allocations.length} mining units for sponsor ${referrerId}`);\n            if (allocationSummary.totalDiscarded > 0) {\n                console.log(`Discarded ${allocationSummary.totalDiscarded} excess referral bonus due to mining unit capacity limits for sponsor ${referrerId}`);\n            }\n            // Only add the actually allocated amount to wallet balance\n            // Excess amount is discarded as per mining unit capacity limits\n            if (allocationSummary.totalAllocated > 0) {\n                await _database__WEBPACK_IMPORTED_MODULE_1__.walletBalanceDb.addEarnings(referrerId, allocationSummary.totalAllocated);\n                console.log(`Added ${allocationSummary.totalAllocated} referral bonus to wallet balance for sponsor ${referrerId}`);\n            }\n        } catch (allocationError) {\n            console.error(`Failed to allocate referral bonus to mining units for ${referrerId}:`, allocationError);\n            // Fallback: Add full amount to wallet balance if allocation fails completely\n            await _database__WEBPACK_IMPORTED_MODULE_1__.walletBalanceDb.addEarnings(referrerId, bonusAmount);\n            console.log(`Fallback: Added ${bonusAmount} referral bonus directly to wallet for sponsor ${referrerId}`);\n        }\n        // Update referral commission earned\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.updateMany({\n            where: {\n                referrerId,\n                referred: {\n                    miningUnits: {\n                        some: {\n                            investmentAmount\n                        }\n                    }\n                }\n            },\n            data: {\n                commissionEarned: {\n                    increment: bonusAmount\n                }\n            }\n        });\n        // Mark the purchaser as having received their first commission\n        if (purchaserId) {\n            await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n                where: {\n                    id: purchaserId\n                },\n                data: {\n                    hasReceivedFirstCommission: true\n                }\n            });\n        }\n        console.log(`First-time direct referral bonus of $${bonusAmount} awarded to active sponsor ${referrerId} from user ${purchaserId}`);\n        return bonusAmount;\n    } catch (error) {\n        console.error('Direct referral bonus error:', error);\n        throw error;\n    }\n}\n// Add points to binary system when someone makes an investment ($100 = 1 point)\nasync function addBinaryPoints(userId, investmentAmount) {\n    try {\n        // Calculate points: $100 investment = 1 point (with 2 decimal precision)\n        // $150 = 1.5 points, $250 = 2.5 points, etc.\n        const points = Math.round(investmentAmount / 100 * 100) / 100; // Round to 2 decimal places\n        if (points <= 0) return; // No points to add if investment is less than $100\n        // Find all upline users and add points to their binary system (ONLY active upliners)\n        const uplineUsers = await getUplineUsers(userId);\n        for (const uplineUser of uplineUsers){\n            // Check if upline user is active (has active mining units)\n            const isActive = await hasActiveMiningUnits(uplineUser.id);\n            if (!isActive) {\n                console.log(`Skipping inactive user ${uplineUser.id} - no active mining units`);\n                continue; // Skip inactive users\n            }\n            // Determine which side this user is on relative to upline\n            const placementSide = await getUserPlacementSide(uplineUser.id, userId);\n            if (placementSide) {\n                // Get current binary points for this user\n                const currentBinaryPoints = await _database__WEBPACK_IMPORTED_MODULE_1__.binaryPointsDb.findByUserId(uplineUser.id);\n                // Get max points per side setting\n                const maxPointsPerSide = parseFloat(await _database__WEBPACK_IMPORTED_MODULE_1__.adminSettingsDb.get('MAX_BINARY_POINTS_PER_SIDE') || '10');\n                // Check current points on the target side\n                const currentLeftPoints = currentBinaryPoints?.leftPoints || 0;\n                const currentRightPoints = currentBinaryPoints?.rightPoints || 0;\n                let pointsToAdd = 0;\n                let sideToUpdate = placementSide;\n                if (placementSide === 'LEFT') {\n                    // Check if left side has reached the maximum\n                    if (currentLeftPoints >= maxPointsPerSide) {\n                        console.log(`User ${uplineUser.id} left side has reached maximum (${currentLeftPoints}/${maxPointsPerSide}). No points added.`);\n                        continue; // Skip adding points to this user\n                    }\n                    // Calculate how many points can be added without exceeding the limit\n                    pointsToAdd = Math.min(points, maxPointsPerSide - currentLeftPoints);\n                } else {\n                    // Check if right side has reached the maximum\n                    if (currentRightPoints >= maxPointsPerSide) {\n                        console.log(`User ${uplineUser.id} right side has reached maximum (${currentRightPoints}/${maxPointsPerSide}). No points added.`);\n                        continue; // Skip adding points to this user\n                    }\n                    // Calculate how many points can be added without exceeding the limit\n                    pointsToAdd = Math.min(points, maxPointsPerSide - currentRightPoints);\n                }\n                // Only add points if there's room\n                if (pointsToAdd > 0) {\n                    const updateData = placementSide === 'LEFT' ? {\n                        leftPoints: pointsToAdd\n                    } : {\n                        rightPoints: pointsToAdd\n                    };\n                    await _database__WEBPACK_IMPORTED_MODULE_1__.binaryPointsDb.upsert({\n                        userId: uplineUser.id,\n                        ...updateData\n                    });\n                    console.log(`Added ${pointsToAdd} points to ${placementSide} side for active user ${uplineUser.id} (${pointsToAdd < points ? 'capped at limit' : 'full amount'})`);\n                }\n            }\n        }\n    } catch (error) {\n        console.error('Binary points addition error:', error);\n        throw error;\n    }\n}\n// Get all upline users for a given user\nasync function getUplineUsers(userId) {\n    try {\n        const uplineUsers = [];\n        let currentUserId = userId;\n        // Traverse up the tree (maximum 10 levels to prevent infinite loops)\n        for(let level = 0; level < 10; level++){\n            const referral = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findFirst({\n                where: {\n                    referredId: currentUserId\n                },\n                include: {\n                    referrer: {\n                        select: {\n                            id: true,\n                            email: true\n                        }\n                    }\n                }\n            });\n            if (!referral) break;\n            uplineUsers.push(referral.referrer);\n            currentUserId = referral.referrerId;\n        }\n        return uplineUsers;\n    } catch (error) {\n        console.error('Upline users fetch error:', error);\n        return [];\n    }\n}\n// Get all ACTIVE upline users for a given user (skip inactive users)\nasync function getActiveUplineUsers(userId) {\n    try {\n        const uplineUsers = [];\n        let currentUserId = userId;\n        // Traverse up the tree (maximum 10 levels to prevent infinite loops)\n        for(let level = 0; level < 10; level++){\n            const referral = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findFirst({\n                where: {\n                    referredId: currentUserId\n                },\n                include: {\n                    referrer: {\n                        select: {\n                            id: true,\n                            email: true,\n                            isActive: true\n                        }\n                    }\n                }\n            });\n            if (!referral) break;\n            // Only add active users to the list\n            if (referral.referrer.isActive) {\n                uplineUsers.push(referral.referrer);\n            }\n            // Continue traversing up regardless of active status\n            currentUserId = referral.referrerId;\n        }\n        return uplineUsers;\n    } catch (error) {\n        console.error('Active upline users fetch error:', error);\n        return [];\n    }\n}\n// Determine which side a user is on relative to an upline user\nasync function getUserPlacementSide(uplineUserId, userId) {\n    try {\n        // Check direct placement first\n        const directReferral = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findFirst({\n            where: {\n                referrerId: uplineUserId,\n                referredId: userId\n            }\n        });\n        if (directReferral) {\n            return directReferral.placementSide;\n        }\n        // Check indirect placement by traversing down the tree\n        const leftSideUsers = await getDownlineUsers(uplineUserId, 'LEFT');\n        const rightSideUsers = await getDownlineUsers(uplineUserId, 'RIGHT');\n        if (leftSideUsers.some((u)=>u.id === userId)) {\n            return 'LEFT';\n        }\n        if (rightSideUsers.some((u)=>u.id === userId)) {\n            return 'RIGHT';\n        }\n        return null;\n    } catch (error) {\n        console.error('Placement side determination error:', error);\n        return null;\n    }\n}\n// Get all downline users for a specific side\nasync function getDownlineUsers(userId, side) {\n    try {\n        const downlineUsers = [];\n        const visited = new Set();\n        // Start with the direct placement on the specified side\n        const initialReferrals = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findMany({\n            where: {\n                referrerId: userId,\n                placementSide: side\n            },\n            select: {\n                referredId: true\n            }\n        });\n        // Use BFS to traverse the entire subtree\n        const queue = initialReferrals.map((r)=>r.referredId);\n        while(queue.length > 0){\n            const currentUserId = queue.shift();\n            // Skip if already visited (prevent infinite loops)\n            if (visited.has(currentUserId)) continue;\n            visited.add(currentUserId);\n            // Add current user to downline\n            downlineUsers.push({\n                id: currentUserId\n            });\n            // Get all referrals (both LEFT and RIGHT) from current user\n            const referrals = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findMany({\n                where: {\n                    referrerId: currentUserId\n                },\n                select: {\n                    referredId: true\n                }\n            });\n            // Add all children to queue for further traversal\n            for (const referral of referrals){\n                if (!visited.has(referral.referredId)) {\n                    queue.push(referral.referredId);\n                }\n            }\n        }\n        return downlineUsers;\n    } catch (error) {\n        console.error('Downline users fetch error:', error);\n        return [];\n    }\n}\n// Get all downline users (both sides combined) for total team count\nasync function getAllDownlineUsers(userId) {\n    try {\n        const downlineUsers = [];\n        const visited = new Set();\n        // Get all direct referrals (both LEFT and RIGHT)\n        const initialReferrals = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findMany({\n            where: {\n                referrerId: userId\n            },\n            select: {\n                referredId: true\n            }\n        });\n        // Use BFS to traverse the entire binary tree\n        const queue = initialReferrals.map((r)=>r.referredId);\n        while(queue.length > 0){\n            const currentUserId = queue.shift();\n            // Skip if already visited (prevent infinite loops)\n            if (visited.has(currentUserId)) continue;\n            visited.add(currentUserId);\n            // Get user info including active status\n            const user = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n                where: {\n                    id: currentUserId\n                },\n                select: {\n                    id: true,\n                    isActive: true\n                }\n            });\n            if (user) {\n                downlineUsers.push({\n                    id: user.id,\n                    isActive: user.isActive\n                });\n                // Get all referrals from current user\n                const referrals = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findMany({\n                    where: {\n                        referrerId: currentUserId\n                    },\n                    select: {\n                        referredId: true\n                    }\n                });\n                // Add all children to queue for further traversal\n                for (const referral of referrals){\n                    if (!visited.has(referral.referredId)) {\n                        queue.push(referral.referredId);\n                    }\n                }\n            }\n        }\n        return downlineUsers;\n    } catch (error) {\n        console.error('All downline users fetch error:', error);\n        return [];\n    }\n}\n// Process weekly binary matching (15:00 UTC on Saturdays)\nasync function processBinaryMatching() {\n    try {\n        console.log('Starting binary matching process...');\n        const maxPointsPerSide = parseFloat(await _database__WEBPACK_IMPORTED_MODULE_1__.adminSettingsDb.get('MAX_BINARY_POINTS_PER_SIDE') || '10');\n        const pointValue = parseFloat(await _database__WEBPACK_IMPORTED_MODULE_1__.adminSettingsDb.get('BINARY_POINT_VALUE') || '10'); // Dynamic point value from settings\n        // Get all users with binary points\n        const usersWithPoints = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.binaryPoints.findMany({\n            where: {\n                OR: [\n                    {\n                        leftPoints: {\n                            gt: 0\n                        }\n                    },\n                    {\n                        rightPoints: {\n                            gt: 0\n                        }\n                    }\n                ]\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true\n                    }\n                }\n            }\n        });\n        console.log(`Processing binary matching for ${usersWithPoints.length} users`);\n        const matchingResults = [];\n        for (const userPoints of usersWithPoints){\n            try {\n                // Calculate matching points (minimum of left and right, capped at max per side)\n                const leftPoints = Math.min(userPoints.leftPoints, maxPointsPerSide);\n                const rightPoints = Math.min(userPoints.rightPoints, maxPointsPerSide);\n                const matchedPoints = Math.min(leftPoints, rightPoints);\n                if (matchedPoints > 0) {\n                    // Calculate direct payout: 1 point = $10\n                    const userPayout = matchedPoints * pointValue;\n                    try {\n                        // Create binary bonus transaction first\n                        const transaction = await _database__WEBPACK_IMPORTED_MODULE_1__.transactionDb.create({\n                            userId: userPoints.userId,\n                            type: 'BINARY_BONUS',\n                            amount: userPayout,\n                            description: `Binary matching bonus - ${matchedPoints} points matched at $${pointValue} per point`,\n                            status: 'COMPLETED'\n                        });\n                        // Allocate earnings to user's mining units using FIFO logic\n                        try {\n                            const allocationSummary = await (0,_miningUnitEarnings__WEBPACK_IMPORTED_MODULE_2__.allocateEarningsToUnits)(userPoints.userId, userPayout, 'BINARY_BONUS', transaction.id, `Binary matching bonus - ${matchedPoints} points matched`);\n                            console.log(`Allocated ${allocationSummary.totalAllocated} of ${userPayout} binary bonus to ${allocationSummary.allocations.length} mining units for user ${userPoints.userId}`);\n                            if (allocationSummary.totalDiscarded > 0) {\n                                console.log(`Discarded ${allocationSummary.totalDiscarded} excess binary bonus due to mining unit capacity limits for user ${userPoints.userId}`);\n                            }\n                            // Only add the actually allocated amount to wallet balance\n                            // Excess amount is discarded as per mining unit capacity limits\n                            if (allocationSummary.totalAllocated > 0) {\n                                await _database__WEBPACK_IMPORTED_MODULE_1__.walletBalanceDb.addEarnings(userPoints.userId, allocationSummary.totalAllocated);\n                                console.log(`Added ${allocationSummary.totalAllocated} binary bonus to wallet balance for user ${userPoints.userId}`);\n                            }\n                        } catch (allocationError) {\n                            console.error(`Failed to allocate binary bonus to mining units for ${userPoints.userId}:`, allocationError);\n                            // Fallback: Add full amount to wallet balance if allocation fails completely\n                            await _database__WEBPACK_IMPORTED_MODULE_1__.walletBalanceDb.addEarnings(userPoints.userId, userPayout);\n                            console.log(`Fallback: Added ${userPayout} binary bonus directly to wallet for user ${userPoints.userId}`);\n                        }\n                        // Calculate remaining points after matching - reset weaker side to 0\n                        // Example: User has 7 left, 5 right -> 5 matched, left becomes 2, right becomes 0\n                        const remainingLeftPoints = Math.max(0, userPoints.leftPoints - matchedPoints);\n                        const remainingRightPoints = Math.max(0, userPoints.rightPoints - matchedPoints);\n                        // Reset the weaker side to 0 after matching (proper binary matching rule)\n                        const finalLeftPoints = userPoints.leftPoints > userPoints.rightPoints ? remainingLeftPoints : 0;\n                        const finalRightPoints = userPoints.rightPoints > userPoints.leftPoints ? remainingRightPoints : 0;\n                        // Update binary points - reset weaker side to 0, keep stronger side remainder\n                        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.binaryPoints.update({\n                            where: {\n                                id: userPoints.id\n                            },\n                            data: {\n                                leftPoints: finalLeftPoints,\n                                rightPoints: finalRightPoints,\n                                matchedPoints: {\n                                    increment: matchedPoints\n                                },\n                                totalMatched: {\n                                    increment: matchedPoints\n                                },\n                                lastMatchDate: new Date(),\n                                flushDate: new Date()\n                            }\n                        });\n                        matchingResults.push({\n                            userId: userPoints.userId,\n                            matchedPoints,\n                            payout: userPayout,\n                            remainingLeftPoints: finalLeftPoints,\n                            remainingRightPoints: finalRightPoints\n                        });\n                        console.log(`User ${userPoints.userId}: ${matchedPoints} points matched, $${userPayout.toFixed(2)} payout, remaining: L${finalLeftPoints} R${finalRightPoints}`);\n                    } catch (payoutError) {\n                        console.error(`Error processing payout for user ${userPoints.userId}:`, payoutError);\n                    // Continue with next user instead of failing the entire process\n                    }\n                } else {\n                    // No matching possible, but still reset excess points if over the limit\n                    const excessLeft = Math.max(0, userPoints.leftPoints - maxPointsPerSide);\n                    const excessRight = Math.max(0, userPoints.rightPoints - maxPointsPerSide);\n                    if (excessLeft > 0 || excessRight > 0) {\n                        try {\n                            // Reset excess points (pressure out)\n                            await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.binaryPoints.update({\n                                where: {\n                                    id: userPoints.id\n                                },\n                                data: {\n                                    leftPoints: Math.min(userPoints.leftPoints, maxPointsPerSide),\n                                    rightPoints: Math.min(userPoints.rightPoints, maxPointsPerSide),\n                                    flushDate: new Date()\n                                }\n                            });\n                            console.log(`User ${userPoints.userId}: Excess points reset - L${excessLeft} R${excessRight} points flushed`);\n                        } catch (flushError) {\n                            console.error(`Error flushing excess points for user ${userPoints.userId}:`, flushError);\n                        }\n                    }\n                }\n            } catch (userError) {\n                console.error(`Error processing binary matching for user ${userPoints.userId}:`, userError);\n            }\n        }\n        // Log the binary matching process\n        await _database__WEBPACK_IMPORTED_MODULE_1__.systemLogDb.create({\n            action: 'BINARY_MATCHING_PROCESSED',\n            details: {\n                usersProcessed: usersWithPoints.length,\n                totalMatchedPoints: matchingResults.reduce((sum, r)=>sum + r.matchedPoints, 0),\n                pointValue,\n                totalPayouts: matchingResults.reduce((sum, r)=>sum + r.payout, 0),\n                timestamp: new Date().toISOString()\n            }\n        });\n        console.log(`Binary matching completed. Processed ${matchingResults.length} users with total payouts: $${matchingResults.reduce((sum, r)=>sum + r.payout, 0).toFixed(2)}`);\n        return {\n            success: true,\n            usersProcessed: matchingResults.length,\n            totalPayouts: matchingResults.reduce((sum, r)=>sum + r.payout, 0),\n            matchingResults\n        };\n    } catch (error) {\n        console.error('Binary matching process error:', error);\n        throw error;\n    }\n}\n// Get sponsor information for a user\nasync function getSponsorInfo(userId) {\n    try {\n        const user = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                id: userId\n            },\n            select: {\n                referrerId: true\n            }\n        });\n        if (!user?.referrerId) return null;\n        const sponsor = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                id: user.referrerId\n            },\n            select: {\n                id: true,\n                email: true,\n                firstName: true,\n                lastName: true\n            }\n        });\n        return sponsor;\n    } catch (error) {\n        console.error('Sponsor info fetch error:', error);\n        return null;\n    }\n}\n// Get direct referral count for a user (sponsored users)\nasync function getDirectReferralCount(userId) {\n    try {\n        const count = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.count({\n            where: {\n                referrerId: userId\n            }\n        });\n        return count;\n    } catch (error) {\n        console.error('Direct referral count error:', error);\n        return 0;\n    }\n}\n// Get total team count (all downline users in binary tree) - uses cached values\nasync function getTotalTeamCount(userId) {\n    try {\n        return await getCachedDownlineCounts(userId);\n    } catch (error) {\n        console.error('Total team count error:', error);\n        return {\n            left: 0,\n            right: 0,\n            total: 0\n        };\n    }\n}\n// Get detailed team statistics\nasync function getDetailedTeamStats(userId) {\n    try {\n        const user = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                id: userId\n            },\n            select: {\n                directReferralCount: true\n            }\n        });\n        const teamCounts = await getCachedDownlineCounts(userId);\n        // Get all downline users for accurate active member count\n        const allDownlineUsers = await getAllDownlineUsers(userId);\n        const activeMembers = allDownlineUsers.filter((u)=>u.isActive).length;\n        // Get recent joins (last 30 days) - direct referrals only\n        const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);\n        const recentJoins = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.count({\n            where: {\n                referrerId: userId,\n                createdAt: {\n                    gte: thirtyDaysAgo\n                }\n            }\n        });\n        return {\n            directReferrals: user?.directReferralCount || 0,\n            leftTeam: teamCounts.left,\n            rightTeam: teamCounts.right,\n            totalTeam: teamCounts.total,\n            activeMembers,\n            recentJoins\n        };\n    } catch (error) {\n        console.error('Detailed team stats error:', error);\n        return {\n            directReferrals: 0,\n            leftTeam: 0,\n            rightTeam: 0,\n            totalTeam: 0,\n            activeMembers: 0,\n            recentJoins: 0\n        };\n    }\n}\n// Find all users in a specific generation (level) of the tree\nasync function getUsersByGeneration(userId, generation) {\n    try {\n        if (generation <= 0) return [];\n        let currentLevelUsers = [\n            {\n                id: userId,\n                side: null\n            }\n        ];\n        for(let level = 1; level <= generation; level++){\n            const nextLevelUsers = [];\n            for (const currentUser of currentLevelUsers){\n                const referrals = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findMany({\n                    where: {\n                        referrerId: currentUser.id\n                    },\n                    include: {\n                        referred: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true,\n                                createdAt: true\n                            }\n                        }\n                    }\n                });\n                for (const referral of referrals){\n                    nextLevelUsers.push({\n                        id: referral.referredId,\n                        side: referral.placementSide\n                    });\n                }\n            }\n            currentLevelUsers = nextLevelUsers;\n        }\n        // Get full user details for the final generation\n        const userDetails = await Promise.all(currentLevelUsers.map(async (user)=>{\n            const userInfo = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n                where: {\n                    id: user.id\n                },\n                select: {\n                    id: true,\n                    email: true,\n                    firstName: true,\n                    lastName: true,\n                    createdAt: true\n                }\n            });\n            return {\n                ...userInfo,\n                placementSide: user.side\n            };\n        }));\n        return userDetails.filter(Boolean);\n    } catch (error) {\n        console.error('Users by generation error:', error);\n        return [];\n    }\n}\n// Enhanced binary tree structure with detailed member information\nasync function getBinaryTreeStructure(userId, depth = 3, expandedNodes = new Set()) {\n    try {\n        const buildTree = async (currentUserId, currentDepth, path = '')=>{\n            if (currentDepth <= 0) return null;\n            const user = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n                where: {\n                    id: currentUserId\n                },\n                select: {\n                    id: true,\n                    email: true,\n                    firstName: true,\n                    lastName: true,\n                    profilePicture: true,\n                    createdAt: true\n                }\n            });\n            if (!user) return null;\n            // Check if user has active mining units (for binary tree display)\n            const isActive = await hasActiveMiningUnits(currentUserId);\n            // Get sponsor information\n            const sponsorInfo = await getSponsorInfo(currentUserId);\n            // Get direct referral count\n            const directReferralCount = await getDirectReferralCount(currentUserId);\n            // Get team counts\n            const teamCounts = await getTotalTeamCount(currentUserId);\n            // Get direct referrals (binary placement)\n            const leftReferral = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findFirst({\n                where: {\n                    referrerId: currentUserId,\n                    placementSide: 'LEFT'\n                },\n                include: {\n                    referred: {\n                        select: {\n                            id: true,\n                            email: true,\n                            firstName: true,\n                            lastName: true,\n                            createdAt: true\n                        }\n                    }\n                }\n            });\n            const rightReferral = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findFirst({\n                where: {\n                    referrerId: currentUserId,\n                    placementSide: 'RIGHT'\n                },\n                include: {\n                    referred: {\n                        select: {\n                            id: true,\n                            email: true,\n                            firstName: true,\n                            lastName: true,\n                            createdAt: true\n                        }\n                    }\n                }\n            });\n            // Get binary points\n            const binaryPoints = await _database__WEBPACK_IMPORTED_MODULE_1__.binaryPointsDb.findByUserId(currentUserId);\n            // Determine if we should load children for infinite depth support\n            // Load children if we have remaining depth AND either:\n            // 1. We're within the initial depth (first 3 levels) - always show first 3 levels\n            // 2. OR this node is explicitly expanded - show children of expanded nodes\n            const isWithinInitialDepth = path.length < 3; // First 3 levels (root = 0, level 1 = 1 char, level 2 = 2 chars)\n            const isNodeExpanded = expandedNodes.has(currentUserId);\n            const shouldLoadChildren = currentDepth > 1 && (isWithinInitialDepth || isNodeExpanded);\n            // Check if children exist (for showing expand button)\n            const hasLeftChild = leftReferral !== null;\n            const hasRightChild = rightReferral !== null;\n            return {\n                user: {\n                    ...user,\n                    isActive\n                },\n                sponsorInfo,\n                directReferralCount,\n                teamCounts,\n                binaryPoints: binaryPoints || {\n                    leftPoints: 0,\n                    rightPoints: 0,\n                    matchedPoints: 0\n                },\n                hasLeftChild,\n                hasRightChild,\n                leftChild: shouldLoadChildren && leftReferral ? await buildTree(leftReferral.referredId, currentDepth - 1, path + 'L') : null,\n                rightChild: shouldLoadChildren && rightReferral ? await buildTree(rightReferral.referredId, currentDepth - 1, path + 'R') : null\n            };\n        };\n        return await buildTree(userId, depth);\n    } catch (error) {\n        console.error('Binary tree structure error:', error);\n        throw error;\n    }\n}\n// Load children for a specific node (for dynamic expansion)\nasync function loadNodeChildren(userId) {\n    try {\n        // Get direct referrals (binary placement)\n        const leftReferral = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findFirst({\n            where: {\n                referrerId: userId,\n                placementSide: 'LEFT'\n            },\n            include: {\n                referred: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true,\n                        profilePicture: true,\n                        createdAt: true\n                    }\n                }\n            }\n        });\n        const rightReferral = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findFirst({\n            where: {\n                referrerId: userId,\n                placementSide: 'RIGHT'\n            },\n            include: {\n                referred: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true,\n                        profilePicture: true,\n                        createdAt: true\n                    }\n                }\n            }\n        });\n        const buildChildNode = async (referral)=>{\n            if (!referral) return null;\n            const childUserId = referral.referredId;\n            // Check if user has active mining units (for binary tree display)\n            const isActive = await hasActiveMiningUnits(childUserId);\n            // Get sponsor information\n            const sponsorInfo = await getSponsorInfo(childUserId);\n            // Get direct referral count\n            const directReferralCount = await getDirectReferralCount(childUserId);\n            // Get team counts\n            const teamCounts = await getTotalTeamCount(childUserId);\n            // Get binary points\n            const binaryPoints = await _database__WEBPACK_IMPORTED_MODULE_1__.binaryPointsDb.findByUserId(childUserId);\n            // Check if this child has its own children\n            const hasLeftChild = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findFirst({\n                where: {\n                    referrerId: childUserId,\n                    placementSide: 'LEFT'\n                },\n                select: {\n                    id: true\n                }\n            }) !== null;\n            const hasRightChild = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findFirst({\n                where: {\n                    referrerId: childUserId,\n                    placementSide: 'RIGHT'\n                },\n                select: {\n                    id: true\n                }\n            }) !== null;\n            return {\n                user: {\n                    ...referral.referred,\n                    isActive\n                },\n                sponsorInfo,\n                directReferralCount,\n                teamCounts,\n                binaryPoints: binaryPoints || {\n                    leftPoints: 0,\n                    rightPoints: 0,\n                    matchedPoints: 0\n                },\n                hasLeftChild,\n                hasRightChild,\n                leftChild: null,\n                rightChild: null\n            };\n        };\n        const leftChild = await buildChildNode(leftReferral);\n        const rightChild = await buildChildNode(rightReferral);\n        return {\n            leftChild,\n            rightChild\n        };\n    } catch (error) {\n        console.error('Load node children error:', error);\n        return {\n            leftChild: null,\n            rightChild: null\n        };\n    }\n}\n// Search for users in the binary tree\nasync function searchUsersInTree(rootUserId, searchTerm, maxResults = 20) {\n    try {\n        const searchPattern = `%${searchTerm.toLowerCase()}%`;\n        // Get all downline users that match the search term\n        const leftUsers = await getDownlineUsers(rootUserId, 'LEFT');\n        const rightUsers = await getDownlineUsers(rootUserId, 'RIGHT');\n        const allDownlineIds = [\n            ...leftUsers,\n            ...rightUsers\n        ].map((u)=>u.id);\n        if (allDownlineIds.length === 0) return [];\n        const matchingUsers = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findMany({\n            where: {\n                id: {\n                    in: allDownlineIds\n                },\n                OR: [\n                    {\n                        email: {\n                            contains: searchTerm,\n                            mode: 'insensitive'\n                        }\n                    },\n                    {\n                        firstName: {\n                            contains: searchTerm,\n                            mode: 'insensitive'\n                        }\n                    },\n                    {\n                        lastName: {\n                            contains: searchTerm,\n                            mode: 'insensitive'\n                        }\n                    }\n                ]\n            },\n            select: {\n                id: true,\n                email: true,\n                firstName: true,\n                lastName: true,\n                createdAt: true,\n                referrerId: true\n            },\n            take: maxResults\n        });\n        // Get placement path and sponsor info for each user\n        const results = await Promise.all(matchingUsers.map(async (user)=>{\n            const placementPath = await getPlacementPath(rootUserId, user.id);\n            const generation = placementPath.split('-').length;\n            let sponsorInfo = undefined;\n            if (user.referrerId) {\n                sponsorInfo = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n                    where: {\n                        id: user.referrerId\n                    },\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                });\n            }\n            return {\n                id: user.id,\n                email: user.email,\n                firstName: user.firstName,\n                lastName: user.lastName,\n                createdAt: user.createdAt,\n                placementPath,\n                generation,\n                sponsorInfo: sponsorInfo || undefined\n            };\n        }));\n        return results;\n    } catch (error) {\n        console.error('Search users in tree error:', error);\n        return [];\n    }\n}\n// Get placement path from root to a specific user (e.g., \"L-R-L\")\nasync function getPlacementPath(rootUserId, targetUserId) {\n    try {\n        if (rootUserId === targetUserId) return 'ROOT';\n        const path = [];\n        let currentUserId = targetUserId;\n        // Traverse up the tree to find path\n        while(currentUserId !== rootUserId){\n            const referral = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findFirst({\n                where: {\n                    referredId: currentUserId\n                }\n            });\n            if (!referral) break;\n            path.unshift(referral.placementSide === 'LEFT' ? 'L' : 'R');\n            currentUserId = referral.referrerId;\n            // Prevent infinite loops\n            if (path.length > 20) break;\n        }\n        return path.join('-') || 'UNKNOWN';\n    } catch (error) {\n        console.error('Get placement path error:', error);\n        return 'UNKNOWN';\n    }\n}\n// Update tree counts after a new user placement\nasync function updateTreeCountsAfterPlacement(sponsorId, placementParentId) {\n    try {\n        // Update counts for the sponsor (if different from placement parent)\n        if (sponsorId !== placementParentId) {\n            await updateCachedDownlineCounts(sponsorId);\n        }\n        // Update counts for the placement parent\n        await updateCachedDownlineCounts(placementParentId);\n        // Update counts for all upline users from the placement parent\n        const uplineUsers = await getUplineUsers(placementParentId);\n        const updatePromises = uplineUsers.map((user)=>updateCachedDownlineCounts(user.id));\n        await Promise.all(updatePromises);\n    } catch (error) {\n        console.error('Update tree counts after placement error:', error);\n    // Don't throw error as this is supplementary to placement\n    }\n}\n// Bulk update tree counts for multiple users (for maintenance)\nasync function bulkUpdateTreeCounts(userIds) {\n    try {\n        const updatePromises = userIds.map((userId)=>updateCachedDownlineCounts(userId));\n        await Promise.all(updatePromises);\n    } catch (error) {\n        console.error('Bulk update tree counts error:', error);\n    }\n}\n// Get tree health statistics\nasync function getTreeHealthStats(rootUserId) {\n    try {\n        const teamCounts = await getCachedDownlineCounts(rootUserId);\n        const totalUsers = teamCounts.total;\n        // Calculate balance ratio\n        const smallerSide = Math.min(teamCounts.left, teamCounts.right);\n        const largerSide = Math.max(teamCounts.left, teamCounts.right);\n        const balanceRatio = largerSide > 0 ? smallerSide / largerSide : 1;\n        // Calculate tree depth statistics\n        let maxDepth = 0;\n        let totalDepth = 0;\n        let userCount = 0;\n        // BFS to calculate depths\n        const queue = [\n            {\n                userId: rootUserId,\n                depth: 0\n            }\n        ];\n        const visited = new Set();\n        while(queue.length > 0){\n            const { userId, depth } = queue.shift();\n            if (visited.has(userId)) continue;\n            visited.add(userId);\n            maxDepth = Math.max(maxDepth, depth);\n            totalDepth += depth;\n            userCount++;\n            const referrals = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findMany({\n                where: {\n                    referrerId: userId\n                },\n                select: {\n                    referredId: true\n                }\n            });\n            for (const referral of referrals){\n                if (!visited.has(referral.referredId)) {\n                    queue.push({\n                        userId: referral.referredId,\n                        depth: depth + 1\n                    });\n                }\n            }\n        }\n        const averageDepth = userCount > 0 ? totalDepth / userCount : 0;\n        // Calculate empty positions (theoretical max - actual users)\n        const theoreticalMax = Math.pow(2, maxDepth + 1) - 1;\n        const emptyPositions = Math.max(0, theoreticalMax - totalUsers);\n        return {\n            totalUsers,\n            balanceRatio,\n            averageDepth,\n            maxDepth,\n            emptyPositions\n        };\n    } catch (error) {\n        console.error('Tree health stats error:', error);\n        return {\n            totalUsers: 0,\n            balanceRatio: 1,\n            averageDepth: 0,\n            maxDepth: 0,\n            emptyPositions: 0\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/referral.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/zod","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmining-units%2Froute&page=%2Fapi%2Fmining-units%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmining-units%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();