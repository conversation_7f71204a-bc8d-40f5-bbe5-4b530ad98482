"use strict";(()=>{var e={};e.id=964,e.ids=[964],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3934:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>f,serverHooks:()=>R,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>E});var a={};t.r(a),t.d(a,{GET:()=>p,POST:()=>m});var s=t(96559),i=t(48088),n=t(37719),o=t(32190),u=t(39542),l=t(6710),d=t(39794),c=t(2746);async function p(e){try{let{authenticated:r,user:t}=await (0,u.b9)(e);if(!r||!t)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});let a=(await (0,d.kp)(t.id)).map(e=>{let r=e.miningEarnings+e.referralEarnings+e.binaryEarnings,t=5*e.investmentAmount,a=r/t*100,s=Math.max(0,t-r);return{...e,totalEarningsCalculated:r,progressPercentage:Math.min(a,100),remainingCapacity:s,maxEarnings:t,willExpireSoon:a>=95}}).sort((e,r)=>new Date(e.createdAt).getTime()-new Date(r.createdAt).getTime());return o.NextResponse.json({success:!0,data:a,summary:{totalUnits:a.length,activeUnits:a.filter(e=>"ACTIVE"===e.status).length,expiredUnits:a.filter(e=>"EXPIRED"===e.status).length,totalMiningEarnings:a.reduce((e,r)=>e+r.miningEarnings,0),totalReferralEarnings:a.reduce((e,r)=>e+r.referralEarnings,0),totalBinaryEarnings:a.reduce((e,r)=>e+r.binaryEarnings,0),totalEarnings:a.reduce((e,r)=>e+(r.miningEarnings+r.referralEarnings+r.binaryEarnings),0),totalInvestment:a.reduce((e,r)=>e+r.investmentAmount,0),totalMiningPower:a.reduce((e,r)=>e+r.thsAmount,0)}})}catch(e){return console.error("Mining units fetch error:",e),o.NextResponse.json({success:!1,error:"Failed to fetch mining units"},{status:500})}}async function m(e){try{let{authenticated:r,user:a}=await (0,u.b9)(e);if(!r||!a)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});let{thsAmount:s,investmentAmount:i}=await e.json();if(!s||!i||s<=0||i<=0)return o.NextResponse.json({success:!1,error:"Invalid TH/s amount or investment amount"},{status:400});let n=parseFloat(await l.rs.get("MINIMUM_PURCHASE")||"50");if(i<n)return o.NextResponse.json({success:!1,error:`Minimum purchase amount is $${n}`},{status:400});let d=parseFloat(await l.rs.get("THS_PRICE")||"50"),p=s*d;if(Math.abs(i-p)>.01*p)return o.NextResponse.json({success:!1,error:"Investment amount does not match TH/s price"},{status:400});let{calculateDynamicROI:m}=await t.e(5112).then(t.bind(t,92731)),f=await m(s),g=await l.k_.getOrCreate(a.id);if(g.availableBalance<i)return o.NextResponse.json({success:!1,error:`Insufficient balance. Available: $${g.availableBalance.toFixed(2)}, Required: $${i.toFixed(2)}`},{status:400});let E=await l.tg.create({userId:a.id,thsAmount:s,investmentAmount:i,dailyROI:f});await l.k_.updateBalance(a.id,{availableBalance:g.availableBalance-i}),await l.DR.create({userId:a.id,type:"PURCHASE",amount:i,description:`Mining unit purchase - ${s} TH/s`,status:"COMPLETED"});let R=await (0,c.lv)(a.id);if(R)try{let e=await (0,c.$D)(R.id,i,a.id);console.log(`Direct referral bonus of $${e} added to sponsor ${R.id}`),await (0,c.gS)(a.id,i),console.log(`Binary points added for investment of $${i}`)}catch(e){console.error("Error processing commissions:",e)}await l.AJ.create({action:"MINING_UNIT_PURCHASED",userId:a.id,details:{miningUnitId:E.id,thsAmount:s,investmentAmount:i,dailyROI:f,sponsorId:R?.id},ipAddress:e.headers.get("x-forwarded-for")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"});try{let{emailNotificationService:e}=await Promise.all([t.e(9526),t.e(3161)]).then(t.bind(t,13161));await e.sendMiningUnitPurchaseNotification({userId:a.id,email:a.email,firstName:a.firstName,lastName:a.lastName,thsAmount:s,investmentAmount:i,dailyROI:f,purchaseDate:E.createdAt,expiryDate:E.expiryDate})}catch(e){console.error("Failed to send mining unit purchase email:",e)}return o.NextResponse.json({success:!0,message:"Mining unit purchased successfully",data:E})}catch(e){return console.error("Mining unit purchase error:",e),o.NextResponse.json({success:!1,error:"Failed to purchase mining unit"},{status:500})}}let f=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/mining-units/route",pathname:"/api/mining-units",filename:"route",bundlePath:"app/api/mining-units/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\mining-units\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:g,workUnitAsyncStorage:E,serverHooks:R}=f;function h(){return(0,n.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:E})}},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37366:e=>{e.exports=require("dns")},39542:(e,r,t)=>{t.d(r,{b9:()=>h,HU:()=>g,qc:()=>I,Lx:()=>T,DY:()=>S,DT:()=>x});var a=t(85663),s=t(43205),i=t.n(s),n=t(6710),o=t(45697);let u=o.z.object({DATABASE_URL:o.z.string().url("DATABASE_URL must be a valid PostgreSQL URL"),DIRECT_URL:o.z.string().url("DIRECT_URL must be a valid PostgreSQL URL"),JWT_SECRET:o.z.string().min(32,"JWT_SECRET must be at least 32 characters long").refine(e=>{let r=/[A-Z]/.test(e),t=/[a-z]/.test(e),a=/\d/.test(e),s=/[!@#$%^&*(),.?":{}|<>]/.test(e);return r&&t&&a&&s},"JWT_SECRET should contain uppercase, lowercase, numbers, and special characters"),JWT_EXPIRES_IN:o.z.string().default("30d"),NODE_ENV:o.z.enum(["development","production","test"]).default("development"),NEXT_PUBLIC_APP_URL:o.z.string().url().optional(),PORT:o.z.string().regex(/^\d+$/).transform(Number).default("3000"),SMTP_HOST:o.z.string().optional(),SMTP_PORT:o.z.string().regex(/^\d+$/).transform(Number).optional(),SMTP_USER:o.z.string().email().optional(),SMTP_PASSWORD:o.z.string().optional(),SMTP_SECURE:o.z.string().transform(e=>"true"===e).optional(),TRON_NETWORK:o.z.enum(["mainnet","testnet"]).default("testnet"),TRON_API_KEY:o.z.string().optional(),USDT_CONTRACT_ADDRESS:o.z.string().optional(),MAX_FILE_SIZE:o.z.string().regex(/^\d+$/).transform(Number).default("10485760"),UPLOAD_DIR:o.z.string().default("./public/uploads"),BCRYPT_ROUNDS:o.z.string().regex(/^\d+$/).transform(Number).default("12"),SESSION_TIMEOUT:o.z.string().regex(/^\d+$/).transform(Number).default("1800"),RATE_LIMIT_WINDOW_MS:o.z.string().regex(/^\d+$/).transform(Number).default("900000"),RATE_LIMIT_MAX_REQUESTS:o.z.string().regex(/^\d+$/).transform(Number).default("100"),COINGECKO_API_URL:o.z.string().url().default("https://api.coingecko.com/api/v3"),LOG_LEVEL:o.z.enum(["error","warn","info","debug"]).default("info"),ENABLE_REQUEST_LOGGING:o.z.string().transform(e=>"true"===e).default("false"),ENABLE_REGISTRATION:o.z.string().transform(e=>"true"===e).default("true"),ENABLE_KYC:o.z.string().transform(e=>"true"===e).default("true"),ENABLE_WITHDRAWALS:o.z.string().transform(e=>"true"===e).default("true")}).refine(e=>{let r=[e.SMTP_HOST,e.SMTP_PORT,e.SMTP_USER,e.SMTP_PASSWORD],t=r.some(e=>void 0!==e),a=r.every(e=>void 0!==e);return!t||!!a},{message:"If SMTP configuration is provided, all SMTP fields (HOST, PORT, USER, PASSWORD) must be provided"});function l(){try{let e=u.safeParse(process.env);if(!e.success){let r=e.error.errors.map(e=>`${e.path.join(".")}: ${e.message}`);return{success:!1,errors:r}}return{success:!0,data:e.data}}catch(e){return{success:!1,errors:["Failed to validate environment variables"]}}}let d=null;function c(){if(!d){let e=l();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),d=e.data,console.log("✅ Environment variables validated successfully")}return d}let p={jwt:{secret:()=>c().JWT_SECRET,expiresIn:()=>c().JWT_EXPIRES_IN},security:{bcryptRounds:()=>c().BCRYPT_ROUNDS,sessionTimeout:()=>c().SESSION_TIMEOUT,maxFileSize:()=>c().MAX_FILE_SIZE,uploadDir:()=>c().UPLOAD_DIR}};if("phase-production-build"!==process.env.NEXT_PHASE){let e=l();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),console.log("✅ Environment variables validated successfully")}let m=async e=>await a.Ay.hash(e,p.security.bcryptRounds()),f=async(e,r)=>await a.Ay.compare(e,r),g=e=>i().sign(e,p.jwt.secret(),{expiresIn:p.jwt.expiresIn()}),E=e=>{try{return i().verify(e,p.jwt.secret())}catch(e){return null}},R=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",r="HC";for(let t=0;t<8;t++)r+=e.charAt(Math.floor(Math.random()*e.length));return r},h=async e=>{let r=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!r)return{authenticated:!1,user:null};let t=E(r);if(!t)return{authenticated:!1,user:null};let a=await n.Gy.findByEmail(t.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},S=async e=>{let r,a;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let t=await n.Gy.findByReferralId(e.referralCode);if(!t)throw Error("Invalid referral code");r=t.id}let s=await m(e.password),i=!1;do a=R(),i=!await n.Gy.findByReferralId(a);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(r){let{placeUserByReferralType:a}=await t.e(2746).then(t.bind(t,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(r,o.id,s)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},T=async e=>{let r=await n.Gy.findByEmail(e.email);if(!r||!await f(e.password,r.password))throw Error("Invalid email or password");return{token:g({userId:r.id,email:r.email}),user:{id:r.id,email:r.email,referralId:r.referralId,kycStatus:r.kycStatus}}},x=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),I=async e=>{let r=await n.Gy.findById(e);return r?.role==="ADMIN"}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,580,7911,925,2746],()=>t(3934));module.exports=a})();