"use strict";(()=>{var e={};e.id=7752,e.ids=[1111,7752],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},21111:(e,r,t)=>{t.d(r,{X:()=>n,emailService:()=>o});var s=t(49526),a=t(6710);class i{async getEmailConfig(){try{let e=await a.T8.getEmailSettings();if(!e||!e.smtpHost||!e.smtpUser||!e.smtpPassword)return console.warn("Email configuration not found or incomplete"),null;return{host:e.smtpHost,port:e.smtpPort||587,secure:e.smtpSecure||!1,user:e.smtpUser,password:e.smtpPassword,fromName:e.fromName||"HashCoreX",fromEmail:e.fromEmail||e.smtpUser}}catch(e){return console.error("Failed to get email configuration:",e),null}}async initializeTransporter(e=!1){try{if(this.config=await this.getEmailConfig(),!this.config)return console.warn("Email configuration not available - email service disabled"),!1;if(!this.config.host||!this.config.user||!this.config.password)return console.error("Email configuration incomplete - missing host, user, or password"),!1;if(this.transporter=s.createTransport({host:this.config.host,port:this.config.port,secure:this.config.secure,auth:{user:this.config.user,pass:this.config.password},tls:{rejectUnauthorized:!1},connectionTimeout:1e4,greetingTimeout:5e3,socketTimeout:1e4}),e)console.log("Email transporter initialized (verification skipped)");else{let e=this.transporter.verify(),r=new Promise((e,r)=>setTimeout(()=>r(Error("Email verification timeout")),15e3));await Promise.race([e,r]),console.log("Email transporter initialized and verified successfully")}return!0}catch(e){return console.error("Failed to initialize email transporter:",e),this.transporter=null,this.config=null,!1}}async sendEmail(e){try{if((!this.transporter||!this.config)&&(console.log("Email transporter not initialized, attempting to initialize..."),!await this.initializeTransporter(!0)))return console.warn("Email service not configured - skipping email send"),!1;if(!e.to||!e.subject)return console.error("Invalid email data - missing recipient or subject"),!1;let r={from:`"${this.config.fromName}" <${this.config.fromEmail}>`,to:e.to,subject:e.subject,html:e.html,text:e.text},t=this.transporter.sendMail(r),s=new Promise((e,r)=>setTimeout(()=>r(Error("Email send timeout")),3e4)),a=await Promise.race([t,s]);return console.log("Email sent successfully:",a.messageId),!0}catch(e){return console.error("Failed to send email:",e),this.transporter=null,this.config=null,!1}}async sendOTPEmail(e,r,t,s="email_verification"){let a="otp_verification";"password_reset"===s?a="password_reset_otp":"two_factor_auth"===s&&(a="two_factor_otp");let i=await this.getEmailTemplate(a);if(!i)return console.error(`Email template '${a}' not found. Please ensure email templates are seeded.`),!1;let o=i.htmlContent,n=i.textContent||"";return o=(o=o.replace(/{{firstName}}/g,t||"User")).replace(/{{otp}}/g,r),n=(n=n.replace(/{{firstName}}/g,t||"User")).replace(/{{otp}}/g,r),await this.sendEmail({to:e,subject:i.subject,html:o,text:n})}async getEmailTemplate(e){try{return await a.T8.getEmailTemplate(e)}catch(e){return console.error("Failed to get email template:",e),null}}async testConnection(){try{if(this.config=await this.getEmailConfig(),!this.config)return console.error("Email configuration not found or incomplete"),!1;return this.transporter=s.createTransport({host:this.config.host,port:this.config.port,secure:this.config.secure,auth:{user:this.config.user,pass:this.config.password},tls:{rejectUnauthorized:!1}}),await this.transporter.verify(),console.log("Email connection test successful"),!0}catch(e){throw console.error("Email connection test failed:",e),this.transporter=null,e}}constructor(){this.transporter=null,this.config=null}}let o=new i,n=()=>Math.floor(1e5+9e5*Math.random()).toString()},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37366:e=>{e.exports=require("dns")},39542:(e,r,t)=>{t.d(r,{b9:()=>T,HU:()=>f,qc:()=>y,Lx:()=>R,DY:()=>w,DT:()=>S});var s=t(85663),a=t(43205),i=t.n(a),o=t(6710),n=t(45697);let l=n.z.object({DATABASE_URL:n.z.string().url("DATABASE_URL must be a valid PostgreSQL URL"),DIRECT_URL:n.z.string().url("DIRECT_URL must be a valid PostgreSQL URL"),JWT_SECRET:n.z.string().min(32,"JWT_SECRET must be at least 32 characters long").refine(e=>{let r=/[A-Z]/.test(e),t=/[a-z]/.test(e),s=/\d/.test(e),a=/[!@#$%^&*(),.?":{}|<>]/.test(e);return r&&t&&s&&a},"JWT_SECRET should contain uppercase, lowercase, numbers, and special characters"),JWT_EXPIRES_IN:n.z.string().default("30d"),NODE_ENV:n.z.enum(["development","production","test"]).default("development"),NEXT_PUBLIC_APP_URL:n.z.string().url().optional(),PORT:n.z.string().regex(/^\d+$/).transform(Number).default("3000"),SMTP_HOST:n.z.string().optional(),SMTP_PORT:n.z.string().regex(/^\d+$/).transform(Number).optional(),SMTP_USER:n.z.string().email().optional(),SMTP_PASSWORD:n.z.string().optional(),SMTP_SECURE:n.z.string().transform(e=>"true"===e).optional(),TRON_NETWORK:n.z.enum(["mainnet","testnet"]).default("testnet"),TRON_API_KEY:n.z.string().optional(),USDT_CONTRACT_ADDRESS:n.z.string().optional(),MAX_FILE_SIZE:n.z.string().regex(/^\d+$/).transform(Number).default("10485760"),UPLOAD_DIR:n.z.string().default("./public/uploads"),BCRYPT_ROUNDS:n.z.string().regex(/^\d+$/).transform(Number).default("12"),SESSION_TIMEOUT:n.z.string().regex(/^\d+$/).transform(Number).default("1800"),RATE_LIMIT_WINDOW_MS:n.z.string().regex(/^\d+$/).transform(Number).default("900000"),RATE_LIMIT_MAX_REQUESTS:n.z.string().regex(/^\d+$/).transform(Number).default("100"),COINGECKO_API_URL:n.z.string().url().default("https://api.coingecko.com/api/v3"),LOG_LEVEL:n.z.enum(["error","warn","info","debug"]).default("info"),ENABLE_REQUEST_LOGGING:n.z.string().transform(e=>"true"===e).default("false"),ENABLE_REGISTRATION:n.z.string().transform(e=>"true"===e).default("true"),ENABLE_KYC:n.z.string().transform(e=>"true"===e).default("true"),ENABLE_WITHDRAWALS:n.z.string().transform(e=>"true"===e).default("true")}).refine(e=>{let r=[e.SMTP_HOST,e.SMTP_PORT,e.SMTP_USER,e.SMTP_PASSWORD],t=r.some(e=>void 0!==e),s=r.every(e=>void 0!==e);return!t||!!s},{message:"If SMTP configuration is provided, all SMTP fields (HOST, PORT, USER, PASSWORD) must be provided"});function c(){try{let e=l.safeParse(process.env);if(!e.success){let r=e.error.errors.map(e=>`${e.path.join(".")}: ${e.message}`);return{success:!1,errors:r}}return{success:!0,data:e.data}}catch(e){return{success:!1,errors:["Failed to validate environment variables"]}}}let u=null;function d(){if(!u){let e=c();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),u=e.data,console.log("✅ Environment variables validated successfully")}return u}let m={jwt:{secret:()=>d().JWT_SECRET,expiresIn:()=>d().JWT_EXPIRES_IN},security:{bcryptRounds:()=>d().BCRYPT_ROUNDS,sessionTimeout:()=>d().SESSION_TIMEOUT,maxFileSize:()=>d().MAX_FILE_SIZE,uploadDir:()=>d().UPLOAD_DIR}};if("phase-production-build"!==process.env.NEXT_PHASE){let e=c();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),console.log("✅ Environment variables validated successfully")}let p=async e=>await s.Ay.hash(e,m.security.bcryptRounds()),g=async(e,r)=>await s.Ay.compare(e,r),f=e=>i().sign(e,m.jwt.secret(),{expiresIn:m.jwt.expiresIn()}),E=e=>{try{return i().verify(e,m.jwt.secret())}catch(e){return null}},h=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",r="HC";for(let t=0;t<8;t++)r+=e.charAt(Math.floor(Math.random()*e.length));return r},T=async e=>{let r=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!r)return{authenticated:!1,user:null};let t=E(r);if(!t)return{authenticated:!1,user:null};let s=await o.Gy.findByEmail(t.email);return s?{authenticated:!0,user:s}:{authenticated:!1,user:null}},w=async e=>{let r,s;if(await o.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let t=await o.Gy.findByReferralId(e.referralCode);if(!t)throw Error("Invalid referral code");r=t.id}let a=await p(e.password),i=!1;do s=h(),i=!await o.Gy.findByReferralId(s);while(!i);let n=await o.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:a,referralId:s});if(r){let{placeUserByReferralType:s}=await t.e(2746).then(t.bind(t,2746)),a="general";"left"===e.placementSide?a="left":"right"===e.placementSide&&(a="right"),await s(r,n.id,a)}return{id:n.id,email:n.email,referralId:n.referralId,kycStatus:n.kycStatus}},R=async e=>{let r=await o.Gy.findByEmail(e.email);if(!r||!await g(e.password,r.password))throw Error("Invalid email or password");return{token:f({userId:r.id,email:r.email}),user:{id:r.id,email:r.email,referralId:r.referralId,kycStatus:r.kycStatus}}},S=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),y=async e=>{let r=await o.Gy.findById(e);return r?.role==="ADMIN"}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},60439:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>f,serverHooks:()=>T,workAsyncStorage:()=>E,workUnitAsyncStorage:()=>h});var s={};t.r(s),t.d(s,{GET:()=>m,POST:()=>g,PUT:()=>p});var a=t(96559),i=t(48088),o=t(37719),n=t(32190),l=t(39542),c=t(6710),u=t(21111),d=t(82629);async function m(e){try{let{authenticated:r,user:t}=await (0,l.b9)(e);if(!r||!t)return n.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});if(!await (0,l.qc)(t.id))return n.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let s=await c.T8.getEmailSettings(),a={...s,smtpPassword:s.smtpPassword?"••••••••":""};return n.NextResponse.json({success:!0,data:a})}catch(r){return console.error("Get email settings error:",r),await d.v5.logApiError(e,r,"GET_EMAIL_SETTINGS_ERROR"),n.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}async function p(e){try{let{authenticated:r,user:t}=await (0,l.b9)(e);if(!r||!t)return n.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});if(!await (0,l.qc)(t.id))return n.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let{smtpHost:s,smtpPort:a,smtpSecure:i,smtpUser:o,smtpPassword:u,fromName:d,fromEmail:m,emailEnabled:p}=await e.json();if(!s||!o||!m)return n.NextResponse.json({success:!1,error:"SMTP Host, User, and From Email are required"},{status:400});let g={smtpHost:s,smtpPort:parseInt(a)||587,smtpSecure:!!i,smtpUser:o,fromName:d||"HashCoreX",fromEmail:m,emailEnabled:!!p};return u&&"••••••••"!==u&&(g.smtpPassword=u),await c.T8.updateEmailSettings(g),n.NextResponse.json({success:!0,message:"Email settings updated successfully"})}catch(r){return console.error("Update email settings error:",r),await d.v5.logApiError(e,r,"UPDATE_EMAIL_SETTINGS_ERROR"),n.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}async function g(e){try{let{authenticated:r,user:t}=await (0,l.b9)(e);if(!r||!t)return n.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});if(!await (0,l.qc)(t.id))return n.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let{testEmail:s}=await e.json();if(!s)return n.NextResponse.json({success:!1,error:"Test email address is required"},{status:400});try{if(!await u.emailService.testConnection())return n.NextResponse.json({success:!1,error:"Email connection test failed. Please check your SMTP settings."},{status:400})}catch(e){return console.error("Email connection test error:",e),n.NextResponse.json({success:!1,error:`Email connection failed: ${e instanceof Error?e.message:"Unknown error"}`},{status:400})}let a=await u.emailService.getEmailTemplate("test_email"),i=!1;if(a){let e=a.htmlContent,r=a.textContent||"";e=e.replace(/{{testDate}}/g,new Date().toISOString()),r=r.replace(/{{testDate}}/g,new Date().toISOString()),i=await u.emailService.sendEmail({to:s,subject:a.subject,html:e,text:r})}else i=await u.emailService.sendEmail({to:s,subject:"HashCoreX Email Test",html:`
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #ffd60a;">HashCoreX Email Test</h2>
            <p>This is a test email to verify your SMTP configuration.</p>
            <p>If you received this email, your email settings are working correctly!</p>
            <p>Sent at: ${new Date().toISOString()}</p>
          </div>
        `,text:"HashCoreX Email Test - If you received this email, your email settings are working correctly!"});if(i)return n.NextResponse.json({success:!0,message:"Test email sent successfully"});return n.NextResponse.json({success:!1,error:"Failed to send test email"},{status:500})}catch(r){return console.error("Test email error:",r),await d.v5.logApiError(e,r,"TEST_EMAIL_ERROR"),n.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}let f=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/email-settings/route",pathname:"/api/admin/email-settings",filename:"route",bundlePath:"app/api/admin/email-settings/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\email-settings\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:E,workUnitAsyncStorage:h,serverHooks:T}=f;function w(){return(0,o.patchFetch)({workAsyncStorage:E,workUnitAsyncStorage:h})}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},82629:(e,r,t)=>{t.d(r,{v5:()=>a});var s=t(6710);class a{static async logError(e){try{let r=e.error instanceof Error?e.error:Error(String(e.error)),t={action:e.action,userId:e.userId,adminId:e.adminId,details:{message:r.message,stack:r.stack,name:r.name,requestUrl:e.requestUrl,requestMethod:e.requestMethod,requestBody:e.requestBody,additionalData:e.additionalData,timestamp:new Date().toISOString()},ipAddress:e.ipAddress,userAgent:e.userAgent};await s.AJ.create(t),console.error(`[${e.action}] Error logged:`,{message:r.message,stack:r.stack,userId:e.userId,adminId:e.adminId,url:e.requestUrl})}catch(r){console.error("Failed to log error to database:",r),console.error("Original error:",e.error)}}static async logApiError(e,r,t,s,a,i){try{let o=null;try{if("GET"!==e.method&&e.headers.get("content-type")?.includes("application/json")){let r=e.clone();(o=await r.json()).password&&(o.password="[REDACTED]"),o.token&&(o.token="[REDACTED]"),o.apiKey&&(o.apiKey="[REDACTED]")}}catch(e){}await this.logError({action:t,error:r,userId:s,adminId:a,ipAddress:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||"unknown",userAgent:e.headers.get("user-agent")||"unknown",requestUrl:e.url,requestMethod:e.method,requestBody:o,additionalData:i})}catch(e){console.error("Failed to log API error:",e),console.error("Original error:",r)}}static async logClientError(e){try{await s.AJ.create({action:"CLIENT_ERROR",userId:e.userId,details:{message:e.message,stack:e.stack,url:e.url,timestamp:e.timestamp,additionalData:e.additionalData},userAgent:e.userAgent})}catch(e){console.error("Failed to log client error:",e)}}static async logAuthError(e,r,t,s){await this.logApiError(e,r,"AUTH_ERROR",void 0,void 0,{email:t,...s})}static async logDatabaseError(e,r,t,a,i){try{await s.AJ.create({action:"DATABASE_ERROR",userId:a,details:{message:e instanceof Error?e.message:String(e),stack:e instanceof Error?e.stack:void 0,operation:r,table:t,timestamp:new Date().toISOString(),additionalData:i}})}catch(r){console.error("Failed to log database error:",r),console.error("Original error:",e)}}static async logBusinessError(e,r,t,s,a){await this.logError({action:"BUSINESS_LOGIC_ERROR",error:e,userId:t,adminId:s,additionalData:{operation:r,...a}})}static async logExternalApiError(e,r,t,s,a){await this.logError({action:"EXTERNAL_API_ERROR",error:e,userId:s,additionalData:{service:r,endpoint:t,...a}})}static async logValidationError(e,r,t,s,a){await this.logError({action:"VALIDATION_ERROR",error:e,userId:s,additionalData:{field:r,value:t,...a}})}}},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,7911,9526,925],()=>t(60439));module.exports=s})();