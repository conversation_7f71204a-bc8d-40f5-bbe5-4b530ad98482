"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[905],{7508:(e,t,r)=>{r.d(t,{Lc:()=>n,hK:()=>i,NC:()=>a,MX:()=>l,Kj:()=>o});var s=r(5155);r(2115);let l=e=>{let{className:t,size:r=24}=e;return(0,s.jsxs)("svg",{width:r,height:r,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:t,children:[(0,s.jsx)("rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("line",{x1:"2",y1:"8",x2:"22",y2:"8",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"2",y1:"12",x2:"22",y2:"12",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"2",y1:"16",x2:"22",y2:"16",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"6",y1:"4",x2:"6",y2:"20",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"10",y1:"4",x2:"10",y2:"20",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"14",y1:"4",x2:"14",y2:"20",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"18",y1:"4",x2:"18",y2:"20",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("circle",{cx:"20",cy:"2",r:"1",fill:"currentColor"}),(0,s.jsx)("path",{d:"M19 1l1 1-1 1-1-1z",fill:"currentColor"})]})},a=e=>{let{className:t,size:r=24}=e;return(0,s.jsxs)("svg",{width:r,height:r,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:t,children:[(0,s.jsx)("rect",{x:"2",y:"6",width:"20",height:"12",rx:"2",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("rect",{x:"4",y:"8",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("rect",{x:"8",y:"8",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("rect",{x:"12",y:"8",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("rect",{x:"16",y:"8",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("rect",{x:"4",y:"12",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("rect",{x:"8",y:"12",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("rect",{x:"12",y:"12",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("rect",{x:"16",y:"12",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("circle",{cx:"20",cy:"4",r:"1",fill:"currentColor"}),(0,s.jsx)("line",{x1:"20",y1:"4",x2:"20",y2:"6",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"18",y1:"2",x2:"22",y2:"2",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"19",y1:"1",x2:"21",y2:"3",stroke:"currentColor",strokeWidth:"1"}),(0,s.jsx)("line",{x1:"21",y1:"1",x2:"19",y2:"3",stroke:"currentColor",strokeWidth:"1"})]})},n=e=>{let{className:t,size:r=24}=e;return(0,s.jsxs)("svg",{width:r,height:r,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:t,children:[(0,s.jsx)("circle",{cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("path",{d:"M8 12h8M12 8v8",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("path",{d:"M10 8h4c1.1 0 2 .9 2 2s-.9 2-2 2h-4",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("path",{d:"M10 14h4c1.1 0 2 .9 2 2s-.9 2-2 2h-4",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("line",{x1:"12",y1:"6",x2:"12",y2:"8",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"12",y1:"16",x2:"12",y2:"18",stroke:"currentColor",strokeWidth:"2"})]})},i=e=>{let{className:t,size:r=24}=e;return(0,s.jsxs)("svg",{width:r,height:r,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:t,children:[(0,s.jsx)("path",{d:"M12 2C8 2 5 5 5 9c0 5 7 13 7 13s7-8 7-13c0-4-3-7-7-7z",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("path",{d:"M12 9c0-2-1-3-3-3s-3 1-3 3 1 3 3 3 3-1 3-3z",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("path",{d:"M8 9c0 1 1 2 2 2s2-1 2-2-1-2-2-2-2 1-2 2z",fill:"currentColor"})]})},o=e=>{let{className:t,size:r=24}=e;return(0,s.jsxs)("svg",{width:r,height:r,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:t,children:[(0,s.jsx)("line",{x1:"12",y1:"12",x2:"12",y2:"22",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("path",{d:"M12 12L8 4c-1-2 0-4 2-4s3 2 2 4l-2 8z",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("path",{d:"M12 12l8-4c2-1 4 0 4 2s-2 3-4 2l-8-2z",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("path",{d:"M12 12l-4 8c-1 2-3 2-4 0s0-3 2-4l8-2z",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("circle",{cx:"12",cy:"12",r:"1",fill:"currentColor"}),(0,s.jsx)("line",{x1:"10",y1:"22",x2:"14",y2:"22",stroke:"currentColor",strokeWidth:"2"})]})}},8505:(e,t,r)=>{r.d(t,{$n:()=>o,Zp:()=>c,Wu:()=>u,aR:()=>d,ZB:()=>x,pd:()=>h,Rh:()=>w,aF:()=>g,ph:()=>z,vp:()=>A,G_:()=>v,eC:()=>k});var s=r(5155),l=r(2115),a=r(2085),n=r(9434);let i=(0,a.F)("inline-flex items-center justify-center rounded-xl font-semibold focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none shadow-lg",{variants:{variant:{primary:"bg-yellow-500 text-white focus:ring-yellow-500",secondary:"bg-gray-200 text-gray-800 focus:ring-gray-500",success:"bg-emerald-500 text-white focus:ring-emerald-500",danger:"bg-red-500 text-white focus:ring-red-500",warning:"bg-yellow-500 text-white focus:ring-yellow-500",destructive:"bg-red-600 text-white focus:ring-red-500",outline:"border-2 border-yellow-500 bg-transparent text-yellow-600 focus:ring-yellow-500",ghost:"text-gray-600 focus:ring-yellow-500 rounded-lg",link:"text-yellow-600 underline-offset-4 focus:ring-yellow-500",premium:"bg-slate-800 text-white focus:ring-slate-500",glass:"glass-morphism text-slate-900 backdrop-blur-xl border border-white/20"},size:{sm:"h-10 px-4 text-sm rounded-lg",md:"h-12 px-6 text-base rounded-xl",lg:"h-14 px-8 text-lg rounded-xl",xl:"h-16 px-10 text-xl rounded-2xl font-bold",icon:"h-12 w-12 rounded-xl"}},defaultVariants:{variant:"primary",size:"md"}}),o=l.forwardRef((e,t)=>{let{className:r,variant:l,size:a,loading:o,leftIcon:c,rightIcon:d,children:x,disabled:u,...h}=e;return(0,s.jsxs)("button",{className:(0,n.cn)(i({variant:l,size:a,className:r})),ref:t,disabled:u||o,...h,children:[o&&(0,s.jsx)("div",{className:"mr-2",children:(0,s.jsx)("div",{className:"spinner"})}),c&&!o&&(0,s.jsx)("span",{className:"mr-2",children:c}),x,d&&!o&&(0,s.jsx)("span",{className:"ml-2",children:d})]})});o.displayName="Button";let c=l.forwardRef((e,t)=>{let{className:r,children:l,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("rounded-2xl border border-gray-200/50 bg-white/90 backdrop-blur-xl shadow-xl overflow-hidden",r),...a,children:l})});c.displayName="Card";let d=l.forwardRef((e,t)=>{let{className:r,children:l,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6 pb-4",r),...a,children:l})});d.displayName="CardHeader";let x=l.forwardRef((e,t)=>{let{className:r,children:l,...a}=e;return(0,s.jsx)("h3",{ref:t,className:(0,n.cn)("text-xl font-semibold leading-none tracking-tight text-dark-900",r),...a,children:l})});x.displayName="CardTitle",l.forwardRef((e,t)=>{let{className:r,children:l,...a}=e;return(0,s.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-gray-500",r),...a,children:l})}).displayName="CardDescription";let u=l.forwardRef((e,t)=>{let{className:r,children:l,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",r),...a,children:l})});u.displayName="CardContent",l.forwardRef((e,t)=>{let{className:r,children:l,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",r),...a,children:l})}).displayName="CardFooter";let h=l.forwardRef((e,t)=>{let{className:r,type:l,label:a,error:i,leftIcon:o,rightIcon:c,...d}=e;return(0,s.jsxs)("div",{className:"w-full",children:[a&&(0,s.jsx)("label",{className:"block text-sm font-medium text-dark-700 mb-2",children:a}),(0,s.jsxs)("div",{className:"relative",children:[o&&(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)("span",{className:"text-gray-400",children:o})}),(0,s.jsx)("input",{type:l,className:(0,n.cn)("flex h-14 w-full rounded-xl border-2 border-gray-200 bg-white/90 backdrop-blur-sm px-4 py-4 text-base shadow-inner focus:shadow-lg placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-solar-500 focus:border-solar-500 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-300 hover:border-gray-300",o&&"pl-12",c&&"pr-12",i&&"border-red-500 focus:ring-red-500 focus:border-red-500",r),ref:t,...d}),c&&(0,s.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:(0,s.jsx)("span",{className:"text-gray-400",children:c})})]}),i&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:i})]})});h.displayName="Input";var m=r(7650),f=r(4416);let g=e=>{let{isOpen:t,onClose:r,title:a,children:i,size:c="md",showCloseButton:d=!0,darkMode:x=!1}=e;if((0,l.useEffect)(()=>(t?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[t]),(0,l.useEffect)(()=>{let e=e=>{"Escape"===e.key&&r()};return t&&document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)}},[t,r]),!t)return null;let u=(0,s.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",children:[(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity",onClick:r}),(0,s.jsxs)("div",{className:(0,n.cn)("relative w-full rounded-xl shadow-xl transform transition-all",x?"bg-slate-800":"bg-white",{sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl"}[c]),onClick:e=>e.stopPropagation(),children:[(0,s.jsxs)("div",{className:(0,n.cn)("flex items-center justify-between p-6 border-b",x?"border-slate-700":"border-gray-200"),children:[(0,s.jsx)("h2",{className:(0,n.cn)("text-xl font-semibold",x?"text-white":"text-dark-900"),children:a}),d&&(0,s.jsx)(o,{variant:"ghost",size:"icon",onClick:r,className:"h-8 w-8 rounded-full",children:(0,s.jsx)(f.A,{className:"h-4 w-4"})})]}),(0,s.jsx)("div",{className:"p-6",children:i})]})]});return(0,m.createPortal)(u,document.body)},w=e=>{let{size:t="md",className:r,text:l}=e;return(0,s.jsxs)("div",{className:(0,n.cn)("flex flex-col items-center justify-center",r),children:[(0,s.jsx)("div",{className:(0,n.cn)("animate-spin rounded-full border-2 border-gray-300 border-t-solar-500",{sm:"h-4 w-4",md:"h-8 w-8",lg:"h-12 w-12"}[t])}),l&&(0,s.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:l})]})};var y=r(1243),p=r(646),j=r(1284);let b=e=>{let{isOpen:t,onClose:r,onConfirm:l,title:a,message:i,confirmText:c="Confirm",cancelText:d="Cancel",variant:x="default",darkMode:u=!1,loading:h=!1}=e;if(!t)return null;let g=(0,s.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50 transition-opacity",onClick:r}),(0,s.jsxs)("div",{className:(0,n.cn)("relative w-full max-w-md rounded-xl shadow-xl transform transition-all",u?"bg-slate-800 border border-slate-700":"bg-white"),onClick:e=>e.stopPropagation(),children:[(0,s.jsxs)("div",{className:(0,n.cn)("flex items-center justify-between p-6 border-b",u?"border-slate-700":"border-gray-200"),children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(()=>{switch(x){case"danger":return(0,s.jsx)(y.A,{className:"h-6 w-6 text-red-500"});case"warning":return(0,s.jsx)(y.A,{className:"h-6 w-6 text-yellow-500"});case"success":return(0,s.jsx)(p.A,{className:"h-6 w-6 text-green-500"});default:return(0,s.jsx)(j.A,{className:"h-6 w-6 text-blue-500"})}})(),(0,s.jsx)("h2",{className:(0,n.cn)("text-lg font-semibold",u?"text-white":"text-gray-900"),children:a})]}),(0,s.jsx)(o,{variant:"ghost",size:"icon",onClick:r,disabled:h,className:"h-8 w-8 rounded-full",children:(0,s.jsx)(f.A,{className:"h-4 w-4"})})]}),(0,s.jsx)("div",{className:"p-6",children:"string"==typeof i?(0,s.jsx)("p",{className:(0,n.cn)("text-sm leading-relaxed",u?"text-slate-300":"text-gray-600"),children:i}):(0,s.jsx)("div",{className:(0,n.cn)("text-sm leading-relaxed",u?"text-slate-300":"text-gray-600"),children:i})}),(0,s.jsxs)("div",{className:(0,n.cn)("flex items-center justify-end space-x-3 p-6 border-t",u?"border-slate-700":"border-gray-200"),children:[(0,s.jsx)(o,{variant:"outline",onClick:r,disabled:h,className:(0,n.cn)(u?"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white":""),children:d}),(0,s.jsx)(o,{variant:(()=>{switch(x){case"danger":return"destructive";case"warning":return"warning";default:return"default"}})(),onClick:l,disabled:h,className:(0,n.cn)(h&&"opacity-50 cursor-not-allowed"),children:h?"Processing...":c})]})]})]});return(0,m.createPortal)(g,document.body)},v=()=>{let[e,t]=l.useState({isOpen:!1,title:"",message:"",onConfirm:()=>{}}),[r,a]=l.useState(!1),n=()=>{r||t(e=>({...e,isOpen:!1}))};return{showConfirm:e=>{t({isOpen:!0,...e,onConfirm:async()=>{a(!0);try{await e.onConfirm(),t(e=>({...e,isOpen:!1}))}catch(e){console.error("Confirm action failed:",e)}finally{a(!1)}}})},hideConfirm:n,ConfirmDialog:()=>(0,s.jsx)(b,{isOpen:e.isOpen,onClose:n,onConfirm:e.onConfirm,title:e.title,message:e.message,variant:e.variant,confirmText:e.confirmText,cancelText:e.cancelText,darkMode:e.darkMode,loading:r}),loading:r}};var N=r(5339);let C=e=>{let{isOpen:t,onClose:r,title:l,message:a,variant:i="info",darkMode:c=!1,showCloseButton:d=!0,buttonText:x="OK",size:u="md"}=e;if(!t)return null;let h=(0,s.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",onClick:r,children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-black/50 backdrop-blur-sm"}),(0,s.jsxs)("div",{className:(0,n.cn)("relative w-full rounded-xl shadow-xl transform transition-all",c?"bg-slate-800 border border-slate-700":"bg-white",{sm:"max-w-sm",md:"max-w-md",lg:"max-w-lg"}[u]),onClick:e=>e.stopPropagation(),children:[(0,s.jsxs)("div",{className:(0,n.cn)("flex items-center justify-between p-6 border-b",c?"border-slate-700":"border-gray-200"),children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(()=>{switch(i){case"error":return(0,s.jsx)(N.A,{className:"h-6 w-6 text-red-500"});case"warning":return(0,s.jsx)(y.A,{className:"h-6 w-6 text-yellow-500"});case"success":return(0,s.jsx)(p.A,{className:"h-6 w-6 text-green-500"});default:return(0,s.jsx)(j.A,{className:"h-6 w-6 text-blue-500"})}})(),(0,s.jsx)("h2",{className:(0,n.cn)("text-lg font-semibold",c?"text-white":"text-gray-900"),children:l})]}),d&&(0,s.jsx)(o,{variant:"ghost",size:"icon",onClick:r,className:"h-8 w-8 rounded-full",children:(0,s.jsx)(f.A,{className:"h-4 w-4"})})]}),(0,s.jsx)("div",{className:"p-6",children:"string"==typeof a?(0,s.jsx)("p",{className:(0,n.cn)("text-sm leading-relaxed",c?"text-slate-300":"text-gray-600"),children:a}):(0,s.jsx)("div",{className:(0,n.cn)("text-sm leading-relaxed",c?"text-slate-300":"text-gray-600"),children:a})}),(0,s.jsx)("div",{className:(0,n.cn)("flex items-center justify-end p-6 border-t",c?"border-slate-700":"border-gray-200"),children:(0,s.jsx)(o,{variant:(()=>{switch(i){case"error":return"danger";case"warning":return"warning";case"success":return"success";default:return"primary"}})(),onClick:r,className:"min-w-[80px]",children:x})})]})]});return(0,m.createPortal)(h,document.body)},k=()=>{let[e,t]=l.useState({isOpen:!1,title:"",message:""}),r=()=>{t(e=>({...e,isOpen:!1}))};return{showMessage:e=>{t({isOpen:!0,...e})},hideMessage:r,MessageBoxComponent:()=>(0,s.jsx)(C,{...e,onClose:r})}};var D=r(1007),W=r(4355),T=r(9869),M=r(6766);let A=e=>{let{currentPicture:t,onUpload:r,onRemove:a,loading:n=!1,disabled:i=!1}=e,[c,d]=(0,l.useState)(null),[x,u]=(0,l.useState)(!1),h=(0,l.useRef)(null),m=e=>{if(!e)return;if(!e.type.startsWith("image/"))return void alert("Please select an image file");if(e.size>5242880)return void alert("File size must be less than 5MB");let t=new FileReader;t.onload=e=>{var t;d(null==(t=e.target)?void 0:t.result)},t.readAsDataURL(e),r(e)},g=c||t;return(0,s.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"w-32 h-32 rounded-full overflow-hidden border-4 border-gray-200 bg-gray-100 flex items-center justify-center",children:g?(0,s.jsx)(M.default,{src:g,alt:"Profile Picture",width:128,height:128,className:"w-full h-full object-cover"}):(0,s.jsx)(D.A,{className:"w-16 h-16 text-gray-400"})}),n&&(0,s.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-white"})}),g&&!n&&(0,s.jsx)("button",{onClick:()=>{d(null),a&&a()},disabled:i,className:"absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors",children:(0,s.jsx)(f.A,{className:"w-4 h-4"})})]}),(0,s.jsx)("div",{className:"border-2 border-dashed rounded-lg p-6 text-center transition-colors ".concat(x?"border-blue-400 bg-blue-50":"border-gray-300 hover:border-gray-400"," ").concat(i?"opacity-50 cursor-not-allowed":"cursor-pointer"),onDrop:e=>{e.preventDefault(),u(!1);let t=e.dataTransfer.files[0];t&&m(t)},onDragOver:e=>{e.preventDefault(),u(!0)},onDragLeave:e=>{e.preventDefault(),u(!1)},onClick:()=>{var e;return!i&&(null==(e=h.current)?void 0:e.click())},children:(0,s.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,s.jsx)(W.A,{className:"w-8 h-8 text-gray-400"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-700",children:"Click to upload or drag and drop"}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"PNG, JPG, GIF up to 5MB"})]})]})}),(0,s.jsx)("input",{ref:h,type:"file",accept:"image/*",onChange:e=>{var t;let r=null==(t=e.target.files)?void 0:t[0];r&&m(r)},className:"hidden",disabled:i}),(0,s.jsxs)(o,{onClick:()=>{var e;return null==(e=h.current)?void 0:e.click()},disabled:i||n,variant:"outline",size:"sm",className:"flex items-center space-x-2",children:[(0,s.jsx)(T.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"Choose File"})]})]})},z=e=>{let{src:t,alt:r="Profile",size:a=40,className:n="",fallbackText:i,fallbackBgColor:o="bg-gray-500",priority:c=!1,loading:d="lazy"}=e,[x,u]=(0,l.useState)(!1),[h,m]=(0,l.useState)(!0),f=t&&!x,g=!t||x;return(0,s.jsxs)("div",{className:"relative flex items-center justify-center overflow-hidden ".concat(o," ").concat(n),style:{width:a,height:a},children:[h&&t&&(0,s.jsx)("div",{className:"absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center",children:(0,s.jsx)("div",{className:"w-4 h-4 bg-gray-300 rounded-full"})}),f&&(0,s.jsx)(M.default,{src:t,alt:r,width:a,height:a,className:"w-full h-full object-cover",priority:c,loading:d,onError:()=>{u(!0),m(!1)},onLoad:()=>{m(!1)},style:{opacity:+!h,transition:"opacity 0.2s ease-in-out"}}),g&&(0,s.jsx)("div",{className:"flex items-center justify-center w-full h-full",children:i?(0,s.jsx)("span",{className:"text-white font-semibold",style:{fontSize:.4*a},children:i}):(0,s.jsx)(D.A,{className:"text-white",style:{width:.5*a,height:.5*a}})})]})}},9434:(e,t,r)=>{r.d(t,{D1:()=>m,Oj:()=>x,Yq:()=>o,ZU:()=>h,ZV:()=>i,cn:()=>a,jI:()=>u,lW:()=>d,r6:()=>c,vv:()=>n});var s=r(2596),l=r(9688);function a(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,l.QP)((0,s.$)(t))}function n(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD";return new Intl.NumberFormat("en-US",{style:"currency",currency:t,minimumFractionDigits:2,maximumFractionDigits:2}).format(e)}function i(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;return new Intl.NumberFormat("en-US",{minimumFractionDigits:t,maximumFractionDigits:t}).format(e)}function o(e){let t="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(t)}function c(e){let t="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(t)}function d(e){if(navigator.clipboard)return navigator.clipboard.writeText(e);let t=document.createElement("textarea");t.value=e,document.body.appendChild(t),t.focus(),t.select();try{return document.execCommand("copy"),Promise.resolve()}catch(e){return Promise.reject(e)}finally{document.body.removeChild(t)}}function x(e){let t=[],r=[],s=e.length>=8;r.push({valid:s,message:"At least 8 characters long"}),s||t.push("Password must be at least 8 characters long");let l=/[A-Z]/.test(e);r.push({valid:l,message:"At least one uppercase letter"}),l||t.push("Password must contain at least one uppercase letter");let a=/[a-z]/.test(e);r.push({valid:a,message:"At least one lowercase letter"}),a||t.push("Password must contain at least one lowercase letter");let n=/\d/.test(e);r.push({valid:n,message:"At least one number"}),n||t.push("Password must contain at least one number");let i=/[!@#$%^&*(),.?":{}|<>]/.test(e);return r.push({valid:i,message:"At least one special character"}),i||t.push("Password must contain at least one special character"),{isValid:0===t.length,errors:t,checks:r}}function u(e){return e>=1e3?"".concat((e/1e3).toFixed(1),"K TH/s"):"".concat(e.toFixed(2)," TH/s")}function h(){let e=new Date,t=new Date;t.setUTCDate(e.getUTCDate()+(6-e.getUTCDay())),t.setUTCHours(15,0,0,0),e>t&&t.setUTCDate(t.getUTCDate()+7);let r=t.getTime()-e.getTime(),s=Math.floor(r/864e5),l=Math.floor(r%864e5/36e5);return{days:s,hours:l,minutes:Math.floor(r%36e5/6e4),seconds:Math.floor(r%6e4/1e3)}}function m(){let e=new Date,t=new Date;t.setUTCDate(e.getUTCDate()+(6-e.getUTCDay())),t.setUTCHours(15,0,0,0),e>t&&t.setUTCDate(t.getUTCDate()+7);let r=t.getTime()-e.getTime(),s=Math.floor(r/864e5),l=Math.floor(r%864e5/36e5);return{days:s,hours:l,minutes:Math.floor(r%36e5/6e4),seconds:Math.floor(r%6e4/1e3)}}}}]);