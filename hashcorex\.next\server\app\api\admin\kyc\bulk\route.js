"use strict";(()=>{var e={};e.id=868,e.ids=[868],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37366:e=>{e.exports=require("dns")},39542:(e,r,t)=>{t.d(r,{b9:()=>S,HU:()=>E,qc:()=>x,Lx:()=>h,DY:()=>T,DT:()=>_});var s=t(85663),a=t(43205),i=t.n(a),n=t(6710),o=t(45697);let u=o.z.object({DATABASE_URL:o.z.string().url("DATABASE_URL must be a valid PostgreSQL URL"),DIRECT_URL:o.z.string().url("DIRECT_URL must be a valid PostgreSQL URL"),JWT_SECRET:o.z.string().min(32,"JWT_SECRET must be at least 32 characters long").refine(e=>{let r=/[A-Z]/.test(e),t=/[a-z]/.test(e),s=/\d/.test(e),a=/[!@#$%^&*(),.?":{}|<>]/.test(e);return r&&t&&s&&a},"JWT_SECRET should contain uppercase, lowercase, numbers, and special characters"),JWT_EXPIRES_IN:o.z.string().default("30d"),NODE_ENV:o.z.enum(["development","production","test"]).default("development"),NEXT_PUBLIC_APP_URL:o.z.string().url().optional(),PORT:o.z.string().regex(/^\d+$/).transform(Number).default("3000"),SMTP_HOST:o.z.string().optional(),SMTP_PORT:o.z.string().regex(/^\d+$/).transform(Number).optional(),SMTP_USER:o.z.string().email().optional(),SMTP_PASSWORD:o.z.string().optional(),SMTP_SECURE:o.z.string().transform(e=>"true"===e).optional(),TRON_NETWORK:o.z.enum(["mainnet","testnet"]).default("testnet"),TRON_API_KEY:o.z.string().optional(),USDT_CONTRACT_ADDRESS:o.z.string().optional(),MAX_FILE_SIZE:o.z.string().regex(/^\d+$/).transform(Number).default("10485760"),UPLOAD_DIR:o.z.string().default("./public/uploads"),BCRYPT_ROUNDS:o.z.string().regex(/^\d+$/).transform(Number).default("12"),SESSION_TIMEOUT:o.z.string().regex(/^\d+$/).transform(Number).default("1800"),RATE_LIMIT_WINDOW_MS:o.z.string().regex(/^\d+$/).transform(Number).default("900000"),RATE_LIMIT_MAX_REQUESTS:o.z.string().regex(/^\d+$/).transform(Number).default("100"),COINGECKO_API_URL:o.z.string().url().default("https://api.coingecko.com/api/v3"),LOG_LEVEL:o.z.enum(["error","warn","info","debug"]).default("info"),ENABLE_REQUEST_LOGGING:o.z.string().transform(e=>"true"===e).default("false"),ENABLE_REGISTRATION:o.z.string().transform(e=>"true"===e).default("true"),ENABLE_KYC:o.z.string().transform(e=>"true"===e).default("true"),ENABLE_WITHDRAWALS:o.z.string().transform(e=>"true"===e).default("true")}).refine(e=>{let r=[e.SMTP_HOST,e.SMTP_PORT,e.SMTP_USER,e.SMTP_PASSWORD],t=r.some(e=>void 0!==e),s=r.every(e=>void 0!==e);return!t||!!s},{message:"If SMTP configuration is provided, all SMTP fields (HOST, PORT, USER, PASSWORD) must be provided"});function l(){try{let e=u.safeParse(process.env);if(!e.success){let r=e.error.errors.map(e=>`${e.path.join(".")}: ${e.message}`);return{success:!1,errors:r}}return{success:!0,data:e.data}}catch(e){return{success:!1,errors:["Failed to validate environment variables"]}}}let d=null;function c(){if(!d){let e=l();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),d=e.data,console.log("✅ Environment variables validated successfully")}return d}let p={jwt:{secret:()=>c().JWT_SECRET,expiresIn:()=>c().JWT_EXPIRES_IN},security:{bcryptRounds:()=>c().BCRYPT_ROUNDS,sessionTimeout:()=>c().SESSION_TIMEOUT,maxFileSize:()=>c().MAX_FILE_SIZE,uploadDir:()=>c().UPLOAD_DIR}};if("phase-production-build"!==process.env.NEXT_PHASE){let e=l();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),console.log("✅ Environment variables validated successfully")}let f=async e=>await s.Ay.hash(e,p.security.bcryptRounds()),m=async(e,r)=>await s.Ay.compare(e,r),E=e=>i().sign(e,p.jwt.secret(),{expiresIn:p.jwt.expiresIn()}),R=e=>{try{return i().verify(e,p.jwt.secret())}catch(e){return null}},g=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",r="HC";for(let t=0;t<8;t++)r+=e.charAt(Math.floor(Math.random()*e.length));return r},S=async e=>{let r=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!r)return{authenticated:!1,user:null};let t=R(r);if(!t)return{authenticated:!1,user:null};let s=await n.Gy.findByEmail(t.email);return s?{authenticated:!0,user:s}:{authenticated:!1,user:null}},T=async e=>{let r,s;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let t=await n.Gy.findByReferralId(e.referralCode);if(!t)throw Error("Invalid referral code");r=t.id}let a=await f(e.password),i=!1;do s=g(),i=!await n.Gy.findByReferralId(s);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:a,referralId:s});if(r){let{placeUserByReferralType:s}=await t.e(2746).then(t.bind(t,2746)),a="general";"left"===e.placementSide?a="left":"right"===e.placementSide&&(a="right"),await s(r,o.id,a)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},h=async e=>{let r=await n.Gy.findByEmail(e.email);if(!r||!await m(e.password,r.password))throw Error("Invalid email or password");return{token:E({userId:r.id,email:r.email}),user:{id:r.id,email:r.email,referralId:r.referralId,kycStatus:r.kycStatus}}},_=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),x=async e=>{let r=await n.Gy.findById(e);return r?.role==="ADMIN"}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},90742:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>R,routeModule:()=>p,serverHooks:()=>E,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{POST:()=>c});var a=t(96559),i=t(48088),n=t(37719),o=t(32190),u=t(39542),l=t(31183),d=t(6710);async function c(e){try{let{authenticated:r,user:t}=await (0,u.b9)(e);if(!r||!t)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if(!await (0,u.qc)(t.id))return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let{userIds:s,action:a,rejectionReason:i}=await e.json();if(!s||!Array.isArray(s)||0===s.length)return o.NextResponse.json({success:!1,error:"User IDs array is required"},{status:400});if(!a||!["APPROVE","REJECT"].includes(a))return o.NextResponse.json({success:!1,error:"Invalid action"},{status:400});if("REJECT"===a&&!i)return o.NextResponse.json({success:!1,error:"Rejection reason is required"},{status:400});let n="APPROVE"===a?"APPROVED":"REJECTED",c=new Date,p=[];for(let r of s)try{let s=await l.prisma.kYCDocument.findMany({where:{userId:r,status:"PENDING"}});if(0===s.length){p.push({userId:r,success:!1,error:"No pending documents found"});continue}await l.prisma.kYCDocument.updateMany({where:{userId:r,status:"PENDING"},data:{status:n,reviewedAt:c,reviewedBy:t.email,rejectionReason:"REJECT"===a?i:null}}),await l.prisma.user.update({where:{id:r},data:{kycStatus:n}}),await d.AJ.create({action:"KYC_BULK_REVIEWED",adminId:t.id,details:{reviewedUserId:r,action:a,rejectionReason:"REJECT"===a?i:null,documentsCount:s.length,reviewedBy:t.email,bulkOperation:!0},ipAddress:e.headers.get("x-forwarded-for")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"}),p.push({userId:r,success:!0,status:n})}catch(e){console.error(`Error processing user ${r}:`,e),p.push({userId:r,success:!1,error:"Processing failed"})}let f=p.filter(e=>e.success).length,m=p.filter(e=>!e.success).length;return o.NextResponse.json({success:!0,message:`Bulk operation completed. ${f} successful, ${m} failed.`,data:{action:a,totalProcessed:s.length,successCount:f,failureCount:m,results:p}})}catch(e){return console.error("Bulk KYC operation error:",e),o.NextResponse.json({success:!1,error:"Failed to perform bulk operation"},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/kyc/bulk/route",pathname:"/api/admin/kyc/bulk",filename:"route",bundlePath:"app/api/admin/kyc/bulk/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\kyc\\bulk\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:m,serverHooks:E}=p;function R(){return(0,n.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:m})}},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,7911,925],()=>t(90742));module.exports=s})();