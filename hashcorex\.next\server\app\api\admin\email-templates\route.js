"use strict";(()=>{var e={};e.id=944,e.ids=[944],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},21820:e=>{e.exports=require("os")},24151:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>R,routeModule:()=>m,serverHooks:()=>f,workAsyncStorage:()=>E,workUnitAsyncStorage:()=>g});var a={};t.r(a),t.d(a,{GET:()=>c,POST:()=>p});var s=t(96559),i=t(48088),o=t(37719),n=t(32190),l=t(39542),u=t(6710),d=t(82629);async function c(e){try{let{authenticated:r,user:t}=await (0,l.b9)(e);if(!r||!t)return n.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});if(!await (0,l.qc)(t.id))return n.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let a=await u.vo.findAll();return n.NextResponse.json({success:!0,data:a})}catch(r){return console.error("Get email templates error:",r),await d.v5.logApiError(e,r,"GET_EMAIL_TEMPLATES_ERROR"),n.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}async function p(e){try{let{authenticated:r,user:t}=await (0,l.b9)(e);if(!r||!t)return n.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});if(!await (0,l.qc)(t.id))return n.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let{name:a,subject:s,htmlContent:i,textContent:o}=await e.json();if(!a||!s||!i)return n.NextResponse.json({success:!1,error:"Name, subject, and HTML content are required"},{status:400});if(await u.vo.findByName(a))return n.NextResponse.json({success:!1,error:"Template with this name already exists"},{status:400});let d=await u.vo.create({name:a,subject:s,htmlContent:i,textContent:o});return n.NextResponse.json({success:!0,message:"Email template created successfully",data:d})}catch(r){return console.error("Create email template error:",r),await d.v5.logApiError(e,r,"CREATE_EMAIL_TEMPLATE_ERROR"),n.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}let m=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/email-templates/route",pathname:"/api/admin/email-templates",filename:"route",bundlePath:"app/api/admin/email-templates/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\email-templates\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:E,workUnitAsyncStorage:g,serverHooks:f}=m;function R(){return(0,o.patchFetch)({workAsyncStorage:E,workUnitAsyncStorage:g})}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37366:e=>{e.exports=require("dns")},39542:(e,r,t)=>{t.d(r,{b9:()=>T,HU:()=>g,qc:()=>_,Lx:()=>S,DY:()=>A,DT:()=>h});var a=t(85663),s=t(43205),i=t.n(s),o=t(6710),n=t(45697);let l=n.z.object({DATABASE_URL:n.z.string().url("DATABASE_URL must be a valid PostgreSQL URL"),DIRECT_URL:n.z.string().url("DIRECT_URL must be a valid PostgreSQL URL"),JWT_SECRET:n.z.string().min(32,"JWT_SECRET must be at least 32 characters long").refine(e=>{let r=/[A-Z]/.test(e),t=/[a-z]/.test(e),a=/\d/.test(e),s=/[!@#$%^&*(),.?":{}|<>]/.test(e);return r&&t&&a&&s},"JWT_SECRET should contain uppercase, lowercase, numbers, and special characters"),JWT_EXPIRES_IN:n.z.string().default("30d"),NODE_ENV:n.z.enum(["development","production","test"]).default("development"),NEXT_PUBLIC_APP_URL:n.z.string().url().optional(),PORT:n.z.string().regex(/^\d+$/).transform(Number).default("3000"),SMTP_HOST:n.z.string().optional(),SMTP_PORT:n.z.string().regex(/^\d+$/).transform(Number).optional(),SMTP_USER:n.z.string().email().optional(),SMTP_PASSWORD:n.z.string().optional(),SMTP_SECURE:n.z.string().transform(e=>"true"===e).optional(),TRON_NETWORK:n.z.enum(["mainnet","testnet"]).default("testnet"),TRON_API_KEY:n.z.string().optional(),USDT_CONTRACT_ADDRESS:n.z.string().optional(),MAX_FILE_SIZE:n.z.string().regex(/^\d+$/).transform(Number).default("10485760"),UPLOAD_DIR:n.z.string().default("./public/uploads"),BCRYPT_ROUNDS:n.z.string().regex(/^\d+$/).transform(Number).default("12"),SESSION_TIMEOUT:n.z.string().regex(/^\d+$/).transform(Number).default("1800"),RATE_LIMIT_WINDOW_MS:n.z.string().regex(/^\d+$/).transform(Number).default("900000"),RATE_LIMIT_MAX_REQUESTS:n.z.string().regex(/^\d+$/).transform(Number).default("100"),COINGECKO_API_URL:n.z.string().url().default("https://api.coingecko.com/api/v3"),LOG_LEVEL:n.z.enum(["error","warn","info","debug"]).default("info"),ENABLE_REQUEST_LOGGING:n.z.string().transform(e=>"true"===e).default("false"),ENABLE_REGISTRATION:n.z.string().transform(e=>"true"===e).default("true"),ENABLE_KYC:n.z.string().transform(e=>"true"===e).default("true"),ENABLE_WITHDRAWALS:n.z.string().transform(e=>"true"===e).default("true")}).refine(e=>{let r=[e.SMTP_HOST,e.SMTP_PORT,e.SMTP_USER,e.SMTP_PASSWORD],t=r.some(e=>void 0!==e),a=r.every(e=>void 0!==e);return!t||!!a},{message:"If SMTP configuration is provided, all SMTP fields (HOST, PORT, USER, PASSWORD) must be provided"});function u(){try{let e=l.safeParse(process.env);if(!e.success){let r=e.error.errors.map(e=>`${e.path.join(".")}: ${e.message}`);return{success:!1,errors:r}}return{success:!0,data:e.data}}catch(e){return{success:!1,errors:["Failed to validate environment variables"]}}}let d=null;function c(){if(!d){let e=u();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),d=e.data,console.log("✅ Environment variables validated successfully")}return d}let p={jwt:{secret:()=>c().JWT_SECRET,expiresIn:()=>c().JWT_EXPIRES_IN},security:{bcryptRounds:()=>c().BCRYPT_ROUNDS,sessionTimeout:()=>c().SESSION_TIMEOUT,maxFileSize:()=>c().MAX_FILE_SIZE,uploadDir:()=>c().UPLOAD_DIR}};if("phase-production-build"!==process.env.NEXT_PHASE){let e=u();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),console.log("✅ Environment variables validated successfully")}let m=async e=>await a.Ay.hash(e,p.security.bcryptRounds()),E=async(e,r)=>await a.Ay.compare(e,r),g=e=>i().sign(e,p.jwt.secret(),{expiresIn:p.jwt.expiresIn()}),f=e=>{try{return i().verify(e,p.jwt.secret())}catch(e){return null}},R=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",r="HC";for(let t=0;t<8;t++)r+=e.charAt(Math.floor(Math.random()*e.length));return r},T=async e=>{let r=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!r)return{authenticated:!1,user:null};let t=f(r);if(!t)return{authenticated:!1,user:null};let a=await o.Gy.findByEmail(t.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},A=async e=>{let r,a;if(await o.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let t=await o.Gy.findByReferralId(e.referralCode);if(!t)throw Error("Invalid referral code");r=t.id}let s=await m(e.password),i=!1;do a=R(),i=!await o.Gy.findByReferralId(a);while(!i);let n=await o.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(r){let{placeUserByReferralType:a}=await t.e(2746).then(t.bind(t,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(r,n.id,s)}return{id:n.id,email:n.email,referralId:n.referralId,kycStatus:n.kycStatus}},S=async e=>{let r=await o.Gy.findByEmail(e.email);if(!r||!await E(e.password,r.password))throw Error("Invalid email or password");return{token:g({userId:r.id,email:r.email}),user:{id:r.id,email:r.email,referralId:r.referralId,kycStatus:r.kycStatus}}},h=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),_=async e=>{let r=await o.Gy.findById(e);return r?.role==="ADMIN"}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},82629:(e,r,t)=>{t.d(r,{v5:()=>s});var a=t(6710);class s{static async logError(e){try{let r=e.error instanceof Error?e.error:Error(String(e.error)),t={action:e.action,userId:e.userId,adminId:e.adminId,details:{message:r.message,stack:r.stack,name:r.name,requestUrl:e.requestUrl,requestMethod:e.requestMethod,requestBody:e.requestBody,additionalData:e.additionalData,timestamp:new Date().toISOString()},ipAddress:e.ipAddress,userAgent:e.userAgent};await a.AJ.create(t),console.error(`[${e.action}] Error logged:`,{message:r.message,stack:r.stack,userId:e.userId,adminId:e.adminId,url:e.requestUrl})}catch(r){console.error("Failed to log error to database:",r),console.error("Original error:",e.error)}}static async logApiError(e,r,t,a,s,i){try{let o=null;try{if("GET"!==e.method&&e.headers.get("content-type")?.includes("application/json")){let r=e.clone();(o=await r.json()).password&&(o.password="[REDACTED]"),o.token&&(o.token="[REDACTED]"),o.apiKey&&(o.apiKey="[REDACTED]")}}catch(e){}await this.logError({action:t,error:r,userId:a,adminId:s,ipAddress:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||"unknown",userAgent:e.headers.get("user-agent")||"unknown",requestUrl:e.url,requestMethod:e.method,requestBody:o,additionalData:i})}catch(e){console.error("Failed to log API error:",e),console.error("Original error:",r)}}static async logClientError(e){try{await a.AJ.create({action:"CLIENT_ERROR",userId:e.userId,details:{message:e.message,stack:e.stack,url:e.url,timestamp:e.timestamp,additionalData:e.additionalData},userAgent:e.userAgent})}catch(e){console.error("Failed to log client error:",e)}}static async logAuthError(e,r,t,a){await this.logApiError(e,r,"AUTH_ERROR",void 0,void 0,{email:t,...a})}static async logDatabaseError(e,r,t,s,i){try{await a.AJ.create({action:"DATABASE_ERROR",userId:s,details:{message:e instanceof Error?e.message:String(e),stack:e instanceof Error?e.stack:void 0,operation:r,table:t,timestamp:new Date().toISOString(),additionalData:i}})}catch(r){console.error("Failed to log database error:",r),console.error("Original error:",e)}}static async logBusinessError(e,r,t,a,s){await this.logError({action:"BUSINESS_LOGIC_ERROR",error:e,userId:t,adminId:a,additionalData:{operation:r,...s}})}static async logExternalApiError(e,r,t,a,s){await this.logError({action:"EXTERNAL_API_ERROR",error:e,userId:a,additionalData:{service:r,endpoint:t,...s}})}static async logValidationError(e,r,t,a,s){await this.logError({action:"VALIDATION_ERROR",error:e,userId:a,additionalData:{field:r,value:t,...s}})}}},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,580,7911,925],()=>t(24151));module.exports=a})();