import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest, isAdmin } from '@/lib/auth';

// POST - Test SMTP connection only (no email sending)
export async function POST(request: NextRequest) {
  try {
    // Authenticate admin
    const { authenticated, user } = await authenticateRequest(request);
    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const isUserAdmin = await isAdmin(user.id);
    if (!isUserAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Import email service
    const { emailService } = await import('@/lib/email');

    // Test connection
    const connectionTest = await emailService.testConnection();
    
    return NextResponse.json({
      success: true,
      connected: connectionTest,
      message: connectionTest ? 'SMTP connection successful' : 'SMTP connection failed',
    });

  } catch (error: any) {
    console.error('SMTP connection test error:', error);
    
    // Extract meaningful error information
    let errorMessage = 'SMTP connection test failed';
    let errorCode = 'UNKNOWN_ERROR';
    
    if (error.message) {
      errorMessage = error.message;
    }
    
    if (error.code) {
      errorCode = error.code;
    }
    
    // Handle specific error types
    if (error.code === 'ENOTFOUND') {
      errorMessage = 'SMTP host not found. Please check the hostname.';
      errorCode = 'HOST_NOT_FOUND';
    } else if (error.code === 'ECONNREFUSED') {
      errorMessage = 'Connection refused. Please check the port and host.';
      errorCode = 'CONNECTION_REFUSED';
    } else if (error.code === 'ETIMEDOUT') {
      errorMessage = 'Connection timeout. Please check your network and SMTP settings.';
      errorCode = 'CONNECTION_TIMEOUT';
    } else if (error.responseCode) {
      errorMessage = `SMTP Error ${error.responseCode}: ${error.response || 'Authentication failed'}`;
      errorCode = `SMTP_${error.responseCode}`;
    }
    
    return NextResponse.json({
      success: false,
      connected: false,
      error: errorMessage,
      errorCode: errorCode,
      details: {
        originalError: error.message,
        code: error.code,
        responseCode: error.responseCode,
      }
    });
  }
}
