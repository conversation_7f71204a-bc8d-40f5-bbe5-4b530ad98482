(()=>{var e={};e.id=5890,e.ids=[5890],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7650:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>o});var r=t(65239),a=t(48088),n=t(88170),i=t.n(n),l=t(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(s,d);let o={children:["",{children:["(auth)",{children:["forgot-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,75990)),"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\(auth)\\forgot-password\\page.tsx"]}]},{}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\(auth)\\forgot-password\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(auth)/forgot-password/page",pathname:"/forgot-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12597:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},13964:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},14061:(e,s,t)=>{"use strict";t.d(s,{h:()=>o});var r=t(60687),a=t(43210),n=t(39996),i=t(41550),l=t(5336),d=t(78122);let o=({email:e,firstName:s,onVerified:t,onResend:o,loading:c=!1,error:m})=>{let[x,h]=(0,a.useState)(["","","","","",""]),[u,p]=(0,a.useState)(600),[f,g]=(0,a.useState)(!1),[b,j]=(0,a.useState)(!1),y=(0,a.useRef)([]);(0,a.useEffect)(()=>{let e=setInterval(()=>{p(e=>e<=1?(g(!0),0):e-1)},1e3);return()=>clearInterval(e)},[]);let v=(e,s)=>{if(s.length>1)return;let r=[...x];r[e]=s,h(r),s&&e<5&&y.current[e+1]?.focus(),r.every(e=>""!==e)&&6===r.join("").length&&t(r.join(""))},w=(e,s)=>{"Backspace"===s.key&&!x[e]&&e>0&&y.current[e-1]?.focus()},N=e=>{e.preventDefault();let s=e.clipboardData.getData("text").replace(/\D/g,"").slice(0,6);6===s.length&&(h(s.split("")),t(s))},P=async()=>{j(!0),g(!1),p(600),await o(),j(!1)};return(0,r.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(i.A,{className:"w-8 h-8 text-green-600"})}),(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Verify Your Email"}),(0,r.jsx)("p",{className:"text-gray-600",children:"We've sent a 6-digit verification code to"}),(0,r.jsx)("p",{className:"font-semibold text-gray-900",children:e})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3 text-center",children:"Enter verification code"}),(0,r.jsx)("div",{className:"flex justify-center space-x-3",children:x.map((e,s)=>(0,r.jsx)(n.pd,{ref:e=>y.current[s]=e,type:"text",inputMode:"numeric",maxLength:1,value:e,onChange:e=>v(s,e.target.value),onKeyDown:e=>w(s,e),onPaste:N,className:"w-12 h-12 text-center text-lg font-semibold border-2 focus:border-green-500",disabled:c},s))})]}),m&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:(0,r.jsx)("p",{className:"text-sm text-red-600 text-center",children:m})}),(0,r.jsx)("div",{className:"text-center",children:u>0?(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["Code expires in ",(0,r.jsx)("span",{className:"font-semibold text-green-600",children:(e=>{let s=Math.floor(e/60);return`${s}:${(e%60).toString().padStart(2,"0")}`})(u)})]}):(0,r.jsx)("p",{className:"text-sm text-red-600",children:"Code has expired"})}),(0,r.jsx)(n.$n,{onClick:()=>{let e=x.join("");6===e.length&&t(e)},disabled:c||x.some(e=>""===e),className:"w-full bg-green-600 hover:bg-green-700 text-white",children:c?(0,r.jsxs)("div",{className:"flex items-center justify-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Verifying..."]}):(0,r.jsxs)("div",{className:"flex items-center justify-center",children:[(0,r.jsx)(l.A,{className:"w-4 h-4 mr-2"}),"Verify Code"]})}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"Didn't receive the code?"}),(0,r.jsx)(n.$n,{onClick:P,disabled:!f||b,variant:"ghost",className:"text-green-600 hover:text-green-700",children:b?(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(d.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Sending..."]}):(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(d.A,{className:"w-4 h-4 mr-2"}),"Resend Code"]})})]}),(0,r.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,r.jsx)("p",{className:"text-sm text-blue-700",children:(0,r.jsx)("strong",{children:"Tips:"})}),(0,r.jsxs)("ul",{className:"text-sm text-blue-600 mt-1 space-y-1",children:[(0,r.jsx)("li",{children:"• Check your spam/junk folder"}),(0,r.jsxs)("li",{children:["• Make sure ",e," is correct"]}),(0,r.jsx)("li",{children:"• You can paste the code from your email"})]})]})]})]})}},16189:(e,s,t)=>{"use strict";var r=t(65773);t.o(r,"usePathname")&&t.d(s,{usePathname:function(){return r.usePathname}}),t.o(r,"useRouter")&&t.d(s,{useRouter:function(){return r.useRouter}}),t.o(r,"useSearchParams")&&t.d(s,{useSearchParams:function(){return r.useSearchParams}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20666:(e,s,t)=>{"use strict";t.d(s,{CU:()=>o});var r=t(60687),a=t(43210),n=t(16189),i=t(57445),l=t(39996);let d=({children:e,requireAuth:s=!1,requireGuest:t=!1,redirectTo:d})=>{let{user:o,loading:c}=(0,i.A)(),m=(0,n.useRouter)(),x=(0,n.usePathname)();return((0,a.useEffect)(()=>{if(!c){if(s&&!o){let e=`/login?redirect=${encodeURIComponent(x)}`;m.replace(e);return}if(t&&o){let e=d||"/dashboard";m.replace(e);return}}},[o,c,s,t,m,x,d]),c)?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(l.Rh,{}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"Checking authentication..."})]})}):s&&!o||t&&o?null:(0,r.jsx)(r.Fragment,{children:e})},o=({children:e,redirectTo:s})=>(0,r.jsx)(d,{requireGuest:!0,redirectTo:s,children:e})},28559:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31607:(e,s,t)=>{Promise.resolve().then(t.bind(t,82364))},33873:e=>{"use strict";e.exports=require("path")},41550:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71359:(e,s,t)=>{Promise.resolve().then(t.bind(t,75990))},75990:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\(auth)\\forgot-password\\page.tsx","default")},78122:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},79551:e=>{"use strict";e.exports=require("url")},82364:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>j});var r=t(60687),a=t(43210),n=t(85814),i=t.n(n),l=t(16189),d=t(39996),o=t(71180),c=t(41550),m=t(28559),x=t(13964),h=t(12597),u=t(13861),p=t(4780),f=t(20666),g=t(14061);function b(){let e=(0,l.useRouter)(),[s,t]=(0,a.useState)("email"),[n,f]=(0,a.useState)(""),[b,j]=(0,a.useState)(""),[y,v]=(0,a.useState)(""),[w,N]=(0,a.useState)(!1),[P,k]=(0,a.useState)(!1),[C,S]=(0,a.useState)(!1),[A,M]=(0,a.useState)(""),[_,R]=(0,a.useState)(""),q=(0,p.Oj)(b),T=async e=>{e.preventDefault(),M(""),S(!0);try{let e=await fetch("/api/auth/send-otp",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:n,purpose:"password_reset"})}),s=await e.json();s.success?t("otp"):M(s.error||"Failed to send reset code")}catch(e){M("Failed to send reset code. Please try again.")}finally{S(!1)}},E=async e=>{M(""),S(!0);try{let s=await fetch("/api/auth/verify-otp",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:n,otp:e,purpose:"password_reset"})}),r=await s.json();r.success?t("password"):M(r.error||"Invalid OTP")}catch(e){M("Failed to verify OTP. Please try again.")}finally{S(!1)}},D=async()=>{M(""),S(!0);try{let e=await fetch("/api/auth/send-otp",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:n,purpose:"password_reset"})}),s=await e.json();s.success||M(s.error||"Failed to resend code")}catch(e){M("Failed to resend code. Please try again.")}finally{S(!1)}},O=async s=>{if(s.preventDefault(),M(""),b!==y)return void M("Passwords do not match");if(!q.isValid)return void M("Please ensure your password meets all requirements");S(!0);try{let s=await fetch("/api/auth/reset-password",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:n,newPassword:b,confirmPassword:y})}),t=await s.json();t.success?(R("Password reset successful! Redirecting to dashboard..."),setTimeout(()=>{e.push("/dashboard")},2e3)):M(t.error||"Failed to reset password")}catch(e){M("Failed to reset password. Please try again.")}finally{S(!1)}};return(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"w-full max-w-lg",children:[(0,r.jsx)("div",{className:"text-center mb-8",children:(0,r.jsxs)(i(),{href:"/",className:"inline-flex items-center justify-center mb-6",children:[(0,r.jsx)(o.MX,{className:"h-8 w-8 text-yellow-500 mr-2"}),(0,r.jsx)("span",{className:"text-2xl font-bold text-gray-900",children:"HashCoreX"})]})}),(0,r.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8",children:["email"===s&&(0,r.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(c.A,{className:"w-8 h-8 text-blue-600"})}),(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Reset Your Password"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Enter your email address and we'll send you a verification code to reset your password."})]}),(0,r.jsxs)("form",{onSubmit:T,className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),(0,r.jsx)(d.pd,{id:"email",type:"email",value:n,onChange:e=>f(e.target.value),placeholder:"Enter your email address",required:!0,disabled:C,className:"w-full"})]}),A&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:(0,r.jsx)("p",{className:"text-sm text-red-600 text-center",children:A})}),(0,r.jsx)(d.$n,{type:"submit",disabled:C||!n,className:"w-full bg-blue-600 hover:bg-blue-700 text-white",children:C?(0,r.jsxs)("div",{className:"flex items-center justify-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Sending Code..."]}):"Send Reset Code"}),(0,r.jsx)("div",{className:"text-center",children:(0,r.jsxs)(i(),{href:"/login",className:"text-sm text-blue-600 hover:text-blue-700 flex items-center justify-center",children:[(0,r.jsx)(m.A,{className:"w-4 h-4 mr-1"}),"Back to Login"]})})]})]}),"otp"===s&&(0,r.jsx)(g.h,{email:n,onVerified:E,onResend:D,loading:C,error:A}),"password"===s&&(0,r.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(x.A,{className:"w-8 h-8 text-green-600"})}),(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Create New Password"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Enter your new password below. Make sure it's strong and secure."})]}),(0,r.jsxs)("form",{onSubmit:O,className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"newPassword",className:"block text-sm font-medium text-gray-700 mb-2",children:"New Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(d.pd,{id:"newPassword",type:w?"text":"password",value:b,onChange:e=>j(e.target.value),placeholder:"Enter new password",required:!0,disabled:C,className:"w-full pr-10"}),(0,r.jsx)("button",{type:"button",onClick:()=>N(!w),className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:w?(0,r.jsx)(h.A,{className:"h-4 w-4 text-gray-400"}):(0,r.jsx)(u.A,{className:"h-4 w-4 text-gray-400"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-2",children:"Confirm New Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(d.pd,{id:"confirmPassword",type:P?"text":"password",value:y,onChange:e=>v(e.target.value),placeholder:"Confirm new password",required:!0,disabled:C,className:"w-full pr-10"}),(0,r.jsx)("button",{type:"button",onClick:()=>k(!P),className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:P?(0,r.jsx)(h.A,{className:"h-4 w-4 text-gray-400"}):(0,r.jsx)(u.A,{className:"h-4 w-4 text-gray-400"})})]})]}),b&&(0,r.jsxs)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-700 mb-2",children:"Password Requirements:"}),(0,r.jsx)("div",{className:"space-y-1",children:q.checks.map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center text-sm",children:[(0,r.jsx)("div",{className:`w-4 h-4 rounded-full mr-2 flex items-center justify-center ${e.valid?"bg-green-100":"bg-gray-100"}`,children:e.valid&&(0,r.jsx)(x.A,{className:"w-3 h-3 text-green-600"})}),(0,r.jsx)("span",{className:e.valid?"text-green-600":"text-gray-500",children:e.message})]},s))})]}),A&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:(0,r.jsx)("p",{className:"text-sm text-red-600 text-center",children:A})}),_&&(0,r.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3",children:(0,r.jsx)("p",{className:"text-sm text-green-600 text-center",children:_})}),(0,r.jsx)(d.$n,{type:"submit",disabled:C||!q.isValid||b!==y,className:"w-full bg-green-600 hover:bg-green-700 text-white",children:C?(0,r.jsxs)("div",{className:"flex items-center justify-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Resetting Password..."]}):"Reset Password"})]})]})]}),(0,r.jsx)("div",{className:"text-center mt-6",children:(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["Remember your password?"," ",(0,r.jsx)(i(),{href:"/login",className:"text-blue-600 hover:text-blue-700 font-medium",children:"Sign in here"})]})})]})})}function j(){return(0,r.jsx)(f.CU,{redirectTo:"/dashboard",children:(0,r.jsx)(b,{})})}}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4447,1771,9419,6806],()=>t(7650));module.exports=r})();