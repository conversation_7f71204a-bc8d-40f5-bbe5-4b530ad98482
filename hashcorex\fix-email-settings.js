const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function fixEmailSettings() {
  try {
    console.log('Fixing email settings in database...');
    
    // Get current email settings
    const settings = await prisma.adminSettings.findMany({
      where: {
        key: {
          in: ['smtpHost', 'smtpUser', 'fromEmail', 'fromName']
        }
      }
    });
    
    console.log('Current settings:');
    settings.forEach(setting => {
      console.log(`${setting.key}: ${setting.value}`);
    });
    
    // Fix settings by removing quotes
    const updates = [];
    
    for (const setting of settings) {
      let cleanValue = setting.value;
      
      // Remove surrounding quotes if they exist
      if (typeof cleanValue === 'string' && 
          ((cleanValue.startsWith('"') && cleanValue.endsWith('"')) ||
           (cleanValue.startsWith("'") && cleanValue.endsWith("'")))) {
        cleanValue = cleanValue.slice(1, -1);
        updates.push({
          key: setting.key,
          oldValue: setting.value,
          newValue: cleanValue
        });
      }
    }
    
    if (updates.length > 0) {
      console.log('\nUpdating settings:');
      for (const update of updates) {
        console.log(`${update.key}: "${update.oldValue}" → "${update.newValue}"`);
        
        await prisma.adminSettings.update({
          where: { key: update.key },
          data: { value: update.newValue }
        });
      }
      console.log('✅ Settings updated successfully');
    } else {
      console.log('✅ No updates needed - settings are already clean');
    }
    
    // Verify the fixes
    console.log('\nVerifying fixed settings:');
    const fixedSettings = await prisma.adminSettings.findMany({
      where: {
        key: {
          in: ['smtpHost', 'smtpUser', 'fromEmail', 'fromName']
        }
      }
    });
    
    fixedSettings.forEach(setting => {
      console.log(`${setting.key}: ${setting.value}`);
    });
    
  } catch (error) {
    console.error('Error fixing email settings:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixEmailSettings();
