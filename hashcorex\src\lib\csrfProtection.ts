/**
 * CSRF Protection System
 * Implements Cross-Site Request Forgery protection for state-changing operations
 */

import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import crypto from 'crypto';

// CSRF token configuration
const CSRF_TOKEN_LENGTH = 32;
const CSRF_COOKIE_NAME = 'csrf-token';
const CSRF_HEADER_NAME = 'x-csrf-token';
const CSRF_TOKEN_EXPIRY = 60 * 60 * 1000; // 1 hour

// CSRF token store (in production, use Redis)
const csrfTokenStore = new Map<string, { token: string; expires: number; userId?: string }>();

// CSRF protection class
export class CSRFProtection {
  
  // Generate a new CSRF token
  static generateToken(userId?: string): string {
    const token = crypto.randomBytes(CSRF_TOKEN_LENGTH).toString('hex');
    const expires = Date.now() + CSRF_TOKEN_EXPIRY;
    
    csrfTokenStore.set(token, {
      token,
      expires,
      userId,
    });
    
    return token;
  }

  // Validate CSRF token
  static validateToken(token: string, userId?: string): boolean {
    const storedToken = csrfTokenStore.get(token);
    
    if (!storedToken) {
      return false;
    }

    // Check if token has expired
    if (Date.now() > storedToken.expires) {
      csrfTokenStore.delete(token);
      return false;
    }

    // Check if token belongs to the user (if userId provided)
    if (userId && storedToken.userId && storedToken.userId !== userId) {
      return false;
    }

    return true;
  }

  // Remove token after use (one-time use)
  static consumeToken(token: string): boolean {
    const isValid = this.validateToken(token);
    if (isValid) {
      csrfTokenStore.delete(token);
    }
    return isValid;
  }

  // Set CSRF token in cookie
  static setCsrfCookie(response: NextResponse, token: string): void {
    response.cookies.set(CSRF_COOKIE_NAME, token, {
      httpOnly: false, // Must be accessible to JavaScript for AJAX requests
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: CSRF_TOKEN_EXPIRY / 1000, // Convert to seconds
      path: '/',
    });
  }

  // Get CSRF token from request
  static getTokenFromRequest(request: NextRequest): string | null {
    // Try header first
    const headerToken = request.headers.get(CSRF_HEADER_NAME);
    if (headerToken) {
      return headerToken;
    }

    // Try form data for non-JSON requests
    const contentType = request.headers.get('content-type');
    if (contentType?.includes('application/x-www-form-urlencoded')) {
      // This would need to be handled in the route handler
      return null;
    }

    return null;
  }

  // Middleware to check CSRF token
  static async validateRequest(request: NextRequest, userId?: string): Promise<{
    valid: boolean;
    error?: string;
  }> {
    const method = request.method.toUpperCase();
    
    // Only check CSRF for state-changing methods
    if (!['POST', 'PUT', 'PATCH', 'DELETE'].includes(method)) {
      return { valid: true };
    }

    // Skip CSRF for certain endpoints (like login, register)
    const pathname = request.nextUrl.pathname;
    const skipPaths = [
      '/api/auth/login',
      '/api/auth/register',
      '/api/auth/send-otp',
      '/api/health',
    ];

    if (skipPaths.some(path => pathname.startsWith(path))) {
      return { valid: true };
    }

    const token = this.getTokenFromRequest(request);
    
    if (!token) {
      return {
        valid: false,
        error: 'CSRF token missing. Please refresh the page and try again.',
      };
    }

    const isValid = this.validateToken(token, userId);
    
    if (!isValid) {
      return {
        valid: false,
        error: 'Invalid or expired CSRF token. Please refresh the page and try again.',
      };
    }

    return { valid: true };
  }

  // Clean up expired tokens
  static cleanupExpiredTokens(): void {
    const now = Date.now();
    for (const [token, data] of csrfTokenStore.entries()) {
      if (now > data.expires) {
        csrfTokenStore.delete(token);
      }
    }
  }

  // Get token statistics for monitoring
  static getTokenStats(): {
    totalTokens: number;
    expiredTokens: number;
    activeTokens: number;
  } {
    const now = Date.now();
    let expiredTokens = 0;
    let activeTokens = 0;

    for (const data of csrfTokenStore.values()) {
      if (now > data.expires) {
        expiredTokens++;
      } else {
        activeTokens++;
      }
    }

    return {
      totalTokens: csrfTokenStore.size,
      expiredTokens,
      activeTokens,
    };
  }
}

// API endpoint to get CSRF token
export async function getCSRFToken(userId?: string): Promise<{
  token: string;
  expires: number;
}> {
  const token = CSRFProtection.generateToken(userId);
  const expires = Date.now() + CSRF_TOKEN_EXPIRY;
  
  return {
    token,
    expires,
  };
}

// Middleware wrapper for CSRF protection
export function withCSRFProtection<T extends any[], R>(
  handler: (...args: T) => Promise<R>
) {
  return async (...args: T): Promise<R | NextResponse> => {
    const request = args[0] as NextRequest;
    
    // Extract user ID if available (from authentication)
    let userId: string | undefined;
    try {
      // This would typically come from your auth middleware
      const authHeader = request.headers.get('authorization');
      const token = authHeader?.replace('Bearer ', '') || request.cookies.get('auth-token')?.value;
      
      if (token) {
        // You would decode the JWT here to get userId
        // For now, we'll skip user-specific validation
      }
    } catch (error) {
      // Continue without user ID
    }

    const csrfValidation = await CSRFProtection.validateRequest(request, userId);
    
    if (!csrfValidation.valid) {
      return NextResponse.json(
        {
          success: false,
          error: csrfValidation.error,
          code: 'CSRF_TOKEN_INVALID',
        },
        { status: 403 }
      );
    }

    return handler(...args);
  };
}

// React hook for CSRF token management (to be used in frontend)
export const csrfTokenUtils = {
  // Get CSRF token from cookie
  getTokenFromCookie(): string | null {
    if (typeof document === 'undefined') return null;
    
    const cookies = document.cookie.split(';');
    for (const cookie of cookies) {
      const [name, value] = cookie.trim().split('=');
      if (name === CSRF_COOKIE_NAME) {
        return decodeURIComponent(value);
      }
    }
    return null;
  },

  // Add CSRF token to fetch headers
  addTokenToHeaders(headers: HeadersInit = {}): HeadersInit {
    const token = this.getTokenFromCookie();
    if (token) {
      return {
        ...headers,
        [CSRF_HEADER_NAME]: token,
      };
    }
    return headers;
  },

  // Fetch with CSRF protection
  async fetchWithCSRF(url: string, options: RequestInit = {}): Promise<Response> {
    const headers = this.addTokenToHeaders(options.headers);
    
    return fetch(url, {
      ...options,
      headers,
    });
  },
};

// Start cleanup interval (run every 10 minutes)
if (typeof window === 'undefined') { // Server-side only
  setInterval(() => {
    CSRFProtection.cleanupExpiredTokens();
  }, 10 * 60 * 1000);
}

export default CSRFProtection;
