(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3698],{1256:(e,s,t)=>{Promise.resolve().then(t.bind(t,9557))},2138:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},4105:(e,s,t)=>{"use strict";t.d(s,{A:()=>n,AuthProvider:()=>i});var a=t(5155),l=t(2115);let r=(0,l.createContext)(void 0),i=e=>{let{children:s}=e,[t,i]=(0,l.useState)(null),[n,c]=(0,l.useState)(!0);(0,l.useEffect)(()=>{d()},[]);let d=async()=>{try{let e=await fetch("/api/auth/me",{credentials:"include"});if(e.ok){let s=await e.json();s.success&&i(s.data)}}catch(e){console.error("Auth check failed:",e)}finally{c(!1)}},x=async(e,s)=>{c(!0);try{let t=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e,password:s})}),a=await t.json();if(!a.success)throw Error(a.error||"Login failed");i(a.data.user),setTimeout(()=>{d()},100)}catch(e){throw e}finally{c(!1)}},o=async(e,s,t,a,l,r,n,d)=>{c(!0);try{let c=await fetch(n?"/api/auth/register?side=".concat(n):"/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,firstName:s,lastName:t,password:a,confirmPassword:l,referralCode:r,otp:d})}),x=await c.json();if(!x.success)throw Error(x.error||"Registration failed");i({id:x.data.user.id,email:x.data.user.email,firstName:x.data.user.firstName||"",lastName:x.data.user.lastName||"",referralId:x.data.user.referralId,role:x.data.user.role||"USER",kycStatus:x.data.user.kycStatus,isActive:x.data.user.isActive||!0,profilePicture:x.data.user.profilePicture||null,createdAt:x.data.user.createdAt,updatedAt:x.data.user.updatedAt})}catch(e){throw e}finally{c(!1)}},m=async()=>{try{await fetch("/api/auth/logout",{method:"POST",credentials:"include"})}catch(e){console.error("Logout error:",e)}finally{i(null)}},h=async()=>{await d()};return(0,a.jsx)(r.Provider,{value:{user:t,loading:n,login:x,register:o,logout:m,refreshUser:h},children:s})},n=()=>{let e=(0,l.useContext)(r);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},4869:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},5196:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5670:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("user-check",[["path",{d:"m16 11 2 2 4-4",key:"9rsbq5"}],["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},5690:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},7550:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},9557:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>eM});var a=t(5155),l=t(2115),r=t(4105),i=t(5695),n=t(6874),c=t.n(n),d=t(6660),x=t(8505),o=t(7508),m=t(3783),h=t(7580),u=t(5525),p=t(5868),j=t(1586),N=t(1539),g=t(2713),b=t(1366),f=t(9946);let v=(0,f.A)("arrow-up-down",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]]);var w=t(3109),y=t(1264),A=t(381),C=t(7434),S=t(4416),E=t(7550),k=t(4835),T=t(4783),P=t(3861),R=t(6474),D=t(1007);let M=e=>{var s,t,i;let{children:n,activeTab:f,onTabChange:M}=e,{user:I,logout:U}=(0,r.A)(),[L,F]=(0,l.useState)(!1),[O,W]=(0,l.useState)(!1),B=(0,l.useRef)(null),Z=[{id:"dashboard",label:"Dashboard",icon:m.A},{id:"users",label:"User Management",icon:h.A},{id:"kyc",label:"KYC Review",icon:u.A},{id:"deposits",label:"Deposits",icon:p.A},{id:"withdrawals",label:"Withdrawals",icon:j.A},{id:"mining-management",label:"Mining Management",icon:N.A},{id:"account-audit",label:"Account Audit",icon:g.A},{id:"support",label:"Support Tickets",icon:b.A},{id:"binary-points",label:"Binary Points",icon:v},{id:"referral-commissions",label:"Referral Commissions",icon:w.A},{id:"email-settings",label:"Email Settings",icon:y.A},{id:"settings",label:"System Settings",icon:A.A},{id:"logs",label:"System Logs",icon:C.A}],_=async()=>{await U()};return(0,l.useEffect)(()=>{let e=e=>{B.current&&!B.current.contains(e.target)&&W(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]),(0,a.jsxs)("div",{className:"min-h-screen bg-slate-900 flex admin-panel","data-admin-panel":"true",children:[L&&(0,a.jsx)("div",{className:"fixed inset-0 z-40 bg-black bg-opacity-75 lg:hidden",onClick:()=>F(!1)}),(0,a.jsx)("aside",{className:"\n        fixed inset-y-0 left-0 z-50 w-64 bg-slate-800 shadow-xl border-r border-slate-700\n        transform transition-all duration-300 ease-in-out\n        ".concat(L?"translate-x-0":"-translate-x-full lg:translate-x-0","\n      "),children:(0,a.jsxs)("div",{className:"flex flex-col h-screen",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between h-14 px-5 border-b border-slate-700 bg-slate-800 flex-shrink-0",children:[(0,a.jsxs)(c(),{href:"/",className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)(o.MX,{className:"h-5 w-5 text-white"})}),(0,a.jsx)("span",{className:"text-lg font-bold text-white",children:"HashCoreX"})]}),(0,a.jsx)("button",{onClick:()=>F(!1),className:"lg:hidden p-1.5 rounded-lg text-slate-400",children:(0,a.jsx)(S.A,{className:"h-4 w-4"})})]}),(0,a.jsx)("div",{className:"px-3 py-3 bg-red-600 border-b border-slate-700",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-white",children:[(0,a.jsx)(u.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"text-sm font-semibold",children:"Admin Panel"})]})}),(0,a.jsx)("nav",{className:"flex-1 px-3 py-4 space-y-1 min-h-0",children:Z.map(e=>{let s=e.icon,t=f===e.id;return(0,a.jsxs)("button",{onClick:()=>{M(e.id),F(!1)},className:"\n                    w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-left group\n                    ".concat(t?"bg-blue-600 text-white shadow-md":"text-slate-300","\n                  "),children:[(0,a.jsx)(s,{className:"h-4 w-4 ".concat(t?"text-white":"text-slate-400")}),(0,a.jsx)("span",{className:"font-medium text-sm",children:e.label})]},e.id)})}),(0,a.jsx)("div",{className:"px-3 py-3 border-t border-slate-700",children:(0,a.jsxs)(c(),{href:"/dashboard",className:"w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-slate-300 group",children:[(0,a.jsx)(E.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"font-medium text-sm",children:"Back to Dashboard"})]})}),(0,a.jsx)("div",{className:"px-3 py-3 border-t border-slate-700 bg-slate-900 flex-shrink-0",children:(0,a.jsxs)("button",{onClick:_,className:"w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-slate-300 group",children:[(0,a.jsx)(k.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"font-medium text-sm",children:"Logout"})]})})]})}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col min-w-0 lg:ml-64",children:[(0,a.jsx)("header",{className:"bg-slate-800 shadow-sm border-b border-slate-700 sticky top-0 z-30",children:(0,a.jsx)("div",{className:"px-4 sm:px-6 lg:px-8 xl:px-12",children:(0,a.jsxs)(d.so,{justify:"between",align:"center",className:"h-16",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("button",{onClick:()=>F(!0),className:"lg:hidden p-2 rounded-lg text-slate-400",children:(0,a.jsx)(T.A,{className:"h-6 w-6"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-white capitalize tracking-wide drop-shadow-lg",children:(null==(s=Z.find(e=>e.id===f))?void 0:s.label)||"Admin Dashboard"}),(0,a.jsx)("p",{className:"text-sm text-slate-300 hidden sm:block font-medium",children:"Manage platform operations and user activities"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("button",{className:"relative p-2 rounded-lg text-slate-400",children:[(0,a.jsx)(P.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{className:"absolute top-1 right-1 h-2 w-2 bg-orange-500 rounded-full"})]}),(0,a.jsx)("div",{className:"px-3 py-1.5 rounded-lg text-xs font-semibold border bg-red-600 text-white border-red-500",children:"ADMIN"}),(0,a.jsxs)("div",{className:"relative",ref:B,children:[(0,a.jsxs)("button",{onClick:()=>W(!O),className:"flex items-center space-x-2 p-1 rounded-lg",children:[(0,a.jsx)(x.ph,{src:null==I?void 0:I.profilePicture,alt:"Profile",size:32,className:"rounded-lg",fallbackText:(null==I||null==(t=I.firstName)?void 0:t.charAt(0).toUpperCase())||(null==I?void 0:I.email.charAt(0).toUpperCase()),fallbackBgColor:"bg-orange-600",loading:"lazy"}),(0,a.jsx)(R.A,{className:"h-4 w-4 text-slate-400 transition-transform ".concat(O?"rotate-180":"")})]}),O&&(0,a.jsxs)("div",{className:"absolute right-0 mt-2 w-64 bg-slate-800 rounded-xl shadow-lg border border-slate-700 py-2 z-50",children:[(0,a.jsx)("div",{className:"px-4 py-3 border-b border-slate-700",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(x.ph,{src:null==I?void 0:I.profilePicture,alt:"Profile",size:40,className:"rounded-lg",fallbackText:(null==I||null==(i=I.firstName)?void 0:i.charAt(0).toUpperCase())||(null==I?void 0:I.email.charAt(0).toUpperCase()),fallbackBgColor:"bg-orange-600",loading:"lazy"}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("p",{className:"text-sm font-semibold text-white truncate",children:(null==I?void 0:I.firstName)&&(null==I?void 0:I.lastName)?"".concat(I.firstName," ").concat(I.lastName):null==I?void 0:I.email.split("@")[0]}),(0,a.jsxs)("p",{className:"text-xs text-slate-400",children:["ID: ",null==I?void 0:I.referralId]}),(0,a.jsx)("p",{className:"text-xs text-red-400 font-medium",children:"Administrator"})]})]})}),(0,a.jsxs)("div",{className:"py-1",children:[(0,a.jsxs)(c(),{href:"/dashboard",onClick:()=>W(!1),className:"w-full flex items-center space-x-3 px-4 py-2 text-sm text-slate-300",children:[(0,a.jsx)(D.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"User Dashboard"})]}),(0,a.jsx)("div",{className:"border-t border-slate-700 my-1"}),(0,a.jsxs)("button",{onClick:()=>{W(!1),_()},className:"w-full flex items-center space-x-3 px-4 py-2 text-sm text-red-400",children:[(0,a.jsx)(k.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Sign Out"})]})]})]})]})]})]})})}),(0,a.jsxs)("main",{className:"flex-1 bg-slate-900 overflow-y-auto relative",children:[(0,a.jsx)("div",{className:"fixed inset-0 bg-cover bg-center bg-no-repeat opacity-10 pointer-events-none",style:{backgroundImage:"url(/admin_background.jpg)",backgroundAttachment:"fixed",zIndex:0}}),(0,a.jsx)("div",{className:"relative z-10 px-4 sm:px-6 lg:px-8 xl:px-12 py-6",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto",children:n})})]})]})]})};var I=t(646),U=t(1243),L=t(5690),F=t(9434);let O=e=>{let{onTabChange:s}=e,[t,r]=(0,l.useState)(null),[i,n]=(0,l.useState)(!0),[c,o]=(0,l.useState)(!1),{showConfirm:m,ConfirmDialog:g}=(0,x.G_)();(0,l.useEffect)(()=>{b()},[]);let b=async()=>{try{let e=await fetch("/api/admin/stats",{credentials:"include"});if(e.ok){let s=await e.json();s.success&&r(s.data)}}catch(e){console.error("Failed to fetch admin stats:",e)}finally{n(!1)}};return i?(0,a.jsx)("div",{className:"space-y-6",children:Array.from({length:4}).map((e,s)=>(0,a.jsx)("div",{className:"animate-pulse",children:(0,a.jsx)("div",{className:"h-32 bg-slate-700 rounded-xl"})},s))}):t?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-white mb-4",children:"User Management"}),(0,a.jsxs)(d.xA,{cols:{default:1,md:2,lg:4},gap:6,children:[(0,a.jsx)(x.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(x.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-slate-400",children:"Total Users"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-blue-400",children:(0,F.ZV)(t.totalUsers,0)})]}),(0,a.jsx)("div",{className:"h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center",children:(0,a.jsx)(h.A,{className:"h-6 w-6 text-white"})})]})})}),(0,a.jsx)(x.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(x.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-slate-400",children:"Active Users"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-blue-400",children:(0,F.ZV)(t.activeUsers,0)})]}),(0,a.jsx)("div",{className:"h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center",children:(0,a.jsx)(I.A,{className:"h-6 w-6 text-white"})})]})})}),(0,a.jsx)(x.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(x.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-slate-400",children:"Pending KYC"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-orange-400",children:(0,F.ZV)(t.pendingKYC,0)})]}),(0,a.jsx)("div",{className:"h-12 w-12 bg-orange-600 rounded-full flex items-center justify-center",children:(0,a.jsx)(u.A,{className:"h-6 w-6 text-white"})})]})})}),(0,a.jsx)(x.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(x.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-slate-400",children:"Approved KYC"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-blue-400",children:(0,F.ZV)(t.approvedKYC,0)})]}),(0,a.jsx)("div",{className:"h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center",children:(0,a.jsx)(u.A,{className:"h-6 w-6 text-white"})})]})})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-white mb-4",children:"Financial Overview"}),(0,a.jsxs)(d.xA,{cols:{default:1,md:2,lg:4},gap:6,children:[(0,a.jsx)(x.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(x.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-slate-400",children:"Total Investments"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-blue-400",children:(0,F.vv)(t.totalInvestments)})]}),(0,a.jsx)("div",{className:"h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center",children:(0,a.jsx)(p.A,{className:"h-6 w-6 text-white"})})]})})}),(0,a.jsx)(x.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(x.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-slate-400",children:"Earnings Distributed"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-blue-400",children:(0,F.vv)(t.totalEarningsDistributed)})]}),(0,a.jsx)("div",{className:"h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center",children:(0,a.jsx)(w.A,{className:"h-6 w-6 text-white"})})]})})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-white mb-4",children:"Mining Operations"}),(0,a.jsxs)(d.xA,{cols:{default:1,md:2},gap:6,children:[(0,a.jsx)(x.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(x.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-slate-400",children:"Total TH/s Sold"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-orange-400",children:(0,F.jI)(t.totalTHSSold)})]}),(0,a.jsx)("div",{className:"h-12 w-12 bg-orange-600 rounded-full flex items-center justify-center",children:(0,a.jsx)(N.A,{className:"h-6 w-6 text-white"})})]})})}),(0,a.jsx)(x.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(x.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-slate-400",children:"Active TH/s"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-blue-400",children:(0,F.jI)(t.activeTHS)})]}),(0,a.jsx)("div",{className:"h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center",children:(0,a.jsx)(N.A,{className:"h-6 w-6 text-white"})})]})})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-white mb-4",children:"Withdrawal Management"}),(0,a.jsxs)(d.xA,{cols:{default:1,md:2},gap:6,children:[(0,a.jsx)(x.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(x.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-slate-400",children:"Pending Withdrawals"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-red-400",children:(0,F.vv)(t.pendingWithdrawalsAmount)}),(0,a.jsxs)("p",{className:"text-xs text-slate-500",children:[(0,F.ZV)(t.pendingWithdrawals,0)," requests"]})]}),(0,a.jsx)("div",{className:"h-12 w-12 bg-red-600 rounded-full flex items-center justify-center",children:(0,a.jsx)(U.A,{className:"h-6 w-6 text-white"})})]})})}),(0,a.jsx)(x.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(x.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-slate-400",children:"Completed Withdrawals"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-green-400",children:(0,F.vv)(t.completedWithdrawalsAmount)})]}),(0,a.jsx)("div",{className:"h-12 w-12 bg-green-600 rounded-full flex items-center justify-center",children:(0,a.jsx)(I.A,{className:"h-6 w-6 text-white"})})]})})})]})]}),(0,a.jsxs)(x.Zp,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsx)(x.aR,{children:(0,a.jsx)(x.ZB,{className:"text-white",children:"Quick Actions"})}),(0,a.jsx)(x.Wu,{children:(0,a.jsxs)(d.xA,{cols:{default:1,md:3},gap:4,children:[(0,a.jsx)("div",{className:"p-4 border border-slate-600 rounded-lg cursor-pointer",onClick:()=>null==s?void 0:s("kyc"),children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(u.A,{className:"h-8 w-8 text-orange-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-white",children:"Review KYC"}),(0,a.jsxs)("p",{className:"text-sm text-slate-400",children:[t.pendingKYC," pending reviews"]})]})]})}),(0,a.jsx)("div",{className:"p-4 border border-slate-600 rounded-lg cursor-pointer",onClick:()=>null==s?void 0:s("withdrawals"),children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(j.A,{className:"h-8 w-8 text-red-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-white",children:"Process Withdrawals"}),(0,a.jsxs)("p",{className:"text-sm text-slate-400",children:[t.pendingWithdrawals," pending"]})]})]})}),(0,a.jsx)("div",{className:"p-4 border border-slate-600 rounded-lg cursor-pointer",onClick:()=>null==s?void 0:s("users"),children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(h.A,{className:"h-8 w-8 text-blue-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-white",children:"Manage Users"}),(0,a.jsxs)("p",{className:"text-sm text-slate-400",children:[t.totalUsers," total users"]})]})]})}),(0,a.jsx)("div",{className:"p-4 border border-slate-600 rounded-lg cursor-pointer ".concat(c?"opacity-50 cursor-not-allowed":""),onClick:c?void 0:()=>{m({title:"Manual Binary Matching",message:"Are you sure you want to manually trigger binary matching? This will process all users with binary points and distribute earnings.",variant:"warning",confirmText:"Process Matching",darkMode:!0,onConfirm:async()=>{o(!0);try{let e=await fetch("/api/admin/binary-matching/manual",{method:"POST",credentials:"include"}),s=await e.json();if(s.success){let e=s.data.matchingResults||[],t=s.data.usersProcessed||0,a=s.data.totalPayouts||0,l="Binary matching completed successfully!\n\n";l+="\uD83D\uDCCA SUMMARY:\n",l+="• Users processed: ".concat(t,"\n"),l+="• Total payouts: $".concat(a.toFixed(2),"\n"),l+="• Total matched points: ".concat(e.reduce((e,s)=>e+s.matchedPoints,0).toFixed(2),"\n\n"),e.length>0&&(l+="\uD83D\uDCB0 DETAILED RESULTS:\n",e.slice(0,10).forEach((e,s)=>{l+="".concat(s+1,". User ").concat(e.userId.substring(0,8),"...\n"),l+="   • Matched: ".concat(e.matchedPoints.toFixed(2)," points\n"),l+="   • Payout: $".concat(e.payout.toFixed(2),"\n"),l+="   • Remaining: L:".concat(e.remainingLeftPoints.toFixed(2)," | R:").concat(e.remainingRightPoints.toFixed(2),"\n\n")}),e.length>10&&(l+="... and ".concat(e.length-10," more users\n\n"))),l+="✅ All earnings have been credited to user wallets.",m({title:"Binary Matching Results",message:l,variant:"success",confirmText:"OK",cancelText:"",darkMode:!0,onConfirm:()=>{}}),b()}else m({title:"Error",message:"Error: ".concat(s.error),variant:"danger",confirmText:"OK",cancelText:"",darkMode:!0,onConfirm:()=>{}})}catch(e){console.error("Manual binary matching error:",e),m({title:"Error",message:"Failed to process binary matching. Please try again.",variant:"danger",confirmText:"OK",cancelText:"",darkMode:!0,onConfirm:()=>{}})}finally{o(!1)}}})},children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(L.A,{className:"h-8 w-8 text-green-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-white",children:c?"Processing...":"Manual Binary Matching"}),(0,a.jsx)("p",{className:"text-sm text-slate-400",children:c?"Please wait...":"Trigger binary matching manually"})]})]})})]})})]}),(0,a.jsx)(g,{})]}):(0,a.jsx)(x.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(x.Wu,{className:"text-center py-8",children:(0,a.jsx)("p",{className:"text-slate-400",children:"Failed to load admin statistics"})})})},W=(0,f.A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]),B=(0,f.A)("shield-check",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]),Z=(0,f.A)("shield-x",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m14.5 9.5-5 5",key:"17q4r4"}],["path",{d:"m9.5 9.5 5 5",key:"18nt4w"}]]);var _=t(7924),H=t(9785);let $=(0,f.A)("ellipsis-vertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]]),z=(0,f.A)("user-x",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"17",x2:"22",y1:"8",y2:"13",key:"3nzzx3"}],["line",{x1:"22",x2:"17",y1:"8",y2:"13",key:"1swrse"}]]);var V=t(5670),G=t(4616);let Y=(0,f.A)("minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]);var K=t(2657);let J=()=>{let[e,s]=(0,l.useState)([]),[t,r]=(0,l.useState)(!0),[i,n]=(0,l.useState)(""),[c,d]=(0,l.useState)(""),[o,m]=(0,l.useState)("all"),[j,N]=(0,l.useState)(1),[g,b]=(0,l.useState)(1),[f,w]=(0,l.useState)(null),[y,A]=(0,l.useState)(null),[C,S]=(0,l.useState)("desc"),[E,k]=(0,l.useState)(!1),[T,P]=(0,l.useState)({userId:"",userName:"",userEmail:"",amount:"",type:"CREDIT",reason:"",description:""}),[D,M]=(0,l.useState)(!1),[I,U]=(0,l.useState)(""),[L,O]=(0,l.useState)(""),[J,q]=(0,l.useState)(!1),[X,Q]=(0,l.useState)(null),[ee,es]=(0,l.useState)(!1);(0,l.useEffect)(()=>{let e=setTimeout(()=>{d(i)},300);return()=>clearTimeout(e)},[i]),(0,l.useEffect)(()=>{c!==i&&N(1)},[c]),(0,l.useEffect)(()=>{el()},[j,c,o,y,C]),(0,l.useEffect)(()=>{let e=e=>{f&&!e.target.closest(".relative")&&w(null)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[f]);let et=e=>{y===e?S("asc"===C?"desc":"asc"):(A(e),S("desc")),N(1)},ea=e=>y!==e?(0,a.jsx)(v,{className:"h-4 w-4 text-slate-400"}):"asc"===C?(0,a.jsx)(W,{className:"h-4 w-4 text-blue-400"}):(0,a.jsx)(R.A,{className:"h-4 w-4 text-blue-400"}),el=async()=>{try{r(!0);let e=new URLSearchParams({page:j.toString(),limit:"20",search:c,status:o});y&&(e.append("sortBy",y),e.append("sortOrder",C));let t=await fetch("/api/admin/users?".concat(e),{credentials:"include"});if(t.ok){let e=await t.json();e.success&&(s(e.data.users),b(e.data.totalPages))}}catch(e){console.error("Failed to fetch users:",e)}finally{r(!1)}},er=async(e,s)=>{try{(await fetch("/api/admin/users/action",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({userId:e,action:s})})).ok&&el()}catch(e){console.error("Failed to perform user action:",e)}},ei=(e,s)=>{P({userId:e.id,userName:"".concat(e.firstName," ").concat(e.lastName),userEmail:e.email,amount:"",type:s,reason:"",description:""}),U(""),O(""),k(!0)},en=async()=>{if(!T.amount||!T.reason)return void U("Amount and reason are required");try{M(!0),U(""),O("");let e=await fetch("/api/admin/wallet/adjust",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({userId:T.userId,amount:parseFloat(T.amount),type:T.type,reason:T.reason,description:T.description})}),s=await e.json();s.success?(O("Wallet ".concat(T.type.toLowerCase()," completed successfully")),setTimeout(()=>{k(!1),el()},2e3)):U(s.error||"Failed to adjust wallet balance")}catch(e){U("An error occurred while adjusting wallet balance")}finally{M(!1)}},ec=async e=>{try{es(!0);let s=await fetch("/api/admin/users/".concat(e,"/details"),{credentials:"include"});if(s.ok){let e=await s.json();e.success&&(Q(e.data),q(!0))}}catch(e){console.error("Failed to fetch user details:",e)}finally{es(!1)}},ed=e=>{switch(e){case"APPROVED":return(0,a.jsx)(B,{className:"h-4 w-4 text-green-400"});case"REJECTED":return(0,a.jsx)(Z,{className:"h-4 w-4 text-red-400"});case"PENDING":return(0,a.jsx)(u.A,{className:"h-4 w-4 text-yellow-400"});default:return(0,a.jsx)(u.A,{className:"h-4 w-4 text-gray-400"})}},ex=e=>(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(e?"bg-blue-600 text-white":"bg-red-600 text-white"),children:e?"Active":"Inactive"}),eo=e=>(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat("ADMIN"===e?"bg-red-600 text-white":"bg-blue-600 text-white"),children:e});return t?(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-8 bg-slate-700 rounded w-1/4 mb-4"}),(0,a.jsx)("div",{className:"h-64 bg-slate-700 rounded"})]})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(x.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(x.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(_.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(x.pd,{placeholder:"Search users by email or name...",value:i,onChange:e=>n(e.target.value),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400"})]})}),(0,a.jsx)("div",{className:"sm:w-48",children:(0,a.jsxs)("select",{value:o,onChange:e=>m(e.target.value),className:"w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"all",children:"All Users"}),(0,a.jsx)("option",{value:"active",children:"Active"}),(0,a.jsx)("option",{value:"inactive",children:"Inactive"}),(0,a.jsx)("option",{value:"pending_kyc",children:"Pending KYC"})]})})]})})}),(0,a.jsxs)(x.Zp,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsx)(x.aR,{children:(0,a.jsxs)(x.ZB,{className:"flex items-center gap-2 text-white",children:[(0,a.jsx)(h.A,{className:"h-5 w-5"}),"Users (",e.length,")"]})}),(0,a.jsxs)(x.Wu,{children:[(0,a.jsx)("div",{className:"hidden lg:block overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"border-b border-slate-600",children:[(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-white",children:"User"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-white",children:"Role"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-white",children:"KYC Status"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-white",children:"Status"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-white cursor-pointer hover:bg-slate-700 select-none",onClick:()=>et("activeTHS"),children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:["Mining Power",ea("activeTHS")]})}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-white cursor-pointer hover:bg-slate-700 select-none",onClick:()=>et("walletBalance"),children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:["Wallet Balance",ea("walletBalance")]})}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-white cursor-pointer hover:bg-slate-700 select-none",onClick:()=>et("createdAt"),children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:["Joined",ea("createdAt")]})}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-white",children:"Actions"})]})}),(0,a.jsx)("tbody",{children:e.map(e=>{var s;return(0,a.jsxs)("tr",{className:"border-b border-slate-700 hover:bg-slate-700",children:[(0,a.jsx)("td",{className:"py-4 px-4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"font-medium text-white",children:[e.firstName," ",e.lastName]}),(0,a.jsx)("div",{className:"text-sm text-slate-400",children:e.email}),(0,a.jsxs)("div",{className:"text-xs text-slate-500",children:["ID: ",e.referralId]})]})}),(0,a.jsx)("td",{className:"py-4 px-4",children:eo(e.role)}),(0,a.jsx)("td",{className:"py-4 px-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[ed(e.kycStatus),(0,a.jsx)("span",{className:"text-sm text-slate-300",children:"NOT_SUBMITTED"===e.kycStatus?"Not Submitted":e.kycStatus})]})}),(0,a.jsx)("td",{className:"py-4 px-4",children:ex(e.isActive)}),(0,a.jsx)("td",{className:"py-4 px-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"h-4 w-4 bg-orange-500 rounded-sm flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-xs font-bold text-white",children:"TH"})}),(0,a.jsxs)("span",{className:"text-sm font-medium text-orange-400",children:[e.activeTHS.toFixed(2)," TH/s"]})]})}),(0,a.jsx)("td",{className:"py-4 px-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(H.A,{className:"h-4 w-4 text-green-400"}),(0,a.jsx)("span",{className:"text-sm font-medium text-green-400",children:(0,F.vv)((null==(s=e.walletBalance)?void 0:s.availableBalance)||0)})]})}),(0,a.jsx)("td",{className:"py-4 px-4 text-sm text-slate-400",children:(0,F.r6)(e.createdAt)}),(0,a.jsx)("td",{className:"py-4 px-4",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)(x.$n,{variant:"outline",size:"sm",onClick:()=>w(f===e.id?null:e.id),className:"border-slate-600 text-slate-300 flex items-center gap-2",children:[(0,a.jsx)($,{className:"h-4 w-4"}),(0,a.jsx)(R.A,{className:"h-3 w-3"})]}),f===e.id&&(0,a.jsx)("div",{className:"absolute right-0 top-full mt-1 w-48 bg-slate-800 border border-slate-600 rounded-md shadow-lg z-10",children:(0,a.jsxs)("div",{className:"py-1",children:[(0,a.jsxs)("button",{onClick:()=>{er(e.id,e.isActive?"deactivate":"activate"),w(null)},className:"flex items-center gap-2 w-full px-4 py-2 text-sm text-slate-300 hover:bg-slate-700",children:[e.isActive?(0,a.jsx)(z,{className:"h-4 w-4"}):(0,a.jsx)(V.A,{className:"h-4 w-4"}),e.isActive?"Deactivate User":"Activate User"]}),"USER"===e.role&&(0,a.jsxs)("button",{onClick:()=>{er(e.id,"promote"),w(null)},className:"flex items-center gap-2 w-full px-4 py-2 text-sm text-slate-300 hover:bg-slate-700",children:[(0,a.jsx)(u.A,{className:"h-4 w-4"}),"Promote to Admin"]}),(0,a.jsx)("div",{className:"border-t border-slate-600 my-1"}),(0,a.jsxs)("button",{onClick:()=>{ei(e,"CREDIT"),w(null)},className:"flex items-center gap-2 w-full px-4 py-2 text-sm text-green-300 hover:bg-slate-700",children:[(0,a.jsx)(G.A,{className:"h-4 w-4"}),"Credit Wallet"]}),(0,a.jsxs)("button",{onClick:()=>{ei(e,"DEBIT"),w(null)},className:"flex items-center gap-2 w-full px-4 py-2 text-sm text-red-300 hover:bg-slate-700",children:[(0,a.jsx)(Y,{className:"h-4 w-4"}),"Debit Wallet"]}),(0,a.jsx)("div",{className:"border-t border-slate-600 my-1"}),(0,a.jsxs)("button",{onClick:()=>{ec(e.id),w(null)},className:"flex items-center gap-2 w-full px-4 py-2 text-sm text-slate-300 hover:bg-slate-700",disabled:ee,children:[(0,a.jsx)(K.A,{className:"h-4 w-4"}),ee?"Loading...":"View Details"]})]})})]})})]},e.id)})})]})}),(0,a.jsx)("div",{className:"lg:hidden space-y-4",children:e.map(e=>{var s;return(0,a.jsxs)("div",{className:"bg-slate-700 rounded-lg p-4 border border-slate-600",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-semibold text-sm",children:e.firstName.charAt(0).toUpperCase()})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"font-medium text-white",children:[e.firstName," ",e.lastName]}),(0,a.jsx)("div",{className:"text-sm text-slate-400",children:e.email}),(0,a.jsxs)("div",{className:"text-xs text-slate-500",children:["ID: ",e.referralId]})]})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(x.$n,{variant:"outline",size:"sm",onClick:()=>w(f===e.id?null:e.id),className:"border-slate-600 text-slate-300",children:(0,a.jsx)($,{className:"h-4 w-4"})}),f===e.id&&(0,a.jsx)("div",{className:"absolute right-0 top-full mt-1 w-48 bg-slate-800 border border-slate-600 rounded-md shadow-lg z-10",children:(0,a.jsxs)("div",{className:"py-1",children:[(0,a.jsxs)("button",{onClick:()=>{er(e.id,e.isActive?"deactivate":"activate"),w(null)},className:"flex items-center gap-2 w-full px-4 py-2 text-sm text-slate-300 hover:bg-slate-700",children:[e.isActive?(0,a.jsx)(z,{className:"h-4 w-4"}):(0,a.jsx)(V.A,{className:"h-4 w-4"}),e.isActive?"Deactivate User":"Activate User"]}),"USER"===e.role&&(0,a.jsxs)("button",{onClick:()=>{er(e.id,"promote"),w(null)},className:"flex items-center gap-2 w-full px-4 py-2 text-sm text-slate-300 hover:bg-slate-700",children:[(0,a.jsx)(u.A,{className:"h-4 w-4"}),"Promote to Admin"]}),(0,a.jsx)("div",{className:"border-t border-slate-600 my-1"}),(0,a.jsxs)("button",{onClick:()=>{ei(e,"CREDIT"),w(null)},className:"flex items-center gap-2 w-full px-4 py-2 text-sm text-green-300 hover:bg-slate-700",children:[(0,a.jsx)(G.A,{className:"h-4 w-4"}),"Credit Wallet"]}),(0,a.jsxs)("button",{onClick:()=>{ei(e,"DEBIT"),w(null)},className:"flex items-center gap-2 w-full px-4 py-2 text-sm text-red-300 hover:bg-slate-700",children:[(0,a.jsx)(Y,{className:"h-4 w-4"}),"Debit Wallet"]}),(0,a.jsx)("div",{className:"border-t border-slate-600 my-1"}),(0,a.jsxs)("button",{onClick:()=>{ec(e.id),w(null)},className:"flex items-center gap-2 w-full px-4 py-2 text-sm text-slate-300 hover:bg-slate-700",disabled:ee,children:[(0,a.jsx)(K.A,{className:"h-4 w-4"}),ee?"Loading...":"View Details"]})]})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-slate-400 text-xs mb-1",children:"Role"}),eo(e.role)]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-slate-400 text-xs mb-1",children:"Status"}),ex(e.isActive)]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-slate-400 text-xs mb-1",children:"KYC Status"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[ed(e.kycStatus),(0,a.jsx)("span",{className:"text-slate-300 text-xs",children:"NOT_SUBMITTED"===e.kycStatus?"Not Submitted":e.kycStatus})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-slate-400 text-xs mb-1",children:"Mining Power"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"h-3 w-3 bg-orange-500 rounded-sm flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-xs font-bold text-white",children:"T"})}),(0,a.jsxs)("span",{className:"text-orange-400 font-medium text-xs",children:[e.activeTHS.toFixed(2)," TH/s"]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-slate-400 text-xs mb-1",children:"Wallet Balance"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(H.A,{className:"h-3 w-3 text-green-400"}),(0,a.jsx)("span",{className:"text-green-400 font-medium text-xs",children:(0,F.vv)((null==(s=e.walletBalance)?void 0:s.availableBalance)||0)})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-slate-400 text-xs mb-1",children:"Joined"}),(0,a.jsx)("span",{className:"text-slate-300 text-xs",children:(0,F.r6)(e.createdAt)})]})]})]},e.id)})}),g>1&&(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row items-center justify-between mt-6 gap-4",children:[(0,a.jsxs)("div",{className:"text-sm text-slate-400",children:["Page ",j," of ",g]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(x.$n,{variant:"outline",size:"sm",onClick:()=>N(e=>Math.max(1,e-1)),disabled:1===j,className:"border-slate-600 text-slate-300 hover:bg-slate-700 px-3 py-2",children:[(0,a.jsx)("span",{className:"hidden sm:inline",children:"Previous"}),(0,a.jsx)("span",{className:"sm:hidden",children:"Prev"})]}),(0,a.jsxs)(x.$n,{variant:"outline",size:"sm",onClick:()=>N(e=>Math.min(g,e+1)),disabled:j===g,className:"border-slate-600 text-slate-300 hover:bg-slate-700 px-3 py-2",children:[(0,a.jsx)("span",{className:"hidden sm:inline",children:"Next"}),(0,a.jsx)("span",{className:"sm:hidden",children:"Next"})]})]})]})]})]}),E&&(0,a.jsx)(x.aF,{isOpen:E,onClose:()=>k(!1),title:"".concat("CREDIT"===T.type?"Credit":"Debit"," User Wallet"),darkMode:!0,children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"bg-slate-700 p-4 rounded-lg",children:[(0,a.jsx)("h3",{className:"font-medium text-white mb-2",children:"User Information"}),(0,a.jsxs)("p",{className:"text-slate-300 text-sm",children:["Name: ",T.userName]}),(0,a.jsxs)("p",{className:"text-slate-300 text-sm",children:["Email: ",T.userEmail]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Amount (USDT)"}),(0,a.jsx)(x.pd,{type:"number",step:"0.01",min:"0",value:T.amount,onChange:e=>P(s=>({...s,amount:e.target.value})),placeholder:"Enter amount",className:"bg-slate-700 border-slate-600 text-white",disabled:D})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Reason *"}),(0,a.jsxs)("select",{value:T.reason,onChange:e=>P(s=>({...s,reason:e.target.value})),className:"w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white rounded-lg",disabled:D,children:[(0,a.jsx)("option",{value:"",children:"Select reason"}),(0,a.jsx)("option",{value:"Manual Adjustment",children:"Manual Adjustment"}),(0,a.jsx)("option",{value:"Bonus Payment",children:"Bonus Payment"}),(0,a.jsx)("option",{value:"Refund",children:"Refund"}),(0,a.jsx)("option",{value:"Correction",children:"Balance Correction"}),(0,a.jsx)("option",{value:"Penalty",children:"Penalty"}),(0,a.jsx)("option",{value:"Promotion",children:"Promotional Credit"}),(0,a.jsx)("option",{value:"Other",children:"Other"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Description (Optional)"}),(0,a.jsx)("textarea",{value:T.description,onChange:e=>P(s=>({...s,description:e.target.value})),placeholder:"Additional details about this adjustment...",rows:3,className:"w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white rounded-lg resize-none",disabled:D})]}),I&&(0,a.jsx)("div",{className:"bg-red-900/20 border border-red-500 rounded-lg p-3",children:(0,a.jsx)("p",{className:"text-red-400 text-sm",children:I})}),L&&(0,a.jsx)("div",{className:"bg-green-900/20 border border-green-500 rounded-lg p-3",children:(0,a.jsx)("p",{className:"text-green-400 text-sm",children:L})}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,a.jsx)(x.$n,{variant:"outline",onClick:()=>k(!1),disabled:D,className:"border-slate-600 text-slate-300 hover:bg-slate-700",children:"Cancel"}),(0,a.jsx)(x.$n,{onClick:en,disabled:D||!T.amount||!T.reason,className:"".concat("CREDIT"===T.type?"bg-green-600 hover:bg-green-700":"bg-red-600 hover:bg-red-700"," text-white"),children:D?"Processing...":"".concat("CREDIT"===T.type?"Credit":"Debit"," Wallet")})]})]})}),J&&X&&(0,a.jsx)(x.aF,{isOpen:J,onClose:()=>q(!1),title:"User Details",darkMode:!0,size:"xl",children:(0,a.jsxs)("div",{className:"space-y-6 max-h-[80vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"bg-slate-700 p-4 rounded-lg",children:[(0,a.jsxs)("h3",{className:"font-medium text-white mb-3 flex items-center gap-2",children:[(0,a.jsx)(h.A,{className:"h-4 w-4"}),"Basic Information"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"Full Name"}),(0,a.jsxs)("p",{className:"text-white",children:[X.firstName," ",X.lastName]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"Email"}),(0,a.jsx)("p",{className:"text-white",children:X.email})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"Referral ID"}),(0,a.jsx)("p",{className:"text-white font-mono",children:X.referralId})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"Role"}),(0,a.jsx)("p",{className:"text-white",children:eo(X.role)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"Status"}),(0,a.jsx)("p",{className:"text-white",children:ex(X.isActive)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"KYC Status"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[ed(X.kycStatus),(0,a.jsx)("span",{className:"text-white",children:"NOT_SUBMITTED"===X.kycStatus?"Not Submitted":X.kycStatus})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"Joined Date"}),(0,a.jsx)("p",{className:"text-white",children:(0,F.r6)(X.createdAt)})]}),X.referrerId&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"Referred By"}),(0,a.jsx)("p",{className:"text-white font-mono",children:X.referrerId})]})]})]}),(0,a.jsxs)("div",{className:"bg-slate-700 p-4 rounded-lg",children:[(0,a.jsxs)("h3",{className:"font-medium text-white mb-3 flex items-center gap-2",children:[(0,a.jsx)(H.A,{className:"h-4 w-4"}),"Wallet Information"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"Available Balance"}),(0,a.jsx)("p",{className:"text-white text-lg font-semibold",children:(0,F.vv)(X.walletBalance.availableBalance)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"Total Earnings"}),(0,a.jsx)("p",{className:"text-green-400 text-lg font-semibold",children:(0,F.vv)(X.walletBalance.totalEarnings)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"Total Deposits"}),(0,a.jsx)("p",{className:"text-blue-400",children:(0,F.vv)(X.walletBalance.totalDeposits)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"Total Withdrawals"}),(0,a.jsx)("p",{className:"text-red-400",children:(0,F.vv)(X.walletBalance.totalWithdrawals)})]})]})]}),(0,a.jsxs)("div",{className:"bg-slate-700 p-4 rounded-lg",children:[(0,a.jsxs)("h3",{className:"font-medium text-white mb-3 flex items-center gap-2",children:[(0,a.jsx)(p.A,{className:"h-4 w-4"}),"Mining Units"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"Total Units"}),(0,a.jsx)("p",{className:"text-white text-lg font-semibold",children:X.miningUnits.totalUnits})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"Total Investment"}),(0,a.jsx)("p",{className:"text-white text-lg font-semibold",children:(0,F.vv)(X.miningUnits.totalInvestment)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"Total TH/s"}),(0,a.jsxs)("p",{className:"text-blue-400",children:[X.miningUnits.totalTHS.toFixed(2)," TH/s"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"Active TH/s"}),(0,a.jsxs)("p",{className:"text-green-400",children:[X.miningUnits.activeTHS.toFixed(2)," TH/s"]})]})]})]}),(0,a.jsxs)("div",{className:"bg-slate-700 p-4 rounded-lg",children:[(0,a.jsxs)("h3",{className:"font-medium text-white mb-3 flex items-center gap-2",children:[(0,a.jsx)(u.A,{className:"h-4 w-4"}),"Binary Points"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"Left Points"}),(0,a.jsx)("p",{className:"text-white text-lg font-semibold",children:X.binaryPoints.leftPoints})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"Right Points"}),(0,a.jsx)("p",{className:"text-white text-lg font-semibold",children:X.binaryPoints.rightPoints})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"Total Matched"}),(0,a.jsx)("p",{className:"text-green-400",children:X.binaryPoints.totalMatched})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"Last Match Date"}),(0,a.jsx)("p",{className:"text-white",children:X.binaryPoints.lastMatchDate?(0,F.r6)(X.binaryPoints.lastMatchDate):"Never"})]})]})]}),(0,a.jsxs)("div",{className:"bg-slate-700 p-4 rounded-lg",children:[(0,a.jsxs)("h3",{className:"font-medium text-white mb-3 flex items-center gap-2",children:[(0,a.jsx)(h.A,{className:"h-4 w-4"}),"Referral Statistics"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"Direct Referrals"}),(0,a.jsx)("p",{className:"text-white text-lg font-semibold",children:X.referralStats.directReferrals})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"Total Team"}),(0,a.jsx)("p",{className:"text-white text-lg font-semibold",children:X.referralStats.totalTeam})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"Left Team"}),(0,a.jsx)("p",{className:"text-blue-400",children:X.referralStats.leftTeam})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"Right Team"}),(0,a.jsx)("p",{className:"text-green-400",children:X.referralStats.rightTeam})]})]})]}),X.recentActivity.length>0&&(0,a.jsxs)("div",{className:"bg-slate-700 p-4 rounded-lg",children:[(0,a.jsxs)("h3",{className:"font-medium text-white mb-3 flex items-center gap-2",children:[(0,a.jsx)(K.A,{className:"h-4 w-4"}),"Recent Activity"]}),(0,a.jsx)("div",{className:"space-y-2 max-h-40 overflow-y-auto",children:X.recentActivity.map((e,s)=>(0,a.jsxs)("div",{className:"flex justify-between items-center p-2 bg-slate-600 rounded",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-white text-sm",children:e.description}),(0,a.jsx)("p",{className:"text-slate-400 text-xs",children:(0,F.r6)(e.date)})]}),e.amount&&(0,a.jsx)("p",{className:"text-green-400 font-semibold",children:(0,F.vv)(e.amount)})]},s))})]})]})})]})};var q=t(3904),X=t(9074);let Q=(0,f.A)("zoom-in",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"11",x2:"11",y1:"8",y2:"14",key:"1vmskp"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]);var ee=t(5196);let es=()=>{let[e,s]=(0,l.useState)([]),[t,r]=(0,l.useState)([]),[i,n]=(0,l.useState)(!0),[c,d]=(0,l.useState)(null),[o,m]=(0,l.useState)(null),[h,p]=(0,l.useState)(null),[j,N]=(0,l.useState)(""),[g,b]=(0,l.useState)(!1),[f,v]=(0,l.useState)(!1),[w,y]=(0,l.useState)(""),[A,E]=(0,l.useState)("ALL");(0,l.useEffect)(()=>{k()},[]),(0,l.useEffect)(()=>{T()},[e,w,A]);let k=async()=>{try{n(!0);let e=await fetch("/api/admin/kyc/all",{credentials:"include"});if(e.ok){let t=await e.json();t.success&&s(t.data)}}catch(e){console.error("Failed to fetch KYC data:",e)}finally{n(!1)}},T=()=>{let s=e;w&&(s=s.filter(e=>e.user.firstName.toLowerCase().includes(w.toLowerCase())||e.user.lastName.toLowerCase().includes(w.toLowerCase())||e.user.email.toLowerCase().includes(w.toLowerCase())||e.user.referralId.toLowerCase().includes(w.toLowerCase()))),"ALL"!==A&&(s=s.filter(e=>e.status===A)),r(s)},P=async(e,s,t)=>{try{b(!0),(await fetch("/api/admin/kyc/review",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({userId:e,action:s.toUpperCase(),rejectionReason:t})})).ok&&(k(),d(null),m(null),p(null),N(""))}catch(e){console.error("Failed to review KYC document:",e)}finally{b(!1)}},R=e=>(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat({PENDING:"bg-yellow-900 text-yellow-300 border border-yellow-700",APPROVED:"bg-green-900 text-green-300 border border-green-700",REJECTED:"bg-red-900 text-red-300 border border-red-700"}[e]),children:e});return i?(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-8 bg-slate-700 rounded w-1/4 mb-4"}),(0,a.jsx)("div",{className:"h-64 bg-slate-700 rounded"})]})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-end gap-4",children:[(0,a.jsxs)(x.$n,{variant:"outline",size:"sm",onClick:k,disabled:i,className:"border-slate-600 text-slate-300",children:[(0,a.jsx)(q.A,{className:"h-4 w-4 mr-2 ".concat(i?"animate-spin":"")}),"Refresh"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm text-slate-400",children:[(0,a.jsx)(U.A,{className:"h-4 w-4 text-orange-400"}),t.filter(e=>"PENDING"===e.status).length," pending reviews"]})]}),(0,a.jsx)(x.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsxs)(x.Wu,{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(_.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400"}),(0,a.jsx)(x.pd,{placeholder:"Search by name, email, or user ID...",value:w,onChange:e=>y(e.target.value),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400"})]})}),(0,a.jsx)("div",{className:"md:w-48",children:(0,a.jsxs)("select",{value:A,onChange:e=>E(e.target.value),className:"w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"ALL",children:"All Status"}),(0,a.jsx)("option",{value:"PENDING",children:"Pending"}),(0,a.jsx)("option",{value:"APPROVED",children:"Approved"}),(0,a.jsx)("option",{value:"REJECTED",children:"Rejected"})]})})]}),(0,a.jsxs)("div",{className:"mt-4 flex items-center gap-4 text-sm text-slate-400",children:[(0,a.jsxs)("span",{children:["Showing ",t.length," of ",e.length," users"]}),(0,a.jsx)("span",{children:"•"}),(0,a.jsxs)("span",{children:[t.filter(e=>"PENDING"===e.status).length," pending"]}),(0,a.jsx)("span",{children:"•"}),(0,a.jsxs)("span",{children:[t.filter(e=>"APPROVED"===e.status).length," approved"]}),(0,a.jsx)("span",{children:"•"}),(0,a.jsxs)("span",{children:[t.filter(e=>"REJECTED"===e.status).length," rejected"]})]})]})}),(0,a.jsx)("div",{className:"grid gap-6",children:0===t.length?(0,a.jsx)(x.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsxs)(x.Wu,{className:"p-12 text-center",children:[(0,a.jsx)(u.A,{className:"h-12 w-12 text-slate-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-white mb-2",children:0===e.length?"No KYC Submissions":"No Results Found"}),(0,a.jsx)("p",{className:"text-slate-400",children:0===e.length?"No users have submitted KYC documents yet.":"Try adjusting your search or filter criteria."})]})}):t.map(e=>(0,a.jsx)(x.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(x.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(D.A,{className:"h-5 w-5 text-slate-400"}),(0,a.jsxs)("span",{className:"font-medium text-white",children:[e.user.firstName," ",e.user.lastName]})]}),R(e.status)]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-slate-400 mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-slate-300",children:"Email:"})," ",e.user.email]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-slate-300",children:"User ID:"})," ",e.user.referralId]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(X.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"font-medium text-slate-300",children:"Submitted:"})," ",(0,F.Yq)(e.submittedAt)]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm text-slate-400 mb-4",children:[(0,a.jsx)(C.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"font-medium text-slate-300",children:"Documents:"})," ",e.documents.length," uploaded"]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 mb-4",children:e.documents.map(e=>(0,a.jsxs)("div",{className:"bg-slate-700 rounded-lg p-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-xs font-medium text-slate-300",children:"SELFIE"===e.documentType?"Selfie":"".concat(e.idType," - ").concat(e.documentSide)}),R(e.status)]}),(0,a.jsx)("div",{className:"flex items-center gap-2",children:(0,a.jsxs)(x.$n,{variant:"outline",size:"sm",onClick:()=>{m(e),v(!0)},className:"border-slate-600 text-slate-300 hover:bg-slate-600 hover:text-white text-xs px-2 py-1",children:[(0,a.jsx)(Q,{className:"h-3 w-3 mr-1"}),"View"]})}),e.rejectionReason&&(0,a.jsx)("p",{className:"text-xs text-red-400 mt-2 truncate",title:e.rejectionReason,children:e.rejectionReason})]},e.id))})]}),(0,a.jsx)("div",{className:"flex flex-col gap-2 ml-4",children:"PENDING"===e.status&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(x.$n,{variant:"outline",size:"sm",onClick:()=>{d(e),p("approve")},className:"border-green-600 text-green-400 hover:text-green-300 hover:bg-green-900/20",children:[(0,a.jsx)(ee.A,{className:"h-4 w-4 mr-1"}),"Approve"]}),(0,a.jsxs)(x.$n,{variant:"outline",size:"sm",onClick:()=>{d(e),p("reject")},className:"border-red-600 text-red-400 hover:text-red-300 hover:bg-red-900/20",children:[(0,a.jsx)(S.A,{className:"h-4 w-4 mr-1"}),"Reject"]})]})})]})})},e.userId))}),f&&o&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center p-4 z-50",children:(0,a.jsxs)("div",{className:"bg-slate-800 border border-slate-700 rounded-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-slate-700",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-white",children:["Document Viewer - ","SELFIE"===o.documentType?"Selfie":"".concat(o.idType," ").concat(o.documentSide)]}),(0,a.jsx)(x.$n,{variant:"outline",size:"sm",onClick:()=>{v(!1),m(null)},className:"border-slate-600 text-slate-300",children:(0,a.jsx)(S.A,{className:"h-4 w-4"})})]}),(0,a.jsx)("div",{className:"p-4 max-h-[calc(90vh-120px)] overflow-auto",children:(0,a.jsx)("img",{src:o.documentUrl,alt:"KYC Document",className:"w-full h-auto max-h-[70vh] object-contain rounded-lg"})})]})}),c&&h&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center p-4 z-50",children:(0,a.jsxs)("div",{className:"bg-slate-800 border border-slate-700 rounded-xl max-w-md w-full p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-4 text-white",children:"approve"===h?"Approve KYC Submission":"Reject KYC Submission"}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsxs)("p",{className:"text-slate-400 mb-2",children:["User: ",(0,a.jsxs)("span",{className:"font-medium text-white",children:[c.user.firstName," ",c.user.lastName]})]}),(0,a.jsxs)("p",{className:"text-slate-400 mb-2",children:["Email: ",(0,a.jsx)("span",{className:"font-medium text-white",children:c.user.email})]}),(0,a.jsxs)("p",{className:"text-slate-400",children:["Documents: ",(0,a.jsxs)("span",{className:"font-medium text-white",children:[c.documents.length," uploaded"]})]})]}),"reject"===h&&(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Rejection Reason *"}),(0,a.jsx)("textarea",{value:j,onChange:e=>N(e.target.value),className:"w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white placeholder-slate-400 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent",rows:3,placeholder:"Please provide a reason for rejection...",required:!0})]}),(0,a.jsxs)("div",{className:"flex gap-3",children:[(0,a.jsx)(x.$n,{variant:"outline",onClick:()=>{d(null),p(null),N("")},disabled:g,className:"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white",children:"Cancel"}),(0,a.jsx)(x.$n,{onClick:()=>P(c.userId,h,"reject"===h?j:void 0),disabled:g||"reject"===h&&!j.trim(),loading:g,className:"approve"===h?"bg-green-600 hover:bg-green-700 text-white":"bg-red-600 hover:bg-red-700 text-white",children:"approve"===h?"Approve":"Reject"})]})]})})]})};var et=t(4186),ea=t(4861),el=t(6932),er=t(4357),ei=t(1284);let en=()=>{var e,s;let[t,r]=(0,l.useState)([]),[i,n]=(0,l.useState)({totalDeposits:0,totalAmount:0,pendingDeposits:0}),[c,d]=(0,l.useState)(!0),[o,m]=(0,l.useState)(null),[h,u]=(0,l.useState)("ALL"),[j,N]=(0,l.useState)(null),[g,b]=(0,l.useState)(null),[f,v]=(0,l.useState)(null),w=async()=>{try{d(!0);let e=new URLSearchParams;"ALL"!==h&&e.append("status",h),e.append("limit","50");let s=await fetch("/api/admin/deposits?".concat(e),{credentials:"include"});if(!s.ok)throw Error("Failed to fetch deposits");let t=await s.json();if(t.success)r(t.data.deposits),n(t.data.stats);else throw Error(t.error||"Failed to fetch deposits")}catch(e){m(e instanceof Error?e.message:"An error occurred")}finally{d(!1)}},y=async(e,s)=>{try{await navigator.clipboard.writeText(e),v(s),setTimeout(()=>v(null),2e3)}catch(e){console.error("Failed to copy to clipboard:",e)}};(0,l.useEffect)(()=>{w()},[h]);let A=e=>{switch(e){case"COMPLETED":return(0,a.jsx)(I.A,{className:"w-4 h-4 text-green-400"});case"PENDING":return(0,a.jsx)(et.A,{className:"w-4 h-4 text-yellow-400"});case"FAILED":case"REJECTED":return(0,a.jsx)(ea.A,{className:"w-4 h-4 text-red-400"});case"VERIFYING":return(0,a.jsx)(U.A,{className:"w-4 h-4 text-blue-400"});default:return(0,a.jsx)(et.A,{className:"w-4 h-4 text-gray-400"})}},C=e=>{switch(e){case"COMPLETED":return"text-green-400 bg-green-400/10";case"PENDING":return"text-yellow-400 bg-yellow-400/10";case"FAILED":case"REJECTED":return"text-red-400 bg-red-400/10";case"VERIFYING":return"text-blue-400 bg-blue-400/10";default:return"text-gray-400 bg-gray-400/10"}};return c?(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)(q.A,{className:"w-8 h-8 animate-spin text-blue-400"})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsxs)("button",{onClick:w,className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors",children:[(0,a.jsx)(q.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Refresh"})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,a.jsx)(x.Zp,{className:"bg-gray-800 border-gray-700",children:(0,a.jsx)(x.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Total Deposits"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:i.totalDeposits})]}),(0,a.jsx)(p.A,{className:"w-8 h-8 text-green-400"})]})})}),(0,a.jsx)(x.Zp,{className:"bg-gray-800 border-gray-700",children:(0,a.jsx)(x.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Total Amount"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:(0,F.vv)(i.totalAmount)})]}),(0,a.jsx)(p.A,{className:"w-8 h-8 text-blue-400"})]})})}),(0,a.jsx)(x.Zp,{className:"bg-gray-800 border-gray-700",children:(0,a.jsx)(x.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Pending Deposits"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:i.pendingDeposits})]}),(0,a.jsx)(et.A,{className:"w-8 h-8 text-yellow-400"})]})})})]}),(0,a.jsx)(x.Zp,{className:"bg-gray-800 border-gray-700",children:(0,a.jsx)(x.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row items-start sm:items-center gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(el.A,{className:"w-5 h-5 text-gray-400"}),(0,a.jsx)("span",{className:"text-gray-400 text-sm font-medium",children:"Filter by Status:"})]}),(0,a.jsxs)("select",{value:h,onChange:e=>u(e.target.value),className:"w-full sm:w-auto bg-gray-700 border border-gray-600 text-white rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"ALL",children:"All Status"}),(0,a.jsx)("option",{value:"PENDING",children:"Pending"}),(0,a.jsx)("option",{value:"VERIFYING",children:"Verifying"}),(0,a.jsx)("option",{value:"CONFIRMED",children:"Confirmed"}),(0,a.jsx)("option",{value:"COMPLETED",children:"Completed"}),(0,a.jsx)("option",{value:"FAILED",children:"Failed"}),(0,a.jsx)("option",{value:"REJECTED",children:"Rejected"})]})]})})}),(0,a.jsxs)(x.Zp,{className:"bg-gray-800 border-gray-700",children:[(0,a.jsx)(x.aR,{children:(0,a.jsx)(x.ZB,{className:"text-white",children:"Recent Deposits"})}),(0,a.jsxs)(x.Wu,{children:[o&&(0,a.jsx)("div",{className:"mb-4 p-4 bg-red-900/20 border border-red-500 rounded-lg",children:(0,a.jsx)("p",{className:"text-red-400",children:o})}),(0,a.jsx)("div",{className:"hidden lg:block overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"border-b border-gray-700",children:[(0,a.jsx)("th",{className:"text-left py-3 px-4 text-gray-400 font-medium",children:"User"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 text-gray-400 font-medium",children:"Amount"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 text-gray-400 font-medium",children:"Status"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 text-gray-400 font-medium",children:"Transaction ID"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 text-gray-400 font-medium",children:"Date"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 text-gray-400 font-medium",children:"Actions"})]})}),(0,a.jsx)("tbody",{children:t.map(e=>{var s;return(0,a.jsxs)("tr",{className:"border-b border-gray-700/50 hover:bg-gray-700/30",children:[(0,a.jsxs)("td",{className:"py-3 px-4",children:[(0,a.jsx)("div",{className:"text-white",children:e.user?"".concat(e.user.firstName," ").concat(e.user.lastName):"Unknown"}),(0,a.jsx)("div",{className:"text-sm text-gray-400",children:(null==(s=e.user)?void 0:s.email)||"No email"})]}),(0,a.jsxs)("td",{className:"py-3 px-4",children:[(0,a.jsxs)("div",{className:"text-white font-medium",children:[(0,F.vv)(e.usdtAmount)," USDT"]}),(0,a.jsxs)("div",{className:"text-sm text-gray-400",children:[e.confirmations," confirmations"]})]}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{className:"inline-flex items-center space-x-2 px-2 py-1 rounded-full text-xs font-medium ".concat(C(e.status)),children:[A(e.status),(0,a.jsx)("span",{children:e.status})]})}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{className:"text-white font-mono text-sm",children:[e.transactionId.slice(0,8),"...",e.transactionId.slice(-8)]})}),(0,a.jsx)("td",{className:"py-3 px-4 text-gray-300",children:(0,F.r6)(e.createdAt)}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("button",{onClick:()=>N(e),className:"flex items-center space-x-1 px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm transition-colors",children:[(0,a.jsx)(K.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"View"})]})})]},e.id)})})]})}),(0,a.jsx)("div",{className:"lg:hidden space-y-4",children:t.map(e=>{var s;return(0,a.jsxs)("div",{className:"bg-gray-700 rounded-lg p-4 border border-gray-600",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-white font-medium",children:e.user?"".concat(e.user.firstName," ").concat(e.user.lastName):"Unknown"}),(0,a.jsx)("div",{className:"text-sm text-gray-400",children:(null==(s=e.user)?void 0:s.email)||"No email"})]}),(0,a.jsxs)("div",{className:"inline-flex items-center space-x-2 px-2 py-1 rounded-full text-xs font-medium ".concat(C(e.status)),children:[A(e.status),(0,a.jsx)("span",{children:e.status})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-3 text-sm mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-gray-400 text-xs mb-1",children:"Amount"}),(0,a.jsxs)("div",{className:"text-white font-medium",children:[(0,F.vv)(e.usdtAmount)," USDT"]}),(0,a.jsxs)("div",{className:"text-xs text-gray-400",children:[e.confirmations," confirmations"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-gray-400 text-xs mb-1",children:"Date"}),(0,a.jsx)("div",{className:"text-gray-300 text-xs",children:(0,F.r6)(e.createdAt)})]})]}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("div",{className:"text-gray-400 text-xs mb-1",children:"Transaction ID"}),(0,a.jsxs)("div",{className:"text-white font-mono text-xs bg-gray-800 p-2 rounded border",children:[e.transactionId.slice(0,8),"...",e.transactionId.slice(-8)]})]}),(0,a.jsxs)("button",{onClick:()=>N(e),className:"w-full flex items-center justify-center space-x-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm transition-colors",children:[(0,a.jsx)(K.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"View Details"})]})]},e.id)})}),0===t.length&&(0,a.jsx)("div",{className:"text-center py-8 text-gray-400",children:"No deposits found"})]})]}),j&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-gray-800 border border-gray-700 rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-white",children:"Deposit Details"}),(0,a.jsx)("button",{onClick:()=>N(null),className:"text-gray-400 hover:text-white",children:(0,a.jsx)(ea.A,{className:"w-6 h-6"})})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-gray-400 text-sm",children:"User"}),(0,a.jsx)("p",{className:"text-white",children:j.user?"".concat(j.user.firstName," ").concat(j.user.lastName):"Unknown"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("p",{className:"text-gray-400 text-sm flex-1",children:null==(e=j.user)?void 0:e.email}),(null==(s=j.user)?void 0:s.email)&&(0,a.jsx)("button",{onClick:()=>{var e;return y((null==(e=j.user)?void 0:e.email)||"","email")},className:"p-1 text-gray-400 hover:text-white transition-colors",title:"Copy email",children:"email"===f?(0,a.jsx)(ee.A,{className:"w-4 h-4 text-green-400"}):(0,a.jsx)(er.A,{className:"w-4 h-4"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-gray-400 text-sm",children:"Amount"}),(0,a.jsxs)("p",{className:"text-white font-medium",children:[(0,F.vv)(j.usdtAmount)," USDT"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-gray-400 text-sm",children:"Status"}),(0,a.jsxs)("div",{className:"inline-flex items-center space-x-2 px-2 py-1 rounded-full text-xs font-medium ".concat(C(j.status)),children:[A(j.status),(0,a.jsx)("span",{children:j.status})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-gray-400 text-sm",children:"Confirmations"}),(0,a.jsx)("p",{className:"text-white",children:j.confirmations})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-gray-400 text-sm",children:"Transaction ID"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("p",{className:"text-white font-mono text-sm break-all flex-1",children:j.transactionId}),(0,a.jsx)("button",{onClick:()=>y(j.transactionId,"transactionId"),className:"p-1 text-gray-400 hover:text-white transition-colors flex-shrink-0",title:"Copy transaction ID",children:"transactionId"===f?(0,a.jsx)(ee.A,{className:"w-4 h-4 text-green-400"}):(0,a.jsx)(er.A,{className:"w-4 h-4"})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-gray-400 text-sm",children:"Sender Address"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("p",{className:"text-white font-mono text-sm break-all flex-1",children:j.senderAddress||"N/A"}),j.senderAddress&&(0,a.jsx)("button",{onClick:()=>y(j.senderAddress||"","senderAddress"),className:"p-1 text-gray-400 hover:text-white transition-colors flex-shrink-0",title:"Copy sender address",children:"senderAddress"===f?(0,a.jsx)(ee.A,{className:"w-4 h-4 text-green-400"}):(0,a.jsx)(er.A,{className:"w-4 h-4"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-gray-400 text-sm",children:"Deposit Address"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("p",{className:"text-white font-mono text-sm break-all flex-1",children:j.tronAddress}),(0,a.jsx)("button",{onClick:()=>y(j.tronAddress,"depositAddress"),className:"p-1 text-gray-400 hover:text-white transition-colors flex-shrink-0",title:"Copy deposit address",children:"depositAddress"===f?(0,a.jsx)(ee.A,{className:"w-4 h-4 text-green-400"}):(0,a.jsx)(er.A,{className:"w-4 h-4"})})]})]})]}),j.failureReason&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-gray-400 text-sm",children:"Failure Reason"}),(0,a.jsx)("p",{className:"text-red-400",children:j.failureReason})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-gray-400 text-sm",children:"Created At"}),(0,a.jsx)("p",{className:"text-white",children:(0,F.r6)(j.createdAt)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-gray-400 text-sm",children:"Processed At"}),(0,a.jsx)("p",{className:"text-white",children:j.processedAt?(0,F.r6)(j.processedAt):"Not processed"})]})]})]}),(0,a.jsx)("div",{className:"mt-6 pt-6 border-t border-gray-700",children:(0,a.jsx)("div",{className:"bg-blue-900/50 border border-blue-700 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(ei.A,{className:"w-5 h-5 text-blue-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-blue-300 font-medium",children:"Automated Processing"}),(0,a.jsx)("p",{className:"text-blue-200 text-sm mt-1",children:"Deposits are now processed automatically. The system verifies transactions and credits wallets once confirmations are met."})]})]})})})]})})]})},ec=()=>{let[e,s]=(0,l.useState)([]),[t,r]=(0,l.useState)(!0),[i,n]=(0,l.useState)(""),[c,d]=(0,l.useState)("all"),[o,m]=(0,l.useState)(null),[h,u]=(0,l.useState)(null),[N,g]=(0,l.useState)(""),[b,f]=(0,l.useState)(""),[v,w]=(0,l.useState)(!1),[y,A]=(0,l.useState)(null);(0,l.useEffect)(()=>{C()},[i,c]);let C=async()=>{try{r(!0);let e=new URLSearchParams({search:i,status:c}),t=await fetch("/api/admin/withdrawals?".concat(e),{credentials:"include"});if(t.ok){let e=await t.json();e.success&&s(e.data)}}catch(e){console.error("Failed to fetch withdrawals:",e)}finally{r(!1)}},E=async(e,s)=>{try{await navigator.clipboard.writeText(e),A(s),setTimeout(()=>A(null),2e3)}catch(e){console.error("Failed to copy to clipboard:",e)}},k=async(e,s,t)=>{try{w(!0),(await fetch("/api/admin/withdrawals/action",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({withdrawalId:e,action:s.toUpperCase(),...t})})).ok&&(C(),m(null),u(null),g(""),f(""))}catch(e){console.error("Failed to process withdrawal action:",e)}finally{w(!1)}},T=e=>{let s={PENDING:{color:"bg-yellow-900 text-yellow-300 border border-yellow-700",icon:et.A},APPROVED:{color:"bg-blue-900 text-blue-300 border border-blue-700",icon:I.A},REJECTED:{color:"bg-red-900 text-red-300 border border-red-700",icon:ea.A},COMPLETED:{color:"bg-green-900 text-green-300 border border-green-700",icon:I.A}}[e],t=s.icon;return(0,a.jsxs)("span",{className:"inline-flex items-center gap-1 px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(s.color),children:[(0,a.jsx)(t,{className:"h-3 w-3"}),e]})};return t?(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-8 bg-slate-700 rounded w-1/4 mb-4"}),(0,a.jsx)("div",{className:"h-64 bg-slate-700 rounded"})]})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("div",{className:"text-sm text-slate-400",children:"Pending Amount"}),(0,a.jsx)("div",{className:"text-2xl font-bold text-yellow-400",children:(0,F.vv)(e.filter(e=>"PENDING"===e.status).reduce((e,s)=>e+s.amount,0))})]})}),(0,a.jsx)(x.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(x.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(_.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(x.pd,{placeholder:"Search by user email or wallet address...",value:i,onChange:e=>n(e.target.value),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-red-500"})]})}),(0,a.jsx)("div",{className:"sm:w-48",children:(0,a.jsxs)("select",{value:c,onChange:e=>d(e.target.value),className:"w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"all",children:"All Withdrawals"}),(0,a.jsx)("option",{value:"pending",children:"Pending"}),(0,a.jsx)("option",{value:"approved",children:"Approved"}),(0,a.jsx)("option",{value:"completed",children:"Completed"}),(0,a.jsx)("option",{value:"rejected",children:"Rejected"})]})})]})})}),(0,a.jsx)("div",{className:"grid gap-4",children:0===e.length?(0,a.jsx)(x.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsxs)(x.Wu,{className:"p-12 text-center",children:[(0,a.jsx)(j.A,{className:"h-12 w-12 text-slate-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-white mb-2",children:"No Withdrawal Requests"}),(0,a.jsx)("p",{className:"text-slate-400",children:"No withdrawal requests match your current filters."})]})}):e.map(e=>(0,a.jsx)(x.Zp,{className:"bg-slate-800 border-slate-700 hover:bg-slate-750 transition-colors",children:(0,a.jsx)(x.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(D.A,{className:"h-5 w-5 text-slate-400"}),(0,a.jsxs)("span",{className:"font-medium text-white",children:[e.user.firstName," ",e.user.lastName]})]}),T(e.status)]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-slate-400 mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-slate-300",children:"Email:"})," ",e.user.email]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-slate-300",children:"User ID:"})," ",e.user.referralId]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(p.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"font-medium text-slate-300",children:"Amount:"})," ",(0,F.vv)(e.amount)]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(X.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"font-medium text-slate-300",children:"Requested:"})," ",(0,F.r6)(e.requestedAt)]})]}),(0,a.jsxs)("div",{className:"text-sm text-slate-400 mb-4",children:[(0,a.jsx)("span",{className:"font-medium text-slate-300",children:"Wallet Address:"}),(0,a.jsxs)("div",{className:"flex items-center gap-2 mt-1",children:[(0,a.jsx)("div",{className:"font-mono text-xs bg-slate-700 border border-slate-600 p-2 rounded break-all text-slate-300 flex-1",children:e.walletAddress}),(0,a.jsx)("button",{onClick:()=>E(e.walletAddress,"wallet-".concat(e.id)),className:"p-2 text-slate-400 hover:text-white transition-colors flex-shrink-0 bg-slate-700 border border-slate-600 rounded",title:"Copy wallet address",children:y==="wallet-".concat(e.id)?(0,a.jsx)(ee.A,{className:"w-4 h-4 text-green-400"}):(0,a.jsx)(er.A,{className:"w-4 h-4"})})]})]}),e.transactionHash&&(0,a.jsxs)("div",{className:"text-sm text-slate-400 mb-4",children:[(0,a.jsx)("span",{className:"font-medium text-slate-300",children:"Transaction Hash:"}),(0,a.jsxs)("div",{className:"flex items-center gap-2 mt-1",children:[(0,a.jsx)("div",{className:"font-mono text-xs bg-green-900/20 border border-green-700 p-2 rounded break-all text-green-300 flex-1",children:e.transactionHash}),(0,a.jsx)("button",{onClick:()=>E(e.transactionHash||"","hash-".concat(e.id)),className:"p-2 text-slate-400 hover:text-white transition-colors flex-shrink-0 bg-green-900/20 border border-green-700 rounded",title:"Copy transaction hash",children:y==="hash-".concat(e.id)?(0,a.jsx)(ee.A,{className:"w-4 h-4 text-green-400"}):(0,a.jsx)(er.A,{className:"w-4 h-4"})})]})]}),e.rejectionReason&&(0,a.jsxs)("div",{className:"bg-red-900/20 border border-red-700 rounded-lg p-3 mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-red-300 text-sm font-medium mb-1",children:[(0,a.jsx)(S.A,{className:"h-4 w-4"}),"Rejection Reason"]}),(0,a.jsx)("p",{className:"text-red-400 text-sm",children:e.rejectionReason})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 ml-4",children:["PENDING"===e.status&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(x.$n,{variant:"outline",size:"sm",onClick:()=>{m(e),u("approve")},className:"border-green-600 text-green-400 hover:text-green-300 hover:bg-green-900/20",children:[(0,a.jsx)(ee.A,{className:"h-4 w-4 mr-1"}),"Approve"]}),(0,a.jsxs)(x.$n,{variant:"outline",size:"sm",onClick:()=>{m(e),u("reject")},className:"border-red-600 text-red-400 hover:text-red-300 hover:bg-red-900/20",children:[(0,a.jsx)(S.A,{className:"h-4 w-4 mr-1"}),"Reject"]})]}),"APPROVED"===e.status&&(0,a.jsxs)(x.$n,{variant:"outline",size:"sm",onClick:()=>{m(e),u("complete")},className:"border-blue-600 text-blue-400 hover:text-blue-300 hover:bg-blue-900/20",children:[(0,a.jsx)(I.A,{className:"h-4 w-4 mr-1"}),"Mark Complete"]})]})]})})},e.id))}),o&&h&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center p-4 z-50",children:(0,a.jsxs)("div",{className:"bg-slate-800 border border-slate-700 rounded-xl max-w-md w-full p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold mb-4 text-white",children:["approve"===h&&"Approve Withdrawal","reject"===h&&"Reject Withdrawal","complete"===h&&"Complete Withdrawal"]}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsxs)("p",{className:"text-slate-400 mb-2",children:["User: ",(0,a.jsxs)("span",{className:"font-medium text-white",children:[o.user.firstName," ",o.user.lastName]})]}),(0,a.jsxs)("p",{className:"text-slate-400",children:["Amount: ",(0,a.jsx)("span",{className:"font-medium text-white",children:(0,F.vv)(o.amount)})]})]}),"reject"===h&&(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Rejection Reason *"}),(0,a.jsx)("textarea",{value:N,onChange:e=>g(e.target.value),className:"w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white placeholder-slate-400 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent",rows:3,placeholder:"Please provide a reason for rejection...",required:!0})]}),"complete"===h&&(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Transaction Hash *"}),(0,a.jsx)(x.pd,{value:b,onChange:e=>f(e.target.value),placeholder:"Enter blockchain transaction hash...",className:"bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500",required:!0})]}),(0,a.jsxs)("div",{className:"flex gap-3",children:[(0,a.jsx)(x.$n,{variant:"outline",onClick:()=>{m(null),u(null),g(""),f("")},disabled:v,className:"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white",children:"Cancel"}),(0,a.jsxs)(x.$n,{onClick:()=>{let e={};"reject"===h&&(e.rejectionReason=N),"complete"===h&&(e.transactionHash=b),k(o.id,h,e)},disabled:v||"reject"===h&&!N.trim()||"complete"===h&&!b.trim(),loading:v,className:"reject"===h?"bg-red-600 hover:bg-red-700 text-white":"approve"===h?"bg-green-600 hover:bg-green-700 text-white":"bg-blue-600 hover:bg-blue-700 text-white",children:["approve"===h&&"Approve","reject"===h&&"Reject","complete"===h&&"Mark Complete"]})]})]})})]})};var ed=t(5339),ex=t(2486);let eo=()=>{let[e,s]=(0,l.useState)([]),[t,r]=(0,l.useState)(!0),[i,n]=(0,l.useState)(null),[c,d]=(0,l.useState)(""),[o,m]=(0,l.useState)(!1),[h,u]=(0,l.useState)({status:"",priority:"",search:""});(0,l.useEffect)(()=>{p()},[]);let p=async()=>{try{let e=await fetch("/api/admin/support/tickets",{credentials:"include"});if(e.ok){let t=await e.json();t.success&&s(t.data)}}catch(e){console.error("Failed to fetch tickets:",e)}finally{r(!1)}},j=async(e,s)=>{try{(await fetch("/api/admin/support/tickets/".concat(e),{method:"PUT",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({status:s})})).ok&&(p(),i&&i.id===e&&n({...i,status:s}))}catch(e){console.error("Failed to update ticket status:",e)}},N=async e=>{if(c.trim()){m(!0);try{if((await fetch("/api/admin/support/tickets/".concat(e,"/responses"),{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({message:c})})).ok){d(""),p();let s=await fetch("/api/admin/support/tickets",{credentials:"include"});if(s.ok){let t=(await s.json()).data.find(s=>s.id===e);t&&n(t)}}}catch(e){console.error("Failed to add response:",e)}finally{m(!1)}}},g=e=>{switch(e){case"OPEN":return(0,a.jsx)(ed.A,{className:"h-4 w-4 text-red-500"});case"IN_PROGRESS":return(0,a.jsx)(et.A,{className:"h-4 w-4 text-yellow-500"});case"RESOLVED":return(0,a.jsx)(I.A,{className:"h-4 w-4 text-green-500"});case"CLOSED":return(0,a.jsx)(I.A,{className:"h-4 w-4 text-gray-500"});default:return(0,a.jsx)(ed.A,{className:"h-4 w-4 text-gray-500"})}},f=e=>{switch(e){case"OPEN":return"bg-red-100 text-red-700";case"IN_PROGRESS":return"bg-yellow-100 text-yellow-700";case"RESOLVED":return"bg-green-100 text-green-700";default:return"bg-gray-100 text-gray-700"}},v=e=>{switch(e){case"LOW":return"bg-blue-100 text-blue-700";case"MEDIUM":return"bg-yellow-100 text-yellow-700";case"HIGH":return"bg-orange-100 text-orange-700";case"URGENT":return"bg-red-100 text-red-700";default:return"bg-gray-100 text-gray-700"}},w=e.filter(e=>{let s=!h.status||e.status===h.status,t=!h.priority||e.priority===h.priority,a=!h.search||e.subject.toLowerCase().includes(h.search.toLowerCase())||e.user.email.toLowerCase().includes(h.search.toLowerCase())||"".concat(e.user.firstName," ").concat(e.user.lastName).toLowerCase().includes(h.search.toLowerCase());return s&&t&&a});return t?(0,a.jsx)("div",{className:"space-y-6",children:Array.from({length:3}).map((e,s)=>(0,a.jsx)("div",{className:"animate-pulse",children:(0,a.jsx)("div",{className:"h-32 bg-slate-700 rounded-xl"})},s))}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(x.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(x.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,a.jsx)("div",{className:"flex-1 min-w-64",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(_.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400"}),(0,a.jsx)("input",{type:"text",placeholder:"Search tickets, users...",value:h.search,onChange:e=>u(s=>({...s,search:e.target.value})),className:"w-full pl-10 pr-4 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})}),(0,a.jsxs)("select",{value:h.status,onChange:e=>u(s=>({...s,status:e.target.value})),className:"px-4 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"",children:"All Status"}),(0,a.jsx)("option",{value:"OPEN",children:"Open"}),(0,a.jsx)("option",{value:"IN_PROGRESS",children:"In Progress"}),(0,a.jsx)("option",{value:"RESOLVED",children:"Resolved"}),(0,a.jsx)("option",{value:"CLOSED",children:"Closed"})]}),(0,a.jsxs)("select",{value:h.priority,onChange:e=>u(s=>({...s,priority:e.target.value})),className:"px-4 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"",children:"All Priority"}),(0,a.jsx)("option",{value:"LOW",children:"Low"}),(0,a.jsx)("option",{value:"MEDIUM",children:"Medium"}),(0,a.jsx)("option",{value:"HIGH",children:"High"}),(0,a.jsx)("option",{value:"URGENT",children:"Urgent"})]})]})})}),(0,a.jsxs)(x.Zp,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsx)(x.aR,{children:(0,a.jsxs)(x.ZB,{className:"flex items-center gap-2 text-white",children:[(0,a.jsx)(b.A,{className:"h-5 w-5"}),"Support Tickets (",w.length,")"]})}),(0,a.jsx)(x.Wu,{children:w.length>0?(0,a.jsx)("div",{className:"space-y-4",children:w.map(e=>(0,a.jsxs)("div",{onClick:()=>n(e),className:"p-4 bg-slate-700 border border-slate-600 rounded-lg hover:bg-slate-600 cursor-pointer transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h4",{className:"font-medium text-white mb-1",children:e.subject}),(0,a.jsxs)("div",{className:"flex items-center gap-3 text-sm text-slate-400",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(D.A,{className:"h-3 w-3"}),e.user.firstName," ",e.user.lastName," (",e.user.email,")"]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(X.A,{className:"h-3 w-3"}),(0,F.r6)(e.createdAt)]})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 ml-4",children:[g(e.status),(0,a.jsx)("span",{className:"text-xs px-2 py-1 rounded-full ".concat(f(e.status)),children:e.status}),(0,a.jsx)("span",{className:"text-xs px-2 py-1 rounded-full ".concat(v(e.priority)),children:e.priority})]})]}),(0,a.jsx)("p",{className:"text-sm text-slate-300 line-clamp-2",children:e.message}),e.responses.length>0&&(0,a.jsxs)("p",{className:"text-xs text-slate-400 mt-2",children:[e.responses.length," response",1!==e.responses.length?"s":""]})]},e.id))}):(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(b.A,{className:"h-12 w-12 text-slate-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-white mb-2",children:"No Support Tickets"}),(0,a.jsx)("p",{className:"text-slate-400",children:"No tickets match your current filters."})]})})]}),i&&(0,a.jsx)(x.aF,{isOpen:!!i,onClose:()=>n(null),title:"Ticket: ".concat(i.subject),size:"xl",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[g(i.status),(0,a.jsx)("span",{className:"text-sm px-2 py-1 rounded-full ".concat(f(i.status)),children:i.status}),(0,a.jsx)("span",{className:"text-sm px-2 py-1 rounded-full ".concat(v(i.priority)),children:i.priority})]}),(0,a.jsx)("div",{className:"flex gap-2",children:(0,a.jsxs)("select",{value:i.status,onChange:e=>j(i.id,e.target.value),className:"px-3 py-1 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"OPEN",children:"Open"}),(0,a.jsx)("option",{value:"IN_PROGRESS",children:"In Progress"}),(0,a.jsx)("option",{value:"RESOLVED",children:"Resolved"}),(0,a.jsx)("option",{value:"CLOSED",children:"Closed"})]})})]}),(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(D.A,{className:"h-4 w-4 text-gray-500"}),(0,a.jsxs)("span",{className:"font-medium text-gray-900",children:[i.user.firstName," ",i.user.lastName]}),(0,a.jsxs)("span",{className:"text-gray-500",children:["(",i.user.email,")"]})]}),(0,a.jsx)("p",{className:"text-gray-900",children:i.message}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:(0,F.r6)(i.createdAt)})]}),i.responses.length>0&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"Responses:"}),i.responses.map(e=>(0,a.jsxs)("div",{className:"p-3 rounded-lg ".concat(e.isAdmin?"bg-blue-50 border-l-4 border-blue-500":"bg-gray-50"),children:[(0,a.jsx)("p",{className:"text-gray-900",children:e.message}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:[e.isAdmin?"Admin":"User"," • ",(0,F.r6)(e.createdAt)]})]},e.id))]}),"CLOSED"!==i.status&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("textarea",{value:c,onChange:e=>d(e.target.value),placeholder:"Add admin response...",rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,a.jsxs)(x.$n,{onClick:()=>N(i.id),disabled:!c.trim()||o,loading:o,className:"w-full",children:[(0,a.jsx)(ex.A,{className:"h-4 w-4 mr-2"}),"Send Admin Response"]})]})]})})]})};var em=t(8500);let eh=(0,f.A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]),eu=(0,f.A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]),ep=()=>{let[e,s]=(0,l.useState)([]),[t,r]=(0,l.useState)([]),[i,n]=(0,l.useState)(!0),[c,d]=(0,l.useState)(""),[o,m]=(0,l.useState)("all"),[u,j]=(0,l.useState)(null),[N,b]=(0,l.useState)(null),[f,y]=(0,l.useState)([]),[A,C]=(0,l.useState)(!1),[S,E]=(0,l.useState)(!1),[k,T]=(0,l.useState)({totalUsers:0,totalLeftPoints:0,totalRightPoints:0,totalMatchedPoints:0,totalPayouts:0}),[P,R]=(0,l.useState)(10),[D,M]=(0,l.useState)("10"),[I,L]=(0,l.useState)(!1),{showConfirm:O,ConfirmDialog:W}=(0,x.G_)(),{showMessage:B,MessageBoxComponent:Z}=(0,x.eC)();(0,l.useEffect)(()=>{$(),z(),H()},[]);let H=async()=>{try{let e=await fetch("/api/admin/settings",{credentials:"include"});if(e.ok){let s=await e.json();if(s.success){let e=s.data.maxBinaryPointsPerSide||10;R(e),M(e.toString()),console.log("Current binary points limit: ".concat(e))}}}catch(e){console.error("Failed to fetch current limit:",e)}},$=async()=>{try{let e=await fetch("/api/admin/binary-points",{credentials:"include"});if(e.ok){let t=await e.json();t.success&&(s(t.data),V(t.data))}}catch(e){console.error("Failed to fetch binary points data:",e)}finally{n(!1)}},z=async()=>{try{let e=await fetch("/api/admin/binary-points/history",{credentials:"include"});if(e.ok){let s=await e.json();s.success&&r(s.data)}}catch(e){console.error("Failed to fetch match history:",e)}},V=e=>{T(e.reduce((e,s)=>({totalUsers:e.totalUsers+1,totalLeftPoints:e.totalLeftPoints+s.leftPoints,totalRightPoints:e.totalRightPoints+s.rightPoints,totalMatchedPoints:e.totalMatchedPoints+s.matchedPoints,totalPayouts:e.totalPayouts+10*s.totalMatched}),{totalUsers:0,totalLeftPoints:0,totalRightPoints:0,totalMatchedPoints:0,totalPayouts:0}))},G=e.filter(e=>{let s=e.user.email.toLowerCase().includes(c.toLowerCase())||e.user.firstName.toLowerCase().includes(c.toLowerCase())||e.user.lastName.toLowerCase().includes(c.toLowerCase()),t="all"===o||"active"===o&&(e.leftPoints>0||e.rightPoints>0)||"inactive"===o&&0===e.leftPoints&&0===e.rightPoints;return s&&t}),Y=async s=>{j(s),E(!0),C(!0),b(e.find(e=>e.userId===s)||null);try{let e=await fetch("/api/admin/binary-points/user-history/".concat(s),{credentials:"include"});if(e.ok){let s=await e.json();s.success&&y(s.data)}}catch(e){console.error("Failed to fetch user match history:",e)}finally{E(!1)}},J=async()=>{let e=parseFloat(D);if(isNaN(e)||e<=0)return void B({title:"Invalid Input",message:"Please enter a valid positive number for the limit",variant:"error",darkMode:!0});if(await O({title:"Update Binary Points Limit",message:"Are you sure you want to update the maximum points per side to ".concat(e,"? This will affect all future binary point additions."),confirmText:"Update Limit",cancelText:"Cancel"})){L(!0);try{let s=await fetch("/api/admin/binary-points/update-limit",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({maxPointsPerSide:e})}),t=await s.json();t.success?(R(e),B({title:"Success",message:"Successfully updated binary points limit to ".concat(e),variant:"success",darkMode:!0})):B({title:"Update Failed",message:"Failed to update limit: ".concat(t.error),variant:"error",darkMode:!0})}catch(e){console.error("Update limit error:",e),B({title:"Error",message:"Failed to update binary points limit",variant:"error",darkMode:!0})}finally{L(!1)}}};return i?(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(x.Zp,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsx)(x.aR,{children:(0,a.jsxs)(x.ZB,{className:"flex items-center gap-2 text-white",children:[(0,a.jsx)(U.A,{className:"h-5 w-5 text-orange-400"}),"Binary Points Limit Configuration"]})}),(0,a.jsxs)(x.Wu,{children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 items-end",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Maximum Points Per Side"}),(0,a.jsx)(x.pd,{type:"number",value:D,onChange:e=>M(e.target.value),className:"bg-slate-700 border-slate-600 text-white",placeholder:"Enter limit",min:"1",step:"0.1"})]}),(0,a.jsx)(x.$n,{onClick:J,disabled:I||D===P.toString(),className:"bg-orange-600 text-white",children:I?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Updating..."]}):"Update Limit"})]}),(0,a.jsxs)("p",{className:"text-sm text-slate-400 mt-2",children:["Current limit: ",(0,a.jsxs)("span",{className:"text-orange-400 font-medium",children:[P," points per side"]})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4",children:[(0,a.jsx)(x.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(x.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-slate-400",children:"Total Users"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:(0,F.ZV)(k.totalUsers)})]}),(0,a.jsx)(h.A,{className:"h-8 w-8 text-blue-400"})]})})}),(0,a.jsx)(x.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(x.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-slate-400",children:"Total Left Points"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:(0,F.ZV)(k.totalLeftPoints)})]}),(0,a.jsx)(w.A,{className:"h-8 w-8 text-green-400"})]})})}),(0,a.jsx)(x.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(x.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-slate-400",children:"Total Right Points"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:(0,F.ZV)(k.totalRightPoints)})]}),(0,a.jsx)(em.A,{className:"h-8 w-8 text-red-400"})]})})}),(0,a.jsx)(x.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(x.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-slate-400",children:"Total Matched"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:(0,F.ZV)(k.totalMatchedPoints)})]}),(0,a.jsx)(v,{className:"h-8 w-8 text-purple-400"})]})})}),(0,a.jsx)(x.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(x.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-slate-400",children:"Total Payouts"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:(0,F.vv)(k.totalPayouts)})]}),(0,a.jsx)(p.A,{className:"h-8 w-8 text-yellow-400"})]})})})]}),(0,a.jsxs)(x.Zp,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsx)(x.aR,{children:(0,a.jsxs)(x.ZB,{className:"flex items-center gap-2 text-white",children:[(0,a.jsx)(v,{className:"h-5 w-5 text-purple-400"}),"Binary Points Management"]})}),(0,a.jsxs)(x.Wu,{children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 mb-6",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(_.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(x.pd,{type:"text",placeholder:"Search by email or name...",value:c,onChange:e=>d(e.target.value),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400"})]})}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)("select",{value:o,onChange:e=>m(e.target.value),className:"px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white text-sm",children:[(0,a.jsx)("option",{value:"all",children:"All Users"}),(0,a.jsx)("option",{value:"active",children:"Active Points"}),(0,a.jsx)("option",{value:"inactive",children:"No Points"})]}),(0,a.jsxs)(x.$n,{onClick:()=>{let e=new Blob([["User Email,Name,Left Points,Right Points,Matched Points,Total Matched,Last Match Date",...G.map(e=>[e.user.email,"".concat(e.user.firstName," ").concat(e.user.lastName),e.leftPoints,e.rightPoints,e.matchedPoints,e.totalMatched,e.lastMatchDate?(0,F.r6)(e.lastMatchDate):"Never"].join(","))].join("\n")],{type:"text/csv"}),s=window.URL.createObjectURL(e),t=document.createElement("a");t.href=s,t.download="binary-points-".concat(new Date().toISOString().split("T")[0],".csv"),t.click(),window.URL.revokeObjectURL(s)},className:"bg-green-600 text-white",children:[(0,a.jsx)(eh,{className:"h-4 w-4 mr-2"}),"Export"]})]})]}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full text-sm",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"border-b border-slate-600",children:[(0,a.jsx)("th",{className:"text-left py-3 px-4 text-slate-300",children:"User"}),(0,a.jsx)("th",{className:"text-right py-3 px-4 text-slate-300",children:"Left Points"}),(0,a.jsx)("th",{className:"text-right py-3 px-4 text-slate-300",children:"Right Points"}),(0,a.jsx)("th",{className:"text-right py-3 px-4 text-slate-300",children:"Matchable"}),(0,a.jsx)("th",{className:"text-right py-3 px-4 text-slate-300",children:"Total Matched"}),(0,a.jsx)("th",{className:"text-right py-3 px-4 text-slate-300",children:"Last Match"}),(0,a.jsx)("th",{className:"text-center py-3 px-4 text-slate-300",children:"Actions"})]})}),(0,a.jsx)("tbody",{children:G.map(e=>{let s=Math.min(e.leftPoints,e.rightPoints);return(0,a.jsxs)("tr",{className:"border-b border-slate-700",children:[(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"font-medium text-white",children:[e.user.firstName," ",e.user.lastName]}),(0,a.jsx)("div",{className:"text-sm text-slate-400",children:e.user.email})]})}),(0,a.jsx)("td",{className:"text-right py-3 px-4 text-white",children:(0,F.ZV)(e.leftPoints)}),(0,a.jsx)("td",{className:"text-right py-3 px-4 text-white",children:(0,F.ZV)(e.rightPoints)}),(0,a.jsx)("td",{className:"text-right py-3 px-4",children:(0,a.jsx)("span",{className:"font-medium ".concat(s>0?"text-green-400":"text-slate-400"),children:(0,F.ZV)(s)})}),(0,a.jsx)("td",{className:"text-right py-3 px-4 text-white",children:(0,F.ZV)(e.totalMatched)}),(0,a.jsx)("td",{className:"text-right py-3 px-4 text-slate-300",children:e.lastMatchDate?(0,F.r6)(e.lastMatchDate):"Never"}),(0,a.jsx)("td",{className:"text-center py-3 px-4",children:(0,a.jsxs)(x.$n,{onClick:()=>Y(e.userId),className:"bg-blue-600 text-white text-xs px-2 py-1",children:[(0,a.jsx)(K.A,{className:"h-3 w-3 mr-1"}),"View"]})})]},e.id)})})]})}),0===G.length&&(0,a.jsx)("div",{className:"text-center py-8 text-slate-400",children:"No binary points data found matching your criteria."})]})]}),A&&N&&(0,a.jsx)(x.aF,{isOpen:A,onClose:()=>{C(!1),j(null),b(null),y([])},title:"Binary Points Details - ".concat(N.user.firstName," ").concat(N.user.lastName),size:"xl",darkMode:!0,children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"bg-slate-700 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"User Information"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-slate-300",children:"Name:"}),(0,a.jsxs)("span",{className:"text-white",children:[N.user.firstName," ",N.user.lastName]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-slate-300",children:"Email:"}),(0,a.jsx)("span",{className:"text-white",children:N.user.email})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-slate-300",children:"Joined:"}),(0,a.jsx)("span",{className:"text-white",children:(0,F.r6)(N.user.createdAt)})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"Current Status"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-slate-300",children:"Left Points:"}),(0,a.jsx)("span",{className:"text-green-400 font-semibold",children:(0,F.ZV)(N.leftPoints)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-slate-300",children:"Right Points:"}),(0,a.jsx)("span",{className:"text-orange-400 font-semibold",children:(0,F.ZV)(N.rightPoints)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-slate-300",children:"Total Matched:"}),(0,a.jsx)("span",{className:"text-purple-400 font-semibold",children:(0,F.ZV)(N.totalMatched)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-slate-300",children:"Last Match:"}),(0,a.jsx)("span",{className:"text-white",children:N.lastMatchDate?(0,F.r6)(N.lastMatchDate):"Never"})]})]})]})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsx)(x.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(x.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-slate-400",children:"Matchable Points"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:(0,F.ZV)(Math.min(N.leftPoints,N.rightPoints))})]}),(0,a.jsx)(g.A,{className:"h-8 w-8 text-purple-400"})]})})}),(0,a.jsx)(x.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(x.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-slate-400",children:"Potential Payout"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:(0,F.vv)(10*Math.min(N.leftPoints,N.rightPoints))})]}),(0,a.jsx)(p.A,{className:"h-8 w-8 text-green-400"})]})})}),(0,a.jsx)(x.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(x.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-slate-400",children:"Total Earned"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:(0,F.vv)(10*N.totalMatched)})]}),(0,a.jsx)(eu,{className:"h-8 w-8 text-blue-400"})]})})})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-white mb-4 flex items-center gap-2",children:[(0,a.jsx)(X.A,{className:"h-5 w-5 text-blue-400"}),"Binary Matching History"]}),S?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400 mx-auto"}),(0,a.jsx)("p",{className:"text-slate-400 mt-2",children:"Loading match history..."})]}):f.length>0?(0,a.jsx)("div",{className:"bg-slate-800 rounded-lg overflow-hidden",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full text-sm",children:[(0,a.jsx)("thead",{className:"bg-slate-700",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"text-left py-3 px-4 text-slate-300",children:"Date"}),(0,a.jsx)("th",{className:"text-right py-3 px-4 text-slate-300",children:"Matched Points"}),(0,a.jsx)("th",{className:"text-right py-3 px-4 text-slate-300",children:"Payout"}),(0,a.jsx)("th",{className:"text-right py-3 px-4 text-slate-300",children:"Left Before"}),(0,a.jsx)("th",{className:"text-right py-3 px-4 text-slate-300",children:"Right Before"}),(0,a.jsx)("th",{className:"text-right py-3 px-4 text-slate-300",children:"Left After"}),(0,a.jsx)("th",{className:"text-right py-3 px-4 text-slate-300",children:"Right After"}),(0,a.jsx)("th",{className:"text-center py-3 px-4 text-slate-300",children:"Type"})]})}),(0,a.jsx)("tbody",{children:f.map((e,s)=>(0,a.jsxs)("tr",{className:s%2==0?"bg-slate-800":"bg-slate-750",children:[(0,a.jsx)("td",{className:"py-3 px-4 text-white",children:(0,F.r6)(e.matchDate)}),(0,a.jsx)("td",{className:"text-right py-3 px-4 text-purple-400 font-semibold",children:(0,F.ZV)(e.matchedPoints)}),(0,a.jsx)("td",{className:"text-right py-3 px-4 text-green-400 font-semibold",children:(0,F.vv)(e.payout)}),(0,a.jsx)("td",{className:"text-right py-3 px-4 text-slate-300",children:(0,F.ZV)(e.leftPointsBefore)}),(0,a.jsx)("td",{className:"text-right py-3 px-4 text-slate-300",children:(0,F.ZV)(e.rightPointsBefore)}),(0,a.jsx)("td",{className:"text-right py-3 px-4 text-green-400",children:(0,F.ZV)(e.leftPointsAfter)}),(0,a.jsx)("td",{className:"text-right py-3 px-4 text-orange-400",children:(0,F.ZV)(e.rightPointsAfter)}),(0,a.jsx)("td",{className:"text-center py-3 px-4",children:(0,a.jsx)("span",{className:"px-2 py-1 rounded text-xs ".concat("WEEKLY"===e.type?"bg-blue-600 text-white":"bg-orange-600 text-white"),children:e.type})})]},e.id))})]})})}):(0,a.jsxs)("div",{className:"text-center py-8 bg-slate-800 rounded-lg",children:[(0,a.jsx)(X.A,{className:"h-12 w-12 text-slate-600 mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-slate-400",children:"No binary matching history found for this user."})]})]}),(0,a.jsx)("div",{className:"flex justify-end space-x-3 pt-4 border-t border-slate-700",children:(0,a.jsx)(x.$n,{variant:"outline",onClick:()=>{C(!1),j(null),b(null),y([])},className:"bg-slate-700 border-slate-600 text-white hover:bg-slate-600",children:"Close"})})]})}),(0,a.jsx)(W,{}),(0,a.jsx)(Z,{})]})};var ej=t(2138);let eN=(0,f.A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]),eg=(0,f.A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]),eb=()=>{let[e,s]=(0,l.useState)([]),[t,r]=(0,l.useState)(null),[i,n]=(0,l.useState)(!0),[c,d]=(0,l.useState)(""),[o,m]=(0,l.useState)("all"),[u,j]=(0,l.useState)("all"),[N,g]=(0,l.useState)("30d"),[b,f]=(0,l.useState)(null),[v,y]=(0,l.useState)(1),[A,C]=(0,l.useState)(1),[S,E]=(0,l.useState)(0);(0,l.useEffect)(()=>{k(),T()},[N,o,u,v]);let k=async()=>{try{let e=new URLSearchParams({dateRange:N,type:o,status:u,page:v.toString(),limit:"20"}),t=await fetch("/api/admin/referral-commissions?".concat(e),{credentials:"include"});if(t.ok){let e=await t.json();e.success&&(s(e.data),e.pagination&&(C(Math.ceil(e.pagination.total/20)),E(e.pagination.total)))}}catch(e){console.error("Failed to fetch referral commissions:",e)}finally{n(!1)}},T=async()=>{try{let e=new URLSearchParams({dateRange:N}),s=await fetch("/api/admin/referral-commissions/stats?".concat(e),{credentials:"include"});if(s.ok){let e=await s.json();e.success&&r(e.data)}}catch(e){console.error("Failed to fetch commission stats:",e)}};return((0,l.useEffect)(()=>{y(1)},[c,o,u,N]),i)?(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,a.jsxs)("div",{className:"space-y-6",children:[t&&(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsx)(x.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(x.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-slate-400",children:"Total Commissions"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:(0,F.ZV)(t.totalCommissions)})]}),(0,a.jsx)(h.A,{className:"h-8 w-8 text-blue-400"})]})})}),(0,a.jsx)(x.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(x.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-slate-400",children:"Total Amount"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:(0,F.vv)(t.totalAmount)})]}),(0,a.jsx)(p.A,{className:"h-8 w-8 text-green-400"})]})})}),(0,a.jsx)(x.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(x.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-slate-400",children:"Average Commission"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:(0,F.vv)(t.averageCommission)})]}),(0,a.jsx)(w.A,{className:"h-8 w-8 text-orange-400"})]})})}),(0,a.jsx)(x.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(x.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-slate-400",children:"Recent Activity"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:(0,F.ZV)(t.recentActivity)})]}),(0,a.jsx)(X.A,{className:"h-8 w-8 text-purple-400"})]})})})]}),(null==t?void 0:t.topEarners)&&t.topEarners.length>0&&(0,a.jsxs)(x.Zp,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsx)(x.aR,{children:(0,a.jsxs)(x.ZB,{className:"flex items-center gap-2 text-white",children:[(0,a.jsx)(w.A,{className:"h-5 w-5 text-orange-400"}),"Top Commission Earners"]})}),(0,a.jsx)(x.Wu,{children:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:t.topEarners.slice(0,6).map((e,s)=>(0,a.jsxs)("div",{className:"p-4 bg-slate-700 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsxs)("span",{className:"text-sm font-medium text-slate-300",children:["#",s+1]}),(0,a.jsx)("span",{className:"text-lg font-bold text-green-400",children:(0,F.vv)(e.totalEarned)})]}),(0,a.jsxs)("div",{className:"text-sm text-white",children:[e.user.firstName," ",e.user.lastName]}),(0,a.jsx)("div",{className:"text-xs text-slate-400",children:e.user.email}),(0,a.jsxs)("div",{className:"text-xs text-slate-400 mt-1",children:[e.commissionCount," commissions"]})]},e.userId))})})]}),(0,a.jsxs)(x.Zp,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsx)(x.aR,{children:(0,a.jsxs)(x.ZB,{className:"flex items-center gap-2 text-white",children:[(0,a.jsx)(h.A,{className:"h-5 w-5 text-blue-400"}),"Referral Commission Tracking"]})}),(0,a.jsxs)(x.Wu,{children:[(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row gap-4 mb-6",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(_.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(x.pd,{type:"text",placeholder:"Search by user email or name...",value:c,onChange:e=>d(e.target.value),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400"})]})}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)("select",{value:N,onChange:e=>g(e.target.value),className:"px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white text-sm",children:[(0,a.jsx)("option",{value:"7d",children:"Last 7 days"}),(0,a.jsx)("option",{value:"30d",children:"Last 30 days"}),(0,a.jsx)("option",{value:"90d",children:"Last 90 days"}),(0,a.jsx)("option",{value:"all",children:"All time"})]}),(0,a.jsxs)("select",{value:o,onChange:e=>m(e.target.value),className:"px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white text-sm",children:[(0,a.jsx)("option",{value:"all",children:"All Types"}),(0,a.jsx)("option",{value:"DIRECT_REFERRAL",children:"Direct Referral"}),(0,a.jsx)("option",{value:"MINING_PURCHASE",children:"Mining Purchase"})]}),(0,a.jsxs)("select",{value:u,onChange:e=>j(e.target.value),className:"px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white text-sm",children:[(0,a.jsx)("option",{value:"all",children:"All Status"}),(0,a.jsx)("option",{value:"COMPLETED",children:"Completed"}),(0,a.jsx)("option",{value:"PENDING",children:"Pending"}),(0,a.jsx)("option",{value:"FAILED",children:"Failed"})]}),(0,a.jsxs)(x.$n,{onClick:()=>{let e=new Blob([["Date,From User,To User,Type,Original Amount,Commission Rate,Commission Amount,Status",...filteredCommissions.map(e=>[(0,F.r6)(e.createdAt),"".concat(e.fromUser.firstName," ").concat(e.fromUser.lastName," (").concat(e.fromUser.email,")"),"".concat(e.toUser.firstName," ").concat(e.toUser.lastName," (").concat(e.toUser.email,")"),e.type,e.originalAmount,"".concat(e.commissionRate,"%"),e.amount,e.status].join(","))].join("\n")],{type:"text/csv"}),s=window.URL.createObjectURL(e),t=document.createElement("a");t.href=s,t.download="referral-commissions-".concat(new Date().toISOString().split("T")[0],".csv"),t.click(),window.URL.revokeObjectURL(s)},className:"bg-green-600 hover:bg-green-700 text-white",children:[(0,a.jsx)(eh,{className:"h-4 w-4 mr-2"}),"Export"]})]})]}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full text-sm",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"border-b border-slate-600",children:[(0,a.jsx)("th",{className:"text-left py-3 px-4 text-slate-300",children:"Date"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 text-slate-300",children:"From → To"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 text-slate-300",children:"Type"}),(0,a.jsx)("th",{className:"text-right py-3 px-4 text-slate-300",children:"Original Amount"}),(0,a.jsx)("th",{className:"text-right py-3 px-4 text-slate-300",children:"Rate"}),(0,a.jsx)("th",{className:"text-right py-3 px-4 text-slate-300",children:"Commission"}),(0,a.jsx)("th",{className:"text-center py-3 px-4 text-slate-300",children:"Status"})]})}),(0,a.jsx)("tbody",{children:e.map(e=>(0,a.jsxs)("tr",{className:"border-b border-slate-700 hover:bg-slate-700/50",children:[(0,a.jsx)("td",{className:"py-3 px-4 text-slate-300",children:(0,F.r6)(e.createdAt)}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)("div",{className:"text-xs",children:[(0,a.jsxs)("div",{className:"text-white",children:[e.fromUser.firstName," ",e.fromUser.lastName]}),(0,a.jsx)("div",{className:"text-slate-400",children:e.fromUser.email})]}),(0,a.jsx)(ej.A,{className:"h-3 w-3 text-slate-400"}),(0,a.jsxs)("div",{className:"text-xs",children:[(0,a.jsxs)("div",{className:"text-white",children:[e.toUser.firstName," ",e.toUser.lastName]}),(0,a.jsx)("div",{className:"text-slate-400",children:e.toUser.email})]})]})}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsx)("span",{className:"px-2 py-1 rounded text-xs ".concat("DIRECT_REFERRAL"===e.type?"bg-blue-600/20 text-blue-400":"bg-green-600/20 text-green-400"),children:"DIRECT_REFERRAL"===e.type?"Referral":"Mining"})}),(0,a.jsx)("td",{className:"text-right py-3 px-4 text-white",children:(0,F.vv)(e.originalAmount)}),(0,a.jsxs)("td",{className:"text-right py-3 px-4 text-slate-300",children:[e.commissionRate,"%"]}),(0,a.jsx)("td",{className:"text-right py-3 px-4 text-green-400 font-medium",children:(0,F.vv)(e.amount)}),(0,a.jsx)("td",{className:"text-center py-3 px-4",children:(0,a.jsx)("span",{className:"px-2 py-1 rounded text-xs ".concat("COMPLETED"===e.status?"bg-green-600/20 text-green-400":"PENDING"===e.status?"bg-yellow-600/20 text-yellow-400":"bg-red-600/20 text-red-400"),children:e.status})})]},e.id))})]})}),0===e.length&&!i&&(0,a.jsx)("div",{className:"text-center py-8 text-slate-400",children:"No referral commissions found matching your criteria."}),A>1&&(0,a.jsxs)("div",{className:"flex items-center justify-between mt-6 pt-4 border-t border-slate-600",children:[(0,a.jsxs)("div",{className:"text-sm text-slate-400",children:["Showing ",(v-1)*20+1," to ",Math.min(20*v,S)," of ",S," results"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(x.$n,{onClick:()=>y(e=>Math.max(e-1,1)),disabled:1===v,className:"bg-slate-700 hover:bg-slate-600 text-white border-slate-600 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,a.jsx)(eN,{className:"h-4 w-4"}),"Previous"]}),(0,a.jsx)("div",{className:"flex items-center gap-1",children:Array.from({length:Math.min(5,A)},(e,s)=>{let t=Math.max(1,Math.min(A-4,v-2))+s;return t>A?null:(0,a.jsx)(x.$n,{onClick:()=>y(t),className:"w-10 h-10 ".concat(v===t?"bg-blue-600 hover:bg-blue-700 text-white":"bg-slate-700 hover:bg-slate-600 text-slate-300 border-slate-600"),children:t},t)})}),(0,a.jsxs)(x.$n,{onClick:()=>y(e=>Math.min(e+1,A)),disabled:v===A,className:"bg-slate-700 hover:bg-slate-600 text-white border-slate-600 disabled:opacity-50 disabled:cursor-not-allowed",children:["Next",(0,a.jsx)(eg,{className:"h-4 w-4"})]})]})]})]})]})]})};var ef=t(4869),ev=t(4229);let ew=(0,f.A)("percent",[["line",{x1:"19",x2:"5",y1:"5",y2:"19",key:"1x9vlm"}],["circle",{cx:"6.5",cy:"6.5",r:"2.5",key:"4mh3h7"}],["circle",{cx:"17.5",cy:"17.5",r:"2.5",key:"1mdrzq"}]]),ey=()=>{let[e,s]=(0,l.useState)(null),[t,r]=(0,l.useState)(!0),[i,n]=(0,l.useState)(!1),[c,d]=(0,l.useState)(!1),[o,m]=(0,l.useState)("pricing"),[h,u]=(0,l.useState)(!1),[j,g]=(0,l.useState)(!1),{showConfirm:b,ConfirmDialog:f}=(0,x.G_)();(0,l.useEffect)(()=>{y()},[]);let y=async()=>{try{r(!0);let e=await fetch("/api/admin/settings",{credentials:"include"});if(e.ok){let t=await e.json();t.success&&s(t.data)}}catch(e){console.error("Failed to fetch settings:",e)}finally{r(!1)}},A=async()=>{if(e)try{n(!0),console.log("Saving settings:",e),console.log("maxBinaryPointsPerSide value:",e.maxBinaryPointsPerSide,typeof e.maxBinaryPointsPerSide),console.log("Full settings object being sent:",JSON.stringify(e,null,2));let s=await fetch("/api/admin/settings",{method:"PUT",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(e)});if(s.ok)d(!0),setTimeout(()=>d(!1),3e3),console.log("Settings saved successfully");else{let e=await s.json();console.error("Failed to save settings:",e)}}catch(e){console.error("Failed to save settings:",e)}finally{n(!1)}},C=(t,a)=>{e&&s({...e,[t]:a})},S=(t,a,l)=>{if(!e)return;let r=[...e.earningsRanges];r[t]={...r[t],[a]:l},s({...e,earningsRanges:r})},E=()=>{e&&s({...e,earningsRanges:[...e.earningsRanges,{minTHS:0,maxTHS:100,dailyReturnMin:.5,dailyReturnMax:1,monthlyReturnMin:10,monthlyReturnMax:15}]})},k=t=>{if(!e)return;let a=e.earningsRanges.filter((e,s)=>s!==t);s({...e,earningsRanges:a})},T=async()=>{b({title:"Update Mining Units ROI",message:"Are you sure you want to update all existing mining units with the new ROI configuration? This will recalculate ROI for all active mining units based on their TH/s amounts.",variant:"warning",confirmText:"Update ROI",cancelText:"Cancel",darkMode:!0,onConfirm:async()=>{u(!0);try{let e=await fetch("/api/admin/update-mining-units-roi",{method:"POST",credentials:"include"}),s=await e.json();s.success?b({title:"ROI Update Completed",message:"Mining units ROI update completed successfully!\n\nUnits updated: ".concat(s.data.unitsUpdated,"\nTotal units: ").concat(s.data.totalUnits),variant:"success",confirmText:"OK",cancelText:"",darkMode:!0,onConfirm:()=>{}}):b({title:"Update Failed",message:"Error: ".concat(s.error),variant:"danger",confirmText:"OK",cancelText:"",darkMode:!0,onConfirm:()=>{}})}catch(e){console.error("Mining units ROI update error:",e),b({title:"Error",message:"Failed to update mining units ROI. Please try again.",variant:"danger",confirmText:"OK",cancelText:"",darkMode:!0,onConfirm:()=>{}})}finally{u(!1)}}})},P=()=>{b({title:"Manual Binary Matching",message:"Are you sure you want to manually trigger binary matching? This will process all users with binary points and distribute earnings.",variant:"warning",confirmText:"Process Matching",darkMode:!0,onConfirm:async()=>{g(!0);try{let e=await fetch("/api/admin/binary-matching/manual",{method:"POST",credentials:"include"}),s=await e.json();if(s.success){let e=s.data.matchingResults||[],t=s.data.usersProcessed||0,a=s.data.totalPayouts||0,l="Binary matching completed successfully!\n\n";l+="\uD83D\uDCCA SUMMARY:\n",l+="• Users processed: ".concat(t,"\n"),l+="• Total payouts: $".concat(a.toFixed(2),"\n"),l+="• Total matched points: ".concat(e.reduce((e,s)=>e+s.matchedPoints,0).toFixed(2),"\n\n"),e.length>0&&(l+="\uD83D\uDCB0 DETAILED RESULTS:\n",e.slice(0,10).forEach((e,s)=>{l+="".concat(s+1,". User ").concat(e.userId.substring(0,8),"...\n"),l+="   • Matched: ".concat(e.matchedPoints.toFixed(2)," points\n"),l+="   • Payout: $".concat(e.payout.toFixed(2),"\n"),l+="   • Remaining: L:".concat(e.remainingLeftPoints.toFixed(2)," | R:").concat(e.remainingRightPoints.toFixed(2),"\n\n")}),e.length>10&&(l+="... and ".concat(e.length-10," more users\n\n"))),l+="✅ All earnings have been credited to user wallets.",b({title:"Binary Matching Results",message:l,variant:"success",confirmText:"OK",cancelText:"",darkMode:!0,onConfirm:()=>{}})}else b({title:"Error",message:"Error: ".concat(s.error),variant:"danger",confirmText:"OK",cancelText:"",darkMode:!0,onConfirm:()=>{}})}catch(e){console.error("Manual binary matching error:",e),b({title:"Error",message:"Failed to process binary matching. Please try again.",variant:"danger",confirmText:"OK",cancelText:"",darkMode:!0,onConfirm:()=>{}})}finally{g(!1)}}})},R=[{id:"pricing",label:"Mining & Pricing",icon:N.A},{id:"earnings",label:"Earnings Config",icon:w.A},{id:"binary",label:"Binary Matching",icon:v},{id:"deposits",label:"Deposits",icon:H.A},{id:"withdrawals",label:"Withdrawals",icon:p.A},{id:"platform",label:"Platform",icon:ef.A}];return t?(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-8 bg-slate-700 rounded w-1/4 mb-4"}),(0,a.jsx)("div",{className:"h-64 bg-slate-700 rounded"})]})}):e?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsx)(x.$n,{onClick:A,loading:i,className:"flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white",children:c?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(I.A,{className:"h-4 w-4"}),"Saved"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(ev.A,{className:"h-4 w-4"}),"Save Changes"]})})}),(0,a.jsx)("div",{className:"border-b border-slate-700",children:(0,a.jsx)("nav",{className:"flex space-x-8",children:R.map(e=>{let s=e.icon;return(0,a.jsxs)("button",{onClick:()=>m(e.id),className:"flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ".concat(o===e.id?"border-blue-500 text-blue-400":"border-transparent text-slate-400 hover:text-slate-300 hover:border-slate-300"),children:[(0,a.jsx)(s,{className:"h-4 w-4"}),e.label]},e.id)})})}),function(){if(!e)return null;switch(o){case"pricing":return(0,a.jsxs)(x.Zp,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsx)(x.aR,{children:(0,a.jsxs)(x.ZB,{className:"flex items-center gap-2 text-white",children:[(0,a.jsx)(N.A,{className:"h-5 w-5 text-blue-400"}),"Mining Unit Pricing"]})}),(0,a.jsx)(x.Wu,{className:"space-y-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"THS Price (USD)"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(x.pd,{type:"number",step:"0.01",value:e.thsPriceUSD,onChange:e=>C("thsPriceUSD",parseFloat(e.target.value)||0),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Minimum Purchase ($)"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(x.pd,{type:"number",step:"0.01",value:e.minPurchaseAmount,onChange:e=>C("minPurchaseAmount",parseFloat(e.target.value)||0),placeholder:"50",className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Maximum Purchase ($)"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(x.pd,{type:"number",step:"0.01",value:e.maxPurchaseAmount,onChange:e=>C("maxPurchaseAmount",parseFloat(e.target.value)||0),placeholder:"100000",className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500"})]})]})]})})]});case"earnings":var s;return(0,a.jsxs)(x.Zp,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsx)(x.aR,{children:(0,a.jsxs)(x.ZB,{className:"flex items-center gap-2 text-white",children:[(0,a.jsx)(w.A,{className:"h-5 w-5 text-orange-400"}),"Dynamic Earnings Configuration"]})}),(0,a.jsxs)(x.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-white",children:"TH/s Based Daily Returns"}),(0,a.jsx)(x.$n,{onClick:E,size:"sm",className:"bg-orange-600 hover:bg-orange-700 text-white px-3 py-1.5 text-xs font-medium",children:"Add Range"})]}),(0,a.jsx)("div",{className:"space-y-3",children:null==(s=e.earningsRanges)?void 0:s.map((e,s)=>(0,a.jsxs)("div",{className:"p-3 bg-slate-700 rounded-lg border border-slate-600",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-3 mb-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs font-medium text-slate-300 mb-1",children:"Min TH/s"}),(0,a.jsx)(x.pd,{type:"number",step:"0.1",value:e.minTHS,onChange:e=>S(s,"minTHS",parseFloat(e.target.value)||0),className:"bg-slate-600 border-slate-500 text-white text-sm h-8"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs font-medium text-slate-300 mb-1",children:"Max TH/s"}),(0,a.jsx)(x.pd,{type:"number",step:"0.1",value:e.maxTHS,onChange:e=>S(s,"maxTHS",parseFloat(e.target.value)||0),className:"bg-slate-600 border-slate-500 text-white text-sm h-8"})]}),(0,a.jsx)("div",{className:"flex items-end",children:(0,a.jsx)(x.$n,{onClick:()=>k(s),size:"sm",className:"bg-red-600 hover:bg-red-700 text-white w-full px-2 py-1 text-xs font-medium h-8",children:"Remove"})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs font-medium text-slate-300 mb-1",children:"Min Daily Return (%)"}),(0,a.jsx)(x.pd,{type:"number",step:"0.01",value:e.dailyReturnMin,onChange:e=>S(s,"dailyReturnMin",parseFloat(e.target.value)||0),className:"bg-slate-600 border-slate-500 text-white text-sm h-8"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs font-medium text-slate-300 mb-1",children:"Max Daily Return (%)"}),(0,a.jsx)(x.pd,{type:"number",step:"0.01",value:e.dailyReturnMax,onChange:e=>S(s,"dailyReturnMax",parseFloat(e.target.value)||0),className:"bg-slate-600 border-slate-500 text-white text-sm h-8"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs font-medium text-slate-300 mb-1",children:"Min Monthly Return (%)"}),(0,a.jsx)(x.pd,{type:"number",step:"0.1",value:e.monthlyReturnMin,onChange:e=>S(s,"monthlyReturnMin",parseFloat(e.target.value)||0),className:"bg-slate-600 border-slate-500 text-white text-sm h-8"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs font-medium text-slate-300 mb-1",children:"Max Monthly Return (%)"}),(0,a.jsx)(x.pd,{type:"number",step:"0.1",value:e.monthlyReturnMax,onChange:e=>S(s,"monthlyReturnMax",parseFloat(e.target.value)||0),className:"bg-slate-600 border-slate-500 text-white text-sm h-8"})]})]})]},s))})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Binary Bonus (%)"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(ew,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(x.pd,{type:"number",step:"0.01",value:e.binaryBonusPercentage,onChange:e=>C("binaryBonusPercentage",parseFloat(e.target.value)||0),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-orange-500"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Referral Bonus (%)"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(ew,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(x.pd,{type:"number",step:"0.01",value:e.referralBonusPercentage,onChange:e=>C("referralBonusPercentage",parseFloat(e.target.value)||0),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-orange-500"})]})]})]}),(0,a.jsxs)("div",{className:"mt-4 p-4 bg-slate-700 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-slate-300 mb-2",children:"Update Existing Mining Units"}),(0,a.jsx)("p",{className:"text-xs text-slate-400",children:"Apply new ROI configuration to all active mining units"})]}),(0,a.jsx)(x.$n,{onClick:T,loading:h,className:"bg-orange-600 hover:bg-orange-700 text-white",children:"Update All Units"})]}),(0,a.jsx)("h4",{className:"text-sm font-medium text-slate-300 mb-2",children:"Important Notes:"}),(0,a.jsxs)("ul",{className:"text-xs text-slate-400 space-y-1",children:[(0,a.jsx)("li",{children:"• Changes to earnings configuration will affect new mining units automatically"}),(0,a.jsx)("li",{children:'• Use "Update All Units" button to apply changes to existing active mining units'}),(0,a.jsx)("li",{children:"• Monthly return limits are enforced to prevent excessive payouts"}),(0,a.jsx)("li",{children:"• TH/s ranges should not overlap for proper calculation"}),(0,a.jsx)("li",{children:"• Daily returns are randomly selected within the specified range for each unit"})]})]})]})]});case"binary":return(0,a.jsxs)(x.Zp,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsx)(x.aR,{children:(0,a.jsxs)(x.ZB,{className:"flex items-center gap-2 text-white",children:[(0,a.jsx)(v,{className:"h-5 w-5 text-purple-400"}),"Binary Matching Settings"]})}),(0,a.jsxs)(x.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Max Points Per Side"}),(0,a.jsx)(x.pd,{type:"number",value:e.maxBinaryPointsPerSide,onChange:e=>{let s=e.target.value,t=""===s?0:parseInt(s);!isNaN(t)&&t>=0&&C("maxBinaryPointsPerSide",t)},className:"bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-purple-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Point Value (USD)"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(x.pd,{type:"number",step:"0.01",value:e.binaryPointValue,onChange:e=>C("binaryPointValue",parseFloat(e.target.value)||0),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-purple-500"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Matching Schedule"}),(0,a.jsx)(x.pd,{type:"text",value:e.binaryMatchingSchedule,onChange:e=>C("binaryMatchingSchedule",e.target.value),placeholder:"Weekly at 15:00 UTC",className:"bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-purple-500"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between pt-4 border-t border-slate-600",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-white",children:"Binary Matching Enabled"}),(0,a.jsx)("p",{className:"text-sm text-slate-400",children:"Enable automatic binary point matching"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:e.binaryMatchingEnabled,onChange:e=>C("binaryMatchingEnabled",e.target.checked),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-slate-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"})]})]}),(0,a.jsxs)("div",{className:"mt-4 p-4 bg-slate-700 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-slate-300 mb-2",children:"Manual Binary Matching"}),(0,a.jsx)("p",{className:"text-xs text-slate-400",children:"Trigger binary matching process manually"})]}),(0,a.jsx)(x.$n,{onClick:P,loading:j,className:"bg-purple-600 hover:bg-purple-700 text-white",children:"Process Matching"})]}),(0,a.jsx)("h4",{className:"text-sm font-medium text-slate-300 mb-2",children:"Binary Matching Rules:"}),(0,a.jsxs)("ul",{className:"text-xs text-slate-400 space-y-1",children:[(0,a.jsx)("li",{children:"• Points are matched weekly at the scheduled time"}),(0,a.jsx)("li",{children:"• Matched points are paid out at the configured point value"}),(0,a.jsx)("li",{children:"• Excess points beyond the maximum are reset (pressure out)"}),(0,a.jsx)("li",{children:"• Only active users (with mining units) receive binary points"}),(0,a.jsx)("li",{children:"• Manual matching can be triggered anytime by admin"})]})]})]})]});case"deposits":return(0,a.jsxs)(x.Zp,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsx)(x.aR,{children:(0,a.jsxs)(x.ZB,{className:"flex items-center gap-2 text-white",children:[(0,a.jsx)(H.A,{className:"h-5 w-5 text-green-400"}),"Deposit Settings"]})}),(0,a.jsxs)(x.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-slate-700 p-4 rounded-lg border border-slate-600",children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-white mb-4 flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full ".concat("mainnet"===e.tronNetwork?"bg-green-500":"bg-orange-500"," animate-pulse")}),"Tron Network Configuration"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Active Network"}),(0,a.jsxs)("select",{value:e.tronNetwork,onChange:e=>C("tronNetwork",e.target.value),className:"w-full bg-slate-600 border-slate-500 text-white rounded-md px-3 py-2 focus:border-blue-500 focus:outline-none",children:[(0,a.jsx)("option",{value:"testnet",children:"Shasta Testnet"}),(0,a.jsx)("option",{value:"mainnet",children:"Mainnet"})]}),(0,a.jsx)("p",{className:"text-xs text-slate-400 mt-1",children:"mainnet"===e.tronNetwork?"Production network for real transactions":"Test network for development"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Current Network Status"}),(0,a.jsxs)("div",{className:"flex items-center gap-2 p-2 bg-slate-600 rounded-md",children:[(0,a.jsx)("div",{className:"w-2 h-2 rounded-full ".concat("mainnet"===e.tronNetwork?"bg-green-500":"bg-orange-500"," animate-pulse")}),(0,a.jsx)("span",{className:"text-sm text-white",children:"mainnet"===e.tronNetwork?"Mainnet Active":"Testnet Active"})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Mainnet API URL"}),(0,a.jsx)(x.pd,{type:"text",value:e.tronMainnetApiUrl,onChange:e=>C("tronMainnetApiUrl",e.target.value),className:"bg-slate-600 border-slate-500 text-white placeholder-slate-400 focus:border-blue-500 font-mono text-sm",placeholder:"https://api.trongrid.io"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Testnet API URL"}),(0,a.jsx)(x.pd,{type:"text",value:e.tronTestnetApiUrl,onChange:e=>C("tronTestnetApiUrl",e.target.value),className:"bg-slate-600 border-slate-500 text-white placeholder-slate-400 focus:border-blue-500 font-mono text-sm",placeholder:"https://api.shasta.trongrid.io"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Mainnet USDT Contract"}),(0,a.jsx)(x.pd,{type:"text",value:e.usdtMainnetContract,onChange:e=>C("usdtMainnetContract",e.target.value),className:"bg-slate-600 border-slate-500 text-white placeholder-slate-400 focus:border-blue-500 font-mono text-sm",placeholder:"TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Testnet USDT Contract"}),(0,a.jsx)(x.pd,{type:"text",value:e.usdtTestnetContract,onChange:e=>C("usdtTestnetContract",e.target.value),className:"bg-slate-600 border-slate-500 text-white placeholder-slate-400 focus:border-blue-500 font-mono text-sm",placeholder:"TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs"})]})]})]}),(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"USDT TRC20 Deposit Address"}),(0,a.jsx)(x.pd,{type:"text",value:e.usdtDepositAddress,onChange:e=>C("usdtDepositAddress",e.target.value),placeholder:"Enter USDT TRC20 address (e.g., TXXXxxxXXXxxxXXX...)",className:"bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-green-500 font-mono text-sm"}),(0,a.jsx)("p",{className:"text-xs text-slate-400 mt-1",children:"This address will be displayed to users for USDT deposits"})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Minimum Deposit"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(x.pd,{type:"number",step:"0.01",value:e.minDepositAmount,onChange:e=>C("minDepositAmount",parseFloat(e.target.value)||0),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-green-500"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Maximum Deposit"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(x.pd,{type:"number",step:"0.01",value:e.maxDepositAmount,onChange:e=>C("maxDepositAmount",parseFloat(e.target.value)||0),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-green-500"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Deposit Fee (%)"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(ew,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(x.pd,{type:"number",step:"0.01",value:e.depositFeePercentage,onChange:e=>C("depositFeePercentage",parseFloat(e.target.value)||0),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-green-500"})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Minimum Confirmations"}),(0,a.jsx)(x.pd,{type:"number",value:e.minConfirmations,onChange:e=>C("minConfirmations",parseInt(e.target.value)||0),className:"bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-green-500"}),(0,a.jsx)("p",{className:"text-xs text-slate-400 mt-1",children:"Number of blockchain confirmations required"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between pt-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-white",children:"Deposits Enabled"}),(0,a.jsx)("p",{className:"text-sm text-slate-400",children:"Allow users to make deposits"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:e.depositEnabled,onChange:e=>C("depositEnabled",e.target.checked),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-slate-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"})]})]})]})]})]});case"withdrawals":return(0,a.jsxs)(x.Zp,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsx)(x.aR,{children:(0,a.jsxs)(x.ZB,{className:"flex items-center gap-2 text-white",children:[(0,a.jsx)(p.A,{className:"h-5 w-5 text-red-400"}),"Withdrawal Settings"]})}),(0,a.jsxs)(x.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Minimum Withdrawal"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(x.pd,{type:"number",step:"0.01",value:e.minWithdrawalAmount,onChange:e=>C("minWithdrawalAmount",parseFloat(e.target.value)||0),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-red-500"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Fixed Fee ($)"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(x.pd,{type:"number",step:"0.01",value:e.withdrawalFeeFixed,onChange:e=>C("withdrawalFeeFixed",parseFloat(e.target.value)||0),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-red-500"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Percentage Fee (%)"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(ew,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(x.pd,{type:"number",step:"0.01",value:e.withdrawalFeePercentage,onChange:e=>C("withdrawalFeePercentage",parseFloat(e.target.value)||0),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-red-500"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Processing Days"}),(0,a.jsx)(x.pd,{type:"number",value:e.withdrawalProcessingDays,onChange:e=>C("withdrawalProcessingDays",parseInt(e.target.value)||0),className:"bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-red-500"})]})]}),(0,a.jsxs)("div",{className:"mt-4 p-4 bg-slate-700 rounded-lg",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-slate-300 mb-2",children:"Fee Calculation Example:"}),(0,a.jsxs)("p",{className:"text-xs text-slate-400",children:["For a $100 withdrawal: Fixed Fee ($",e.withdrawalFeeFixed,") + Percentage Fee ($",(100*(e.withdrawalFeePercentage/100)).toFixed(2),") = Total Fee: $",(e.withdrawalFeeFixed+100*(e.withdrawalFeePercentage/100)).toFixed(2)]}),(0,a.jsxs)("p",{className:"text-xs text-slate-400 mt-1",children:["User receives: $",(100-(e.withdrawalFeeFixed+100*(e.withdrawalFeePercentage/100))).toFixed(2)]})]})]})]});case"platform":return(0,a.jsxs)(x.Zp,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsx)(x.aR,{children:(0,a.jsxs)(x.ZB,{className:"flex items-center gap-2 text-white",children:[(0,a.jsx)(ef.A,{className:"h-5 w-5 text-blue-400"}),"Platform Settings"]})}),(0,a.jsxs)(x.Wu,{className:"space-y-4",children:[(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6"}),(0,a.jsxs)("div",{className:"space-y-4 pt-4 border-t border-slate-600",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-white",children:"Registration Enabled"}),(0,a.jsx)("p",{className:"text-sm text-slate-400",children:"Allow new user registrations"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:e.registrationEnabled,onChange:e=>C("registrationEnabled",e.target.checked),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-slate-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-white",children:"KYC Required"}),(0,a.jsx)("p",{className:"text-sm text-slate-400",children:"Require KYC verification for withdrawals"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:e.kycRequired,onChange:e=>C("kycRequired",e.target.checked),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-slate-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]})]})]})]});default:return null}}(),(0,a.jsx)(f,{})]}):(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(U.A,{className:"h-12 w-12 text-red-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-white mb-2",children:"Failed to Load Settings"}),(0,a.jsx)("p",{className:"text-slate-400",children:"Unable to load system settings. Please try again."})]})},eA=()=>{let[e,s]=(0,l.useState)([]),[t,r]=(0,l.useState)(!0),[i,n]=(0,l.useState)(""),[c,d]=(0,l.useState)("all"),[o,m]=(0,l.useState)("today"),[h,u]=(0,l.useState)(1),[p,j]=(0,l.useState)(1);(0,l.useEffect)(()=>{N()},[h,i,c,o]);let N=async()=>{try{r(!0);let e=new URLSearchParams({page:h.toString(),limit:"20",search:i,action:c,dateRange:o}),t=await fetch("/api/admin/logs?".concat(e),{credentials:"include"});if(t.ok){let e=await t.json();e.success&&(s(e.logs),j(e.totalPages))}}catch(e){console.error("Failed to fetch logs:",e)}finally{r(!1)}},g=async()=>{try{let e=new URLSearchParams({search:i,action:c,dateRange:o,export:"true"}),s=await fetch("/api/admin/logs/export?".concat(e),{credentials:"include"});if(s.ok){let e=await s.blob(),t=window.URL.createObjectURL(e),a=document.createElement("a");a.href=t,a.download="system-logs-".concat(new Date().toISOString().split("T")[0],".csv"),document.body.appendChild(a),a.click(),window.URL.revokeObjectURL(t),document.body.removeChild(a)}}catch(e){console.error("Failed to export logs:",e)}},b=e=>{switch(e){case"USER_LOGIN":case"USER_LOGOUT":return(0,a.jsx)(D.A,{className:"h-4 w-4 text-blue-400"});case"USER_REGISTERED":case"USER_REGISTER":return(0,a.jsx)(I.A,{className:"h-4 w-4 text-green-400"});case"MINING_UNIT_PURCHASED":case"MINING_PURCHASE":return(0,a.jsx)(eu,{className:"h-4 w-4 text-purple-400"});case"WITHDRAWAL_REQUESTED":case"WITHDRAWAL_REQUEST":case"WITHDRAWAL_APPROVED":case"WITHDRAWAL_REJECTED":return(0,a.jsx)(eu,{className:"h-4 w-4 text-orange-400"});case"DEPOSIT_VERIFICATION_ATTEMPT":case"DEPOSIT_CONFIRMED":case"DEPOSIT_FAILED":return(0,a.jsx)(eu,{className:"h-4 w-4 text-cyan-400"});case"KYC_SUBMITTED":case"KYC_SUBMIT":case"KYC_APPROVED":case"KYC_APPROVE":case"KYC_REJECTED":case"KYC_REJECT":return(0,a.jsx)(C.A,{className:"h-4 w-4 text-orange-400"});case"BINARY_MATCHING_PROCESSED":case"MANUAL_BINARY_MATCHING_TRIGGERED":case"BINARY_MATCHING_CRON_EXECUTED":return(0,a.jsx)(eu,{className:"h-4 w-4 text-green-400"});case"ADMIN_ACTION":case"WALLET_ADJUSTMENT":return(0,a.jsx)(U.A,{className:"h-4 w-4 text-red-400"});case"LOGIN_FAILED":case"CLIENT_ERROR":case"DATABASE_ERROR":case"AUTH_ERROR":return(0,a.jsx)(ea.A,{className:"h-4 w-4 text-red-400"});default:return(0,a.jsx)(ei.A,{className:"h-4 w-4 text-slate-400"})}},f=e=>{switch(e){case"USER_LOGIN":case"USER_REGISTERED":case"USER_REGISTER":case"KYC_APPROVED":case"KYC_APPROVE":case"DEPOSIT_CONFIRMED":case"WITHDRAWAL_APPROVED":case"BINARY_MATCHING_PROCESSED":case"MANUAL_BINARY_MATCHING_TRIGGERED":return"text-green-300 bg-green-900/20 border border-green-700";case"USER_LOGOUT":return"text-blue-300 bg-blue-900/20 border border-blue-700";case"MINING_UNIT_PURCHASED":case"MINING_PURCHASE":case"WITHDRAWAL_REQUESTED":case"WITHDRAWAL_REQUEST":return"text-purple-300 bg-purple-900/20 border border-purple-700";case"DEPOSIT_VERIFICATION_ATTEMPT":return"text-cyan-300 bg-cyan-900/20 border border-cyan-700";case"KYC_SUBMITTED":case"KYC_SUBMIT":return"text-orange-300 bg-orange-900/20 border border-orange-700";case"KYC_REJECTED":case"KYC_REJECT":case"WITHDRAWAL_REJECTED":case"ADMIN_ACTION":case"WALLET_ADJUSTMENT":case"LOGIN_FAILED":case"CLIENT_ERROR":case"DATABASE_ERROR":case"AUTH_ERROR":case"DEPOSIT_FAILED":return"text-red-300 bg-red-900/20 border border-red-700";default:return"text-slate-300 bg-slate-700 border border-slate-600"}},v=e=>{if(!e)return null;try{if("object"==typeof e)return e;if("string"==typeof e)return JSON.parse(e);return e}catch(s){return e}},w=e=>{let s=v(e);return s?"string"==typeof s?s:"object"==typeof s?"CREDIT"===s.type||"DEBIT"===s.type?(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-slate-400",children:"Type:"})," ",(0,a.jsx)("span",{className:"text-white",children:String(s.type)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-slate-400",children:"Amount:"})," ",(0,a.jsxs)("span",{className:"text-green-400",children:["$",String(s.amount)]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-slate-400",children:"Reason:"})," ",(0,a.jsx)("span",{className:"text-white",children:String(s.reason)})]}),s.description&&(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-slate-400",children:"Description:"})," ",(0,a.jsx)("span",{className:"text-white",children:String(s.description)})]})]}):s.targetUser&&"object"==typeof s.targetUser?(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-slate-400",children:"Type:"})," ",(0,a.jsx)("span",{className:"text-white",children:String(s.type)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-slate-400",children:"Amount:"})," ",(0,a.jsxs)("span",{className:"text-green-400",children:["$",String(s.amount)]})]}),s.reason&&(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-slate-400",children:"Reason:"})," ",(0,a.jsx)("span",{className:"text-white",children:String(s.reason)})]}),s.description&&(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-slate-400",children:"Description:"})," ",(0,a.jsx)("span",{className:"text-white",children:String(s.description)})]}),void 0!==s.previousBalance&&(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-slate-400",children:"Previous Balance:"})," ",(0,a.jsxs)("span",{className:"text-slate-300",children:["$",String(s.previousBalance)]})]}),void 0!==s.newBalance&&(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-slate-400",children:"New Balance:"})," ",(0,a.jsxs)("span",{className:"text-slate-300",children:["$",String(s.newBalance)]})]})]}):s.targetUserId||s.targetUserEmail?(0,a.jsxs)("div",{className:"space-y-1",children:[s.targetUserEmail&&(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-slate-400",children:"User:"})," ",(0,a.jsx)("span",{className:"text-white",children:String(s.targetUserEmail)})]}),s.targetUserId&&(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-slate-400",children:"User ID:"})," ",(0,a.jsx)("span",{className:"text-slate-300",children:String(s.targetUserId)})]}),s.amount&&(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-slate-400",children:"Amount:"})," ",(0,a.jsxs)("span",{className:"text-green-400",children:["$",String(s.amount)]})]}),s.reason&&(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-slate-400",children:"Reason:"})," ",(0,a.jsx)("span",{className:"text-white",children:String(s.reason)})]})]}):(0,a.jsx)("div",{className:"space-y-1",children:Object.entries(s).map(e=>{let s,[t,l]=e;if("targetUser"===t)return null;if(null==l)s="N/A";else if("object"==typeof l)try{s=JSON.stringify(l,null,2)}catch(e){s="[Complex Object]"}else s=String(l);return(0,a.jsxs)("div",{children:[(0,a.jsxs)("span",{className:"text-slate-400 capitalize",children:[t.replace(/([A-Z])/g," $1").toLowerCase(),":"]})," ",(0,a.jsx)("span",{className:"text-white whitespace-pre-wrap",children:s})]},t)}).filter(Boolean)}):String(s):"No details available"};return t?(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-8 bg-slate-700 rounded w-1/4 mb-4"}),(0,a.jsx)("div",{className:"h-64 bg-slate-700 rounded"})]})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsxs)(x.$n,{onClick:g,variant:"outline",className:"flex items-center gap-2 border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white",children:[(0,a.jsx)(eh,{className:"h-4 w-4"}),"Export Logs"]})}),(0,a.jsx)(x.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(x.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsx)("div",{children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(_.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(x.pd,{placeholder:"Search logs...",value:i,onChange:e=>n(e.target.value),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500"})]})}),(0,a.jsx)("div",{children:(0,a.jsxs)("select",{value:c,onChange:e=>d(e.target.value),className:"w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"all",children:"All Actions"}),(0,a.jsx)("option",{value:"USER_LOGIN",children:"User Login"}),(0,a.jsx)("option",{value:"USER_LOGOUT",children:"User Logout"}),(0,a.jsx)("option",{value:"USER_REGISTERED",children:"User Registration"}),(0,a.jsx)("option",{value:"MINING_UNIT_PURCHASED",children:"Mining Purchase"}),(0,a.jsx)("option",{value:"WITHDRAWAL_REQUESTED",children:"Withdrawal Request"}),(0,a.jsx)("option",{value:"WITHDRAWAL_APPROVED",children:"Withdrawal Approved"}),(0,a.jsx)("option",{value:"WITHDRAWAL_REJECTED",children:"Withdrawal Rejected"}),(0,a.jsx)("option",{value:"DEPOSIT_VERIFICATION_ATTEMPT",children:"Deposit Verification"}),(0,a.jsx)("option",{value:"DEPOSIT_CONFIRMED",children:"Deposit Confirmed"}),(0,a.jsx)("option",{value:"KYC_SUBMITTED",children:"KYC Submission"}),(0,a.jsx)("option",{value:"KYC_APPROVED",children:"KYC Approval"}),(0,a.jsx)("option",{value:"KYC_REJECTED",children:"KYC Rejection"}),(0,a.jsx)("option",{value:"BINARY_MATCHING_PROCESSED",children:"Binary Matching"}),(0,a.jsx)("option",{value:"MANUAL_BINARY_MATCHING_TRIGGERED",children:"Manual Binary Matching"}),(0,a.jsx)("option",{value:"ADMIN_ACTION",children:"Admin Actions"}),(0,a.jsx)("option",{value:"WALLET_ADJUSTMENT",children:"Wallet Adjustments"}),(0,a.jsx)("option",{value:"LOGIN_FAILED",children:"Failed Logins"}),(0,a.jsx)("option",{value:"CLIENT_ERROR",children:"Client Errors"})]})}),(0,a.jsx)("div",{children:(0,a.jsxs)("select",{value:o,onChange:e=>m(e.target.value),className:"w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"today",children:"Today"}),(0,a.jsx)("option",{value:"week",children:"This Week"}),(0,a.jsx)("option",{value:"month",children:"This Month"}),(0,a.jsx)("option",{value:"all",children:"All Time"})]})}),(0,a.jsxs)("div",{className:"text-sm text-slate-400 flex items-center",children:[(0,a.jsx)(eu,{className:"h-4 w-4 mr-1"}),e.length," logs found"]})]})})}),(0,a.jsxs)(x.Zp,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsx)(x.aR,{children:(0,a.jsxs)(x.ZB,{className:"flex items-center gap-2 text-white",children:[(0,a.jsx)(C.A,{className:"h-5 w-5 text-blue-400"}),"Activity Logs"]})}),(0,a.jsxs)(x.Wu,{children:[(0,a.jsx)("div",{className:"space-y-2",children:0===e.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(C.A,{className:"h-12 w-12 text-slate-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-white mb-2",children:"No Logs Found"}),(0,a.jsx)("p",{className:"text-slate-400",children:"No activity logs match your current filters."})]}):e.map(e=>(0,a.jsxs)("div",{className:"bg-slate-700 rounded-lg border border-slate-600 hover:border-slate-500 transition-all duration-200 hover:shadow-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-slate-600",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"p-2 rounded-full bg-slate-600",children:b(e.action)}),(0,a.jsx)("div",{children:(0,a.jsx)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ".concat(f(e.action)),children:e.action.replace(/_/g," ")})})]}),(0,a.jsx)("div",{className:"text-sm text-slate-400",children:(0,F.r6)(e.createdAt)})]}),(0,a.jsxs)("div",{className:"p-4 space-y-3",children:[e.user&&(0,a.jsxs)("div",{className:"flex items-center gap-2 p-2 bg-slate-800 rounded-lg",children:[(0,a.jsx)(D.A,{className:"h-4 w-4 text-blue-400"}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("span",{className:"font-medium text-white",children:[e.user.firstName," ",e.user.lastName]}),(0,a.jsxs)("span",{className:"text-slate-400 ml-2 text-sm",children:["(",e.user.email,")"]})]})]}),e.details&&(0,a.jsxs)("div",{className:"p-3 bg-slate-800 rounded-lg",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-slate-300 mb-2",children:"Details:"}),(0,a.jsx)("div",{className:"text-sm",children:w(e.details)})]}),(e.ipAddress||e.userAgent)&&(0,a.jsx)("div",{className:"p-2 bg-slate-800 rounded-lg",children:(0,a.jsxs)("div",{className:"text-xs text-slate-400 space-y-1",children:[e.ipAddress&&(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"font-medium",children:"IP Address:"}),(0,a.jsx)("span",{className:"text-slate-300",children:e.ipAddress})]}),e.userAgent&&(0,a.jsxs)("div",{className:"flex items-start gap-2",children:[(0,a.jsx)("span",{className:"font-medium whitespace-nowrap",children:"User Agent:"}),(0,a.jsx)("span",{className:"text-slate-300 break-all",children:e.userAgent})]})]})})]})]},e.id))}),p>1&&(0,a.jsxs)("div",{className:"flex items-center justify-between mt-6 pt-6 border-t border-slate-600",children:[(0,a.jsxs)("div",{className:"text-sm text-slate-400",children:["Page ",h," of ",p]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(x.$n,{variant:"outline",size:"sm",onClick:()=>u(e=>Math.max(1,e-1)),disabled:1===h,className:"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white disabled:opacity-50",children:"Previous"}),(0,a.jsx)(x.$n,{variant:"outline",size:"sm",onClick:()=>u(e=>Math.min(p,e+1)),disabled:h===p,className:"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white disabled:opacity-50",children:"Next"})]})]})]})]})]})};var eC=t(8749);let eS=(0,f.A)("test-tube",[["path",{d:"M14.5 2v17.5c0 1.4-1.1 2.5-2.5 2.5c-1.4 0-2.5-1.1-2.5-2.5V2",key:"125lnx"}],["path",{d:"M8.5 2h7",key:"csnxdl"}],["path",{d:"M14.5 16h-5",key:"1ox875"}]]),eE=(0,f.A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]),ek=(0,f.A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),eT=e=>{let{isOpen:s,onClose:t,template:r,onSave:i}=e,[n,c]=(0,l.useState)({name:"",subject:"",htmlContent:"",textContent:"",isActive:!0}),[d,o]=(0,l.useState)(!1),[m,h]=(0,l.useState)(!1),{showMessage:u}=(0,x.eC)();(0,l.useEffect)(()=>{r?c({name:r.name,subject:r.subject,htmlContent:r.htmlContent,textContent:r.textContent||"",isActive:r.isActive}):c({name:"",subject:"",htmlContent:"",textContent:"",isActive:!0})},[r]);let p=async()=>{if(!n.name||!n.subject||!n.htmlContent)return void u({title:"Validation Error",message:"Name, subject, and HTML content are required",variant:"error"});try{o(!0);let e=r?"/api/admin/email-templates/".concat(r.name):"/api/admin/email-templates",s=r?"PUT":"POST",a=await fetch(e,{method:s,headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(n)}),l=await a.json();if(l.success)u({title:"Success",message:r?"Template updated successfully":"Template created successfully",variant:"success"}),i(),t();else throw Error(l.error||"Failed to save template")}catch(e){console.error("Failed to save template:",e),u({title:"Error",message:e instanceof Error?e.message:"Failed to save template",variant:"error"})}finally{o(!1)}},j=(e,s)=>{c(t=>({...t,[e]:s}))};return s?(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-2 sm:p-4",children:(0,a.jsxs)("div",{className:"bg-slate-800 rounded-lg shadow-xl w-full max-w-6xl h-[95vh] sm:h-[90vh] overflow-hidden flex flex-col",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 sm:p-6 border-b border-slate-700 flex-shrink-0",children:[(0,a.jsx)("h2",{className:"text-lg sm:text-xl font-semibold text-white",children:r?"Edit Template":"Create Template"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(x.$n,{onClick:()=>h(!m),variant:"outline",size:"sm",className:"border-slate-600 text-slate-300 hidden sm:flex",children:[(0,a.jsx)(K.A,{className:"w-4 h-4 mr-2"}),m?"Edit":"Preview"]}),(0,a.jsx)(x.$n,{onClick:()=>h(!m),variant:"outline",size:"sm",className:"border-slate-600 text-slate-300 sm:hidden",children:(0,a.jsx)(K.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:t,className:"text-slate-400 hover:text-white",children:(0,a.jsx)(S.A,{className:"w-5 h-5"})})]})]}),(0,a.jsx)("div",{className:"p-4 sm:p-6 overflow-y-auto flex-1",children:m?(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsxs)("div",{className:"bg-slate-900 p-4 rounded-lg",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-white mb-2",children:"Preview"}),(0,a.jsxs)("div",{className:"border border-slate-600 rounded-lg overflow-hidden",children:[(0,a.jsx)("div",{className:"bg-slate-700 px-4 py-2 border-b border-slate-600",children:(0,a.jsxs)("p",{className:"text-sm text-slate-300",children:[(0,a.jsx)("strong",{children:"Subject:"})," ",n.subject]})}),(0,a.jsx)("div",{className:"p-4 bg-white",children:(0,a.jsx)("div",{dangerouslySetInnerHTML:{__html:n.htmlContent.replace(/\{\{(\w+)\}\}/g,'<span style="background: yellow; padding: 2px 4px; border-radius: 3px;">$1</span>')}})})]})]})}):(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Template Name *"}),(0,a.jsx)(x.pd,{value:n.name,onChange:e=>j("name",e.target.value),placeholder:"e.g., otp_verification, welcome_email",disabled:!!r,className:"bg-slate-700 border-slate-600 text-white"}),(0,a.jsx)("p",{className:"text-xs text-slate-500 mt-1",children:"Use lowercase with underscores (e.g., otp_verification)"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Subject *"}),(0,a.jsx)(x.pd,{value:n.subject,onChange:e=>j("subject",e.target.value),placeholder:"Email subject line",className:"bg-slate-700 border-slate-600 text-white"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"HTML Content *"}),(0,a.jsx)("textarea",{value:n.htmlContent,onChange:e=>j("htmlContent",e.target.value),placeholder:"HTML email content with variables like {{firstName}}, {{otp}}",rows:12,className:"w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white font-mono text-sm"}),(0,a.jsxs)("p",{className:"text-xs text-slate-500 mt-1",children:["Use variables: ","{{firstName}}, {{lastName}}, {{otp}}, {{email}}"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Text Content (Optional)"}),(0,a.jsx)("textarea",{value:n.textContent,onChange:e=>j("textContent",e.target.value),placeholder:"Plain text version of the email",rows:6,className:"w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white font-mono text-sm"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",id:"isActive",checked:n.isActive,onChange:e=>j("isActive",e.target.checked),className:"rounded border-slate-600 text-blue-600 focus:ring-blue-500"}),(0,a.jsx)("label",{htmlFor:"isActive",className:"text-sm text-slate-300",children:"Template is active"})]})]})}),(0,a.jsxs)("div",{className:"flex items-center justify-end gap-3 p-4 sm:p-6 border-t border-slate-700 flex-shrink-0",children:[(0,a.jsx)(x.$n,{onClick:t,variant:"outline",className:"border-slate-600 text-slate-300",children:"Cancel"}),(0,a.jsxs)(x.$n,{onClick:p,disabled:d,className:"bg-blue-600 hover:bg-blue-700 text-white",children:[d?(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}):(0,a.jsx)(ev.A,{className:"w-4 h-4 mr-2"}),r?"Update":"Create"," Template"]})]})]})}):null},eP=()=>{let[e,s]=(0,l.useState)("settings"),[t,r]=(0,l.useState)({smtpHost:"",smtpPort:587,smtpSecure:!1,smtpUser:"",smtpPassword:"",fromName:"HashCoreX",fromEmail:"",emailEnabled:!0}),[i,n]=(0,l.useState)([]),[c,d]=(0,l.useState)(!0),[o,m]=(0,l.useState)(!1),[h,p]=(0,l.useState)(!1),[j,N]=(0,l.useState)(!1),[g,b]=(0,l.useState)(!1),[f,v]=(0,l.useState)(""),[w,S]=(0,l.useState)(null),[E,k]=(0,l.useState)(!1),[T,P]=(0,l.useState)(!1),[R,D]=(0,l.useState)(!1),{showMessage:M,MessageBoxComponent:I}=(0,x.eC)();(0,l.useEffect)(()=>{U(),"templates"===e&&B()},[e]);let U=async()=>{try{d(!0);let e=await fetch("/api/admin/email-settings",{credentials:"include"});if(e.ok){let s=await e.json();if(s.success){let e=e=>"string"==typeof e?e.replace(/^["']|["']$/g,""):e||"",t={smtpHost:e(s.data.smtpHost),smtpPort:s.data.smtpPort||587,smtpSecure:!!s.data.smtpSecure,smtpUser:e(s.data.smtpUser),smtpPassword:e(s.data.smtpPassword),fromName:e(s.data.fromName)||"HashCoreX",fromEmail:e(s.data.fromEmail),emailEnabled:!!s.data.emailEnabled};r(t);let a=t.smtpHost&&t.smtpUser&&t.smtpPassword;P(a)}}}catch(e){console.error("Failed to fetch email settings:",e),M({title:"Error",message:"Failed to load email settings",variant:"error"})}finally{d(!1)}},L=async()=>{try{m(!0);let e=await fetch("/api/admin/email-settings",{method:"PUT",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(t)}),s=await e.json();if(s.success)M({title:"Success",message:"Email settings saved successfully",variant:"success"});else throw Error(s.error||"Failed to save settings")}catch(e){console.error("Failed to save email settings:",e),M({title:"Error",message:e instanceof Error?e.message:"Failed to save email settings",variant:"error"})}finally{m(!1)}},F=async()=>{try{p(!0);let e=await fetch("/api/admin/email-settings/test-connection",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include"}),s=await e.json();s.success&&s.connected?M({title:"Success",message:"SMTP connection successful! Your email settings are working correctly.",variant:"success"}):M({title:"Connection Failed",message:s.error||"SMTP connection failed. Please check your settings.",variant:"error"})}catch(e){console.error("Failed to test SMTP connection:",e),M({title:"Error",message:e instanceof Error?e.message:"Failed to test SMTP connection",variant:"error"})}finally{p(!1)}},O=async()=>{if(!f)return void M({title:"Error",message:"Please enter a test email address",variant:"error"});try{p(!0);let e=await fetch("/api/admin/email-settings",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({testEmail:f})}),s=await e.json();if(s.success)M({title:"Success",message:"Test email sent successfully! Check your inbox.",variant:"success"});else throw Error(s.error||"Failed to send test email")}catch(e){console.error("Failed to send test email:",e),M({title:"Error",message:e instanceof Error?e.message:"Failed to send test email",variant:"error"})}finally{p(!1)}},W=(e,s)=>{r(t=>({...t,[e]:s}))},B=async()=>{try{let e=await fetch("/api/admin/email-templates",{credentials:"include"}),s=await e.json();if(s.success)n(s.data);else throw Error(s.error||"Failed to fetch templates")}catch(e){console.error("Failed to fetch email templates:",e),M({title:"Error",message:"Failed to fetch email templates",variant:"error"})}},Z=()=>{S(null),k(!0)},_=e=>{S(e),k(!0)},H=async e=>{if(confirm("Are you sure you want to delete this template?"))try{let s=await fetch("/api/admin/email-templates/".concat(e),{method:"DELETE",credentials:"include"}),t=await s.json();if(t.success)M({title:"Success",message:"Template deleted successfully",variant:"success"}),B();else throw Error(t.error||"Failed to delete template")}catch(e){console.error("Failed to delete template:",e),M({title:"Error",message:e instanceof Error?e.message:"Failed to delete template",variant:"error"})}},$=()=>{T&&D(!0)},z=()=>{P(!1),D(!1),M({title:"SMTP Settings Unlocked",message:"You can now edit SMTP configuration. Remember to save your changes.",variant:"success"})},V=(e,s)=>{T?$():W(e,s)},Y=async()=>{try{N(!0);let e=await fetch("/api/admin/email-templates/seed",{method:"POST",credentials:"include"}),s=await e.json();if(s.success){let e=s.data.filter(e=>"created"===e.status).length,t=s.data.filter(e=>"exists"===e.status).length;M({title:"Success",message:"Templates seeded successfully! Created: ".concat(e,", Already existed: ").concat(t),variant:"success"}),B()}else throw Error(s.error||"Failed to seed templates")}catch(e){console.error("Failed to seed email templates:",e),M({title:"Error",message:"Failed to seed email templates",variant:"error"})}finally{N(!1)}};return c?(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(I,{}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-white",children:"Email Settings"}),(0,a.jsx)("p",{className:"text-slate-400 mt-1",children:"Configure SMTP settings and email templates"})]}),(0,a.jsx)("div",{className:"flex items-center space-x-3",children:(0,a.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",checked:t.emailEnabled,onChange:e=>W("emailEnabled",e.target.checked),className:"rounded border-slate-600 text-blue-600 focus:ring-blue-500"}),(0,a.jsx)("span",{className:"text-sm text-slate-300",children:"Email Enabled"})]})})]}),(0,a.jsxs)("div",{className:"flex space-x-1 bg-slate-800 p-1 rounded-lg",children:[(0,a.jsxs)("button",{onClick:()=>s("settings"),className:"flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors ".concat("settings"===e?"bg-blue-600 text-white":"text-slate-400 hover:text-white hover:bg-slate-700"),children:[(0,a.jsx)(A.A,{className:"w-4 h-4 mr-2"}),"SMTP Settings"]}),(0,a.jsxs)("button",{onClick:()=>s("templates"),className:"flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors ".concat("templates"===e?"bg-blue-600 text-white":"text-slate-400 hover:text-white hover:bg-slate-700"),children:[(0,a.jsx)(C.A,{className:"w-4 h-4 mr-2"}),"Email Templates"]})]}),"settings"===e&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(x.Zp,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsx)(x.aR,{children:(0,a.jsxs)(x.ZB,{className:"flex items-center justify-between text-white",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(A.A,{className:"h-5 w-5"}),"SMTP Configuration"]}),T&&(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(u.A,{className:"h-4 w-4 text-green-400"}),(0,a.jsx)("span",{className:"text-sm text-green-400",children:"Protected"})]})]})}),(0,a.jsxs)(x.Wu,{className:"space-y-4",children:[T&&(0,a.jsxs)("div",{className:"bg-blue-900/20 border border-blue-700 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-blue-300 mb-2",children:[(0,a.jsx)(u.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"font-medium",children:"SMTP Settings Protected"})]}),(0,a.jsx)("p",{className:"text-sm text-blue-200 mb-3",children:"SMTP configuration is locked to prevent accidental changes. Click on any field to unlock and edit."}),(0,a.jsx)(x.$n,{onClick:z,size:"sm",className:"bg-blue-600 hover:bg-blue-700 text-white",children:"Unlock SMTP Settings"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"SMTP Host *"}),(0,a.jsx)(x.pd,{type:"text",value:t.smtpHost,onChange:e=>V("smtpHost",e.target.value),onClick:$,placeholder:"smtp.gmail.com",className:"bg-slate-700 border-slate-600 text-white ".concat(T?"cursor-pointer":""),readOnly:T})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"SMTP Port"}),(0,a.jsx)(x.pd,{type:"number",value:t.smtpPort,onChange:e=>V("smtpPort",parseInt(e.target.value)),onClick:$,placeholder:"587",className:"bg-slate-700 border-slate-600 text-white ".concat(T?"cursor-pointer":""),readOnly:T})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"SMTP User *"}),(0,a.jsx)(x.pd,{type:"text",value:t.smtpUser,onChange:e=>V("smtpUser",e.target.value),onClick:$,placeholder:"<EMAIL>",className:"bg-slate-700 border-slate-600 text-white ".concat(T?"cursor-pointer":""),readOnly:T})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"SMTP Password *"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(x.pd,{type:g?"text":"password",value:t.smtpPassword,onChange:e=>V("smtpPassword",e.target.value),onClick:$,placeholder:"App password or SMTP password",className:"bg-slate-700 border-slate-600 text-white pr-10 ".concat(T?"cursor-pointer":""),readOnly:T}),(0,a.jsx)("button",{type:"button",onClick:()=>b(!g),className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:g?(0,a.jsx)(eC.A,{className:"h-4 w-4 text-slate-400"}):(0,a.jsx)(K.A,{className:"h-4 w-4 text-slate-400"})})]})]})]}),(0,a.jsx)("div",{className:"flex items-center space-x-4",children:(0,a.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",checked:t.smtpSecure,onChange:e=>W("smtpSecure",e.target.checked),className:"rounded border-slate-600 text-blue-600 focus:ring-blue-500"}),(0,a.jsx)("span",{className:"text-sm text-slate-300",children:"Use SSL/TLS"})]})})]})]}),(0,a.jsxs)(x.Zp,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsx)(x.aR,{children:(0,a.jsxs)(x.ZB,{className:"flex items-center gap-2 text-white",children:[(0,a.jsx)(y.A,{className:"h-5 w-5"}),"Email Configuration"]})}),(0,a.jsx)(x.Wu,{className:"space-y-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"From Name"}),(0,a.jsx)(x.pd,{type:"text",value:t.fromName,onChange:e=>W("fromName",e.target.value),placeholder:"HashCoreX",className:"bg-slate-700 border-slate-600 text-white"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"From Email *"}),(0,a.jsx)(x.pd,{type:"email",value:t.fromEmail,onChange:e=>W("fromEmail",e.target.value),placeholder:"<EMAIL>",className:"bg-slate-700 border-slate-600 text-white"})]})]})})]}),(0,a.jsxs)(x.Zp,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsx)(x.aR,{children:(0,a.jsxs)(x.ZB,{className:"flex items-center gap-2 text-white",children:[(0,a.jsx)(eS,{className:"h-5 w-5"}),"Test Email Configuration"]})}),(0,a.jsx)(x.Wu,{className:"space-y-4",children:(0,a.jsxs)("div",{className:"flex items-end space-x-4",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Test Email Address"}),(0,a.jsx)(x.pd,{type:"email",value:f,onChange:e=>v(e.target.value),placeholder:"<EMAIL>",className:"bg-slate-700 border-slate-600 text-white"})]}),(0,a.jsxs)(x.$n,{onClick:O,disabled:h||!f,variant:"outline",className:"border-blue-600 text-blue-400 hover:bg-blue-600 hover:text-white",children:[h?(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"}):(0,a.jsx)(ex.A,{className:"h-4 w-4 mr-2"}),"Send Test"]})]})})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,a.jsxs)(x.$n,{onClick:F,disabled:h||!t.smtpHost||!t.smtpUser,className:"bg-green-600 hover:bg-green-700 text-white",children:[h?(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}):(0,a.jsx)(eS,{className:"h-4 w-4 mr-2"}),"Test Connection"]}),(0,a.jsxs)(x.$n,{onClick:L,disabled:o,className:"bg-blue-600 hover:bg-blue-700 text-white",children:[o?(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}):(0,a.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Save Settings"]})]})]}),"templates"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-white",children:"Email Templates"}),(0,a.jsxs)("p",{className:"text-slate-400 text-sm",children:["Manage email templates with variables like ","{{firstName}}, {{otp}}, etc."]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)(x.$n,{onClick:Y,variant:"outline",className:"border-blue-600 text-blue-400 hover:bg-blue-900/20",loading:j,children:[(0,a.jsx)(C.A,{className:"w-4 h-4 mr-2"}),j?"Seeding...":"Seed Default Templates"]}),(0,a.jsxs)(x.$n,{onClick:Z,className:"bg-green-600 hover:bg-green-700 text-white",children:[(0,a.jsx)(G.A,{className:"w-4 h-4 mr-2"}),"New Template"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[i.map(e=>(0,a.jsx)(x.Zp,{className:"bg-slate-800 border-slate-700 hover:border-slate-600 transition-colors",children:(0,a.jsx)(x.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"flex items-start justify-between",children:(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"font-semibold text-white text-lg mb-2",children:e.name}),(0,a.jsx)("span",{className:"inline-flex px-3 py-1 rounded-full text-xs font-medium ".concat(e.isActive?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"),children:e.isActive?"Active":"Inactive"})]})}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-slate-300 text-sm font-medium",children:"Subject:"}),(0,a.jsx)("p",{className:"text-slate-400 text-sm line-clamp-2",children:e.subject})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-slate-300 text-sm font-medium",children:"Last Updated:"}),(0,a.jsx)("p",{className:"text-slate-500 text-xs",children:new Date(e.updatedAt).toLocaleDateString()})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 pt-2 border-t border-slate-700",children:[(0,a.jsxs)(x.$n,{onClick:()=>_(e),variant:"outline",size:"sm",className:"flex-1 border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white",children:[(0,a.jsx)(eE,{className:"w-4 h-4 mr-2"}),"Edit"]}),(0,a.jsx)(x.$n,{onClick:()=>H(e.name),variant:"outline",size:"sm",className:"border-red-600 text-red-400 hover:bg-red-900/20 hover:border-red-500",children:(0,a.jsx)(ek,{className:"w-4 h-4"})})]})]})})},e.id)),0===i.length&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(C.A,{className:"w-12 h-12 text-slate-600 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-slate-400 mb-2",children:"No templates found"}),(0,a.jsx)("p",{className:"text-slate-500 mb-4",children:"Create your first email template to get started."}),(0,a.jsxs)(x.$n,{onClick:Z,className:"bg-blue-600 hover:bg-blue-700 text-white",children:[(0,a.jsx)(G.A,{className:"w-4 h-4 mr-2"}),"Create Template"]})]})]})]}),(0,a.jsx)(eT,{isOpen:E,onClose:()=>k(!1),template:w,onSave:B}),R&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsx)("div",{className:"bg-slate-800 rounded-lg shadow-xl max-w-md w-full",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,a.jsx)(u.A,{className:"h-6 w-6 text-yellow-400"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Unlock SMTP Settings"})]}),(0,a.jsx)("p",{className:"text-slate-300 mb-6",children:"Are you sure you want to unlock SMTP configuration? This will allow editing of sensitive email settings."}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(x.$n,{onClick:()=>D(!1),variant:"outline",className:"flex-1 border-slate-600 text-slate-300",children:"Cancel"}),(0,a.jsx)(x.$n,{onClick:z,className:"flex-1 bg-yellow-600 hover:bg-yellow-700 text-white",children:"Unlock Settings"})]})]})})}),(0,a.jsx)(I,{})]})},eR=()=>{let[e,s]=(0,l.useState)([]),[t,r]=(0,l.useState)({totalActiveUnits:0,totalActiveTHS:0,totalInvestment:0,totalEarningsDistributed:0,averageDailyROI:0,activeUsers:0}),[i,n]=(0,l.useState)(!0),[c,d]=(0,l.useState)(""),[o,m]=(0,l.useState)("ALL"),[u,j]=(0,l.useState)(null),[b,f]=(0,l.useState)(!1);(0,l.useEffect)(()=>{v()},[c,o]);let v=async()=>{try{n(!0);let e=new URLSearchParams;c&&e.append("search",c),"ALL"!==o&&e.append("status",o);let a=await fetch("/api/admin/mining?".concat(e.toString()),{credentials:"include"});if(a.ok){let e=await a.json();s(e.units||[]),r(e.stats||t)}}catch(e){console.error("Failed to fetch mining data:",e)}finally{n(!1)}},y=e=>"ACTIVE"===e?"bg-green-100 text-green-800 border-green-200":"bg-gray-100 text-gray-800 border-gray-200",A=e=>e>=80?"bg-red-500":e>=60?"bg-yellow-500":"bg-green-500",C=e=>{j(e),f(!0)};return i?(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)(q.A,{className:"h-8 w-8 animate-spin text-blue-500"})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-white",children:"Mining Management"}),(0,a.jsx)("p",{className:"text-slate-400 mt-1",children:"Monitor and manage all mining units across the platform"})]}),(0,a.jsxs)(x.$n,{onClick:v,className:"flex items-center gap-2",children:[(0,a.jsx)(q.A,{className:"h-4 w-4"}),"Refresh"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4",children:[(0,a.jsx)(x.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(x.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-slate-400 text-sm",children:"Active Units"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:t.totalActiveUnits})]}),(0,a.jsx)(eu,{className:"h-8 w-8 text-green-500"})]})})}),(0,a.jsx)(x.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(x.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-slate-400 text-sm",children:"Total TH/s"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:(0,F.jI)(t.totalActiveTHS)})]}),(0,a.jsx)(N.A,{className:"h-8 w-8 text-yellow-500"})]})})}),(0,a.jsx)(x.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(x.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-slate-400 text-sm",children:"Total Investment"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:(0,F.vv)(t.totalInvestment)})]}),(0,a.jsx)(p.A,{className:"h-8 w-8 text-blue-500"})]})})}),(0,a.jsx)(x.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(x.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-slate-400 text-sm",children:"Earnings Paid"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:(0,F.vv)(t.totalEarningsDistributed)})]}),(0,a.jsx)(w.A,{className:"h-8 w-8 text-green-500"})]})})}),(0,a.jsx)(x.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(x.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-slate-400 text-sm",children:"Avg Daily ROI"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-white",children:[t.averageDailyROI.toFixed(2),"%"]})]}),(0,a.jsx)(g.A,{className:"h-8 w-8 text-purple-500"})]})})}),(0,a.jsx)(x.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(x.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-slate-400 text-sm",children:"Active Users"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:t.activeUsers})]}),(0,a.jsx)(h.A,{className:"h-8 w-8 text-indigo-500"})]})})})]}),(0,a.jsx)(x.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(x.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(_.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(x.pd,{placeholder:"Search by user name, email, or referral ID...",value:c,onChange:e=>d(e.target.value),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400"})]})}),(0,a.jsx)("div",{className:"sm:w-48",children:(0,a.jsxs)("select",{value:o,onChange:e=>m(e.target.value),className:"w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"ALL",children:"All Status"}),(0,a.jsx)("option",{value:"ACTIVE",children:"Active"}),(0,a.jsx)("option",{value:"EXPIRED",children:"Expired"})]})})]})})}),(0,a.jsxs)(x.Zp,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsx)(x.aR,{children:(0,a.jsxs)(x.ZB,{className:"text-white flex items-center gap-2",children:[(0,a.jsx)(N.A,{className:"h-5 w-5"}),"Mining Units (",e.length,")"]})}),(0,a.jsx)(x.Wu,{children:e.length>0?(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full text-sm",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"border-b border-slate-700",children:[(0,a.jsx)("th",{className:"text-left py-3 px-4 text-slate-300 font-medium",children:"User"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 text-slate-300 font-medium",children:"TH/s"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 text-slate-300 font-medium",children:"Investment"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 text-slate-300 font-medium",children:"Earned"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 text-slate-300 font-medium",children:"Daily ROI"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 text-slate-300 font-medium",children:"Progress"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 text-slate-300 font-medium",children:"Status"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 text-slate-300 font-medium",children:"Purchase Date"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 text-slate-300 font-medium",children:"Actions"})]})}),(0,a.jsx)("tbody",{children:e.map(e=>(0,a.jsxs)("tr",{className:"border-b border-slate-700 hover:bg-slate-700/50",children:[(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"text-white font-medium",children:[e.user.firstName," ",e.user.lastName]}),(0,a.jsx)("div",{className:"text-slate-400 text-xs",children:e.user.email}),(0,a.jsxs)("div",{className:"text-slate-400 text-xs",children:["ID: ",e.user.referralId]})]})}),(0,a.jsx)("td",{className:"py-3 px-4 text-white font-mono",children:(0,F.jI)(e.thsAmount)}),(0,a.jsx)("td",{className:"py-3 px-4 text-white",children:(0,F.vv)(e.investmentAmount)}),(0,a.jsx)("td",{className:"py-3 px-4 text-green-400",children:(0,F.vv)(e.miningEarnings)}),(0,a.jsxs)("td",{className:"py-3 px-4 text-white",children:[e.dailyROI.toFixed(2),"%"]}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"flex-1 bg-slate-700 rounded-full h-2",children:(0,a.jsx)("div",{className:"h-2 rounded-full ".concat(A(e.progressPercentage)),style:{width:"".concat(Math.min(e.progressPercentage,100),"%")}})}),(0,a.jsxs)("span",{className:"text-xs text-slate-400 w-12",children:[e.progressPercentage.toFixed(0),"%"]})]})}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium border ".concat(y(e.status)),children:e.status})}),(0,a.jsx)("td",{className:"py-3 px-4 text-slate-300 text-xs",children:(0,F.r6)(e.purchaseDate)}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsx)(x.$n,{variant:"outline",size:"sm",onClick:()=>C(e),className:"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white",children:(0,a.jsx)(K.A,{className:"h-4 w-4"})})})]},e.id))})]})}):(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(N.A,{className:"h-12 w-12 text-slate-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-white mb-2",children:"No Mining Units Found"}),(0,a.jsx)("p",{className:"text-slate-400",children:c||"ALL"!==o?"Try adjusting your search or filter criteria.":"No mining units have been purchased yet."})]})})]}),b&&u&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsx)("div",{className:"bg-slate-800 rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-white",children:"Mining Unit Details"}),(0,a.jsx)(x.$n,{variant:"outline",size:"sm",onClick:()=>{f(!1),j(null)},className:"border-slate-600 text-slate-300 hover:bg-slate-700",children:"\xd7"})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-slate-700 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"text-lg font-medium text-white mb-3",children:"User Information"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-slate-400 text-sm",children:"Name"}),(0,a.jsxs)("p",{className:"text-white",children:[u.user.firstName," ",u.user.lastName]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-slate-400 text-sm",children:"Email"}),(0,a.jsx)("p",{className:"text-white",children:u.user.email})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-slate-400 text-sm",children:"Referral ID"}),(0,a.jsx)("p",{className:"text-white",children:u.user.referralId})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-slate-400 text-sm",children:"User ID"}),(0,a.jsx)("p",{className:"text-white font-mono text-xs",children:u.userId})]})]})]}),(0,a.jsxs)("div",{className:"bg-slate-700 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"text-lg font-medium text-white mb-3",children:"Mining Unit Information"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-slate-400 text-sm",children:"TH/s Amount"}),(0,a.jsx)("p",{className:"text-white font-mono",children:(0,F.jI)(u.thsAmount)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-slate-400 text-sm",children:"Investment Amount"}),(0,a.jsx)("p",{className:"text-white",children:(0,F.vv)(u.investmentAmount)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-slate-400 text-sm",children:"Daily ROI"}),(0,a.jsxs)("p",{className:"text-white",children:[u.dailyROI.toFixed(2),"%"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-slate-400 text-sm",children:"Status"}),(0,a.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium border ".concat(y(u.status)),children:u.status})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-slate-400 text-sm",children:"Purchase Date"}),(0,a.jsx)("p",{className:"text-white",children:(0,F.r6)(u.purchaseDate)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-slate-400 text-sm",children:"Expiry Date"}),(0,a.jsx)("p",{className:"text-white",children:(0,F.r6)(u.expiryDate)})]})]})]}),(0,a.jsxs)("div",{className:"bg-slate-700 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"text-lg font-medium text-white mb-3",children:"Earnings Breakdown"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-slate-400 text-sm",children:"Mining Earnings"}),(0,a.jsx)("p",{className:"text-green-400 font-semibold",children:(0,F.vv)(u.miningEarnings)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-slate-400 text-sm",children:"Referral Earnings"}),(0,a.jsx)("p",{className:"text-blue-400 font-semibold",children:(0,F.vv)(u.referralEarnings)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-slate-400 text-sm",children:"Binary Earnings"}),(0,a.jsx)("p",{className:"text-purple-400 font-semibold",children:(0,F.vv)(u.binaryEarnings)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-slate-400 text-sm",children:"Total Earnings"}),(0,a.jsx)("p",{className:"text-yellow-400 font-semibold",children:(0,F.vv)(u.totalEarnings)})]})]})]}),(0,a.jsxs)("div",{className:"bg-slate-700 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"text-lg font-medium text-white mb-3",children:"Progress Information"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[(0,a.jsx)("span",{className:"text-slate-400",children:"Progress to 5x Return"}),(0,a.jsxs)("span",{className:"text-white",children:[u.progressPercentage.toFixed(1),"%"]})]}),(0,a.jsx)("div",{className:"w-full bg-slate-600 rounded-full h-2",children:(0,a.jsx)("div",{className:"h-2 rounded-full ".concat(A(u.progressPercentage)),style:{width:"".concat(Math.min(u.progressPercentage,100),"%")}})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-slate-400",children:"Maximum Earnings (5x)"}),(0,a.jsx)("p",{className:"text-white",children:(0,F.vv)(5*u.investmentAmount)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-slate-400",children:"Remaining Capacity"}),(0,a.jsx)("p",{className:"text-white",children:(0,F.vv)(Math.max(0,5*u.investmentAmount-u.totalEarnings))})]})]})]})]}),(0,a.jsxs)("div",{className:"bg-slate-700 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"text-lg font-medium text-white mb-3",children:"Technical Information"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-slate-400 text-sm",children:"Mining Unit ID"}),(0,a.jsx)("p",{className:"text-white font-mono text-xs break-all",children:u.id})]})]})]})]})})})]})},eD=()=>{let[e,s]=(0,l.useState)([]),[t,r]=(0,l.useState)({totalUsers:0,usersWithDiscrepancies:0,totalBalanceMismatch:0,totalTransactions:0,auditedUsers:0}),[i,n]=(0,l.useState)(!0),[c,d]=(0,l.useState)(""),[o,m]=(0,l.useState)(!1),[u,j]=(0,l.useState)(null),[N,b]=(0,l.useState)(!1),[f,v]=(0,l.useState)(!1),[w,y]=(0,l.useState)({startDate:"",endDate:""});(0,l.useEffect)(()=>{A()},[c,o,w]);let A=async()=>{try{n(!0);let e=new URLSearchParams;c&&e.append("search",c),o&&e.append("discrepancies","true"),w.startDate&&e.append("startDate",w.startDate),w.endDate&&e.append("endDate",w.endDate);let a=await fetch("/api/admin/account-audit?".concat(e.toString()),{credentials:"include"});if(a.ok){let e=await a.json();e.success&&(s(e.data.users||[]),r(e.data.stats||t))}}catch(e){console.error("Failed to fetch audit data:",e)}finally{n(!1)}},C=async e=>{try{v(!0);let s=new URLSearchParams;e&&s.append("userId",e),w.startDate&&s.append("startDate",w.startDate),w.endDate&&s.append("endDate",w.endDate);let t=await fetch("/api/admin/account-audit/export?".concat(s.toString()),{credentials:"include"});if(t.ok){let s=await t.blob(),a=window.URL.createObjectURL(s),l=document.createElement("a");l.style.display="none",l.href=a,l.download=e?"user-audit-".concat(e,"-").concat(new Date().toISOString().split("T")[0],".csv"):"account-audit-".concat(new Date().toISOString().split("T")[0],".csv"),document.body.appendChild(l),l.click(),window.URL.revokeObjectURL(a),document.body.removeChild(l)}}catch(e){console.error("Failed to export audit data:",e)}finally{v(!1)}},S=e=>{j(e),b(!0)},E=e.filter(e=>{let s=!c||e.user.firstName.toLowerCase().includes(c.toLowerCase())||e.user.lastName.toLowerCase().includes(c.toLowerCase())||e.user.email.toLowerCase().includes(c.toLowerCase())||e.user.referralId.toLowerCase().includes(c.toLowerCase()),t=!o||e.hasDiscrepancies;return s&&t});return i?(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)(q.A,{className:"h-8 w-8 animate-spin text-blue-500"})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-white",children:"Account Audit"}),(0,a.jsx)("p",{className:"text-slate-400",children:"Monitor and audit user wallet activities and transaction balances"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)(x.$n,{onClick:()=>C(),disabled:f,className:"bg-green-600 hover:bg-green-700",children:[f?(0,a.jsx)(q.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,a.jsx)(eh,{className:"h-4 w-4 mr-2"}),"Export All"]}),(0,a.jsxs)(x.$n,{onClick:A,variant:"outline",className:"border-slate-600 text-slate-300 hover:bg-slate-700",children:[(0,a.jsx)(q.A,{className:"h-4 w-4 mr-2"}),"Refresh"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6",children:[(0,a.jsx)(x.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(x.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-slate-400",children:"Total Users"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:t.totalUsers})]}),(0,a.jsx)(h.A,{className:"h-8 w-8 text-blue-400"})]})})}),(0,a.jsx)(x.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(x.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-slate-400",children:"With Discrepancies"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-red-400",children:t.usersWithDiscrepancies})]}),(0,a.jsx)(U.A,{className:"h-8 w-8 text-red-400"})]})})}),(0,a.jsx)(x.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(x.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-slate-400",children:"Balance Mismatch"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-yellow-400",children:(0,F.vv)(Math.abs(t.totalBalanceMismatch))})]}),(0,a.jsx)(p.A,{className:"h-8 w-8 text-yellow-400"})]})})}),(0,a.jsx)(x.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(x.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-slate-400",children:"Total Transactions"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-green-400",children:t.totalTransactions})]}),(0,a.jsx)(g.A,{className:"h-8 w-8 text-green-400"})]})})}),(0,a.jsx)(x.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(x.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-slate-400",children:"Audited Users"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-purple-400",children:t.auditedUsers})]}),(0,a.jsx)(I.A,{className:"h-8 w-8 text-purple-400"})]})})})]}),(0,a.jsx)(x.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(x.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Search Users"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(_.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400"}),(0,a.jsx)(x.pd,{value:c,onChange:e=>d(e.target.value),placeholder:"Name, email, or referral ID...",className:"pl-10 bg-slate-700 border-slate-600 text-white"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Start Date"}),(0,a.jsx)(x.pd,{type:"date",value:w.startDate,onChange:e=>y(s=>({...s,startDate:e.target.value})),className:"bg-slate-700 border-slate-600 text-white"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"End Date"}),(0,a.jsx)(x.pd,{type:"date",value:w.endDate,onChange:e=>y(s=>({...s,endDate:e.target.value})),className:"bg-slate-700 border-slate-600 text-white"})]}),(0,a.jsx)("div",{className:"flex items-end",children:(0,a.jsxs)(x.$n,{onClick:()=>m(!o),variant:o?"default":"outline",className:o?"bg-red-600 hover:bg-red-700":"border-slate-600 text-slate-300 hover:bg-slate-700",children:[(0,a.jsx)(el.A,{className:"h-4 w-4 mr-2"}),o?"Show All":"Show Discrepancies"]})})]})})}),(0,a.jsxs)(x.Zp,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsx)(x.aR,{children:(0,a.jsxs)(x.ZB,{className:"text-white flex items-center",children:[(0,a.jsx)(g.A,{className:"h-5 w-5 mr-2"}),"Account Audit Results (",E.length," users)"]})}),(0,a.jsx)(x.Wu,{children:E.length>0?(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"border-b border-slate-700",children:[(0,a.jsx)("th",{className:"text-left py-3 px-4 text-slate-300 font-medium",children:"User"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 text-slate-300 font-medium",children:"Current Balance"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 text-slate-300 font-medium",children:"Calculated Balance"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 text-slate-300 font-medium",children:"Mismatch"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 text-slate-300 font-medium",children:"Transactions"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 text-slate-300 font-medium",children:"Last Activity"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 text-slate-300 font-medium",children:"Status"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 text-slate-300 font-medium",children:"Actions"})]})}),(0,a.jsx)("tbody",{children:E.map(e=>(0,a.jsxs)("tr",{className:"border-b border-slate-700 hover:bg-slate-700/50",children:[(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"text-white font-medium",children:[e.user.firstName," ",e.user.lastName]}),(0,a.jsx)("div",{className:"text-slate-400 text-sm",children:e.user.email}),(0,a.jsxs)("div",{className:"text-slate-500 text-xs",children:["ID: ",e.user.referralId]})]})}),(0,a.jsx)("td",{className:"py-3 px-4 text-white font-mono",children:(0,F.vv)(e.currentBalance)}),(0,a.jsx)("td",{className:"py-3 px-4 text-white font-mono",children:(0,F.vv)(e.calculatedBalance)}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("span",{className:"font-mono ".concat(.01>Math.abs(e.balanceMismatch)?"text-green-400":"text-red-400 font-bold"),children:[e.balanceMismatch>0?"+":"",(0,F.vv)(e.balanceMismatch)]})}),(0,a.jsx)("td",{className:"py-3 px-4 text-slate-300",children:e.transactionCount}),(0,a.jsx)("td",{className:"py-3 px-4 text-slate-300 text-sm",children:(0,F.r6)(e.lastActivity)}),(0,a.jsx)("td",{className:"py-3 px-4",children:e.hasDiscrepancies?(0,a.jsxs)("span",{className:"px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 border border-red-200",children:[(0,a.jsx)(U.A,{className:"h-3 w-3 inline mr-1"}),"Discrepancy"]}):(0,a.jsxs)("span",{className:"px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200",children:[(0,a.jsx)(I.A,{className:"h-3 w-3 inline mr-1"}),"Balanced"]})}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(x.$n,{variant:"outline",size:"sm",onClick:()=>S(e),className:"border-slate-600 text-slate-300 hover:bg-slate-700",children:(0,a.jsx)(K.A,{className:"h-4 w-4"})}),(0,a.jsx)(x.$n,{variant:"outline",size:"sm",onClick:()=>C(e.userId),disabled:f,className:"border-slate-600 text-slate-300 hover:bg-slate-700",children:(0,a.jsx)(eh,{className:"h-4 w-4"})})]})})]},e.userId))})]})}):(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(g.A,{className:"h-12 w-12 text-slate-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-white mb-2",children:"No Audit Data Found"}),(0,a.jsx)("p",{className:"text-slate-400",children:c||o?"Try adjusting your search or filter criteria.":"No users found for audit analysis."})]})})]}),N&&u&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsx)("div",{className:"bg-slate-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("h3",{className:"text-xl font-semibold text-white",children:["Account Audit Details - ",u.user.firstName," ",u.user.lastName]}),(0,a.jsx)(x.$n,{variant:"outline",size:"sm",onClick:()=>b(!1),className:"border-slate-600 text-slate-300 hover:bg-slate-700",children:"\xd7"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,a.jsx)(x.Zp,{className:"bg-slate-700",children:(0,a.jsxs)(x.Wu,{className:"p-4",children:[(0,a.jsx)("h4",{className:"text-lg font-medium text-white mb-3",children:"User Information"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-slate-400",children:"Name:"}),(0,a.jsxs)("span",{className:"text-white",children:[u.user.firstName," ",u.user.lastName]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-slate-400",children:"Email:"}),(0,a.jsx)("span",{className:"text-white",children:u.user.email})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-slate-400",children:"Referral ID:"}),(0,a.jsx)("span",{className:"text-white",children:u.user.referralId})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-slate-400",children:"User ID:"}),(0,a.jsx)("span",{className:"text-white font-mono text-xs",children:u.userId})]})]})]})}),(0,a.jsx)(x.Zp,{className:"bg-slate-700",children:(0,a.jsxs)(x.Wu,{className:"p-4",children:[(0,a.jsx)("h4",{className:"text-lg font-medium text-white mb-3",children:"Balance Analysis"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-slate-400",children:"Current Balance:"}),(0,a.jsx)("span",{className:"text-white font-mono",children:(0,F.vv)(u.currentBalance)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-slate-400",children:"Calculated Balance:"}),(0,a.jsx)("span",{className:"text-white font-mono",children:(0,F.vv)(u.calculatedBalance)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-slate-400",children:"Mismatch:"}),(0,a.jsxs)("span",{className:"font-mono font-bold ".concat(.01>Math.abs(u.balanceMismatch)?"text-green-400":"text-red-400"),children:[u.balanceMismatch>0?"+":"",(0,F.vv)(u.balanceMismatch)]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-slate-400",children:"Status:"}),(0,a.jsx)("span",{className:u.hasDiscrepancies?"text-red-400":"text-green-400",children:u.hasDiscrepancies?"Has Discrepancies":"Balanced"})]})]})]})})]}),(0,a.jsx)(x.Zp,{className:"bg-slate-700",children:(0,a.jsxs)(x.Wu,{className:"p-4",children:[(0,a.jsx)("h4",{className:"text-lg font-medium text-white mb-3",children:"Transaction Summary"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-xl font-bold text-green-400",children:(0,F.vv)(u.totalDeposits)}),(0,a.jsx)("div",{className:"text-xs text-slate-400",children:"Deposits"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-xl font-bold text-red-400",children:(0,F.vv)(u.totalWithdrawals)}),(0,a.jsx)("div",{className:"text-xs text-slate-400",children:"Withdrawals"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-xl font-bold text-blue-400",children:(0,F.vv)(u.totalEarnings)}),(0,a.jsx)("div",{className:"text-xs text-slate-400",children:"Earnings"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-xl font-bold text-orange-400",children:(0,F.vv)(u.totalPurchases)}),(0,a.jsx)("div",{className:"text-xs text-slate-400",children:"Purchases"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-xl font-bold text-cyan-400",children:(0,F.vv)(u.adminCredits)}),(0,a.jsx)("div",{className:"text-xs text-slate-400",children:"Admin Credits"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-xl font-bold text-pink-400",children:(0,F.vv)(u.adminDebits)}),(0,a.jsx)("div",{className:"text-xs text-slate-400",children:"Admin Debits"})]})]}),(0,a.jsx)("div",{className:"mt-4 pt-4 border-t border-slate-600",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-400",children:u.transactionCount}),(0,a.jsx)("div",{className:"text-sm text-slate-400",children:"Total Transactions"})]})})]})}),(0,a.jsx)("div",{className:"flex justify-end space-x-3 mt-6",children:(0,a.jsxs)(x.$n,{onClick:()=>C(u.userId),disabled:f,className:"bg-green-600 hover:bg-green-700",children:[f?(0,a.jsx)(q.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,a.jsx)(eh,{className:"h-4 w-4 mr-2"}),"Export User Audit"]})})]})})})]})};function eM(){let{user:e,loading:s}=(0,r.A)(),t=(0,i.useRouter)(),[n,c]=(0,l.useState)("dashboard"),[d,o]=(0,l.useState)(null),[m,h]=(0,l.useState)(!0);return((0,l.useEffect)(()=>{let t=async()=>{if(!s&&e)try{let e=await fetch("/api/admin/check",{credentials:"include"});if(e.ok){let s=await e.json();o(s.isAdmin)}else o(!1)}catch(e){console.error("Error checking admin status:",e),o(!1)}finally{h(!1)}else s||e||(h(!1),o(!1))};s||t()},[e,s]),(0,l.useEffect)(()=>{s||e||t.push("/login")},[e,s,t]),(0,l.useEffect)(()=>{m||s||!e||!1!==d||t.push("/dashboard")},[d,m,e,s,t]),s||m)?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)(x.Rh,{size:"lg",text:"Loading admin panel..."})}):e&&!1!==d?(0,a.jsx)(M,{activeTab:n,onTabChange:c,children:(()=>{switch(n){case"dashboard":default:return(0,a.jsx)(O,{onTabChange:c});case"users":return(0,a.jsx)(J,{});case"kyc":return(0,a.jsx)(es,{});case"deposits":return(0,a.jsx)(en,{});case"withdrawals":return(0,a.jsx)(ec,{});case"mining-management":return(0,a.jsx)(eR,{});case"account-audit":return(0,a.jsx)(eD,{});case"support":return(0,a.jsx)(eo,{});case"binary-points":return(0,a.jsx)(ep,{});case"referral-commissions":return(0,a.jsx)(eb,{});case"email-settings":return(0,a.jsx)(eP,{});case"settings":return(0,a.jsx)(ey,{});case"logs":return(0,a.jsx)(eA,{})}})()}):null}}},e=>{var s=s=>e(e.s=s);e.O(0,[36,4641,905,4279,8441,1684,7358],()=>s(1256)),_N_E=e.O()}]);