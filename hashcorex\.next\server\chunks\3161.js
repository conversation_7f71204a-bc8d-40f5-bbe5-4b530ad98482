"use strict";exports.id=3161,exports.ids=[1111,3161],exports.modules={13161:(e,t,a)=>{a.d(t,{emailNotificationService:()=>s});var i=a(21111),r=a(6710);class n{async sendDepositSuccessNotification(e){try{let t=await i.emailService.getEmailTemplate("deposit_success");t||console.warn('Email template "deposit_success" not found. Using default template.');let a=t?.subject||"Deposit Confirmed - HashCoreX",n=t?.htmlContent||this.getDefaultDepositSuccessTemplate(),s=t?.textContent||"";n=this.replaceVariables(n,{firstName:e.firstName,lastName:e.lastName,email:e.email,amount:e.amount.toString(),transactionId:e.transactionId,currency:e.currency}),s=this.replaceVariables(s,{firstName:e.firstName,lastName:e.lastName,email:e.email,amount:e.amount.toString(),transactionId:e.transactionId,currency:e.currency});let o=await r.XB.create({to:e.email,subject:a,template:"deposit_success",status:"PENDING"});if(await i.emailService.sendEmail({to:e.email,subject:a,html:n,text:s||`Deposit Confirmed

Amount: ${e.amount} ${e.currency}
Transaction ID: ${e.transactionId}`}))return await r.XB.updateStatus(o.id,"SENT"),!0;return await r.XB.updateStatus(o.id,"FAILED","Email service error"),!1}catch(e){return console.error("Error sending deposit success notification:",e),!1}}async sendKYCStatusNotification(e){try{let t="APPROVED"===e.status?"kyc_approved":"kyc_rejected",a=await i.emailService.getEmailTemplate(t);a||console.warn(`Email template "${t}" not found. Using default template.`);let n=a?.subject||`KYC ${"APPROVED"===e.status?"Approved":"Rejected"} - HashCoreX`,s=a?.htmlContent||this.getDefaultKYCTemplate(e.status),o=a?.textContent||"";s=this.replaceVariables(s,{firstName:e.firstName,lastName:e.lastName,email:e.email,status:e.status,rejectionReason:e.rejectionReason||""}),o=this.replaceVariables(o,{firstName:e.firstName,lastName:e.lastName,email:e.email,status:e.status,rejectionReason:e.rejectionReason||""});let l=await r.XB.create({to:e.email,subject:n,template:t,status:"PENDING"});if(await i.emailService.sendEmail({to:e.email,subject:n,html:s,text:o||`KYC ${e.status}

${e.rejectionReason?`Reason: ${e.rejectionReason}`:"Your KYC has been approved."}`}))return await r.XB.updateStatus(l.id,"SENT"),!0;return await r.XB.updateStatus(l.id,"FAILED","Email service error"),!1}catch(e){return console.error("Error sending KYC status notification:",e),!1}}async sendWithdrawalStatusNotification(e){try{let t=this.getWithdrawalTemplateName(e.status),a=await i.emailService.getEmailTemplate(t);a||console.warn(`Email template "${t}" not found. Using default template.`);let n=a?.subject||`Withdrawal ${this.getWithdrawalStatusText(e.status)} - HashCoreX`,s=a?.htmlContent||this.getDefaultWithdrawalTemplate(e.status),o=a?.textContent||"";s=this.replaceVariables(s,{firstName:e.firstName,lastName:e.lastName,email:e.email,amount:e.amount.toString(),status:e.status,transactionHash:e.transactionHash||"",rejectionReason:e.rejectionReason||"",usdtAddress:e.usdtAddress||""}),o=this.replaceVariables(o,{firstName:e.firstName,lastName:e.lastName,email:e.email,amount:e.amount.toString(),status:e.status,transactionHash:e.transactionHash||"",rejectionReason:e.rejectionReason||"",usdtAddress:e.usdtAddress||""});let l=await r.XB.create({to:e.email,subject:n,template:t,status:"PENDING"});if(await i.emailService.sendEmail({to:e.email,subject:n,html:s,text:o||this.getDefaultWithdrawalText(e)}))return await r.XB.updateStatus(l.id,"SENT"),!0;return await r.XB.updateStatus(l.id,"FAILED","Email service error"),!1}catch(e){return console.error("Error sending withdrawal status notification:",e),!1}}replaceVariables(e,t){let a=e;for(let[e,i]of Object.entries(t)){let t=RegExp(`{{${e}}}`,"g");a=a.replace(t,i)}return a}getWithdrawalTemplateName(e){switch(e){case"APPROVED":return"withdrawal_approved";case"REJECTED":return"withdrawal_rejected";case"COMPLETED":return"withdrawal_completed";case"FAILED":return"withdrawal_failed";default:return"withdrawal_status"}}getWithdrawalStatusText(e){switch(e){case"APPROVED":return"Approved";case"REJECTED":return"Rejected";case"COMPLETED":return"Completed";case"FAILED":return"Failed";default:return"Updated"}}getDefaultDepositSuccessTemplate(){return`
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #ffd60a 0%, #10b981 100%); padding: 20px; text-align: center;">
          <h1 style="color: white; margin: 0;">HashCoreX</h1>
        </div>
        <div style="padding: 30px; background: #f9f9f9;">
          <h2 style="color: #333;">Deposit Confirmed!</h2>
          <p>Hello {{firstName}},</p>
          <p>Great news! Your deposit has been successfully confirmed and credited to your account.</p>
          <div style="background: white; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #10b981;">
            <h3 style="margin: 0 0 10px 0; color: #10b981;">Deposit Details</h3>
            <p><strong>Amount:</strong> {{amount}} {{currency}}</p>
            <p><strong>Transaction ID:</strong> {{transactionId}}</p>
            <p><strong>Status:</strong> Confirmed</p>
          </div>
          <p>Your funds are now available in your wallet and you can start using them immediately.</p>
          <p>Best regards,<br>The HashCoreX Team</p>
        </div>
        <div style="background: #333; color: white; padding: 20px; text-align: center; font-size: 12px;">
          <p>&copy; 2024 HashCoreX. All rights reserved.</p>
        </div>
      </div>
    `}getDefaultKYCTemplate(e){let t="APPROVED"===e;return`
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #ffd60a 0%, ${t?"#10b981":"#ef4444"} 100%); padding: 20px; text-align: center;">
          <h1 style="color: white; margin: 0;">HashCoreX</h1>
        </div>
        <div style="padding: 30px; background: #f9f9f9;">
          <h2 style="color: #333;">${t?"KYC Approved!":"KYC Rejected"}</h2>
          <p>Hello {{firstName}},</p>
          <p>${t?"Congratulations! Your KYC verification has been approved. You now have full access to all platform features.":"Unfortunately, your KYC verification has been rejected. Please review the reason below and resubmit with correct documents."}</p>
          ${!t?'<div style="background: white; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #ef4444;"><p><strong>Rejection Reason:</strong> {{rejectionReason}}</p></div>':""}
          <p>Best regards,<br>The HashCoreX Team</p>
        </div>
        <div style="background: #333; color: white; padding: 20px; text-align: center; font-size: 12px;">
          <p>&copy; 2024 HashCoreX. All rights reserved.</p>
        </div>
      </div>
    `}getDefaultWithdrawalTemplate(e){let t=e=>{switch(e){case"APPROVED":case"COMPLETED":return"#10b981";case"REJECTED":case"FAILED":return"#ef4444";default:return"#6b7280"}};return`
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #ffd60a 0%, ${t(e)} 100%); padding: 20px; text-align: center;">
          <h1 style="color: white; margin: 0;">HashCoreX</h1>
        </div>
        <div style="padding: 30px; background: #f9f9f9;">
          <h2 style="color: #333;">Withdrawal {{status}}</h2>
          <p>Hello {{firstName}},</p>
          <p>Your withdrawal request has been {{status}}.</p>
          <div style="background: white; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid ${t(e)};">
            <h3 style="margin: 0 0 10px 0; color: ${t(e)};">Withdrawal Details</h3>
            <p><strong>Amount:</strong> {{amount}} USDT</p>
            <p><strong>Status:</strong> {{status}}</p>
            {{#if usdtAddress}}<p><strong>Address:</strong> {{usdtAddress}}</p>{{/if}}
            {{#if transactionHash}}<p><strong>Transaction Hash:</strong> {{transactionHash}}</p>{{/if}}
            {{#if rejectionReason}}<p><strong>Reason:</strong> {{rejectionReason}}</p>{{/if}}
          </div>
          <p>Best regards,<br>The HashCoreX Team</p>
        </div>
        <div style="background: #333; color: white; padding: 20px; text-align: center; font-size: 12px;">
          <p>&copy; 2024 HashCoreX. All rights reserved.</p>
        </div>
      </div>
    `}getDefaultWithdrawalText(e){let t=`Withdrawal ${e.status}

Amount: ${e.amount} USDT
Status: ${e.status}`;return e.usdtAddress&&(t+=`
Address: ${e.usdtAddress}`),e.transactionHash&&(t+=`
Transaction Hash: ${e.transactionHash}`),e.rejectionReason&&(t+=`
Reason: ${e.rejectionReason}`),t}async sendMiningUnitPurchaseNotification(e){try{let t=await i.emailService.getEmailTemplate("mining_unit_purchase");t||console.warn('Email template "mining_unit_purchase" not found. Using default template.');let a=t?.subject||"Mining Unit Purchase Confirmed - HashCoreX",n=t?.htmlContent||this.getDefaultMiningUnitPurchaseTemplate(),s=t?.textContent||this.getDefaultMiningUnitPurchaseText(e);n=this.replaceVariables(n,{firstName:e.firstName,lastName:e.lastName,email:e.email,thsAmount:e.thsAmount.toString(),investmentAmount:e.investmentAmount.toString(),dailyROI:e.dailyROI.toFixed(2),purchaseDate:new Date(e.purchaseDate).toLocaleDateString(),expiryDate:new Date(e.expiryDate).toLocaleDateString()}),s=this.replaceVariables(s,{firstName:e.firstName,lastName:e.lastName,email:e.email,thsAmount:e.thsAmount.toString(),investmentAmount:e.investmentAmount.toString(),dailyROI:e.dailyROI.toFixed(2),purchaseDate:new Date(e.purchaseDate).toLocaleDateString(),expiryDate:new Date(e.expiryDate).toLocaleDateString()});let o=await r.XB.create({to:e.email,subject:a,template:"mining_unit_purchase",status:"PENDING"}),l=await i.emailService.sendEmail({to:e.email,subject:a,html:n,text:s});return await r.XB.updateStatus(o.id,l?"SENT":"FAILED"),l}catch(e){return console.error("Failed to send mining unit purchase notification:",e),!1}}async sendMiningUnitExpiryNotification(e){try{let t=await i.emailService.getEmailTemplate("mining_unit_expiry");t||console.warn('Email template "mining_unit_expiry" not found. Using default template.');let a=t?.subject||"Mining Unit Expired - HashCoreX",n=t?.htmlContent||this.getDefaultMiningUnitExpiryTemplate(),s=t?.textContent||this.getDefaultMiningUnitExpiryText(e);n=this.replaceVariables(n,{firstName:e.firstName,lastName:e.lastName,email:e.email,thsAmount:e.thsAmount.toString(),investmentAmount:e.investmentAmount.toString(),totalEarned:e.totalEarned.toString(),purchaseDate:new Date(e.purchaseDate).toLocaleDateString(),expiryDate:new Date(e.expiryDate).toLocaleDateString(),expiryReason:"TIME_LIMIT"===e.expiryReason?"24-month time limit reached":"5x return limit achieved"}),s=this.replaceVariables(s,{firstName:e.firstName,lastName:e.lastName,email:e.email,thsAmount:e.thsAmount.toString(),investmentAmount:e.investmentAmount.toString(),totalEarned:e.totalEarned.toString(),purchaseDate:new Date(e.purchaseDate).toLocaleDateString(),expiryDate:new Date(e.expiryDate).toLocaleDateString(),expiryReason:"TIME_LIMIT"===e.expiryReason?"24-month time limit reached":"5x return limit achieved"});let o=await r.XB.create({to:e.email,subject:a,template:"mining_unit_expiry",status:"PENDING"}),l=await i.emailService.sendEmail({to:e.email,subject:a,html:n,text:s});return await r.XB.updateStatus(o.id,l?"SENT":"FAILED"),l}catch(e){return console.error("Failed to send mining unit expiry notification:",e),!1}}getDefaultMiningUnitPurchaseTemplate(){return`
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #ffd60a 0%, #10b981 100%); padding: 20px; text-align: center;">
          <h1 style="color: white; margin: 0;">HashCoreX</h1>
        </div>
        <div style="padding: 30px; background: #f9f9f9;">
          <h2 style="color: #333;">Mining Unit Purchase Confirmed!</h2>
          <p>Hello {{firstName}},</p>
          <p>Congratulations! Your mining unit purchase has been successfully processed and is now active.</p>
          <div style="background: white; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #10b981;">
            <h3 style="margin: 0 0 10px 0; color: #10b981;">Mining Unit Details</h3>
            <p><strong>TH/s Amount:</strong> {{thsAmount}} TH/s</p>
            <p><strong>Investment Amount:</strong> ${{investmentAmount}} USDT</p>
            <p><strong>Daily ROI:</strong> {{dailyROI}}%</p>
            <p><strong>Purchase Date:</strong> {{purchaseDate}}</p>
            <p><strong>Expiry Date:</strong> {{expiryDate}}</p>
          </div>
          <div style="background: #e3f2fd; padding: 15px; margin: 20px 0; border-radius: 8px;">
            <h4 style="margin: 0 0 10px 0; color: #1976d2;">Important Information</h4>
            <ul style="margin: 0; padding-left: 20px;">
              <li>Your mining unit will start generating daily returns immediately</li>
              <li>Earnings are paid out weekly on Sundays at 00:00 AM GMT+5:30</li>
              <li>Mining units expire after 24 months or when they reach 5x return, whichever comes first</li>
              <li>You can track your earnings in the Mining section of your dashboard</li>
            </ul>
          </div>
          <p>Thank you for choosing HashCoreX for your mining investment!</p>
          <p>Best regards,<br>The HashCoreX Team</p>
        </div>
        <div style="background: #333; color: white; padding: 20px; text-align: center; font-size: 12px;">
          <p>&copy; 2024 HashCoreX. All rights reserved.</p>
        </div>
      </div>
    `}getDefaultMiningUnitExpiryTemplate(){return`
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #ffd60a 0%, #ff9800 100%); padding: 20px; text-align: center;">
          <h1 style="color: white; margin: 0;">HashCoreX</h1>
        </div>
        <div style="padding: 30px; background: #f9f9f9;">
          <h2 style="color: #333;">Mining Unit Expired</h2>
          <p>Hello {{firstName}},</p>
          <p>We're writing to inform you that one of your mining units has reached its expiry condition.</p>
          <div style="background: white; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #ff9800;">
            <h3 style="margin: 0 0 10px 0; color: #ff9800;">Expired Mining Unit Details</h3>
            <p><strong>TH/s Amount:</strong> {{thsAmount}} TH/s</p>
            <p><strong>Original Investment:</strong> ${{investmentAmount}} USDT</p>
            <p><strong>Total Earned:</strong> ${{totalEarned}} USDT</p>
            <p><strong>Purchase Date:</strong> {{purchaseDate}}</p>
            <p><strong>Expiry Date:</strong> {{expiryDate}}</p>
            <p><strong>Expiry Reason:</strong> {{expiryReason}}</p>
          </div>
          <div style="background: #e8f5e8; padding: 15px; margin: 20px 0; border-radius: 8px;">
            <h4 style="margin: 0 0 10px 0; color: #2e7d32;">What Happens Next?</h4>
            <ul style="margin: 0; padding-left: 20px;">
              <li>All accumulated earnings have been credited to your wallet</li>
              <li>This mining unit will no longer generate daily returns</li>
              <li>You can view the complete history in your Mining section</li>
              <li>Consider purchasing new mining units to continue earning</li>
            </ul>
          </div>
          <p>Thank you for your continued trust in HashCoreX!</p>
          <p>Best regards,<br>The HashCoreX Team</p>
        </div>
        <div style="background: #333; color: white; padding: 20px; text-align: center; font-size: 12px;">
          <p>&copy; 2024 HashCoreX. All rights reserved.</p>
        </div>
      </div>
    `}getDefaultMiningUnitPurchaseText(e){return`Mining Unit Purchase Confirmed!

Hello ${e.firstName},

Your mining unit purchase has been successfully processed:

TH/s Amount: ${e.thsAmount} TH/s
Investment Amount: $${e.investmentAmount} USDT
Daily ROI: ${e.dailyROI.toFixed(2)}%
Purchase Date: ${new Date(e.purchaseDate).toLocaleDateString()}
Expiry Date: ${new Date(e.expiryDate).toLocaleDateString()}

Your mining unit will start generating daily returns immediately.

Best regards,
The HashCoreX Team`}getDefaultMiningUnitExpiryText(e){return`Mining Unit Expired

Hello ${e.firstName},

One of your mining units has expired:

TH/s Amount: ${e.thsAmount} TH/s
Original Investment: $${e.investmentAmount} USDT
Total Earned: $${e.totalEarned} USDT
Purchase Date: ${new Date(e.purchaseDate).toLocaleDateString()}
Expiry Date: ${new Date(e.expiryDate).toLocaleDateString()}
Expiry Reason: ${"TIME_LIMIT"===e.expiryReason?"24-month time limit reached":"5x return limit achieved"}

All earnings have been credited to your wallet.

Best regards,
The HashCoreX Team`}async sendWelcomeEmailNotification(e){try{let t=await i.emailService.getEmailTemplate("welcome_email");t||console.warn('Email template "welcome_email" not found. Using default template.');let a=t?.subject||"Welcome to HashCoreX - Your Mining Journey Begins!",n=t?.htmlContent||this.getDefaultWelcomeTemplate(),s=t?.textContent||this.getDefaultWelcomeText(e);n=this.replaceVariables(n,{firstName:e.firstName,lastName:e.lastName,email:e.email}),s=this.replaceVariables(s,{firstName:e.firstName,lastName:e.lastName,email:e.email});let o=await r.XB.create({to:e.email,subject:a,template:"welcome_email",status:"PENDING"}),l=await i.emailService.sendEmail({to:e.email,subject:a,html:n,text:s});return await r.XB.updateStatus(o.id,l?"SENT":"FAILED"),l}catch(e){return console.error("Failed to send welcome email notification:",e),!1}}getDefaultWelcomeTemplate(){return`
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
          <h1 style="color: white; margin: 0; font-size: 28px;">Welcome to HashCoreX!</h1>
          <p style="color: #f0f0f0; margin: 10px 0 0 0;">Your Sustainable Mining Journey Starts Here</p>
        </div>

        <div style="background: white; padding: 40px; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
          <h2 style="color: #667eea; margin: 0 0 20px 0; font-size: 24px;">🎉 Welcome {{firstName}}!</h2>

          <p style="margin: 0 0 20px 0; font-size: 16px;">Thank you for joining HashCoreX, the leading sustainable cryptocurrency mining platform.</p>

          <div style="background: #f8f9fa; padding: 25px; margin: 25px 0; border-radius: 8px; border-left: 4px solid #667eea;">
            <h3 style="margin: 0 0 15px 0; color: #667eea; font-size: 18px;">🚀 What's Next?</h3>
            <ul style="margin: 0; padding-left: 20px; color: #555;">
              <li style="margin-bottom: 8px;">Complete your KYC verification to unlock all features</li>
              <li style="margin-bottom: 8px;">Explore our mining packages and start earning</li>
              <li style="margin-bottom: 8px;">Invite friends and earn referral bonuses</li>
              <li style="margin-bottom: 8px;">Track your earnings in real-time on your dashboard</li>
            </ul>
          </div>

          <div style="background: #e3f2fd; padding: 20px; margin: 25px 0; border-radius: 8px; border-left: 4px solid #2196f3;">
            <h4 style="margin: 0 0 15px 0; color: #1976d2; font-size: 16px;">💡 Getting Started Tips</h4>
            <ul style="margin: 0; padding-left: 20px; color: #555;">
              <li style="margin-bottom: 8px;">Start with a small mining unit to understand the platform</li>
              <li style="margin-bottom: 8px;">Check your dashboard daily for earnings updates</li>
              <li style="margin-bottom: 8px;">Join our community for tips and updates</li>
              <li style="margin-bottom: 8px;">Contact support if you have any questions</li>
            </ul>
          </div>

          <div style="text-align: center; margin: 30px 0;">
            <a href="https://hashcorex.com/dashboard" style="background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block; margin-right: 10px;">Go to Dashboard</a>
            <a href="https://hashcorex.com/contact" style="background: #10b981; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">Contact Support</a>
          </div>

          <p style="margin: 25px 0 0 0; font-size: 16px;">We're excited to have you on board and look forward to helping you achieve your mining goals!</p>

          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;">
            <p>Best regards,<br><strong>The HashCoreX Team</strong></p>
          </div>
        </div>
      </div>
    `}getDefaultWelcomeText(e){return`Welcome to HashCoreX!

Hello ${e.firstName},

Thank you for joining HashCoreX, the leading sustainable cryptocurrency mining platform.

What's Next?
- Complete your KYC verification to unlock all features
- Explore our mining packages and start earning
- Invite friends and earn referral bonuses
- Track your earnings in real-time on your dashboard

Getting Started Tips:
- Start with a small mining unit to understand the platform
- Check your dashboard daily for earnings updates
- Join our community for tips and updates
- Contact support if you have any questions

We're excited to have you on board and look forward to helping you achieve your mining goals!

Best regards,
The HashCoreX Team`}}let s=new n},21111:(e,t,a)=>{a.d(t,{X:()=>o,emailService:()=>s});var i=a(49526),r=a(6710);class n{async getEmailConfig(){try{let e=await r.T8.getEmailSettings();if(!e||!e.smtpHost||!e.smtpUser||!e.smtpPassword)return console.warn("Email configuration not found or incomplete"),null;return{host:e.smtpHost,port:e.smtpPort||587,secure:e.smtpSecure||!1,user:e.smtpUser,password:e.smtpPassword,fromName:e.fromName||"HashCoreX",fromEmail:e.fromEmail||e.smtpUser}}catch(e){return console.error("Failed to get email configuration:",e),null}}async initializeTransporter(e=!1){try{if(this.config=await this.getEmailConfig(),!this.config)return console.warn("Email configuration not available - email service disabled"),!1;if(!this.config.host||!this.config.user||!this.config.password)return console.error("Email configuration incomplete - missing host, user, or password"),!1;if(this.transporter=i.createTransport({host:this.config.host,port:this.config.port,secure:this.config.secure,auth:{user:this.config.user,pass:this.config.password},tls:{rejectUnauthorized:!1},connectionTimeout:1e4,greetingTimeout:5e3,socketTimeout:1e4}),e)console.log("Email transporter initialized (verification skipped)");else{let e=this.transporter.verify(),t=new Promise((e,t)=>setTimeout(()=>t(Error("Email verification timeout")),15e3));await Promise.race([e,t]),console.log("Email transporter initialized and verified successfully")}return!0}catch(e){return console.error("Failed to initialize email transporter:",e),this.transporter=null,this.config=null,!1}}async sendEmail(e){try{if((!this.transporter||!this.config)&&(console.log("Email transporter not initialized, attempting to initialize..."),!await this.initializeTransporter(!0)))return console.warn("Email service not configured - skipping email send"),!1;if(!e.to||!e.subject)return console.error("Invalid email data - missing recipient or subject"),!1;let t={from:`"${this.config.fromName}" <${this.config.fromEmail}>`,to:e.to,subject:e.subject,html:e.html,text:e.text},a=this.transporter.sendMail(t),i=new Promise((e,t)=>setTimeout(()=>t(Error("Email send timeout")),3e4)),r=await Promise.race([a,i]);return console.log("Email sent successfully:",r.messageId),!0}catch(e){return console.error("Failed to send email:",e),this.transporter=null,this.config=null,!1}}async sendOTPEmail(e,t,a,i="email_verification"){let r="otp_verification";"password_reset"===i?r="password_reset_otp":"two_factor_auth"===i&&(r="two_factor_otp");let n=await this.getEmailTemplate(r);if(!n)return console.error(`Email template '${r}' not found. Please ensure email templates are seeded.`),!1;let s=n.htmlContent,o=n.textContent||"";return s=(s=s.replace(/{{firstName}}/g,a||"User")).replace(/{{otp}}/g,t),o=(o=o.replace(/{{firstName}}/g,a||"User")).replace(/{{otp}}/g,t),await this.sendEmail({to:e,subject:n.subject,html:s,text:o})}async getEmailTemplate(e){try{return await r.T8.getEmailTemplate(e)}catch(e){return console.error("Failed to get email template:",e),null}}async testConnection(){try{if(this.config=await this.getEmailConfig(),!this.config)return console.error("Email configuration not found or incomplete"),!1;return this.transporter=i.createTransport({host:this.config.host,port:this.config.port,secure:this.config.secure,auth:{user:this.config.user,pass:this.config.password},tls:{rejectUnauthorized:!1}}),await this.transporter.verify(),console.log("Email connection test successful"),!0}catch(e){throw console.error("Email connection test failed:",e),this.transporter=null,e}}constructor(){this.transporter=null,this.config=null}}let s=new n,o=()=>Math.floor(1e5+9e5*Math.random()).toString()}};