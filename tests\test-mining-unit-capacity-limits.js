/**
 * Test Script for Mining Unit Capacity Limits and Excess Earnings Handling
 * 
 * This script tests the new functionality where:
 * 1. Excess earnings are properly discarded when mining units reach 5x capacity
 * 2. Only allocated amounts are added to wallet balance
 * 3. Excess amounts are NOT added to wallet balance
 * 
 * Run this script to verify the mining unit capacity limit changes work correctly.
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// Test configuration
const TEST_USER_ID = 'test-user-capacity-limits';
const TEST_EMAIL = '<EMAIL>';

async function createTestUser() {
  console.log('🔧 Creating test user...');
  
  try {
    // Delete existing test user if exists
    await prisma.user.deleteMany({
      where: { email: TEST_EMAIL }
    });

    // Create test user
    const bcrypt = require('bcryptjs');
    const hashedPassword = await bcrypt.hash('testpassword123', 10);

    const user = await prisma.user.create({
      data: {
        id: TEST_USER_ID,
        email: TEST_EMAIL,
        firstName: 'Test',
        lastName: 'User',
        password: hashedPassword,
        referralId: 'TESTCAP123',
        isActive: true,
        kycStatus: 'APPROVED'
      }
    });

    // Create wallet balance
    await prisma.walletBalance.create({
      data: {
        userId: user.id,
        availableBalance: 0,
        pendingBalance: 0,
        totalDeposits: 0,
        totalWithdrawals: 0,
        totalEarnings: 0
      }
    });

    console.log('✅ Test user created:', user.id);
    return user;
  } catch (error) {
    console.error('❌ Error creating test user:', error);
    throw error;
  }
}

async function createTestMiningUnit(userId, investmentAmount = 100, currentEarnings = 450) {
  console.log(`🔧 Creating test mining unit with $${investmentAmount} investment and $${currentEarnings} current earnings...`);
  
  try {
    const expiryDate = new Date();
    expiryDate.setMonth(expiryDate.getMonth() + 24); // 24 months from now

    const miningUnit = await prisma.miningUnit.create({
      data: {
        userId,
        thsAmount: investmentAmount / 10, // $10 per TH/s
        investmentAmount,
        dailyROI: 0.43,
        expiryDate,
        status: 'ACTIVE',
        miningEarnings: currentEarnings, // Already earned $450 out of $500 limit
        referralEarnings: 0,
        binaryEarnings: 0,
        totalEarned: currentEarnings
      }
    });

    console.log('✅ Test mining unit created:', {
      id: miningUnit.id,
      investmentAmount: miningUnit.investmentAmount,
      currentEarnings: miningUnit.miningEarnings,
      remainingCapacity: (investmentAmount * 5) - currentEarnings
    });

    return miningUnit;
  } catch (error) {
    console.error('❌ Error creating test mining unit:', error);
    throw error;
  }
}

async function testExcessEarningsHandling(userId) {
  console.log('🧪 Testing excess earnings handling...');

  try {
    // Import the allocation function
    const { allocateEarningsToUnits } = require('../src/lib/miningUnitEarnings');
    const { transactionDb } = require('../src/lib/database');

    // Get wallet balance before allocation
    const walletBefore = await prisma.walletBalance.findUnique({
      where: { userId }
    });

    console.log('Wallet before allocation:', {
      availableBalance: walletBefore.availableBalance,
      totalEarnings: walletBefore.totalEarnings
    });

    // Create a transaction for $100 commission (should only allocate $50, discard $50)
    const transaction = await transactionDb.create({
      userId,
      type: 'DIRECT_REFERRAL',
      amount: 100,
      description: 'Test referral commission - should only allocate $50',
      status: 'COMPLETED'
    });

    // Test the allocation
    const allocationSummary = await allocateEarningsToUnits(
      userId,
      100, // Trying to allocate $100
      'DIRECT_REFERRAL',
      transaction.id,
      'Test referral commission'
    );

    console.log('✅ Allocation Summary:', {
      totalAllocated: allocationSummary.totalAllocated,
      totalDiscarded: allocationSummary.totalDiscarded,
      allocationSuccess: allocationSummary.allocationSuccess,
      allocationsCount: allocationSummary.allocations.length
    });

    // Verify the mining unit is now at capacity and expired
    const updatedUnit = await prisma.miningUnit.findFirst({
      where: { userId, status: 'EXPIRED' }
    });

    if (updatedUnit) {
      console.log('✅ Mining unit properly expired:', {
        totalEarnings: updatedUnit.miningEarnings + updatedUnit.referralEarnings + updatedUnit.binaryEarnings,
        status: updatedUnit.status
      });
    }

    // Test that only allocated amount should be added to wallet (not the full $100)
    // This would be done by the calling code (referral.ts, etc.)
    const { walletBalanceDb } = require('../src/lib/database');
    if (allocationSummary.totalAllocated > 0) {
      await walletBalanceDb.addEarnings(userId, allocationSummary.totalAllocated);
    }

    // Get wallet balance after allocation
    const walletAfter = await prisma.walletBalance.findUnique({
      where: { userId }
    });

    console.log('Wallet after allocation:', {
      availableBalance: walletAfter.availableBalance,
      totalEarnings: walletAfter.totalEarnings,
      earningsIncrease: walletAfter.totalEarnings - walletBefore.totalEarnings
    });

    // Verify that only $50 was added to wallet, not $100
    const expectedIncrease = allocationSummary.totalAllocated;
    const actualIncrease = walletAfter.totalEarnings - walletBefore.totalEarnings;

    if (actualIncrease === expectedIncrease) {
      console.log('✅ SUCCESS: Only allocated amount added to wallet');
      console.log(`   Expected: $${expectedIncrease}, Actual: $${actualIncrease}`);
    } else {
      console.log('❌ FAILURE: Incorrect amount added to wallet');
      console.log(`   Expected: $${expectedIncrease}, Actual: $${actualIncrease}`);
    }

    return allocationSummary;

  } catch (error) {
    console.error('❌ Error testing excess earnings handling:', error);
    throw error;
  }
}

async function cleanupTestData() {
  console.log('🧹 Cleaning up test data...');
  
  try {
    // Delete test user and related data
    await prisma.miningUnitEarningsAllocation.deleteMany({
      where: {
        miningUnit: {
          userId: TEST_USER_ID
        }
      }
    });
    
    await prisma.miningUnit.deleteMany({
      where: { userId: TEST_USER_ID }
    });
    
    await prisma.transaction.deleteMany({
      where: { userId: TEST_USER_ID }
    });
    
    await prisma.walletBalance.deleteMany({
      where: { userId: TEST_USER_ID }
    });
    
    await prisma.user.deleteMany({
      where: { id: TEST_USER_ID }
    });

    console.log('✅ Test data cleaned up');
  } catch (error) {
    console.error('❌ Error cleaning up test data:', error);
  }
}

async function runCapacityLimitTest() {
  console.log('🚀 Starting Mining Unit Capacity Limits Test\n');
  
  try {
    // Step 1: Create test user and mining unit near capacity
    const user = await createTestUser();
    const miningUnit = await createTestMiningUnit(user.id, 100, 450); // $100 unit with $450 earned (limit: $500)

    // Step 2: Test excess earnings handling
    const allocationSummary = await testExcessEarningsHandling(user.id);

    // Step 3: Verify results
    console.log('\n📊 Test Results Summary:');
    console.log(`   Mining Unit Investment: $100`);
    console.log(`   Mining Unit Limit (5x): $500`);
    console.log(`   Previous Earnings: $450`);
    console.log(`   Remaining Capacity: $50`);
    console.log(`   Attempted Allocation: $100`);
    console.log(`   Actually Allocated: $${allocationSummary.totalAllocated}`);
    console.log(`   Discarded (Excess): $${allocationSummary.totalDiscarded}`);

    if (allocationSummary.totalAllocated === 50 && allocationSummary.totalDiscarded === 50) {
      console.log('\n✅ SUCCESS: Excess earnings properly handled!');
      console.log('   ✓ Only $50 allocated to mining unit');
      console.log('   ✓ Excess $50 properly discarded');
      console.log('   ✓ Mining unit expired at 5x limit');
    } else {
      console.log('\n❌ FAILURE: Excess earnings not handled correctly');
    }

    console.log('\n✅ Capacity limits test completed successfully!');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error);
  } finally {
    // Cleanup
    await cleanupTestData();
    await prisma.$disconnect();
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  runCapacityLimitTest();
}

module.exports = {
  createTestUser,
  createTestMiningUnit,
  testExcessEarningsHandling,
  cleanupTestData,
  runCapacityLimitTest
};
