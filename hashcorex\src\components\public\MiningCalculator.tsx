'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardContent, Input } from '@/components/ui';
import { Calculator, DollarSign, Zap } from 'lucide-react';
import { formatCurrency, formatNumber } from '@/lib/utils';

interface EarningsRange {
  minTHS: number;
  maxTHS: number;
  dailyReturnMin: number;
  dailyReturnMax: number;
  monthlyReturnMin: number;
  monthlyReturnMax: number;
}

interface MiningCalculatorProps {
  className?: string;
  showTitle?: boolean;
  title?: string;
}

export const MiningCalculator: React.FC<MiningCalculatorProps> = ({
  className = '',
  showTitle = true,
  title = 'Mining Rate Calculator'
}) => {
  const [formData, setFormData] = useState({
    thsAmount: '20',
    investmentAmount: '1000',
  });
  const [thsPrice, setThsPrice] = useState(50); // Default price
  const [earningsRanges, setEarningsRanges] = useState<EarningsRange[]>([
    { minTHS: 0, maxTHS: 10, dailyReturnMin: 0.3, dailyReturnMax: 0.5, monthlyReturnMin: 10.0, monthlyReturnMax: 15.0 },
    { minTHS: 10, maxTHS: 50, dailyReturnMin: 0.4, dailyReturnMax: 0.6, monthlyReturnMin: 10.0, monthlyReturnMax: 15.0 },
    { minTHS: 50, maxTHS: 999999, dailyReturnMin: 0.5, dailyReturnMax: 0.7, monthlyReturnMin: 10.0, monthlyReturnMax: 15.0 },
  ]);
  const [minPurchaseAmount, setMinPurchaseAmount] = useState(100);

  // Fetch current TH/s price and ROI range
  useEffect(() => {
    fetchPricing();
  }, []);

  const fetchPricing = async () => {
    try {
      const response = await fetch('/api/admin/settings/pricing');
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setThsPrice(data.data.thsPrice);
          setEarningsRanges(data.data.earningsRanges);
          setMinPurchaseAmount(data.data.minPurchaseAmount);
        }
      }
    } catch (error) {
      console.error('Failed to fetch pricing:', error);
    }
  };

  const handleThsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const ths = parseFloat(e.target.value) || 0;
    const investment = ths * thsPrice;
    
    setFormData({
      thsAmount: e.target.value,
      investmentAmount: investment > 0 ? investment.toFixed(2) : '',
    });
  };

  const handleInvestmentChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const investment = parseFloat(e.target.value) || 0;
    const ths = investment / thsPrice;

    setFormData({
      thsAmount: ths > 0 ? ths.toFixed(4) : '',
      investmentAmount: e.target.value,
    });
  };

  // Get the appropriate earnings range for a given TH/s amount
  const getEarningsRangeForTHS = (thsAmount: number) => {
    const range = earningsRanges.find(range =>
      thsAmount >= range.minTHS && thsAmount <= range.maxTHS
    );
    return range || earningsRanges[earningsRanges.length - 1]; // Fallback to highest range
  };

  const calculateEstimatedEarnings = () => {
    const investment = parseFloat(formData.investmentAmount) || 0;
    const thsAmount = parseFloat(formData.thsAmount) || 0;
    if (investment <= 0 || thsAmount <= 0) return { daily: { min: 0, max: 0 }, weekly: { min: 0, max: 0 }, monthly: { min: 0, max: 0 }, range: null };

    const range = getEarningsRangeForTHS(thsAmount);
    const dailyMin = (investment * range.dailyReturnMin) / 100;
    const dailyMax = (investment * range.dailyReturnMax) / 100;

    return {
      daily: { min: dailyMin, max: dailyMax },
      weekly: { min: dailyMin * 7, max: dailyMax * 7 },
      monthly: { min: dailyMin * 30, max: dailyMax * 30 },
      range,
    };
  };

  const estimatedEarnings = calculateEstimatedEarnings();

  return (
    <div className={`bg-white rounded-2xl p-8 shadow-lg ${className}`}>
      {showTitle && (
        <h3 className="text-2xl font-bold text-dark-900 mb-6 text-center flex items-center justify-center">
          <Calculator className="h-6 w-6 mr-2 text-solar-600" />
          {title}
        </h3>
      )}

      {/* Current Pricing Info */}
      <div className="bg-gradient-to-r from-solar-50 to-eco-50 rounded-xl p-4 mb-6">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-center sm:text-left">
          <div>
            <span className="text-sm font-medium text-gray-600 block mb-1">Current TH/s Price</span>
            <span className="text-xl font-bold text-solar-600">
              {formatCurrency(thsPrice)} / TH/s
            </span>
          </div>
          <div className="text-center sm:text-right">
            <span className="text-sm font-medium text-gray-600 block mb-1">
              {estimatedEarnings.range ?
                `Daily ROI Range (${estimatedEarnings.range.minTHS}-${estimatedEarnings.range.maxTHS === 999999 ? '∞' : estimatedEarnings.range.maxTHS} TH/s)` :
                'Daily ROI Range'
              }
            </span>
            <span className="text-xl font-bold text-eco-600">
              {estimatedEarnings.range ?
                `${estimatedEarnings.range.dailyReturnMin}% - ${estimatedEarnings.range.dailyReturnMax}%` :
                '0.3% - 0.7%'
              }
            </span>
          </div>
        </div>
      </div>

      {/* Input Fields */}
      <div className="space-y-4 mb-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <div>
            <Input
              label="TH/s Amount"
              type="number"
              step="0.0001"
              min="0"
              value={formData.thsAmount}
              onChange={handleThsChange}
              placeholder="Enter TH/s amount"
              leftIcon={<Zap className="h-4 w-4" />}
              className="h-12"
            />
          </div>

          <div>
            <Input
              label="Investment Amount (USD)"
              type="number"
              step="0.01"
              min={minPurchaseAmount}
              value={formData.investmentAmount}
              onChange={handleInvestmentChange}
              placeholder="Enter investment amount"
              leftIcon={<DollarSign className="h-4 w-4" />}
              className="h-12"
            />
          </div>
        </div>
      </div>

      {/* Estimated Earnings */}
      {estimatedEarnings.daily.min > 0 && (
        <div className="space-y-4">
          <div className="flex justify-between items-center py-3 border-b border-gray-100">
            <span className="text-gray-600">Investment Amount</span>
            <span className="font-semibold text-dark-900">{formatCurrency(parseFloat(formData.investmentAmount) || 0)}</span>
          </div>
          <div className="flex justify-between items-center py-3 border-b border-gray-100">
            <span className="text-gray-600">TH/s Purchased</span>
            <span className="font-semibold text-dark-900">{formatNumber(parseFloat(formData.thsAmount) || 0, 4)} TH/s</span>
          </div>
          <div className="flex justify-between items-center py-3 border-b border-gray-100">
            <span className="text-gray-600">
              Daily ROI ({estimatedEarnings.range ? `${estimatedEarnings.range.dailyReturnMin}-${estimatedEarnings.range.dailyReturnMax}%` : '0.3-0.7%'})
            </span>
            <span className="font-semibold text-eco-600">
              {formatCurrency(estimatedEarnings.daily.min)}-{formatCurrency(estimatedEarnings.daily.max)}
            </span>
          </div>
          <div className="flex justify-between items-center py-3 border-b border-gray-100">
            <span className="text-gray-600">Weekly Earnings</span>
            <span className="font-semibold text-eco-600">
              {formatCurrency(estimatedEarnings.weekly.min)}-{formatCurrency(estimatedEarnings.weekly.max)}
            </span>
          </div>
          <div className="flex justify-between items-center py-3 bg-solar-50 rounded-lg px-4">
            <span className="text-gray-600">Monthly Potential</span>
            <span className="font-bold text-solar-600 text-lg">
              {formatCurrency(estimatedEarnings.monthly.min)}-{formatCurrency(estimatedEarnings.monthly.max)}
            </span>
          </div>
        </div>
      )}

      {/* TH/s Based Earnings Tiers */}
      <div className="mt-6 pt-6 border-t border-gray-200">
        <h4 className="text-sm font-medium text-gray-700 mb-3">TH/s Based Earnings Tiers</h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          {earningsRanges.map((range, index) => (
            <div key={index} className="bg-gray-50 rounded-lg p-3 border border-gray-200">
              <div className="text-xs font-medium text-gray-600 mb-1">
                {range.minTHS} - {range.maxTHS === 999999 ? '∞' : range.maxTHS} TH/s
              </div>
              <div className="text-sm font-bold text-eco-600">
                {range.dailyReturnMin}% - {range.dailyReturnMax}%
              </div>
              <div className="text-xs text-gray-500">
                Monthly: {range.monthlyReturnMin}% - {range.monthlyReturnMax}%
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
