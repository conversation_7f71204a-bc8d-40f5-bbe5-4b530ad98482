"use strict";(()=>{var e={};e.id=1376,e.ids=[1376],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16918:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>E,routeModule:()=>c,serverHooks:()=>f,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>p});var a={};t.r(a),t.d(a,{GET:()=>d});var s=t(96559),i=t(48088),n=t(37719),o=t(32190),l=t(39542),u=t(31183);async function d(e){try{let{authenticated:r,user:t}=await (0,l.b9)(e);if(!r||!t)return o.NextResponse.json({success:!1,error:"Authentication required"},{status:401});if("ADMIN"!==t.role)return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let{searchParams:a}=new URL(e.url),s=a.get("dateRange")||"30d";a.get("type");let i=a.get("status")||"all",n=parseInt(a.get("page")||"1"),d=parseInt(a.get("limit")||"20"),c=(n-1)*d,m={};if("all"!==s){let e=parseInt(s.replace("d",""));m={gte:new Date(Date.now()-24*e*36e5)}}let p={type:"DIRECT_REFERRAL"};"all"!==s&&(p.createdAt=m),"all"!==i&&(p.status=i);let f=await u.prisma.transaction.findMany({where:p,include:{user:{select:{id:!0,email:!0,firstName:!0,lastName:!0}}},orderBy:{createdAt:"desc"},take:d,skip:c}),E=await Promise.all(f.map(async e=>{let r=null;if(e.reference&&e.reference.startsWith("from_user:")){let t=e.reference.replace("from_user:","");r=await u.prisma.user.findUnique({where:{id:t},select:{id:!0,email:!0,firstName:!0,lastName:!0}})}if(!r&&e.user)for(let t of(await u.prisma.user.findMany({where:{referrerId:e.userId},select:{id:!0,email:!0,firstName:!0,lastName:!0,miningUnits:{select:{id:!0,investmentAmount:!0,createdAt:!0},orderBy:{createdAt:"desc"}}}}))){for(let a of t.miningUnits){let s=.1*a.investmentAmount,i=Math.abs(new Date(e.createdAt).getTime()-new Date(a.createdAt).getTime());if(.01>Math.abs(s-e.amount)&&i<=18e5){r={id:t.id,email:t.email,firstName:t.firstName,lastName:t.lastName};break}}if(r)break}let t=e.amount/.1;return{id:e.id,fromUserId:r?.id||"unknown",toUserId:e.userId,fromUser:r||{id:"unknown",email:"Unknown",firstName:"Unknown",lastName:"User"},toUser:e.user,amount:e.amount,commissionRate:10,originalAmount:t,type:"DIRECT_REFERRAL",description:e.description||"",createdAt:e.createdAt,status:e.status}})),g=await u.prisma.transaction.count({where:p});return o.NextResponse.json({success:!0,data:E,pagination:{total:g,limit:d,offset:c,hasMore:c+d<g}})}catch(e){return console.error("Referral commissions fetch error:",e),o.NextResponse.json({success:!1,error:"Failed to fetch referral commissions"},{status:500})}}let c=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/referral-commissions/route",pathname:"/api/admin/referral-commissions",filename:"route",bundlePath:"app/api/admin/referral-commissions/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\referral-commissions\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:m,workUnitAsyncStorage:p,serverHooks:f}=c;function E(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:p})}},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37366:e=>{e.exports=require("dns")},39542:(e,r,t)=>{t.d(r,{b9:()=>S,HU:()=>E,qc:()=>I,Lx:()=>_,DY:()=>T,DT:()=>h});var a=t(85663),s=t(43205),i=t.n(s),n=t(6710),o=t(45697);let l=o.z.object({DATABASE_URL:o.z.string().url("DATABASE_URL must be a valid PostgreSQL URL"),DIRECT_URL:o.z.string().url("DIRECT_URL must be a valid PostgreSQL URL"),JWT_SECRET:o.z.string().min(32,"JWT_SECRET must be at least 32 characters long").refine(e=>{let r=/[A-Z]/.test(e),t=/[a-z]/.test(e),a=/\d/.test(e),s=/[!@#$%^&*(),.?":{}|<>]/.test(e);return r&&t&&a&&s},"JWT_SECRET should contain uppercase, lowercase, numbers, and special characters"),JWT_EXPIRES_IN:o.z.string().default("30d"),NODE_ENV:o.z.enum(["development","production","test"]).default("development"),NEXT_PUBLIC_APP_URL:o.z.string().url().optional(),PORT:o.z.string().regex(/^\d+$/).transform(Number).default("3000"),SMTP_HOST:o.z.string().optional(),SMTP_PORT:o.z.string().regex(/^\d+$/).transform(Number).optional(),SMTP_USER:o.z.string().email().optional(),SMTP_PASSWORD:o.z.string().optional(),SMTP_SECURE:o.z.string().transform(e=>"true"===e).optional(),TRON_NETWORK:o.z.enum(["mainnet","testnet"]).default("testnet"),TRON_API_KEY:o.z.string().optional(),USDT_CONTRACT_ADDRESS:o.z.string().optional(),MAX_FILE_SIZE:o.z.string().regex(/^\d+$/).transform(Number).default("10485760"),UPLOAD_DIR:o.z.string().default("./public/uploads"),BCRYPT_ROUNDS:o.z.string().regex(/^\d+$/).transform(Number).default("12"),SESSION_TIMEOUT:o.z.string().regex(/^\d+$/).transform(Number).default("1800"),RATE_LIMIT_WINDOW_MS:o.z.string().regex(/^\d+$/).transform(Number).default("900000"),RATE_LIMIT_MAX_REQUESTS:o.z.string().regex(/^\d+$/).transform(Number).default("100"),COINGECKO_API_URL:o.z.string().url().default("https://api.coingecko.com/api/v3"),LOG_LEVEL:o.z.enum(["error","warn","info","debug"]).default("info"),ENABLE_REQUEST_LOGGING:o.z.string().transform(e=>"true"===e).default("false"),ENABLE_REGISTRATION:o.z.string().transform(e=>"true"===e).default("true"),ENABLE_KYC:o.z.string().transform(e=>"true"===e).default("true"),ENABLE_WITHDRAWALS:o.z.string().transform(e=>"true"===e).default("true")}).refine(e=>{let r=[e.SMTP_HOST,e.SMTP_PORT,e.SMTP_USER,e.SMTP_PASSWORD],t=r.some(e=>void 0!==e),a=r.every(e=>void 0!==e);return!t||!!a},{message:"If SMTP configuration is provided, all SMTP fields (HOST, PORT, USER, PASSWORD) must be provided"});function u(){try{let e=l.safeParse(process.env);if(!e.success){let r=e.error.errors.map(e=>`${e.path.join(".")}: ${e.message}`);return{success:!1,errors:r}}return{success:!0,data:e.data}}catch(e){return{success:!1,errors:["Failed to validate environment variables"]}}}let d=null;function c(){if(!d){let e=u();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),d=e.data,console.log("✅ Environment variables validated successfully")}return d}let m={jwt:{secret:()=>c().JWT_SECRET,expiresIn:()=>c().JWT_EXPIRES_IN},security:{bcryptRounds:()=>c().BCRYPT_ROUNDS,sessionTimeout:()=>c().SESSION_TIMEOUT,maxFileSize:()=>c().MAX_FILE_SIZE,uploadDir:()=>c().UPLOAD_DIR}};if("phase-production-build"!==process.env.NEXT_PHASE){let e=u();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),console.log("✅ Environment variables validated successfully")}let p=async e=>await a.Ay.hash(e,m.security.bcryptRounds()),f=async(e,r)=>await a.Ay.compare(e,r),E=e=>i().sign(e,m.jwt.secret(),{expiresIn:m.jwt.expiresIn()}),g=e=>{try{return i().verify(e,m.jwt.secret())}catch(e){return null}},R=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",r="HC";for(let t=0;t<8;t++)r+=e.charAt(Math.floor(Math.random()*e.length));return r},S=async e=>{let r=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!r)return{authenticated:!1,user:null};let t=g(r);if(!t)return{authenticated:!1,user:null};let a=await n.Gy.findByEmail(t.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},T=async e=>{let r,a;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let t=await n.Gy.findByReferralId(e.referralCode);if(!t)throw Error("Invalid referral code");r=t.id}let s=await p(e.password),i=!1;do a=R(),i=!await n.Gy.findByReferralId(a);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(r){let{placeUserByReferralType:a}=await t.e(2746).then(t.bind(t,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(r,o.id,s)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},_=async e=>{let r=await n.Gy.findByEmail(e.email);if(!r||!await f(e.password,r.password))throw Error("Invalid email or password");return{token:E({userId:r.id,email:r.email}),user:{id:r.id,email:r.email,referralId:r.referralId,kycStatus:r.kycStatus}}},h=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),I=async e=>{let r=await n.Gy.findById(e);return r?.role==="ADMIN"}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,580,7911,925],()=>t(16918));module.exports=a})();