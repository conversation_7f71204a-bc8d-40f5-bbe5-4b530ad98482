"use strict";(()=>{var e={};e.id=9530,e.ids=[9530],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37366:e=>{e.exports=require("dns")},39542:(e,t,r)=>{r.d(t,{b9:()=>I,HU:()=>g,qc:()=>R,Lx:()=>w,DY:()=>S,DT:()=>_});var s=r(85663),a=r(43205),i=r.n(a),n=r(6710),o=r(45697);let u=o.z.object({DATABASE_URL:o.z.string().url("DATABASE_URL must be a valid PostgreSQL URL"),DIRECT_URL:o.z.string().url("DIRECT_URL must be a valid PostgreSQL URL"),JWT_SECRET:o.z.string().min(32,"JWT_SECRET must be at least 32 characters long").refine(e=>{let t=/[A-Z]/.test(e),r=/[a-z]/.test(e),s=/\d/.test(e),a=/[!@#$%^&*(),.?":{}|<>]/.test(e);return t&&r&&s&&a},"JWT_SECRET should contain uppercase, lowercase, numbers, and special characters"),JWT_EXPIRES_IN:o.z.string().default("30d"),NODE_ENV:o.z.enum(["development","production","test"]).default("development"),NEXT_PUBLIC_APP_URL:o.z.string().url().optional(),PORT:o.z.string().regex(/^\d+$/).transform(Number).default("3000"),SMTP_HOST:o.z.string().optional(),SMTP_PORT:o.z.string().regex(/^\d+$/).transform(Number).optional(),SMTP_USER:o.z.string().email().optional(),SMTP_PASSWORD:o.z.string().optional(),SMTP_SECURE:o.z.string().transform(e=>"true"===e).optional(),TRON_NETWORK:o.z.enum(["mainnet","testnet"]).default("testnet"),TRON_API_KEY:o.z.string().optional(),USDT_CONTRACT_ADDRESS:o.z.string().optional(),MAX_FILE_SIZE:o.z.string().regex(/^\d+$/).transform(Number).default("10485760"),UPLOAD_DIR:o.z.string().default("./public/uploads"),BCRYPT_ROUNDS:o.z.string().regex(/^\d+$/).transform(Number).default("12"),SESSION_TIMEOUT:o.z.string().regex(/^\d+$/).transform(Number).default("1800"),RATE_LIMIT_WINDOW_MS:o.z.string().regex(/^\d+$/).transform(Number).default("900000"),RATE_LIMIT_MAX_REQUESTS:o.z.string().regex(/^\d+$/).transform(Number).default("100"),COINGECKO_API_URL:o.z.string().url().default("https://api.coingecko.com/api/v3"),LOG_LEVEL:o.z.enum(["error","warn","info","debug"]).default("info"),ENABLE_REQUEST_LOGGING:o.z.string().transform(e=>"true"===e).default("false"),ENABLE_REGISTRATION:o.z.string().transform(e=>"true"===e).default("true"),ENABLE_KYC:o.z.string().transform(e=>"true"===e).default("true"),ENABLE_WITHDRAWALS:o.z.string().transform(e=>"true"===e).default("true")}).refine(e=>{let t=[e.SMTP_HOST,e.SMTP_PORT,e.SMTP_USER,e.SMTP_PASSWORD],r=t.some(e=>void 0!==e),s=t.every(e=>void 0!==e);return!r||!!s},{message:"If SMTP configuration is provided, all SMTP fields (HOST, PORT, USER, PASSWORD) must be provided"});function d(){try{let e=u.safeParse(process.env);if(!e.success){let t=e.error.errors.map(e=>`${e.path.join(".")}: ${e.message}`);return{success:!1,errors:t}}return{success:!0,data:e.data}}catch(e){return{success:!1,errors:["Failed to validate environment variables"]}}}let l=null;function c(){if(!l){let e=d();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),l=e.data,console.log("✅ Environment variables validated successfully")}return l}let p={jwt:{secret:()=>c().JWT_SECRET,expiresIn:()=>c().JWT_EXPIRES_IN},security:{bcryptRounds:()=>c().BCRYPT_ROUNDS,sessionTimeout:()=>c().SESSION_TIMEOUT,maxFileSize:()=>c().MAX_FILE_SIZE,uploadDir:()=>c().UPLOAD_DIR}};if("phase-production-build"!==process.env.NEXT_PHASE){let e=d();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),console.log("✅ Environment variables validated successfully")}let f=async e=>await s.Ay.hash(e,p.security.bcryptRounds()),m=async(e,t)=>await s.Ay.compare(e,t),g=e=>i().sign(e,p.jwt.secret(),{expiresIn:p.jwt.expiresIn()}),T=e=>{try{return i().verify(e,p.jwt.secret())}catch(e){return null}},E=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},I=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=T(t);if(!r)return{authenticated:!1,user:null};let s=await n.Gy.findByEmail(r.email);return s?{authenticated:!0,user:s}:{authenticated:!1,user:null}},S=async e=>{let t,s;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await n.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let a=await f(e.password),i=!1;do s=E(),i=!await n.Gy.findByReferralId(s);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:a,referralId:s});if(t){let{placeUserByReferralType:s}=await r.e(2746).then(r.bind(r,2746)),a="general";"left"===e.placementSide?a="left":"right"===e.placementSide&&(a="right"),await s(t,o.id,a)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},w=async e=>{let t=await n.Gy.findByEmail(e.email);if(!t||!await m(e.password,t.password))throw Error("Invalid email or password");return{token:g({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},_=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),R=async e=>{let t=await n.Gy.findById(e);return t?.role==="ADMIN"}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45492:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>I,routeModule:()=>m,serverHooks:()=>E,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>T});var s={};r.r(s),r.d(s,{POST:()=>f});var a=r(96559),i=r(48088),n=r(37719),o=r(32190),u=r(39542),d=r(6710),l=r(59480),c=r(1827);let p=new Map;async function f(e){try{let{authenticated:t,user:r}=await (0,u.b9)(e);if(!t||!r)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if(!function(e){let t=Date.now(),r=p.get(e);return!r||t>r.resetTime?(p.set(e,{count:1,resetTime:t+6e4}),!0):!(r.count>=5)&&(r.count++,!0)}(r.id))return o.NextResponse.json({success:!1,error:"Too many verification requests. Please wait before trying again."},{status:429});let{transactionId:s}=await e.json();if(!s||"string"!=typeof s)return o.NextResponse.json({success:!1,error:"Transaction ID is required"},{status:400});if(!(0,l.TA)(s))return o.NextResponse.json({success:!1,error:"Invalid Tron transaction ID format"},{status:400});let a=await d.J6.findByTransactionId(s);if(a)return await d.AJ.create({action:"DEPOSIT_DUPLICATE_ATTEMPT",userId:r.id,details:{transactionId:s,existingDepositId:a.id,existingStatus:a.status},ipAddress:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"}),o.NextResponse.json({success:!1,error:"This transaction ID has already been submitted. Please check your deposit history.",data:{existingStatus:a.status,submittedAt:a.createdAt}},{status:400});let i=await d.rs.get("usdtDepositAddress");i||(i=await d.rs.get("USDT_DEPOSIT_ADDRESS")),i&&(i=i.replace(/['"]/g,"").trim());let n=await d.rs.get("minDepositAmount");n||(n=await d.rs.get("MIN_DEPOSIT_AMOUNT")),n=parseFloat(n||"10");let f=await d.rs.get("maxDepositAmount");f||(f=await d.rs.get("MAX_DEPOSIT_AMOUNT")),f=parseFloat(f||"10000");let m=await d.rs.get("depositEnabled");m||(m=await d.rs.get("DEPOSIT_ENABLED")),m="true"===m||!0===m;let g=await d.rs.get("minConfirmations");if(g||(g=await d.rs.get("MIN_CONFIRMATIONS")),g=parseInt(g||"1"),!m)return o.NextResponse.json({success:!1,error:"Deposits are currently disabled"},{status:503});if(!i)return o.NextResponse.json({success:!1,error:"Deposit address not configured. Please contact support."},{status:503});return await d.AJ.create({action:"DEPOSIT_VERIFICATION_ATTEMPT",userId:r.id,details:{transactionId:s},ipAddress:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"}),await d.J6.create({userId:r.id,transactionId:s,amount:0,usdtAmount:0,tronAddress:i,senderAddress:"",blockNumber:"",blockTimestamp:new Date,confirmations:0}),await d.J6.updateStatus(s,"PENDING_VERIFICATION"),await d.DR.create({userId:r.id,type:"DEPOSIT",amount:0,description:`USDT TRC20 Deposit - TX: ${s}`,status:"PENDING"}),await c.j.addTransactionForVerification(s,i),await d.AJ.create({action:"DEPOSIT_SUBMITTED",userId:r.id,details:{transactionId:s,depositAddress:i,status:"PENDING_VERIFICATION"},ipAddress:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"}),o.NextResponse.json({success:!0,message:"Deposit transaction submitted successfully. We are now verifying your transaction. This may take up to 2 minutes.",data:{transactionId:s,status:"PENDING_VERIFICATION",estimatedVerificationTime:"Within 2 minutes",nextSteps:["Transaction verification in progress","Confirmation checking will begin once transaction is found",`Wallet will be credited automatically after ${g} confirmations`]}})}catch(t){if(console.error("Deposit verification error:",t),e.headers.get("authorization"))try{let{user:r}=await (0,u.b9)(e);r&&await d.AJ.create({action:"DEPOSIT_VERIFICATION_ERROR",userId:r.id,details:{error:t instanceof Error?t.message:"Unknown error"},ipAddress:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"})}catch(e){console.error("Failed to log error:",e)}return o.NextResponse.json({success:!1,error:"Failed to verify deposit. Please try again later."},{status:500})}}let m=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/wallet/deposit/verify/route",pathname:"/api/wallet/deposit/verify",filename:"route",bundlePath:"app/api/wallet/deposit/verify/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\wallet\\deposit\\verify\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:g,workUnitAsyncStorage:T,serverHooks:E}=m;function I(){return(0,n.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:T})}},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,7911,9526,925,9480,3161,1827],()=>r(45492));module.exports=s})();