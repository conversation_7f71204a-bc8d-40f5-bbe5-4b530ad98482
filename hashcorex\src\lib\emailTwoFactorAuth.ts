/**
 * Email-Based Two-Factor Authentication (2FA) System
 * Implements email OTP-based 2FA for enhanced security
 */

import { prisma } from './prisma';
import { systemLogDb, otpDb } from './database';
import { emailService, generateOTP } from './email';

// 2FA configuration
const OTP_EXPIRY_MINUTES = 10;
const MAX_ATTEMPTS = 3;
const LOCKOUT_DURATION_MINUTES = 30;

// 2FA interfaces
interface TwoFactorVerification {
  valid: boolean;
  attemptsRemaining?: number;
  lockedUntil?: Date;
}

interface TwoFactorStatus {
  enabled: boolean;
  enabledAt?: Date;
  lastUsed?: Date;
}

// Email-Based Two-Factor Authentication class
export class EmailTwoFactorAuth {
  
  // Check if user has 2FA enabled
  static async isEnabled(userId: string): Promise<boolean> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { twoFactorEnabled: true },
    });

    return user?.twoFactorEnabled || false;
  }

  // Get 2FA status for user
  static async getStatus(userId: string): Promise<TwoFactorStatus> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        twoFactorEnabled: true,
        twoFactorEnabledAt: true,
      },
    });

    return {
      enabled: user?.twoFactorEnabled || false,
      enabledAt: user?.twoFactorEnabledAt || undefined,
    };
  }

  // Enable 2FA for a user
  static async enableTwoFactor(
    userId: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<{ success: boolean; error?: string }> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        firstName: true,
        twoFactorEnabled: true,
      },
    });

    if (!user) {
      return { success: false, error: 'User not found' };
    }

    if (user.twoFactorEnabled) {
      return { success: false, error: '2FA is already enabled' };
    }

    // Enable 2FA
    await prisma.user.update({
      where: { id: userId },
      data: {
        twoFactorEnabled: true,
        twoFactorEnabledAt: new Date(),
      },
    });

    // Log 2FA enablement
    await systemLogDb.create({
      action: 'EMAIL_TWO_FACTOR_ENABLED',
      userId,
      details: {
        email: user.email,
        enabledAt: new Date().toISOString(),
      },
      ipAddress: ipAddress || 'unknown',
      userAgent: userAgent || 'unknown',
    });

    return { success: true };
  }

  // Disable 2FA for a user
  static async disableTwoFactor(
    userId: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<{ success: boolean; error?: string }> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        twoFactorEnabled: true,
      },
    });

    if (!user) {
      return { success: false, error: 'User not found' };
    }

    if (!user.twoFactorEnabled) {
      return { success: false, error: '2FA is not enabled' };
    }

    // Disable 2FA
    await prisma.user.update({
      where: { id: userId },
      data: {
        twoFactorEnabled: false,
        twoFactorEnabledAt: null,
      },
    });

    // Log 2FA disablement
    await systemLogDb.create({
      action: 'EMAIL_TWO_FACTOR_DISABLED',
      userId,
      details: {
        email: user.email,
        disabledAt: new Date().toISOString(),
      },
      ipAddress: ipAddress || 'unknown',
      userAgent: userAgent || 'unknown',
    });

    return { success: true };
  }

  // Send 2FA OTP via email
  static async sendTwoFactorOTP(
    userId: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<{ success: boolean; error?: string; expiresAt?: Date }> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        firstName: true,
        twoFactorEnabled: true,
      },
    });

    if (!user) {
      return { success: false, error: 'User not found' };
    }

    if (!user.twoFactorEnabled) {
      return { success: false, error: '2FA is not enabled for this user' };
    }

    // Generate OTP
    const otp = generateOTP();
    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + OTP_EXPIRY_MINUTES);

    // Save OTP to database
    await otpDb.create({
      email: user.email,
      otp,
      purpose: 'two_factor_auth',
      expiresAt,
    });

    // Send OTP email
    try {
      const emailSent = await emailService.sendOTPEmail(
        user.email,
        otp,
        user.firstName,
        'two_factor_auth'
      );

      if (emailSent) {
        // Log 2FA OTP sent
        await systemLogDb.create({
          action: 'EMAIL_TWO_FACTOR_OTP_SENT',
          userId,
          details: {
            email: user.email,
            sentAt: new Date().toISOString(),
            expiresAt: expiresAt.toISOString(),
          },
          ipAddress: ipAddress || 'unknown',
          userAgent: userAgent || 'unknown',
        });

        return { 
          success: true, 
          expiresAt 
        };
      } else {
        return { success: false, error: 'Failed to send 2FA code. Please try again.' };
      }
    } catch (error) {
      console.error('Failed to send 2FA OTP:', error);
      return { success: false, error: 'Failed to send 2FA code. Please try again.' };
    }
  }

  // Verify 2FA OTP
  static async verifyTwoFactorOTP(
    userId: string,
    otp: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<TwoFactorVerification> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        twoFactorEnabled: true,
      },
    });

    if (!user || !user.twoFactorEnabled) {
      return { valid: false };
    }

    // Verify OTP
    const otpRecord = await otpDb.findValid(user.email, 'two_factor_auth');
    
    if (!otpRecord) {
      // Log failed attempt
      await systemLogDb.create({
        action: 'EMAIL_TWO_FACTOR_VERIFICATION_FAILED',
        userId,
        details: {
          reason: 'No valid OTP found',
          failedAt: new Date().toISOString(),
        },
        ipAddress: ipAddress || 'unknown',
        userAgent: userAgent || 'unknown',
      });

      return { valid: false };
    }

    if (otpRecord.otp !== otp) {
      // Log failed attempt
      await systemLogDb.create({
        action: 'EMAIL_TWO_FACTOR_VERIFICATION_FAILED',
        userId,
        details: {
          reason: 'Invalid OTP',
          failedAt: new Date().toISOString(),
        },
        ipAddress: ipAddress || 'unknown',
        userAgent: userAgent || 'unknown',
      });

      return { valid: false };
    }

    // Mark OTP as verified
    await otpDb.markAsVerified(otpRecord.id);

    // Log successful verification
    await systemLogDb.create({
      action: 'EMAIL_TWO_FACTOR_VERIFIED',
      userId,
      details: {
        email: user.email,
        verifiedAt: new Date().toISOString(),
      },
      ipAddress: ipAddress || 'unknown',
      userAgent: userAgent || 'unknown',
    });

    return { valid: true };
  }

  // Check if user needs 2FA verification during login
  static async requiresTwoFactorVerification(userId: string): Promise<boolean> {
    return await this.isEnabled(userId);
  }

  // Clean up expired 2FA OTPs
  static async cleanupExpiredOTPs(): Promise<number> {
    try {
      const result = await prisma.oTP.deleteMany({
        where: {
          purpose: 'two_factor_auth',
          expiresAt: {
            lt: new Date(),
          },
        },
      });

      return result.count;
    } catch (error) {
      console.error('Failed to cleanup expired 2FA OTPs:', error);
      return 0;
    }
  }
}

// Cleanup job - should be run periodically
export async function cleanupExpired2FAOTPs(): Promise<void> {
  const cleaned = await EmailTwoFactorAuth.cleanupExpiredOTPs();
  if (cleaned > 0) {
    console.log(`Cleaned up ${cleaned} expired 2FA OTPs`);
  }
}

// Start cleanup interval (run every 30 minutes)
if (typeof window === 'undefined') { // Server-side only
  setInterval(cleanupExpired2FAOTPs, 30 * 60 * 1000); // 30 minutes
}

export default EmailTwoFactorAuth;
