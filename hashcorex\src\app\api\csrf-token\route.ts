/**
 * CSRF Token API Endpoint
 * Provides CSRF tokens for client-side requests
 */

import { NextRequest, NextResponse } from 'next/server';
import { CSRFProtection } from '@/lib/csrfProtection';
import { authenticateRequest } from '@/lib/auth';

// GET - Get CSRF token
export async function GET(request: NextRequest) {
  try {
    // Get user ID if authenticated (optional)
    let userId: string | undefined;
    
    try {
      const { authenticated, user } = await authenticateRequest(request);
      if (authenticated && user) {
        userId = user.id;
      }
    } catch (error) {
      // Continue without user ID - CSRF tokens can be issued to anonymous users
    }

    // Generate CSRF token
    const token = CSRFProtection.generateToken(userId);
    const expires = Date.now() + (60 * 60 * 1000); // 1 hour

    // Create response with CSRF token
    const response = NextResponse.json({
      success: true,
      data: {
        token,
        expires,
      },
    });

    // Set CSRF token in cookie
    CSRFProtection.setCsrfCookie(response, token);

    // Add security headers
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('X-Frame-Options', 'DENY');
    response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate');

    return response;

  } catch (error) {
    console.error('CSRF token generation error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to generate CSRF token' 
      },
      { status: 500 }
    );
  }
}

// POST - Validate CSRF token (for testing purposes)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { token } = body;

    if (!token) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Token is required' 
        },
        { status: 400 }
      );
    }

    // Get user ID if authenticated
    let userId: string | undefined;
    
    try {
      const { authenticated, user } = await authenticateRequest(request);
      if (authenticated && user) {
        userId = user.id;
      }
    } catch (error) {
      // Continue without user ID
    }

    // Validate token
    const isValid = CSRFProtection.validateToken(token, userId);

    return NextResponse.json({
      success: true,
      data: {
        valid: isValid,
        message: isValid ? 'Token is valid' : 'Token is invalid or expired',
      },
    });

  } catch (error) {
    console.error('CSRF token validation error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to validate CSRF token' 
      },
      { status: 500 }
    );
  }
}
