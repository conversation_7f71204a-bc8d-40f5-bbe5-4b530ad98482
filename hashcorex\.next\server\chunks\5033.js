"use strict";exports.id=5033,exports.ids=[5033],exports.modules={39542:(e,t,r)=>{r.d(t,{b9:()=>p,HU:()=>E,qc:()=>y,Lx:()=>g,DY:()=>S,DT:()=>R});var s=r(85663),a=r(43205),n=r.n(a),i=r(6710),o=r(45697);let l=o.z.object({DATABASE_URL:o.z.string().url("DATABASE_URL must be a valid PostgreSQL URL"),DIRECT_URL:o.z.string().url("DIRECT_URL must be a valid PostgreSQL URL"),JWT_SECRET:o.z.string().min(32,"JWT_SECRET must be at least 32 characters long").refine(e=>{let t=/[A-Z]/.test(e),r=/[a-z]/.test(e),s=/\d/.test(e),a=/[!@#$%^&*(),.?":{}|<>]/.test(e);return t&&r&&s&&a},"JWT_SECRET should contain uppercase, lowercase, numbers, and special characters"),JWT_EXPIRES_IN:o.z.string().default("30d"),NODE_ENV:o.z.enum(["development","production","test"]).default("development"),NEXT_PUBLIC_APP_URL:o.z.string().url().optional(),PORT:o.z.string().regex(/^\d+$/).transform(Number).default("3000"),SMTP_HOST:o.z.string().optional(),SMTP_PORT:o.z.string().regex(/^\d+$/).transform(Number).optional(),SMTP_USER:o.z.string().email().optional(),SMTP_PASSWORD:o.z.string().optional(),SMTP_SECURE:o.z.string().transform(e=>"true"===e).optional(),TRON_NETWORK:o.z.enum(["mainnet","testnet"]).default("testnet"),TRON_API_KEY:o.z.string().optional(),USDT_CONTRACT_ADDRESS:o.z.string().optional(),MAX_FILE_SIZE:o.z.string().regex(/^\d+$/).transform(Number).default("10485760"),UPLOAD_DIR:o.z.string().default("./public/uploads"),BCRYPT_ROUNDS:o.z.string().regex(/^\d+$/).transform(Number).default("12"),SESSION_TIMEOUT:o.z.string().regex(/^\d+$/).transform(Number).default("1800"),RATE_LIMIT_WINDOW_MS:o.z.string().regex(/^\d+$/).transform(Number).default("900000"),RATE_LIMIT_MAX_REQUESTS:o.z.string().regex(/^\d+$/).transform(Number).default("100"),COINGECKO_API_URL:o.z.string().url().default("https://api.coingecko.com/api/v3"),LOG_LEVEL:o.z.enum(["error","warn","info","debug"]).default("info"),ENABLE_REQUEST_LOGGING:o.z.string().transform(e=>"true"===e).default("false"),ENABLE_REGISTRATION:o.z.string().transform(e=>"true"===e).default("true"),ENABLE_KYC:o.z.string().transform(e=>"true"===e).default("true"),ENABLE_WITHDRAWALS:o.z.string().transform(e=>"true"===e).default("true")}).refine(e=>{let t=[e.SMTP_HOST,e.SMTP_PORT,e.SMTP_USER,e.SMTP_PASSWORD],r=t.some(e=>void 0!==e),s=t.every(e=>void 0!==e);return!r||!!s},{message:"If SMTP configuration is provided, all SMTP fields (HOST, PORT, USER, PASSWORD) must be provided"});function d(){try{let e=l.safeParse(process.env);if(!e.success){let t=e.error.errors.map(e=>`${e.path.join(".")}: ${e.message}`);return{success:!1,errors:t}}return{success:!0,data:e.data}}catch(e){return{success:!1,errors:["Failed to validate environment variables"]}}}let u=null;function c(){if(!u){let e=d();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),u=e.data,console.log("✅ Environment variables validated successfully")}return u}let f={jwt:{secret:()=>c().JWT_SECRET,expiresIn:()=>c().JWT_EXPIRES_IN},security:{bcryptRounds:()=>c().BCRYPT_ROUNDS,sessionTimeout:()=>c().SESSION_TIMEOUT,maxFileSize:()=>c().MAX_FILE_SIZE,uploadDir:()=>c().UPLOAD_DIR}};if("phase-production-build"!==process.env.NEXT_PHASE){let e=d();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),console.log("✅ Environment variables validated successfully")}let I=async e=>await s.Ay.hash(e,f.security.bcryptRounds()),T=async(e,t)=>await s.Ay.compare(e,t),E=e=>n().sign(e,f.jwt.secret(),{expiresIn:f.jwt.expiresIn()}),m=e=>{try{return n().verify(e,f.jwt.secret())}catch(e){return null}},A=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},p=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=m(t);if(!r)return{authenticated:!1,user:null};let s=await i.Gy.findByEmail(r.email);return s?{authenticated:!0,user:s}:{authenticated:!1,user:null}},S=async e=>{let t,s;if(await i.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await i.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let a=await I(e.password),n=!1;do s=A(),n=!await i.Gy.findByReferralId(s);while(!n);let o=await i.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:a,referralId:s});if(t){let{placeUserByReferralType:s}=await r.e(2746).then(r.bind(r,2746)),a="general";"left"===e.placementSide?a="left":"right"===e.placementSide&&(a="right"),await s(t,o.id,a)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},g=async e=>{let t=await i.Gy.findByEmail(e.email);if(!t||!await T(e.password,t.password))throw Error("Invalid email or password");return{token:E({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},R=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),y=async e=>{let t=await i.Gy.findById(e);return t?.role==="ADMIN"}},82180:(e,t,r)=>{r.d(t,{N1:()=>c,U5:()=>T,UL:()=>I,nC:()=>f,pz:()=>E});var s=r(32190),a=r(6710);let n={VALIDATION:"Invalid input provided",AUTHENTICATION:"Authentication failed",AUTHORIZATION:"Access denied",RATE_LIMIT:"Too many requests",DATABASE:"Service temporarily unavailable",EXTERNAL_API:"External service error",FILE_UPLOAD:"File upload failed",BUSINESS_LOGIC:"Operation failed",SYSTEM:"Internal server error",UNKNOWN:"An unexpected error occurred"},i={VALIDATION:400,AUTHENTICATION:401,AUTHORIZATION:403,RATE_LIMIT:429,DATABASE:503,EXTERNAL_API:502,FILE_UPLOAD:400,BUSINESS_LOGIC:422,SYSTEM:500,UNKNOWN:500};function o(e,t,r,s,a){return{type:e,severity:t,message:r,userMessage:s||n[e],statusCode:i[e],details:function(e){if(!e)return;let t={};for(let[r,s]of Object.entries(e))r.toLowerCase().includes("password")||r.toLowerCase().includes("secret")||r.toLowerCase().includes("token")||r.toLowerCase().includes("key")||("string"==typeof s?t[r]=s.substring(0,1e3):"number"==typeof s||"boolean"==typeof s?t[r]=s:Array.isArray(s)?t[r]=s.slice(0,10):"object"==typeof s&&null!==s&&(t[r]="[Object]"));return t}(a),shouldLog:"LOW"!==t,shouldAlert:"CRITICAL"===t}}async function l(e,t){let r;return(r=e instanceof Error?function(e){let t=e.message.toLowerCase();return t.includes("prisma")||t.includes("database")||t.includes("connection")?o("DATABASE","HIGH",e.message,"Database service temporarily unavailable"):t.includes("validation")||t.includes("invalid")||t.includes("required")?o("VALIDATION","LOW",e.message,"Invalid input provided"):t.includes("unauthorized")||t.includes("authentication")||t.includes("token")?o("AUTHENTICATION","MEDIUM",e.message,"Authentication required"):t.includes("forbidden")||t.includes("access denied")||t.includes("permission")?o("AUTHORIZATION","MEDIUM",e.message,"Access denied"):t.includes("file")||t.includes("upload")||t.includes("multipart")?o("FILE_UPLOAD","LOW",e.message,"File upload failed"):o("SYSTEM","MEDIUM",e.message,"Internal server error")}(e):"object"==typeof e&&null!==e&&"type"in e?e:o("UNKNOWN","MEDIUM","Unknown error occurred","An unexpected error occurred")).shouldLog&&await d(r,t),r.shouldAlert&&await u(r,t),r}async function d(e,t){try{await a.AJ.create({action:`ERROR_${e.type}`,userId:t?.userId,details:{type:e.type,severity:e.severity,message:e.message,statusCode:e.statusCode,endpoint:t?.endpoint,requestId:t?.requestId,timestamp:new Date().toISOString(),...e.details},ipAddress:t?.ipAddress||"unknown",userAgent:t?.userAgent||"unknown"})}catch(e){console.error("Failed to log secure error:",e)}}async function u(e,t){console.error("CRITICAL ERROR ALERT:",{type:e.type,severity:e.severity,message:e.message,context:t,timestamp:new Date().toISOString()})}function c(e){let t=s.NextResponse.json({success:!1,error:e.userMessage,type:e.type,...!1},{status:e.statusCode});return t.headers.set("X-Content-Type-Options","nosniff"),t.headers.set("X-Frame-Options","DENY"),t.headers.set("X-XSS-Protection","1; mode=block"),"RATE_LIMIT"===e.type&&e.details&&(e.details.retryAfter&&t.headers.set("Retry-After",e.details.retryAfter.toString()),void 0!==e.details.remaining&&t.headers.set("X-RateLimit-Remaining",e.details.remaining.toString()),e.details.resetTime&&t.headers.set("X-RateLimit-Reset",new Date(e.details.resetTime).toISOString())),t}function f(e,t){return async(...r)=>{try{return await e(...r)}catch(a){let e=r[0],s={endpoint:t?.endpoint||e?.url||"unknown",ipAddress:e?.headers?.get?.("x-forwarded-for")||"unknown",userAgent:e?.headers?.get?.("user-agent")||"unknown",requestId:e?.headers?.get?.("x-request-id")||void 0};return c(await l(a,s))}}}function I(e,t){return o("VALIDATION","LOW",e,e,t)}function T(e,t,r){return o("RATE_LIMIT","MEDIUM","Rate limit exceeded","Too many requests. Please try again later.",{retryAfter:e,remaining:t,resetTime:r})}function E(e){return o("AUTHENTICATION","MEDIUM",e||"Authentication failed","Authentication required")}}};