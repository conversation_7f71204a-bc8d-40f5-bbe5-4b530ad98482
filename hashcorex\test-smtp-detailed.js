const nodemailer = require('nodemailer');
const { PrismaClient } = require('@prisma/client');
const dns = require('dns').promises;

const prisma = new PrismaClient();

async function testSMTPDetailed() {
  try {
    console.log('🔍 Detailed SMTP Connection Test');
    console.log('================================\n');
    
    // Get email settings from database
    const emailKeys = [
      'smtpHost',
      'smtpPort', 
      'smtpSecure',
      'smtpUser',
      'smtpPassword',
      'fromName',
      'fromEmail',
      'emailEnabled'
    ];
    
    const settings = await prisma.adminSettings.findMany({
      where: {
        key: {
          in: emailKeys
        }
      }
    });
    
    const settingsObject = {};
    settings.forEach(setting => {
      settingsObject[setting.key] = setting.value;
    });
    
    console.log('📧 Current SMTP Settings:');
    console.log(`Host: ${settingsObject.smtpHost}`);
    console.log(`Port: ${settingsObject.smtpPort}`);
    console.log(`Secure: ${settingsObject.smtpSecure}`);
    console.log(`User: ${settingsObject.smtpUser}`);
    console.log(`From: ${settingsObject.fromEmail}\n`);
    
    // Step 1: DNS Resolution Test
    console.log('🌐 Step 1: DNS Resolution Test');
    console.log('------------------------------');
    try {
      const addresses = await dns.lookup(settingsObject.smtpHost);
      console.log(`✅ DNS Resolution successful: ${settingsObject.smtpHost} → ${addresses.address}`);
    } catch (dnsError) {
      console.log(`❌ DNS Resolution failed: ${dnsError.message}`);
      console.log('💡 Suggestions:');
      console.log('   - Check if the hostname is correct');
      console.log('   - Try alternative hostnames:');
      console.log(`     • mail.${settingsObject.smtpHost.replace('premium315.', '')}`);
      console.log(`     • smtp.${settingsObject.smtpHost.replace('premium315.', '')}`);
      console.log('   - Contact your hosting provider for correct SMTP settings');
      return;
    }
    
    // Step 2: Port Connectivity Test
    console.log('\n🔌 Step 2: Port Connectivity Test');
    console.log('----------------------------------');
    const net = require('net');
    
    const testConnection = () => {
      return new Promise((resolve, reject) => {
        const socket = new net.Socket();
        const timeout = setTimeout(() => {
          socket.destroy();
          reject(new Error('Connection timeout'));
        }, 10000);
        
        socket.connect(parseInt(settingsObject.smtpPort), settingsObject.smtpHost, () => {
          clearTimeout(timeout);
          socket.destroy();
          resolve(true);
        });
        
        socket.on('error', (err) => {
          clearTimeout(timeout);
          reject(err);
        });
      });
    };
    
    try {
      await testConnection();
      console.log(`✅ Port ${settingsObject.smtpPort} is accessible on ${settingsObject.smtpHost}`);
    } catch (portError) {
      console.log(`❌ Port connectivity failed: ${portError.message}`);
      console.log('💡 Suggestions:');
      console.log('   - Check if the port number is correct');
      console.log('   - Common SMTP ports: 25, 465 (SSL), 587 (TLS)');
      console.log('   - Check firewall settings');
      return;
    }
    
    // Step 3: SMTP Authentication Test
    console.log('\n🔐 Step 3: SMTP Authentication Test');
    console.log('-----------------------------------');
    
    const transporter = nodemailer.createTransport({
      host: settingsObject.smtpHost,
      port: parseInt(settingsObject.smtpPort),
      secure: settingsObject.smtpSecure === 'true',
      auth: {
        user: settingsObject.smtpUser,
        pass: settingsObject.smtpPassword,
      },
      tls: {
        rejectUnauthorized: false,
      },
      connectionTimeout: 10000,
      greetingTimeout: 5000,
      socketTimeout: 10000,
    });
    
    try {
      const verified = await transporter.verify();
      console.log('✅ SMTP Authentication successful');
      console.log('✅ Email service is ready to send emails');
      
      // Optional: Send a test email to yourself
      console.log('\n📨 Sending test email...');
      const testEmail = {
        from: `"${settingsObject.fromName}" <${settingsObject.fromEmail}>`,
        to: settingsObject.smtpUser,
        subject: 'SMTP Test - HashCoreX',
        text: 'This is a test email to verify SMTP configuration.',
        html: '<p>This is a test email to verify SMTP configuration.</p><p><strong>✅ Your SMTP settings are working correctly!</strong></p>'
      };
      
      const result = await transporter.sendMail(testEmail);
      console.log(`✅ Test email sent successfully: ${result.messageId}`);
      console.log(`📬 Check your inbox at: ${settingsObject.smtpUser}`);
      
    } catch (authError) {
      console.log(`❌ SMTP Authentication failed: ${authError.message}`);
      console.log('💡 Suggestions:');
      console.log('   - Verify username and password are correct');
      console.log('   - Check if 2FA is enabled (may need app password)');
      console.log('   - Ensure "Less secure app access" is enabled (for Gmail)');
      console.log('   - Contact your hosting provider for SMTP credentials');
      
      if (authError.responseCode) {
        console.log(`   - SMTP Error Code: ${authError.responseCode}`);
      }
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testSMTPDetailed();
