"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[36],{646:(e,t,r)=>{r.d(t,{A:()=>o});let o=(0,r(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},901:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RouterContext",{enumerable:!0,get:function(){return o}});let o=r(8229)._(r(2115)).default.createContext(null)},1007:(e,t,r)=>{r.d(t,{A:()=>o});let o=(0,r(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1193:(e,t)=>{function r(e){var t;let{config:r,src:o,width:n,quality:a}=e,l=a||(null==(t=r.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return r.path+"?url="+encodeURIComponent(o)+"&w="+n+"&q="+l+(o.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o}}),r.__next_img_default=!0;let o=r},1243:(e,t,r)=>{r.d(t,{A:()=>o});let o=(0,r(9946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},1284:(e,t,r)=>{r.d(t,{A:()=>o});let o=(0,r(9946).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},1469:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return s},getImageProps:function(){return i}});let o=r(8229),n=r(8883),a=r(3063),l=o._(r(1193));function i(e){let{props:t}=(0,n.getImgProps)(e,{defaultLoader:l.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let s=a.Image},2085:(e,t,r)=>{r.d(t,{F:()=>l});var o=r(2596);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=o.$,l=(e,t)=>r=>{var o;if((null==t?void 0:t.variants)==null)return a(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:l,defaultVariants:i}=t,s=Object.keys(l).map(e=>{let t=null==r?void 0:r[e],o=null==i?void 0:i[e];if(null===t)return null;let a=n(t)||n(o);return l[e][a]}),d=r&&Object.entries(r).reduce((e,t)=>{let[r,o]=t;return void 0===o||(e[r]=o),e},{});return a(e,s,null==t||null==(o=t.compoundVariants)?void 0:o.reduce((e,t)=>{let{class:r,className:o,...n}=t;return Object.entries(n).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...i,...d}[t]):({...i,...d})[t]===r})?[...e,r,o]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},2464:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AmpStateContext",{enumerable:!0,get:function(){return o}});let o=r(8229)._(r(2115)).default.createContext({})},2596:(e,t,r)=>{r.d(t,{$:()=>o});function o(){for(var e,t,r=0,o="",n=arguments.length;r<n;r++)(e=arguments[r])&&(t=function e(t){var r,o,n="";if("string"==typeof t||"number"==typeof t)n+=t;else if("object"==typeof t)if(Array.isArray(t)){var a=t.length;for(r=0;r<a;r++)t[r]&&(o=e(t[r]))&&(n&&(n+=" "),n+=o)}else for(o in t)t[o]&&(n&&(n+=" "),n+=o);return n}(e))&&(o&&(o+=" "),o+=t);return o}},2664:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return a}});let o=r(9991),n=r(7102);function a(e){if(!(0,o.isAbsoluteUrl)(e))return!0;try{let t=(0,o.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,n.hasBasePath)(r.pathname)}catch(e){return!1}}},2757:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{formatUrl:function(){return a},formatWithValidation:function(){return i},urlObjectKeys:function(){return l}});let o=r(6966)._(r(8859)),n=/https?|ftp|gopher|file/;function a(e){let{auth:t,hostname:r}=e,a=e.protocol||"",l=e.pathname||"",i=e.hash||"",s=e.query||"",d=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?d=t+e.host:r&&(d=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(d+=":"+e.port)),s&&"object"==typeof s&&(s=String(o.urlQueryToSearchParams(s)));let c=e.search||s&&"?"+s||"";return a&&!a.endsWith(":")&&(a+=":"),e.slashes||(!a||n.test(a))&&!1!==d?(d="//"+(d||""),l&&"/"!==l[0]&&(l="/"+l)):d||(d=""),i&&"#"!==i[0]&&(i="#"+i),c&&"?"!==c[0]&&(c="?"+c),""+a+d+(l=l.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+i}let l=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function i(e){return a(e)}},3063:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return w}});let o=r(8229),n=r(6966),a=r(5155),l=n._(r(2115)),i=o._(r(7650)),s=o._(r(5564)),d=r(8883),c=r(5840),u=r(6752);r(3230);let f=r(901),p=o._(r(1193)),m=r(6654),g={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function b(e,t,r,o,n,a,l){let i=null==e?void 0:e.src;e&&e["data-loaded-src"]!==i&&(e["data-loaded-src"]=i,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&n(!0),null==r?void 0:r.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let o=!1,n=!1;r.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>o,isPropagationStopped:()=>n,persist:()=>{},preventDefault:()=>{o=!0,t.preventDefault()},stopPropagation:()=>{n=!0,t.stopPropagation()}})}(null==o?void 0:o.current)&&o.current(e)}}))}function h(e){return l.use?{fetchPriority:e}:{fetchpriority:e}}let y=(0,l.forwardRef)((e,t)=>{let{src:r,srcSet:o,sizes:n,height:i,width:s,decoding:d,className:c,style:u,fetchPriority:f,placeholder:p,loading:g,unoptimized:y,fill:v,onLoadRef:w,onLoadingCompleteRef:x,setBlurComplete:k,setShowAltText:_,sizesInput:j,onLoad:P,onError:z,...O}=e,C=(0,l.useCallback)(e=>{e&&(z&&(e.src=e.src),e.complete&&b(e,p,w,x,k,y,j))},[r,p,w,x,k,z,y,j]),E=(0,m.useMergedRef)(t,C);return(0,a.jsx)("img",{...O,...h(f),loading:g,width:s,height:i,decoding:d,"data-nimg":v?"fill":"1",className:c,style:u,sizes:n,srcSet:o,src:r,ref:E,onLoad:e=>{b(e.currentTarget,p,w,x,k,y,j)},onError:e=>{_(!0),"empty"!==p&&k(!0),z&&z(e)}})});function v(e){let{isAppRouter:t,imgAttributes:r}=e,o={as:"image",imageSrcSet:r.srcSet,imageSizes:r.sizes,crossOrigin:r.crossOrigin,referrerPolicy:r.referrerPolicy,...h(r.fetchPriority)};return t&&i.default.preload?(i.default.preload(r.src,o),null):(0,a.jsx)(s.default,{children:(0,a.jsx)("link",{rel:"preload",href:r.srcSet?void 0:r.src,...o},"__nimg-"+r.src+r.srcSet+r.sizes)})}let w=(0,l.forwardRef)((e,t)=>{let r=(0,l.useContext)(f.RouterContext),o=(0,l.useContext)(u.ImageConfigContext),n=(0,l.useMemo)(()=>{var e;let t=g||o||c.imageConfigDefault,r=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),n=t.deviceSizes.sort((e,t)=>e-t),a=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:r,deviceSizes:n,qualities:a}},[o]),{onLoad:i,onLoadingComplete:s}=e,m=(0,l.useRef)(i);(0,l.useEffect)(()=>{m.current=i},[i]);let b=(0,l.useRef)(s);(0,l.useEffect)(()=>{b.current=s},[s]);let[h,w]=(0,l.useState)(!1),[x,k]=(0,l.useState)(!1),{props:_,meta:j}=(0,d.getImgProps)(e,{defaultLoader:p.default,imgConf:n,blurComplete:h,showAltText:x});return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(y,{..._,unoptimized:j.unoptimized,placeholder:j.placeholder,fill:j.fill,onLoadRef:m,onLoadingCompleteRef:b,setBlurComplete:w,setShowAltText:k,sizesInput:e.sizes,ref:t}),j.priority?(0,a.jsx)(v,{isAppRouter:!r,imgAttributes:_}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3180:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},4355:(e,t,r)=>{r.d(t,{A:()=>o});let o=(0,r(9946).A)("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])},4416:(e,t,r)=>{r.d(t,{A:()=>o});let o=(0,r(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},5029:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return l}});let o=r(2115),n=o.useLayoutEffect,a=o.useEffect;function l(e){let{headManager:t,reduceComponentsToState:r}=e;function l(){if(t&&t.mountedInstances){let n=o.Children.toArray(Array.from(t.mountedInstances).filter(Boolean));t.updateHead(r(n,e))}}return n(()=>{var r;return null==t||null==(r=t.mountedInstances)||r.add(e.children),()=>{var r;null==t||null==(r=t.mountedInstances)||r.delete(e.children)}}),n(()=>(t&&(t._pendingUpdate=l),()=>{t&&(t._pendingUpdate=l)})),a(()=>(t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null),()=>{t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null)})),null}},5100:(e,t)=>{function r(e){let{widthInt:t,heightInt:r,blurWidth:o,blurHeight:n,blurDataURL:a,objectFit:l}=e,i=o?40*o:t,s=n?40*n:r,d=i&&s?"viewBox='0 0 "+i+" "+s+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+d+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(d?"none":"contain"===l?"xMidYMid":"cover"===l?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+a+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},5339:(e,t,r)=>{r.d(t,{A:()=>o});let o=(0,r(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5564:(e,t,r)=>{var o=r(9509);Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return b},defaultHead:function(){return f}});let n=r(8229),a=r(6966),l=r(5155),i=a._(r(2115)),s=n._(r(5029)),d=r(2464),c=r(2830),u=r(7544);function f(e){void 0===e&&(e=!1);let t=[(0,l.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,l.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function p(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===i.default.Fragment?e.concat(i.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}r(3230);let m=["name","httpEquiv","charSet","itemProp"];function g(e,t){let{inAmpMode:r}=t;return e.reduce(p,[]).reverse().concat(f(r).reverse()).filter(function(){let e=new Set,t=new Set,r=new Set,o={};return n=>{let a=!0,l=!1;if(n.key&&"number"!=typeof n.key&&n.key.indexOf("$")>0){l=!0;let t=n.key.slice(n.key.indexOf("$")+1);e.has(t)?a=!1:e.add(t)}switch(n.type){case"title":case"base":t.has(n.type)?a=!1:t.add(n.type);break;case"meta":for(let e=0,t=m.length;e<t;e++){let t=m[e];if(n.props.hasOwnProperty(t))if("charSet"===t)r.has(t)?a=!1:r.add(t);else{let e=n.props[t],r=o[t]||new Set;("name"!==t||!l)&&r.has(e)?a=!1:(r.add(e),o[t]=r)}}}return a}}()).reverse().map((e,t)=>{let n=e.key||t;if(o.env.__NEXT_OPTIMIZE_FONTS&&!r&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,i.default.cloneElement(e,t)}return i.default.cloneElement(e,{key:n})})}let b=function(e){let{children:t}=e,r=(0,i.useContext)(d.AmpStateContext),o=(0,i.useContext)(c.HeadManagerContext);return(0,l.jsx)(s.default,{reduceComponentsToState:g,headManager:o,inAmpMode:(0,u.isInAmpMode)(r),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5840:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return o}});let r=["default","imgix","cloudinary","akamai","custom"],o={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},6654:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return n}});let o=r(2115);function n(e,t){let r=(0,o.useRef)(null),n=(0,o.useRef)(null);return(0,o.useCallback)(o=>{if(null===o){let e=r.current;e&&(r.current=null,e());let t=n.current;t&&(n.current=null,t())}else e&&(r.current=a(e,o)),t&&(n.current=a(t,o))},[e,t])}function a(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6752:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ImageConfigContext",{enumerable:!0,get:function(){return a}});let o=r(8229)._(r(2115)),n=r(5840),a=o.default.createContext(n.imageConfigDefault)},6766:(e,t,r)=>{r.d(t,{default:()=>n.a});var o=r(1469),n=r.n(o)},6874:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return b},useLinkStatus:function(){return y}});let o=r(6966),n=r(5155),a=o._(r(2115)),l=r(2757),i=r(5227),s=r(9818),d=r(6654),c=r(9991),u=r(5929);r(3230);let f=r(4930),p=r(2664),m=r(6634);function g(e){return"string"==typeof e?e:(0,l.formatUrl)(e)}function b(e){let t,r,o,[l,b]=(0,a.useOptimistic)(f.IDLE_LINK_STATUS),y=(0,a.useRef)(null),{href:v,as:w,children:x,prefetch:k=null,passHref:_,replace:j,shallow:P,scroll:z,onClick:O,onMouseEnter:C,onTouchStart:E,legacyBehavior:S=!1,onNavigate:M,ref:A,unstable_dynamicOnHover:N,...R}=e;t=x,S&&("string"==typeof t||"number"==typeof t)&&(t=(0,n.jsx)("a",{children:t}));let I=a.default.useContext(i.AppRouterContext),T=!1!==k,L=null===k?s.PrefetchKind.AUTO:s.PrefetchKind.FULL,{href:U,as:D}=a.default.useMemo(()=>{let e=g(v);return{href:e,as:w?g(w):e}},[v,w]);S&&(r=a.default.Children.only(t));let F=S?r&&"object"==typeof r&&r.ref:A,G=a.default.useCallback(e=>(null!==I&&(y.current=(0,f.mountLinkInstance)(e,U,I,L,T,b)),()=>{y.current&&((0,f.unmountLinkForCurrentNavigation)(y.current),y.current=null),(0,f.unmountPrefetchableInstance)(e)}),[T,U,I,L,b]),W={ref:(0,d.useMergedRef)(G,F),onClick(e){S||"function"!=typeof O||O(e),S&&r.props&&"function"==typeof r.props.onClick&&r.props.onClick(e),I&&(e.defaultPrevented||function(e,t,r,o,n,l,i){let{nodeName:s}=e.currentTarget;if(!("A"===s.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,p.isLocalURL)(t)){n&&(e.preventDefault(),location.replace(t));return}e.preventDefault(),a.default.startTransition(()=>{if(i){let e=!1;if(i({preventDefault:()=>{e=!0}}),e)return}(0,m.dispatchNavigateAction)(r||t,n?"replace":"push",null==l||l,o.current)})}}(e,U,D,y,j,z,M))},onMouseEnter(e){S||"function"!=typeof C||C(e),S&&r.props&&"function"==typeof r.props.onMouseEnter&&r.props.onMouseEnter(e),I&&T&&(0,f.onNavigationIntent)(e.currentTarget,!0===N)},onTouchStart:function(e){S||"function"!=typeof E||E(e),S&&r.props&&"function"==typeof r.props.onTouchStart&&r.props.onTouchStart(e),I&&T&&(0,f.onNavigationIntent)(e.currentTarget,!0===N)}};return(0,c.isAbsoluteUrl)(D)?W.href=D:S&&!_&&("a"!==r.type||"href"in r.props)||(W.href=(0,u.addBasePath)(D)),o=S?a.default.cloneElement(r,W):(0,n.jsx)("a",{...R,...W,children:t}),(0,n.jsx)(h.Provider,{value:l,children:o})}r(3180);let h=(0,a.createContext)(f.IDLE_LINK_STATUS),y=()=>(0,a.useContext)(h);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7544:(e,t)=>{function r(e){let{ampFirst:t=!1,hybrid:r=!1,hasQuery:o=!1}=void 0===e?{}:e;return t||r&&o}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return r}})},8859:(e,t)=>{function r(e){let t={};for(let[r,o]of e.entries()){let e=t[r];void 0===e?t[r]=o:Array.isArray(e)?e.push(o):t[r]=[e,o]}return t}function o(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function n(e){let t=new URLSearchParams;for(let[r,n]of Object.entries(e))if(Array.isArray(n))for(let e of n)t.append(r,o(e));else t.set(r,o(n));return t}function a(e){for(var t=arguments.length,r=Array(t>1?t-1:0),o=1;o<t;o++)r[o-1]=arguments[o];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,o]of t.entries())e.append(r,o)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return a},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return n}})},8883:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return s}}),r(3230);let o=r(5100),n=r(5840),a=["-moz-initial","fill","none","scale-down",void 0];function l(e){return void 0!==e.default}function i(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function s(e,t){var r,s;let d,c,u,{src:f,sizes:p,unoptimized:m=!1,priority:g=!1,loading:b,className:h,quality:y,width:v,height:w,fill:x=!1,style:k,overrideSrc:_,onLoad:j,onLoadingComplete:P,placeholder:z="empty",blurDataURL:O,fetchPriority:C,decoding:E="async",layout:S,objectFit:M,objectPosition:A,lazyBoundary:N,lazyRoot:R,...I}=e,{imgConf:T,showAltText:L,blurComplete:U,defaultLoader:D}=t,F=T||n.imageConfigDefault;if("allSizes"in F)d=F;else{let e=[...F.deviceSizes,...F.imageSizes].sort((e,t)=>e-t),t=F.deviceSizes.sort((e,t)=>e-t),o=null==(r=F.qualities)?void 0:r.sort((e,t)=>e-t);d={...F,allSizes:e,deviceSizes:t,qualities:o}}if(void 0===D)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let G=I.loader||D;delete I.loader,delete I.srcSet;let W="__next_img_default"in G;if(W){if("custom"===d.loader)throw Object.defineProperty(Error('Image with src "'+f+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=G;G=t=>{let{config:r,...o}=t;return e(o)}}if(S){"fill"===S&&(x=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[S];e&&(k={...k,...e});let t={responsive:"100vw",fill:"100vw"}[S];t&&!p&&(p=t)}let $="",q=i(v),B=i(w);if((s=f)&&"object"==typeof s&&(l(s)||void 0!==s.src)){let e=l(f)?f.default:f;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(c=e.blurWidth,u=e.blurHeight,O=O||e.blurDataURL,$=e.src,!x)if(q||B){if(q&&!B){let t=q/e.width;B=Math.round(e.height*t)}else if(!q&&B){let t=B/e.height;q=Math.round(e.width*t)}}else q=e.width,B=e.height}let K=!g&&("lazy"===b||void 0===b);(!(f="string"==typeof f?f:$)||f.startsWith("data:")||f.startsWith("blob:"))&&(m=!0,K=!1),d.unoptimized&&(m=!0),W&&!d.dangerouslyAllowSVG&&f.split("?",1)[0].endsWith(".svg")&&(m=!0);let V=i(y),H=Object.assign(x?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:M,objectPosition:A}:{},L?{}:{color:"transparent"},k),X=U||"empty"===z?null:"blur"===z?'url("data:image/svg+xml;charset=utf-8,'+(0,o.getImageBlurSvg)({widthInt:q,heightInt:B,blurWidth:c,blurHeight:u,blurDataURL:O||"",objectFit:H.objectFit})+'")':'url("'+z+'")',Z=a.includes(H.objectFit)?"fill"===H.objectFit?"100% 100%":"cover":H.objectFit,Q=X?{backgroundSize:Z,backgroundPosition:H.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:X}:{},J=function(e){let{config:t,src:r,unoptimized:o,width:n,quality:a,sizes:l,loader:i}=e;if(o)return{src:r,srcSet:void 0,sizes:void 0};let{widths:s,kind:d}=function(e,t,r){let{deviceSizes:o,allSizes:n}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let o;o=e.exec(r);)t.push(parseInt(o[2]));if(t.length){let e=.01*Math.min(...t);return{widths:n.filter(t=>t>=o[0]*e),kind:"w"}}return{widths:n,kind:"w"}}return"number"!=typeof t?{widths:o,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>n.find(t=>t>=e)||n[n.length-1]))],kind:"x"}}(t,n,l),c=s.length-1;return{sizes:l||"w"!==d?l:"100vw",srcSet:s.map((e,o)=>i({config:t,src:r,quality:a,width:e})+" "+("w"===d?e:o+1)+d).join(", "),src:i({config:t,src:r,quality:a,width:s[c]})}}({config:d,src:f,unoptimized:m,width:q,quality:V,sizes:p,loader:G});return{props:{...I,loading:K?"lazy":b,fetchPriority:C,width:q,height:B,decoding:E,className:h,style:{...H,...Q},sizes:J.sizes,srcSet:J.srcSet,src:_||J.src},meta:{unoptimized:m,priority:g,placeholder:z,fill:x}}}},9688:(e,t,r)=>{r.d(t,{QP:()=>ed});let o=e=>{let t=i(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:o}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),n(r,t)||l(e)},getConflictingClassGroupIds:(e,t)=>{let n=r[e]||[];return t&&o[e]?[...n,...o[e]]:n}}},n=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],o=t.nextPart.get(r),a=o?n(e.slice(1),o):void 0;if(a)return a;if(0===t.validators.length)return;let l=e.join("-");return t.validators.find(({validator:e})=>e(l))?.classGroupId},a=/^\[(.+)\]$/,l=e=>{if(a.test(e)){let t=a.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},i=e=>{let{theme:t,classGroups:r}=e,o={nextPart:new Map,validators:[]};for(let e in r)s(r[e],o,e,t);return o},s=(e,t,r,o)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:d(t,e)).classGroupId=r;return}if("function"==typeof e)return c(e)?void s(e(o),t,r,o):void t.validators.push({validator:e,classGroupId:r});Object.entries(e).forEach(([e,n])=>{s(n,d(t,e),r,o)})})},d=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},c=e=>e.isThemeGetter,u=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,o=new Map,n=(n,a)=>{r.set(n,a),++t>e&&(t=0,o=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=o.get(e))?(n(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):n(e,t)}}},f=e=>{let{prefix:t,experimentalParseClassName:r}=e,o=e=>{let t,r=[],o=0,n=0,a=0;for(let l=0;l<e.length;l++){let i=e[l];if(0===o&&0===n){if(":"===i){r.push(e.slice(a,l)),a=l+1;continue}if("/"===i){t=l;continue}}"["===i?o++:"]"===i?o--:"("===i?n++:")"===i&&n--}let l=0===r.length?e:e.substring(a),i=p(l);return{modifiers:r,hasImportantModifier:i!==l,baseClassName:i,maybePostfixModifierPosition:t&&t>a?t-a:void 0}};if(t){let e=t+":",r=o;o=t=>t.startsWith(e)?r(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(r){let e=o;o=t=>r({className:t,parseClassName:e})}return o},p=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,m=e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let r=[],o=[];return e.forEach(e=>{"["===e[0]||t[e]?(r.push(...o.sort(),e),o=[]):o.push(e)}),r.push(...o.sort()),r}},g=e=>({cache:u(e.cacheSize),parseClassName:f(e),sortModifiers:m(e),...o(e)}),b=/\s+/,h=(e,t)=>{let{parseClassName:r,getClassGroupId:o,getConflictingClassGroupIds:n,sortModifiers:a}=t,l=[],i=e.trim().split(b),s="";for(let e=i.length-1;e>=0;e-=1){let t=i[e],{isExternal:d,modifiers:c,hasImportantModifier:u,baseClassName:f,maybePostfixModifierPosition:p}=r(t);if(d){s=t+(s.length>0?" "+s:s);continue}let m=!!p,g=o(m?f.substring(0,p):f);if(!g){if(!m||!(g=o(f))){s=t+(s.length>0?" "+s:s);continue}m=!1}let b=a(c).join(":"),h=u?b+"!":b,y=h+g;if(l.includes(y))continue;l.push(y);let v=n(g,m);for(let e=0;e<v.length;++e){let t=v[e];l.push(h+t)}s=t+(s.length>0?" "+s:s)}return s};function y(){let e,t,r=0,o="";for(;r<arguments.length;)(e=arguments[r++])&&(t=v(e))&&(o&&(o+=" "),o+=t);return o}let v=e=>{let t;if("string"==typeof e)return e;let r="";for(let o=0;o<e.length;o++)e[o]&&(t=v(e[o]))&&(r&&(r+=" "),r+=t);return r},w=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},x=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,k=/^\((?:(\w[\w-]*):)?(.+)\)$/i,_=/^\d+\/\d+$/,j=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,P=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,z=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,O=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,C=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,E=e=>_.test(e),S=e=>!!e&&!Number.isNaN(Number(e)),M=e=>!!e&&Number.isInteger(Number(e)),A=e=>e.endsWith("%")&&S(e.slice(0,-1)),N=e=>j.test(e),R=()=>!0,I=e=>P.test(e)&&!z.test(e),T=()=>!1,L=e=>O.test(e),U=e=>C.test(e),D=e=>!G(e)&&!V(e),F=e=>ee(e,en,T),G=e=>x.test(e),W=e=>ee(e,ea,I),$=e=>ee(e,el,S),q=e=>ee(e,er,T),B=e=>ee(e,eo,U),K=e=>ee(e,es,L),V=e=>k.test(e),H=e=>et(e,ea),X=e=>et(e,ei),Z=e=>et(e,er),Q=e=>et(e,en),J=e=>et(e,eo),Y=e=>et(e,es,!0),ee=(e,t,r)=>{let o=x.exec(e);return!!o&&(o[1]?t(o[1]):r(o[2]))},et=(e,t,r=!1)=>{let o=k.exec(e);return!!o&&(o[1]?t(o[1]):r)},er=e=>"position"===e||"percentage"===e,eo=e=>"image"===e||"url"===e,en=e=>"length"===e||"size"===e||"bg-size"===e,ea=e=>"length"===e,el=e=>"number"===e,ei=e=>"family-name"===e,es=e=>"shadow"===e;Symbol.toStringTag;let ed=function(e,...t){let r,o,n,a=function(i){return o=(r=g(t.reduce((e,t)=>t(e),e()))).cache.get,n=r.cache.set,a=l,l(i)};function l(e){let t=o(e);if(t)return t;let a=h(e,r);return n(e,a),a}return function(){return a(y.apply(null,arguments))}}(()=>{let e=w("color"),t=w("font"),r=w("text"),o=w("font-weight"),n=w("tracking"),a=w("leading"),l=w("breakpoint"),i=w("container"),s=w("spacing"),d=w("radius"),c=w("shadow"),u=w("inset-shadow"),f=w("text-shadow"),p=w("drop-shadow"),m=w("blur"),g=w("perspective"),b=w("aspect"),h=w("ease"),y=w("animate"),v=()=>["auto","avoid","all","avoid-page","page","left","right","column"],x=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],k=()=>[...x(),V,G],_=()=>["auto","hidden","clip","visible","scroll"],j=()=>["auto","contain","none"],P=()=>[V,G,s],z=()=>[E,"full","auto",...P()],O=()=>[M,"none","subgrid",V,G],C=()=>["auto",{span:["full",M,V,G]},M,V,G],I=()=>[M,"auto",V,G],T=()=>["auto","min","max","fr",V,G],L=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],U=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...P()],et=()=>[E,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...P()],er=()=>[e,V,G],eo=()=>[...x(),Z,q,{position:[V,G]}],en=()=>["no-repeat",{repeat:["","x","y","space","round"]}],ea=()=>["auto","cover","contain",Q,F,{size:[V,G]}],el=()=>[A,H,W],ei=()=>["","none","full",d,V,G],es=()=>["",S,H,W],ed=()=>["solid","dashed","dotted","double"],ec=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],eu=()=>[S,A,Z,q],ef=()=>["","none",m,V,G],ep=()=>["none",S,V,G],em=()=>["none",S,V,G],eg=()=>[S,V,G],eb=()=>[E,"full",...P()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[N],breakpoint:[N],color:[R],container:[N],"drop-shadow":[N],ease:["in","out","in-out"],font:[D],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[N],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[N],shadow:[N],spacing:["px",S],text:[N],"text-shadow":[N],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",E,G,V,b]}],container:["container"],columns:[{columns:[S,G,V,i]}],"break-after":[{"break-after":v()}],"break-before":[{"break-before":v()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:k()}],overflow:[{overflow:_()}],"overflow-x":[{"overflow-x":_()}],"overflow-y":[{"overflow-y":_()}],overscroll:[{overscroll:j()}],"overscroll-x":[{"overscroll-x":j()}],"overscroll-y":[{"overscroll-y":j()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:z()}],"inset-x":[{"inset-x":z()}],"inset-y":[{"inset-y":z()}],start:[{start:z()}],end:[{end:z()}],top:[{top:z()}],right:[{right:z()}],bottom:[{bottom:z()}],left:[{left:z()}],visibility:["visible","invisible","collapse"],z:[{z:[M,"auto",V,G]}],basis:[{basis:[E,"full","auto",i,...P()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[S,E,"auto","initial","none",G]}],grow:[{grow:["",S,V,G]}],shrink:[{shrink:["",S,V,G]}],order:[{order:[M,"first","last","none",V,G]}],"grid-cols":[{"grid-cols":O()}],"col-start-end":[{col:C()}],"col-start":[{"col-start":I()}],"col-end":[{"col-end":I()}],"grid-rows":[{"grid-rows":O()}],"row-start-end":[{row:C()}],"row-start":[{"row-start":I()}],"row-end":[{"row-end":I()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":T()}],"auto-rows":[{"auto-rows":T()}],gap:[{gap:P()}],"gap-x":[{"gap-x":P()}],"gap-y":[{"gap-y":P()}],"justify-content":[{justify:[...L(),"normal"]}],"justify-items":[{"justify-items":[...U(),"normal"]}],"justify-self":[{"justify-self":["auto",...U()]}],"align-content":[{content:["normal",...L()]}],"align-items":[{items:[...U(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...U(),{baseline:["","last"]}]}],"place-content":[{"place-content":L()}],"place-items":[{"place-items":[...U(),"baseline"]}],"place-self":[{"place-self":["auto",...U()]}],p:[{p:P()}],px:[{px:P()}],py:[{py:P()}],ps:[{ps:P()}],pe:[{pe:P()}],pt:[{pt:P()}],pr:[{pr:P()}],pb:[{pb:P()}],pl:[{pl:P()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":P()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":P()}],"space-y-reverse":["space-y-reverse"],size:[{size:et()}],w:[{w:[i,"screen",...et()]}],"min-w":[{"min-w":[i,"screen","none",...et()]}],"max-w":[{"max-w":[i,"screen","none","prose",{screen:[l]},...et()]}],h:[{h:["screen","lh",...et()]}],"min-h":[{"min-h":["screen","lh","none",...et()]}],"max-h":[{"max-h":["screen","lh",...et()]}],"font-size":[{text:["base",r,H,W]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[o,V,$]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",A,G]}],"font-family":[{font:[X,G,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[n,V,G]}],"line-clamp":[{"line-clamp":[S,"none",V,$]}],leading:[{leading:[a,...P()]}],"list-image":[{"list-image":["none",V,G]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",V,G]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:er()}],"text-color":[{text:er()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ed(),"wavy"]}],"text-decoration-thickness":[{decoration:[S,"from-font","auto",V,W]}],"text-decoration-color":[{decoration:er()}],"underline-offset":[{"underline-offset":[S,"auto",V,G]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:P()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",V,G]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",V,G]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:eo()}],"bg-repeat":[{bg:en()}],"bg-size":[{bg:ea()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},M,V,G],radial:["",V,G],conic:[M,V,G]},J,B]}],"bg-color":[{bg:er()}],"gradient-from-pos":[{from:el()}],"gradient-via-pos":[{via:el()}],"gradient-to-pos":[{to:el()}],"gradient-from":[{from:er()}],"gradient-via":[{via:er()}],"gradient-to":[{to:er()}],rounded:[{rounded:ei()}],"rounded-s":[{"rounded-s":ei()}],"rounded-e":[{"rounded-e":ei()}],"rounded-t":[{"rounded-t":ei()}],"rounded-r":[{"rounded-r":ei()}],"rounded-b":[{"rounded-b":ei()}],"rounded-l":[{"rounded-l":ei()}],"rounded-ss":[{"rounded-ss":ei()}],"rounded-se":[{"rounded-se":ei()}],"rounded-ee":[{"rounded-ee":ei()}],"rounded-es":[{"rounded-es":ei()}],"rounded-tl":[{"rounded-tl":ei()}],"rounded-tr":[{"rounded-tr":ei()}],"rounded-br":[{"rounded-br":ei()}],"rounded-bl":[{"rounded-bl":ei()}],"border-w":[{border:es()}],"border-w-x":[{"border-x":es()}],"border-w-y":[{"border-y":es()}],"border-w-s":[{"border-s":es()}],"border-w-e":[{"border-e":es()}],"border-w-t":[{"border-t":es()}],"border-w-r":[{"border-r":es()}],"border-w-b":[{"border-b":es()}],"border-w-l":[{"border-l":es()}],"divide-x":[{"divide-x":es()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":es()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ed(),"hidden","none"]}],"divide-style":[{divide:[...ed(),"hidden","none"]}],"border-color":[{border:er()}],"border-color-x":[{"border-x":er()}],"border-color-y":[{"border-y":er()}],"border-color-s":[{"border-s":er()}],"border-color-e":[{"border-e":er()}],"border-color-t":[{"border-t":er()}],"border-color-r":[{"border-r":er()}],"border-color-b":[{"border-b":er()}],"border-color-l":[{"border-l":er()}],"divide-color":[{divide:er()}],"outline-style":[{outline:[...ed(),"none","hidden"]}],"outline-offset":[{"outline-offset":[S,V,G]}],"outline-w":[{outline:["",S,H,W]}],"outline-color":[{outline:er()}],shadow:[{shadow:["","none",c,Y,K]}],"shadow-color":[{shadow:er()}],"inset-shadow":[{"inset-shadow":["none",u,Y,K]}],"inset-shadow-color":[{"inset-shadow":er()}],"ring-w":[{ring:es()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:er()}],"ring-offset-w":[{"ring-offset":[S,W]}],"ring-offset-color":[{"ring-offset":er()}],"inset-ring-w":[{"inset-ring":es()}],"inset-ring-color":[{"inset-ring":er()}],"text-shadow":[{"text-shadow":["none",f,Y,K]}],"text-shadow-color":[{"text-shadow":er()}],opacity:[{opacity:[S,V,G]}],"mix-blend":[{"mix-blend":[...ec(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ec()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[S]}],"mask-image-linear-from-pos":[{"mask-linear-from":eu()}],"mask-image-linear-to-pos":[{"mask-linear-to":eu()}],"mask-image-linear-from-color":[{"mask-linear-from":er()}],"mask-image-linear-to-color":[{"mask-linear-to":er()}],"mask-image-t-from-pos":[{"mask-t-from":eu()}],"mask-image-t-to-pos":[{"mask-t-to":eu()}],"mask-image-t-from-color":[{"mask-t-from":er()}],"mask-image-t-to-color":[{"mask-t-to":er()}],"mask-image-r-from-pos":[{"mask-r-from":eu()}],"mask-image-r-to-pos":[{"mask-r-to":eu()}],"mask-image-r-from-color":[{"mask-r-from":er()}],"mask-image-r-to-color":[{"mask-r-to":er()}],"mask-image-b-from-pos":[{"mask-b-from":eu()}],"mask-image-b-to-pos":[{"mask-b-to":eu()}],"mask-image-b-from-color":[{"mask-b-from":er()}],"mask-image-b-to-color":[{"mask-b-to":er()}],"mask-image-l-from-pos":[{"mask-l-from":eu()}],"mask-image-l-to-pos":[{"mask-l-to":eu()}],"mask-image-l-from-color":[{"mask-l-from":er()}],"mask-image-l-to-color":[{"mask-l-to":er()}],"mask-image-x-from-pos":[{"mask-x-from":eu()}],"mask-image-x-to-pos":[{"mask-x-to":eu()}],"mask-image-x-from-color":[{"mask-x-from":er()}],"mask-image-x-to-color":[{"mask-x-to":er()}],"mask-image-y-from-pos":[{"mask-y-from":eu()}],"mask-image-y-to-pos":[{"mask-y-to":eu()}],"mask-image-y-from-color":[{"mask-y-from":er()}],"mask-image-y-to-color":[{"mask-y-to":er()}],"mask-image-radial":[{"mask-radial":[V,G]}],"mask-image-radial-from-pos":[{"mask-radial-from":eu()}],"mask-image-radial-to-pos":[{"mask-radial-to":eu()}],"mask-image-radial-from-color":[{"mask-radial-from":er()}],"mask-image-radial-to-color":[{"mask-radial-to":er()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":x()}],"mask-image-conic-pos":[{"mask-conic":[S]}],"mask-image-conic-from-pos":[{"mask-conic-from":eu()}],"mask-image-conic-to-pos":[{"mask-conic-to":eu()}],"mask-image-conic-from-color":[{"mask-conic-from":er()}],"mask-image-conic-to-color":[{"mask-conic-to":er()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:eo()}],"mask-repeat":[{mask:en()}],"mask-size":[{mask:ea()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",V,G]}],filter:[{filter:["","none",V,G]}],blur:[{blur:ef()}],brightness:[{brightness:[S,V,G]}],contrast:[{contrast:[S,V,G]}],"drop-shadow":[{"drop-shadow":["","none",p,Y,K]}],"drop-shadow-color":[{"drop-shadow":er()}],grayscale:[{grayscale:["",S,V,G]}],"hue-rotate":[{"hue-rotate":[S,V,G]}],invert:[{invert:["",S,V,G]}],saturate:[{saturate:[S,V,G]}],sepia:[{sepia:["",S,V,G]}],"backdrop-filter":[{"backdrop-filter":["","none",V,G]}],"backdrop-blur":[{"backdrop-blur":ef()}],"backdrop-brightness":[{"backdrop-brightness":[S,V,G]}],"backdrop-contrast":[{"backdrop-contrast":[S,V,G]}],"backdrop-grayscale":[{"backdrop-grayscale":["",S,V,G]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[S,V,G]}],"backdrop-invert":[{"backdrop-invert":["",S,V,G]}],"backdrop-opacity":[{"backdrop-opacity":[S,V,G]}],"backdrop-saturate":[{"backdrop-saturate":[S,V,G]}],"backdrop-sepia":[{"backdrop-sepia":["",S,V,G]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":P()}],"border-spacing-x":[{"border-spacing-x":P()}],"border-spacing-y":[{"border-spacing-y":P()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",V,G]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[S,"initial",V,G]}],ease:[{ease:["linear","initial",h,V,G]}],delay:[{delay:[S,V,G]}],animate:[{animate:["none",y,V,G]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[g,V,G]}],"perspective-origin":[{"perspective-origin":k()}],rotate:[{rotate:ep()}],"rotate-x":[{"rotate-x":ep()}],"rotate-y":[{"rotate-y":ep()}],"rotate-z":[{"rotate-z":ep()}],scale:[{scale:em()}],"scale-x":[{"scale-x":em()}],"scale-y":[{"scale-y":em()}],"scale-z":[{"scale-z":em()}],"scale-3d":["scale-3d"],skew:[{skew:eg()}],"skew-x":[{"skew-x":eg()}],"skew-y":[{"skew-y":eg()}],transform:[{transform:[V,G,"","none","gpu","cpu"]}],"transform-origin":[{origin:k()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eb()}],"translate-x":[{"translate-x":eb()}],"translate-y":[{"translate-y":eb()}],"translate-z":[{"translate-z":eb()}],"translate-none":["translate-none"],accent:[{accent:er()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:er()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",V,G]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":P()}],"scroll-mx":[{"scroll-mx":P()}],"scroll-my":[{"scroll-my":P()}],"scroll-ms":[{"scroll-ms":P()}],"scroll-me":[{"scroll-me":P()}],"scroll-mt":[{"scroll-mt":P()}],"scroll-mr":[{"scroll-mr":P()}],"scroll-mb":[{"scroll-mb":P()}],"scroll-ml":[{"scroll-ml":P()}],"scroll-p":[{"scroll-p":P()}],"scroll-px":[{"scroll-px":P()}],"scroll-py":[{"scroll-py":P()}],"scroll-ps":[{"scroll-ps":P()}],"scroll-pe":[{"scroll-pe":P()}],"scroll-pt":[{"scroll-pt":P()}],"scroll-pr":[{"scroll-pr":P()}],"scroll-pb":[{"scroll-pb":P()}],"scroll-pl":[{"scroll-pl":P()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",V,G]}],fill:[{fill:["none",...er()]}],"stroke-w":[{stroke:[S,H,W,$]}],stroke:[{stroke:["none",...er()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},9869:(e,t,r)=>{r.d(t,{A:()=>o});let o=(0,r(9946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},9946:(e,t,r)=>{r.d(t,{A:()=>u});var o=r(2115);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),l=e=>{let t=a(e);return t.charAt(0).toUpperCase()+t.slice(1)},i=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},s=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,o.forwardRef)((e,t)=>{let{color:r="currentColor",size:n=24,strokeWidth:a=2,absoluteStrokeWidth:l,className:c="",children:u,iconNode:f,...p}=e;return(0,o.createElement)("svg",{ref:t,...d,width:n,height:n,stroke:r,strokeWidth:l?24*Number(a)/Number(n):a,className:i("lucide",c),...!u&&!s(p)&&{"aria-hidden":"true"},...p},[...f.map(e=>{let[t,r]=e;return(0,o.createElement)(t,r)}),...Array.isArray(u)?u:[u]])}),u=(e,t)=>{let r=(0,o.forwardRef)((r,a)=>{let{className:s,...d}=r;return(0,o.createElement)(c,{ref:a,iconNode:t,className:i("lucide-".concat(n(l(e))),"lucide-".concat(e),s),...d})});return r.displayName=l(e),r}},9991:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return m},MiddlewareNotFoundError:function(){return y},MissingStaticPage:function(){return h},NormalizeError:function(){return g},PageNotFoundError:function(){return b},SP:function(){return f},ST:function(){return p},WEB_VITALS:function(){return r},execOnce:function(){return o},getDisplayName:function(){return s},getLocationOrigin:function(){return l},getURL:function(){return i},isAbsoluteUrl:function(){return a},isResSent:function(){return d},loadGetInitialProps:function(){return u},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return v}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function o(e){let t,r=!1;return function(){for(var o=arguments.length,n=Array(o),a=0;a<o;a++)n[a]=arguments[a];return r||(r=!0,t=e(...n)),t}}let n=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>n.test(e);function l(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function i(){let{href:e}=window.location,t=l();return e.substring(t.length)}function s(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function d(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function u(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await u(t.Component,t.ctx)}:{};let o=await e.getInitialProps(t);if(r&&d(r))return o;if(!o)throw Object.defineProperty(Error('"'+s(e)+'.getInitialProps()" should resolve to an object. But found "'+o+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return o}let f="undefined"!=typeof performance,p=f&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class m extends Error{}class g extends Error{}class b extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class h extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class y extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function v(e){return JSON.stringify({message:e.message,stack:e.stack})}}}]);