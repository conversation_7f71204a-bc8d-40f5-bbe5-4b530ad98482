"use strict";(()=>{var e={};e.id=5533,e.ids=[5533],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29236:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>g,routeModule:()=>c,serverHooks:()=>m,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>f});var a={};t.r(a),t.d(a,{GET:()=>d});var s=t(96559),i=t(48088),n=t(37719),o=t(32190),l=t(39542),u=t(6710);async function d(e){try{let{authenticated:r,user:t}=await (0,l.b9)(e);if(!r||!t)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});let a=parseFloat(await u.rs.get("minWithdrawalAmount")||"10"),s=parseFloat(await u.rs.get("withdrawalFeeFixed")||"3"),i=parseFloat(await u.rs.get("withdrawalFeePercentage")||"1"),n=parseInt(await u.rs.get("withdrawalProcessingDays")||"3");return o.NextResponse.json({success:!0,data:{minWithdrawalAmount:a,fixedFee:s,percentageFee:i,processingDays:n}})}catch(e){return console.error("Withdrawal settings fetch error:",e),o.NextResponse.json({success:!1,error:"Failed to fetch withdrawal settings"},{status:500})}}let c=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/wallet/withdrawal-settings/route",pathname:"/api/wallet/withdrawal-settings",filename:"route",bundlePath:"app/api/wallet/withdrawal-settings/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\wallet\\withdrawal-settings\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:p,workUnitAsyncStorage:f,serverHooks:m}=c;function g(){return(0,n.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:f})}},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37366:e=>{e.exports=require("dns")},39542:(e,r,t)=>{t.d(r,{b9:()=>w,HU:()=>g,qc:()=>R,Lx:()=>T,DY:()=>h,DT:()=>_});var a=t(85663),s=t(43205),i=t.n(s),n=t(6710),o=t(45697);let l=o.z.object({DATABASE_URL:o.z.string().url("DATABASE_URL must be a valid PostgreSQL URL"),DIRECT_URL:o.z.string().url("DIRECT_URL must be a valid PostgreSQL URL"),JWT_SECRET:o.z.string().min(32,"JWT_SECRET must be at least 32 characters long").refine(e=>{let r=/[A-Z]/.test(e),t=/[a-z]/.test(e),a=/\d/.test(e),s=/[!@#$%^&*(),.?":{}|<>]/.test(e);return r&&t&&a&&s},"JWT_SECRET should contain uppercase, lowercase, numbers, and special characters"),JWT_EXPIRES_IN:o.z.string().default("30d"),NODE_ENV:o.z.enum(["development","production","test"]).default("development"),NEXT_PUBLIC_APP_URL:o.z.string().url().optional(),PORT:o.z.string().regex(/^\d+$/).transform(Number).default("3000"),SMTP_HOST:o.z.string().optional(),SMTP_PORT:o.z.string().regex(/^\d+$/).transform(Number).optional(),SMTP_USER:o.z.string().email().optional(),SMTP_PASSWORD:o.z.string().optional(),SMTP_SECURE:o.z.string().transform(e=>"true"===e).optional(),TRON_NETWORK:o.z.enum(["mainnet","testnet"]).default("testnet"),TRON_API_KEY:o.z.string().optional(),USDT_CONTRACT_ADDRESS:o.z.string().optional(),MAX_FILE_SIZE:o.z.string().regex(/^\d+$/).transform(Number).default("10485760"),UPLOAD_DIR:o.z.string().default("./public/uploads"),BCRYPT_ROUNDS:o.z.string().regex(/^\d+$/).transform(Number).default("12"),SESSION_TIMEOUT:o.z.string().regex(/^\d+$/).transform(Number).default("1800"),RATE_LIMIT_WINDOW_MS:o.z.string().regex(/^\d+$/).transform(Number).default("900000"),RATE_LIMIT_MAX_REQUESTS:o.z.string().regex(/^\d+$/).transform(Number).default("100"),COINGECKO_API_URL:o.z.string().url().default("https://api.coingecko.com/api/v3"),LOG_LEVEL:o.z.enum(["error","warn","info","debug"]).default("info"),ENABLE_REQUEST_LOGGING:o.z.string().transform(e=>"true"===e).default("false"),ENABLE_REGISTRATION:o.z.string().transform(e=>"true"===e).default("true"),ENABLE_KYC:o.z.string().transform(e=>"true"===e).default("true"),ENABLE_WITHDRAWALS:o.z.string().transform(e=>"true"===e).default("true")}).refine(e=>{let r=[e.SMTP_HOST,e.SMTP_PORT,e.SMTP_USER,e.SMTP_PASSWORD],t=r.some(e=>void 0!==e),a=r.every(e=>void 0!==e);return!t||!!a},{message:"If SMTP configuration is provided, all SMTP fields (HOST, PORT, USER, PASSWORD) must be provided"});function u(){try{let e=l.safeParse(process.env);if(!e.success){let r=e.error.errors.map(e=>`${e.path.join(".")}: ${e.message}`);return{success:!1,errors:r}}return{success:!0,data:e.data}}catch(e){return{success:!1,errors:["Failed to validate environment variables"]}}}let d=null;function c(){if(!d){let e=u();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),d=e.data,console.log("✅ Environment variables validated successfully")}return d}let p={jwt:{secret:()=>c().JWT_SECRET,expiresIn:()=>c().JWT_EXPIRES_IN},security:{bcryptRounds:()=>c().BCRYPT_ROUNDS,sessionTimeout:()=>c().SESSION_TIMEOUT,maxFileSize:()=>c().MAX_FILE_SIZE,uploadDir:()=>c().UPLOAD_DIR}};if("phase-production-build"!==process.env.NEXT_PHASE){let e=u();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),console.log("✅ Environment variables validated successfully")}let f=async e=>await a.Ay.hash(e,p.security.bcryptRounds()),m=async(e,r)=>await a.Ay.compare(e,r),g=e=>i().sign(e,p.jwt.secret(),{expiresIn:p.jwt.expiresIn()}),E=e=>{try{return i().verify(e,p.jwt.secret())}catch(e){return null}},S=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",r="HC";for(let t=0;t<8;t++)r+=e.charAt(Math.floor(Math.random()*e.length));return r},w=async e=>{let r=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!r)return{authenticated:!1,user:null};let t=E(r);if(!t)return{authenticated:!1,user:null};let a=await n.Gy.findByEmail(t.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},h=async e=>{let r,a;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let t=await n.Gy.findByReferralId(e.referralCode);if(!t)throw Error("Invalid referral code");r=t.id}let s=await f(e.password),i=!1;do a=S(),i=!await n.Gy.findByReferralId(a);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(r){let{placeUserByReferralType:a}=await t.e(2746).then(t.bind(t,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(r,o.id,s)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},T=async e=>{let r=await n.Gy.findByEmail(e.email);if(!r||!await m(e.password,r.password))throw Error("Invalid email or password");return{token:g({userId:r.id,email:r.email}),user:{id:r.id,email:r.email,referralId:r.referralId,kycStatus:r.kycStatus}}},_=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),R=async e=>{let r=await n.Gy.findById(e);return r?.role==="ADMIN"}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,580,7911,925],()=>t(29236));module.exports=a})();