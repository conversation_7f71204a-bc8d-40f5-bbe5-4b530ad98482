"use strict";(()=>{var e={};e.id=218,e.ids=[218],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8023:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>S,serverHooks:()=>f,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>T});var s={};r.r(s),r.d(s,{GET:()=>c,POST:()=>p,PUT:()=>E});var a=r(96559),i=r(48088),n=r(37719),o=r(32190),u=r(39542),d=r(6710),l=r(59480);async function c(e){try{let{authenticated:t,user:r}=await (0,u.b9)(e);if(!t||!r)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if(!await (0,u.qc)(r.id))return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let s=await d.rs.get("USDT_DEPOSIT_ADDRESS"),a=await d.rs.get("MIN_DEPOSIT_AMOUNT"),i=await d.rs.get("MAX_DEPOSIT_AMOUNT"),n=await d.rs.get("DEPOSIT_ENABLED"),l=await d.rs.get("MIN_CONFIRMATIONS"),c=await d.rs.get("DEPOSIT_FEE_PERCENTAGE");return o.NextResponse.json({success:!0,data:{depositAddress:s||"",minDepositAmount:parseFloat(a||"10"),maxDepositAmount:parseFloat(i||"10000"),depositEnabled:"true"===n,minConfirmations:parseInt(l||"1"),depositFeePercentage:parseFloat(c||"0")}})}catch(e){return console.error("Deposit settings fetch error:",e),o.NextResponse.json({success:!1,error:"Failed to fetch deposit settings"},{status:500})}}async function p(e){try{let{authenticated:t,user:r}=await (0,u.b9)(e);if(!t||!r)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if(!await (0,u.qc)(r.id))return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let{depositAddress:s,minDepositAmount:a,maxDepositAmount:i,depositEnabled:n,minConfirmations:c,depositFeePercentage:p}=await e.json(),E=[];if(s&&!(0,l.af)(s)&&E.push("Invalid USDT TRC20 address format"),void 0!==a&&(a<0||a>1e6)&&E.push("Minimum deposit amount must be between 0 and 1,000,000"),void 0!==i&&(i<0||i>1e6)&&E.push("Maximum deposit amount must be between 0 and 1,000,000"),void 0!==a&&void 0!==i&&a>i&&E.push("Minimum deposit amount cannot be greater than maximum deposit amount"),void 0!==c&&(c<0||c>100)&&E.push("Minimum confirmations must be between 0 and 100"),void 0!==p&&(p<0||p>50)&&E.push("Deposit fee percentage must be between 0 and 50"),E.length>0)return o.NextResponse.json({success:!1,error:E.join(", ")},{status:400});let S=[];return void 0!==s&&(await d.rs.set("USDT_DEPOSIT_ADDRESS",s),S.push({key:"USDT_DEPOSIT_ADDRESS",value:s})),void 0!==a&&(await d.rs.set("MIN_DEPOSIT_AMOUNT",a.toString()),S.push({key:"MIN_DEPOSIT_AMOUNT",value:a.toString()})),void 0!==i&&(await d.rs.set("MAX_DEPOSIT_AMOUNT",i.toString()),S.push({key:"MAX_DEPOSIT_AMOUNT",value:i.toString()})),void 0!==n&&(await d.rs.set("DEPOSIT_ENABLED",n.toString()),S.push({key:"DEPOSIT_ENABLED",value:n.toString()})),void 0!==c&&(await d.rs.set("MIN_CONFIRMATIONS",c.toString()),S.push({key:"MIN_CONFIRMATIONS",value:c.toString()})),void 0!==p&&(await d.rs.set("DEPOSIT_FEE_PERCENTAGE",p.toString()),S.push({key:"DEPOSIT_FEE_PERCENTAGE",value:p.toString()})),await d.AJ.create({action:"DEPOSIT_SETTINGS_UPDATED",adminId:r.id,details:{updates:S},ipAddress:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"}),o.NextResponse.json({success:!0,message:"Deposit settings updated successfully"})}catch(e){return console.error("Deposit settings update error:",e),o.NextResponse.json({success:!1,error:"Failed to update deposit settings"},{status:500})}}async function E(e){try{let{authenticated:t,user:r}=await (0,u.b9)(e);if(!t||!r)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if(!await (0,u.qc)(r.id))return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let s=[{key:"USDT_DEPOSIT_ADDRESS",value:""},{key:"MIN_DEPOSIT_AMOUNT",value:"10"},{key:"MAX_DEPOSIT_AMOUNT",value:"10000"},{key:"DEPOSIT_ENABLED",value:"true"},{key:"MIN_CONFIRMATIONS",value:"1"},{key:"DEPOSIT_FEE_PERCENTAGE",value:"0"}];for(let e of s)await d.rs.set(e.key,e.value);return await d.AJ.create({action:"DEPOSIT_SETTINGS_RESET",adminId:r.id,details:{defaultSettings:s},ipAddress:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"}),o.NextResponse.json({success:!0,message:"Deposit settings reset to defaults"})}catch(e){return console.error("Deposit settings reset error:",e),o.NextResponse.json({success:!1,error:"Failed to reset deposit settings"},{status:500})}}let S=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/deposit-settings/route",pathname:"/api/admin/deposit-settings",filename:"route",bundlePath:"app/api/admin/deposit-settings/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\deposit-settings\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:T,serverHooks:f}=S;function g(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:T})}},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37366:e=>{e.exports=require("dns")},39542:(e,t,r)=>{r.d(t,{b9:()=>g,HU:()=>m,qc:()=>R,Lx:()=>I,DY:()=>_,DT:()=>N});var s=r(85663),a=r(43205),i=r.n(a),n=r(6710),o=r(45697);let u=o.z.object({DATABASE_URL:o.z.string().url("DATABASE_URL must be a valid PostgreSQL URL"),DIRECT_URL:o.z.string().url("DIRECT_URL must be a valid PostgreSQL URL"),JWT_SECRET:o.z.string().min(32,"JWT_SECRET must be at least 32 characters long").refine(e=>{let t=/[A-Z]/.test(e),r=/[a-z]/.test(e),s=/\d/.test(e),a=/[!@#$%^&*(),.?":{}|<>]/.test(e);return t&&r&&s&&a},"JWT_SECRET should contain uppercase, lowercase, numbers, and special characters"),JWT_EXPIRES_IN:o.z.string().default("30d"),NODE_ENV:o.z.enum(["development","production","test"]).default("development"),NEXT_PUBLIC_APP_URL:o.z.string().url().optional(),PORT:o.z.string().regex(/^\d+$/).transform(Number).default("3000"),SMTP_HOST:o.z.string().optional(),SMTP_PORT:o.z.string().regex(/^\d+$/).transform(Number).optional(),SMTP_USER:o.z.string().email().optional(),SMTP_PASSWORD:o.z.string().optional(),SMTP_SECURE:o.z.string().transform(e=>"true"===e).optional(),TRON_NETWORK:o.z.enum(["mainnet","testnet"]).default("testnet"),TRON_API_KEY:o.z.string().optional(),USDT_CONTRACT_ADDRESS:o.z.string().optional(),MAX_FILE_SIZE:o.z.string().regex(/^\d+$/).transform(Number).default("10485760"),UPLOAD_DIR:o.z.string().default("./public/uploads"),BCRYPT_ROUNDS:o.z.string().regex(/^\d+$/).transform(Number).default("12"),SESSION_TIMEOUT:o.z.string().regex(/^\d+$/).transform(Number).default("1800"),RATE_LIMIT_WINDOW_MS:o.z.string().regex(/^\d+$/).transform(Number).default("900000"),RATE_LIMIT_MAX_REQUESTS:o.z.string().regex(/^\d+$/).transform(Number).default("100"),COINGECKO_API_URL:o.z.string().url().default("https://api.coingecko.com/api/v3"),LOG_LEVEL:o.z.enum(["error","warn","info","debug"]).default("info"),ENABLE_REQUEST_LOGGING:o.z.string().transform(e=>"true"===e).default("false"),ENABLE_REGISTRATION:o.z.string().transform(e=>"true"===e).default("true"),ENABLE_KYC:o.z.string().transform(e=>"true"===e).default("true"),ENABLE_WITHDRAWALS:o.z.string().transform(e=>"true"===e).default("true")}).refine(e=>{let t=[e.SMTP_HOST,e.SMTP_PORT,e.SMTP_USER,e.SMTP_PASSWORD],r=t.some(e=>void 0!==e),s=t.every(e=>void 0!==e);return!r||!!s},{message:"If SMTP configuration is provided, all SMTP fields (HOST, PORT, USER, PASSWORD) must be provided"});function d(){try{let e=u.safeParse(process.env);if(!e.success){let t=e.error.errors.map(e=>`${e.path.join(".")}: ${e.message}`);return{success:!1,errors:t}}return{success:!0,data:e.data}}catch(e){return{success:!1,errors:["Failed to validate environment variables"]}}}let l=null;function c(){if(!l){let e=d();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),l=e.data,console.log("✅ Environment variables validated successfully")}return l}let p={jwt:{secret:()=>c().JWT_SECRET,expiresIn:()=>c().JWT_EXPIRES_IN},security:{bcryptRounds:()=>c().BCRYPT_ROUNDS,sessionTimeout:()=>c().SESSION_TIMEOUT,maxFileSize:()=>c().MAX_FILE_SIZE,uploadDir:()=>c().UPLOAD_DIR}};if("phase-production-build"!==process.env.NEXT_PHASE){let e=d();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),console.log("✅ Environment variables validated successfully")}let E=async e=>await s.Ay.hash(e,p.security.bcryptRounds()),S=async(e,t)=>await s.Ay.compare(e,t),m=e=>i().sign(e,p.jwt.secret(),{expiresIn:p.jwt.expiresIn()}),T=e=>{try{return i().verify(e,p.jwt.secret())}catch(e){return null}},f=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},g=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=T(t);if(!r)return{authenticated:!1,user:null};let s=await n.Gy.findByEmail(r.email);return s?{authenticated:!0,user:s}:{authenticated:!1,user:null}},_=async e=>{let t,s;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await n.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let a=await E(e.password),i=!1;do s=f(),i=!await n.Gy.findByReferralId(s);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:a,referralId:s});if(t){let{placeUserByReferralType:s}=await r.e(2746).then(r.bind(r,2746)),a="general";"left"===e.placementSide?a="left":"right"===e.placementSide&&(a="right"),await s(t,o.id,a)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},I=async e=>{let t=await n.Gy.findByEmail(e.email);if(!t||!await S(e.password,t.password))throw Error("Invalid email or password");return{token:m({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},N=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),R=async e=>{let t=await n.Gy.findById(e);return t?.role==="ADMIN"}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,7911,925,9480],()=>r(8023));module.exports=s})();