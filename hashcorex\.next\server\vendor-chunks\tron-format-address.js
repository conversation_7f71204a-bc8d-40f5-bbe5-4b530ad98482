"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tron-format-address";
exports.ids = ["vendor-chunks/tron-format-address"];
exports.modules = {

/***/ "(rsc)/./node_modules/tron-format-address/build/lib/base58.js":
/*!**************************************************************!*\
  !*** ./node_modules/tron-format-address/build/lib/base58.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.decode58 = exports.encode58 = void 0;\nconst ALPHABET = '123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz';\nconst ALPHABET_MAP = ALPHABET.split('').reduce((acc, x, i) => {\n    acc[x] = i;\n    return acc;\n}, {});\nconst BASE = 58;\nconst encode58 = (buffer) => {\n    if (buffer.length === 0)\n        return '';\n    const digits = [0];\n    for (let i = 0; i < buffer.length; i++) {\n        for (let j = 0; j < digits.length; j++)\n            digits[j] <<= 8;\n        digits[0] += buffer[i];\n        let carry = 0;\n        for (let j = 0; j < digits.length; ++j) {\n            digits[j] += carry;\n            carry = (digits[j] / BASE) | 0;\n            digits[j] %= BASE;\n        }\n        while (carry) {\n            digits.push(carry % BASE);\n            carry = (carry / BASE) | 0;\n        }\n    }\n    for (let i = 0; buffer[i] === 0 && i < buffer.length - 1; i++)\n        digits.push(0);\n    return [...digits]\n        .reverse()\n        .map((digit) => ALPHABET[digit])\n        .join('');\n};\nexports.encode58 = encode58;\nconst decode58 = (data) => {\n    if (data.length === 0)\n        return [];\n    const bytes = [0];\n    for (let i = 0; i < data.length; i++) {\n        const c = data[i];\n        if (!(c in ALPHABET_MAP))\n            throw new Error('Non-base58 character');\n        for (let j = 0; j < bytes.length; j++)\n            bytes[j] *= BASE;\n        bytes[0] += ALPHABET_MAP[c];\n        let carry = 0;\n        for (let j = 0; j < bytes.length; ++j) {\n            bytes[j] += carry;\n            carry = bytes[j] >> 8;\n            bytes[j] &= 0xff;\n        }\n        while (carry) {\n            bytes.push(carry & 0xff);\n            carry >>= 8;\n        }\n    }\n    for (let i = 0; data[i] === '1' && i < data.length - 1; i++)\n        bytes.push(0);\n    return bytes.reverse();\n};\nexports.decode58 = decode58;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/tron-format-address/build/lib/base58.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/tron-format-address/build/lib/crypto.js":
/*!**************************************************************!*\
  !*** ./node_modules/tron-format-address/build/lib/crypto.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.toHex = exports.fromHex = void 0;\nconst crypto_1 = __importDefault(__webpack_require__(/*! crypto */ \"crypto\"));\nconst base58_1 = __webpack_require__(/*! ./base58 */ \"(rsc)/./node_modules/tron-format-address/build/lib/base58.js\");\nconst sha256 = (msg) => crypto_1.default.createHash('sha256').update(Buffer.from(msg, 'hex')).digest('hex');\nconst fromHex = (hex) => {\n    const addr = `41${hex.substring(2)}`;\n    const doubleSha256 = sha256(sha256(addr));\n    const checkSum = doubleSha256.substring(0, 8);\n    const address = Buffer.from(addr + checkSum, 'hex');\n    return (0, base58_1.encode58)(address);\n};\nexports.fromHex = fromHex;\nconst toHex = (base58Sting) => {\n    if (base58Sting.length <= 4)\n        throw new Error('Invalid address provided');\n    let address = Buffer.from((0, base58_1.decode58)(base58Sting)).toString('hex');\n    const checkSum = address.substring(address.length - 8, address.length);\n    address = address.substring(0, address.length - 8);\n    const checkSum1 = sha256(sha256(address)).substring(0, 8);\n    if (`${checkSum}` === `${checkSum1}`)\n        return `0x${address.substring(2)}`;\n    throw new Error('Invalid address provided');\n};\nexports.toHex = toHex;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/tron-format-address/build/lib/crypto.js\n");

/***/ })

};
;