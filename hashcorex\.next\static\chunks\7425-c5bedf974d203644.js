"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7425],{1264:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},1470:(e,t,a)=>{a.d(t,{CU:()=>d});var r=a(5155),s=a(2115),l=a(5695),c=a(4105),i=a(8505);let n=e=>{let{children:t,requireAuth:a=!1,requireGuest:n=!1,redirectTo:d}=e,{user:o,loading:u}=(0,c.A)(),h=(0,l.useRouter)(),m=(0,l.usePathname)();return((0,s.useEffect)(()=>{if(!u){if(a&&!o){let e="/login?redirect=".concat(encodeURIComponent(m));h.replace(e);return}if(n&&o){let e=d||"/dashboard";h.replace(e);return}}},[o,u,a,n,h,m,d]),u)?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(i.Rh,{}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"Checking authentication..."})]})}):a&&!o||n&&o?null:(0,r.jsx)(r.Fragment,{children:t})},d=e=>{let{children:t,redirectTo:a}=e;return(0,r.jsx)(n,{requireGuest:!0,redirectTo:a,children:t})}},2657:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3904:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(9946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},3997:(e,t,a)=>{a.d(t,{h:()=>d});var r=a(5155),s=a(2115),l=a(8505),c=a(1264),i=a(646),n=a(3904);let d=e=>{let{email:t,firstName:a,onVerified:d,onResend:o,loading:u=!1,error:h}=e,[m,x]=(0,s.useState)(["","","","","",""]),[f,y]=(0,s.useState)(600),[p,j]=(0,s.useState)(!1),[g,v]=(0,s.useState)(!1),N=(0,s.useRef)([]);(0,s.useEffect)(()=>{let e=setInterval(()=>{y(e=>e<=1?(j(!0),0):e-1)},1e3);return()=>clearInterval(e)},[]);let b=(e,t)=>{if(t.length>1)return;let a=[...m];if(a[e]=t,x(a),t&&e<5){var r;null==(r=N.current[e+1])||r.focus()}a.every(e=>""!==e)&&6===a.join("").length&&d(a.join(""))},k=(e,t)=>{if("Backspace"===t.key&&!m[e]&&e>0){var a;null==(a=N.current[e-1])||a.focus()}},w=e=>{e.preventDefault();let t=e.clipboardData.getData("text").replace(/\D/g,"").slice(0,6);6===t.length&&(x(t.split("")),d(t))},A=async()=>{v(!0),j(!1),y(600),await o(),v(!1)};return(0,r.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(c.A,{className:"w-8 h-8 text-green-600"})}),(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Verify Your Email"}),(0,r.jsx)("p",{className:"text-gray-600",children:"We've sent a 6-digit verification code to"}),(0,r.jsx)("p",{className:"font-semibold text-gray-900",children:t})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3 text-center",children:"Enter verification code"}),(0,r.jsx)("div",{className:"flex justify-center space-x-3",children:m.map((e,t)=>(0,r.jsx)(l.pd,{ref:e=>N.current[t]=e,type:"text",inputMode:"numeric",maxLength:1,value:e,onChange:e=>b(t,e.target.value),onKeyDown:e=>k(t,e),onPaste:w,className:"w-12 h-12 text-center text-lg font-semibold border-2 focus:border-green-500",disabled:u},t))})]}),h&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:(0,r.jsx)("p",{className:"text-sm text-red-600 text-center",children:h})}),(0,r.jsx)("div",{className:"text-center",children:f>0?(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["Code expires in ",(0,r.jsx)("span",{className:"font-semibold text-green-600",children:(e=>{let t=Math.floor(e/60);return"".concat(t,":").concat((e%60).toString().padStart(2,"0"))})(f)})]}):(0,r.jsx)("p",{className:"text-sm text-red-600",children:"Code has expired"})}),(0,r.jsx)(l.$n,{onClick:()=>{let e=m.join("");6===e.length&&d(e)},disabled:u||m.some(e=>""===e),className:"w-full bg-green-600 hover:bg-green-700 text-white",children:u?(0,r.jsxs)("div",{className:"flex items-center justify-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Verifying..."]}):(0,r.jsxs)("div",{className:"flex items-center justify-center",children:[(0,r.jsx)(i.A,{className:"w-4 h-4 mr-2"}),"Verify Code"]})}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"Didn't receive the code?"}),(0,r.jsx)(l.$n,{onClick:A,disabled:!p||g,variant:"ghost",className:"text-green-600 hover:text-green-700",children:g?(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(n.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Sending..."]}):(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(n.A,{className:"w-4 h-4 mr-2"}),"Resend Code"]})})]}),(0,r.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,r.jsx)("p",{className:"text-sm text-blue-700",children:(0,r.jsx)("strong",{children:"Tips:"})}),(0,r.jsxs)("ul",{className:"text-sm text-blue-600 mt-1 space-y-1",children:[(0,r.jsx)("li",{children:"• Check your spam/junk folder"}),(0,r.jsxs)("li",{children:["• Make sure ",t," is correct"]}),(0,r.jsx)("li",{children:"• You can paste the code from your email"})]})]})]})]})}},4105:(e,t,a)=>{a.d(t,{A:()=>i,AuthProvider:()=>c});var r=a(5155),s=a(2115);let l=(0,s.createContext)(void 0),c=e=>{let{children:t}=e,[a,c]=(0,s.useState)(null),[i,n]=(0,s.useState)(!0);(0,s.useEffect)(()=>{d()},[]);let d=async()=>{try{let e=await fetch("/api/auth/me",{credentials:"include"});if(e.ok){let t=await e.json();t.success&&c(t.data)}}catch(e){console.error("Auth check failed:",e)}finally{n(!1)}},o=async(e,t)=>{n(!0);try{let a=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e,password:t})}),r=await a.json();if(!r.success)throw Error(r.error||"Login failed");c(r.data.user),setTimeout(()=>{d()},100)}catch(e){throw e}finally{n(!1)}},u=async(e,t,a,r,s,l,i,d)=>{n(!0);try{let n=await fetch(i?"/api/auth/register?side=".concat(i):"/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,firstName:t,lastName:a,password:r,confirmPassword:s,referralCode:l,otp:d})}),o=await n.json();if(!o.success)throw Error(o.error||"Registration failed");c({id:o.data.user.id,email:o.data.user.email,firstName:o.data.user.firstName||"",lastName:o.data.user.lastName||"",referralId:o.data.user.referralId,role:o.data.user.role||"USER",kycStatus:o.data.user.kycStatus,isActive:o.data.user.isActive||!0,profilePicture:o.data.user.profilePicture||null,createdAt:o.data.user.createdAt,updatedAt:o.data.user.updatedAt})}catch(e){throw e}finally{n(!1)}},h=async()=>{try{await fetch("/api/auth/logout",{method:"POST",credentials:"include"})}catch(e){console.error("Logout error:",e)}finally{c(null)}},m=async()=>{await d()};return(0,r.jsx)(l.Provider,{value:{user:a,loading:i,login:o,register:u,logout:h,refreshUser:m},children:t})},i=()=>{let e=(0,s.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},5196:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(9946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5695:(e,t,a)=>{var r=a(8999);a.o(r,"usePathname")&&a.d(t,{usePathname:function(){return r.usePathname}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(t,{useSearchParams:function(){return r.useSearchParams}})},7550:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},8749:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(9946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])}}]);