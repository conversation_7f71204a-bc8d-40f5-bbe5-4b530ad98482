exports.id=6806,exports.ids=[6806],exports.modules={2470:(e,t,r)=>{Promise.resolve().then(r.bind(r,57445))},4780:(e,t,r)=>{"use strict";r.d(t,{D1:()=>m,Oj:()=>u,Yq:()=>o,ZU:()=>x,ZV:()=>n,cn:()=>a,jI:()=>h,lW:()=>d,r6:()=>c,vv:()=>i});var s=r(49384),l=r(82348);function a(...e){return(0,l.QP)((0,s.$)(e))}function i(e,t="USD"){return new Intl.NumberFormat("en-US",{style:"currency",currency:t,minimumFractionDigits:2,maximumFractionDigits:2}).format(e)}function n(e,t=2){return new Intl.NumberFormat("en-US",{minimumFractionDigits:t,maximumFractionDigits:t}).format(e)}function o(e){let t="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(t)}function c(e){let t="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(t)}function d(e){if(navigator.clipboard)return navigator.clipboard.writeText(e);let t=document.createElement("textarea");t.value=e,document.body.appendChild(t),t.focus(),t.select();try{return document.execCommand("copy"),Promise.resolve()}catch(e){return Promise.reject(e)}finally{document.body.removeChild(t)}}function u(e){let t=[],r=[],s=e.length>=8;r.push({valid:s,message:"At least 8 characters long"}),s||t.push("Password must be at least 8 characters long");let l=/[A-Z]/.test(e);r.push({valid:l,message:"At least one uppercase letter"}),l||t.push("Password must contain at least one uppercase letter");let a=/[a-z]/.test(e);r.push({valid:a,message:"At least one lowercase letter"}),a||t.push("Password must contain at least one lowercase letter");let i=/\d/.test(e);r.push({valid:i,message:"At least one number"}),i||t.push("Password must contain at least one number");let n=/[!@#$%^&*(),.?":{}|<>]/.test(e);return r.push({valid:n,message:"At least one special character"}),n||t.push("Password must contain at least one special character"),{isValid:0===t.length,errors:t,checks:r}}function h(e){return e>=1e3?`${(e/1e3).toFixed(1)}K TH/s`:`${e.toFixed(2)} TH/s`}function x(){let e=new Date,t=new Date;t.setUTCDate(e.getUTCDate()+(6-e.getUTCDay())),t.setUTCHours(15,0,0,0),e>t&&t.setUTCDate(t.getUTCDate()+7);let r=t.getTime()-e.getTime(),s=Math.floor(r/864e5),l=Math.floor(r%864e5/36e5);return{days:s,hours:l,minutes:Math.floor(r%36e5/6e4),seconds:Math.floor(r%6e4/1e3)}}function m(){let e=new Date,t=new Date;t.setUTCDate(e.getUTCDate()+(6-e.getUTCDay())),t.setUTCHours(15,0,0,0),e>t&&t.setUTCDate(t.getUTCDate()+7);let r=t.getTime()-e.getTime(),s=Math.floor(r/864e5),l=Math.floor(r%864e5/36e5);return{days:s,hours:l,minutes:Math.floor(r%36e5/6e4),seconds:Math.floor(r%6e4/1e3)}}},26207:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},34014:(e,t,r)=>{Promise.resolve().then(r.bind(r,49567))},39996:(e,t,r)=>{"use strict";r.d(t,{$n:()=>c,Zp:()=>d,Wu:()=>x,aR:()=>u,ZB:()=>h,pd:()=>m,Rh:()=>p,aF:()=>y,ph:()=>S,vp:()=>W,G_:()=>N,eC:()=>P});var s=r(60687),l=r(43210),a=r.n(l),i=r(24224),n=r(4780);let o=(0,i.F)("inline-flex items-center justify-center rounded-xl font-semibold focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none shadow-lg",{variants:{variant:{primary:"bg-yellow-500 text-white focus:ring-yellow-500",secondary:"bg-gray-200 text-gray-800 focus:ring-gray-500",success:"bg-emerald-500 text-white focus:ring-emerald-500",danger:"bg-red-500 text-white focus:ring-red-500",warning:"bg-yellow-500 text-white focus:ring-yellow-500",destructive:"bg-red-600 text-white focus:ring-red-500",outline:"border-2 border-yellow-500 bg-transparent text-yellow-600 focus:ring-yellow-500",ghost:"text-gray-600 focus:ring-yellow-500 rounded-lg",link:"text-yellow-600 underline-offset-4 focus:ring-yellow-500",premium:"bg-slate-800 text-white focus:ring-slate-500",glass:"glass-morphism text-slate-900 backdrop-blur-xl border border-white/20"},size:{sm:"h-10 px-4 text-sm rounded-lg",md:"h-12 px-6 text-base rounded-xl",lg:"h-14 px-8 text-lg rounded-xl",xl:"h-16 px-10 text-xl rounded-2xl font-bold",icon:"h-12 w-12 rounded-xl"}},defaultVariants:{variant:"primary",size:"md"}}),c=a().forwardRef(({className:e,variant:t,size:r,loading:l,leftIcon:a,rightIcon:i,children:c,disabled:d,...u},h)=>(0,s.jsxs)("button",{className:(0,n.cn)(o({variant:t,size:r,className:e})),ref:h,disabled:d||l,...u,children:[l&&(0,s.jsx)("div",{className:"mr-2",children:(0,s.jsx)("div",{className:"spinner"})}),a&&!l&&(0,s.jsx)("span",{className:"mr-2",children:a}),c,i&&!l&&(0,s.jsx)("span",{className:"ml-2",children:i})]}));c.displayName="Button";let d=a().forwardRef(({className:e,children:t,...r},l)=>(0,s.jsx)("div",{ref:l,className:(0,n.cn)("rounded-2xl border border-gray-200/50 bg-white/90 backdrop-blur-xl shadow-xl overflow-hidden",e),...r,children:t}));d.displayName="Card";let u=a().forwardRef(({className:e,children:t,...r},l)=>(0,s.jsx)("div",{ref:l,className:(0,n.cn)("flex flex-col space-y-1.5 p-6 pb-4",e),...r,children:t}));u.displayName="CardHeader";let h=a().forwardRef(({className:e,children:t,...r},l)=>(0,s.jsx)("h3",{ref:l,className:(0,n.cn)("text-xl font-semibold leading-none tracking-tight text-dark-900",e),...r,children:t}));h.displayName="CardTitle",a().forwardRef(({className:e,children:t,...r},l)=>(0,s.jsx)("p",{ref:l,className:(0,n.cn)("text-sm text-gray-500",e),...r,children:t})).displayName="CardDescription";let x=a().forwardRef(({className:e,children:t,...r},l)=>(0,s.jsx)("div",{ref:l,className:(0,n.cn)("p-6 pt-0",e),...r,children:t}));x.displayName="CardContent",a().forwardRef(({className:e,children:t,...r},l)=>(0,s.jsx)("div",{ref:l,className:(0,n.cn)("flex items-center p-6 pt-0",e),...r,children:t})).displayName="CardFooter";let m=a().forwardRef(({className:e,type:t,label:r,error:l,leftIcon:a,rightIcon:i,...o},c)=>(0,s.jsxs)("div",{className:"w-full",children:[r&&(0,s.jsx)("label",{className:"block text-sm font-medium text-dark-700 mb-2",children:r}),(0,s.jsxs)("div",{className:"relative",children:[a&&(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)("span",{className:"text-gray-400",children:a})}),(0,s.jsx)("input",{type:t,className:(0,n.cn)("flex h-14 w-full rounded-xl border-2 border-gray-200 bg-white/90 backdrop-blur-sm px-4 py-4 text-base shadow-inner focus:shadow-lg placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-solar-500 focus:border-solar-500 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-300 hover:border-gray-300",a&&"pl-12",i&&"pr-12",l&&"border-red-500 focus:ring-red-500 focus:border-red-500",e),ref:c,...o}),i&&(0,s.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:(0,s.jsx)("span",{className:"text-gray-400",children:i})})]}),l&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:l})]}));m.displayName="Input";var f=r(51215),g=r(11860);let y=({isOpen:e,onClose:t,title:r,children:a,size:i="md",showCloseButton:o=!0,darkMode:d=!1})=>{if((0,l.useEffect)(()=>(e?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[e]),(0,l.useEffect)(()=>{let r=e=>{"Escape"===e.key&&t()};return e&&document.addEventListener("keydown",r),()=>{document.removeEventListener("keydown",r)}},[e,t]),!e)return null;let u=(0,s.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",children:[(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity",onClick:t}),(0,s.jsxs)("div",{className:(0,n.cn)("relative w-full rounded-xl shadow-xl transform transition-all",d?"bg-slate-800":"bg-white",{sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl"}[i]),onClick:e=>e.stopPropagation(),children:[(0,s.jsxs)("div",{className:(0,n.cn)("flex items-center justify-between p-6 border-b",d?"border-slate-700":"border-gray-200"),children:[(0,s.jsx)("h2",{className:(0,n.cn)("text-xl font-semibold",d?"text-white":"text-dark-900"),children:r}),o&&(0,s.jsx)(c,{variant:"ghost",size:"icon",onClick:t,className:"h-8 w-8 rounded-full",children:(0,s.jsx)(g.A,{className:"h-4 w-4"})})]}),(0,s.jsx)("div",{className:"p-6",children:a})]})]});return(0,f.createPortal)(u,document.body)},p=({size:e="md",className:t,text:r})=>(0,s.jsxs)("div",{className:(0,n.cn)("flex flex-col items-center justify-center",t),children:[(0,s.jsx)("div",{className:(0,n.cn)("animate-spin rounded-full border-2 border-gray-300 border-t-solar-500",{sm:"h-4 w-4",md:"h-8 w-8",lg:"h-12 w-12"}[e])}),r&&(0,s.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:r})]});var w=r(43649),b=r(5336),v=r(96882);let j=({isOpen:e,onClose:t,onConfirm:r,title:l,message:a,confirmText:i="Confirm",cancelText:o="Cancel",variant:d="default",darkMode:u=!1,loading:h=!1})=>{if(!e)return null;let x=(0,s.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50 transition-opacity",onClick:t}),(0,s.jsxs)("div",{className:(0,n.cn)("relative w-full max-w-md rounded-xl shadow-xl transform transition-all",u?"bg-slate-800 border border-slate-700":"bg-white"),onClick:e=>e.stopPropagation(),children:[(0,s.jsxs)("div",{className:(0,n.cn)("flex items-center justify-between p-6 border-b",u?"border-slate-700":"border-gray-200"),children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(()=>{switch(d){case"danger":return(0,s.jsx)(w.A,{className:"h-6 w-6 text-red-500"});case"warning":return(0,s.jsx)(w.A,{className:"h-6 w-6 text-yellow-500"});case"success":return(0,s.jsx)(b.A,{className:"h-6 w-6 text-green-500"});default:return(0,s.jsx)(v.A,{className:"h-6 w-6 text-blue-500"})}})(),(0,s.jsx)("h2",{className:(0,n.cn)("text-lg font-semibold",u?"text-white":"text-gray-900"),children:l})]}),(0,s.jsx)(c,{variant:"ghost",size:"icon",onClick:t,disabled:h,className:"h-8 w-8 rounded-full",children:(0,s.jsx)(g.A,{className:"h-4 w-4"})})]}),(0,s.jsx)("div",{className:"p-6",children:"string"==typeof a?(0,s.jsx)("p",{className:(0,n.cn)("text-sm leading-relaxed",u?"text-slate-300":"text-gray-600"),children:a}):(0,s.jsx)("div",{className:(0,n.cn)("text-sm leading-relaxed",u?"text-slate-300":"text-gray-600"),children:a})}),(0,s.jsxs)("div",{className:(0,n.cn)("flex items-center justify-end space-x-3 p-6 border-t",u?"border-slate-700":"border-gray-200"),children:[(0,s.jsx)(c,{variant:"outline",onClick:t,disabled:h,className:(0,n.cn)(u?"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white":""),children:o}),(0,s.jsx)(c,{variant:(()=>{switch(d){case"danger":return"destructive";case"warning":return"warning";default:return"default"}})(),onClick:r,disabled:h,className:(0,n.cn)(h&&"opacity-50 cursor-not-allowed"),children:h?"Processing...":i})]})]})]});return(0,f.createPortal)(x,document.body)},N=()=>{let[e,t]=a().useState({isOpen:!1,title:"",message:"",onConfirm:()=>{}}),[r,l]=a().useState(!1),i=()=>{r||t(e=>({...e,isOpen:!1}))};return{showConfirm:e=>{t({isOpen:!0,...e,onConfirm:async()=>{l(!0);try{await e.onConfirm(),t(e=>({...e,isOpen:!1}))}catch(e){console.error("Confirm action failed:",e)}finally{l(!1)}}})},hideConfirm:i,ConfirmDialog:()=>(0,s.jsx)(j,{isOpen:e.isOpen,onClose:i,onConfirm:e.onConfirm,title:e.title,message:e.message,variant:e.variant,confirmText:e.confirmText,cancelText:e.cancelText,darkMode:e.darkMode,loading:r}),loading:r}};var C=r(93613);let k=({isOpen:e,onClose:t,title:r,message:l,variant:a="info",darkMode:i=!1,showCloseButton:o=!0,buttonText:d="OK",size:u="md"})=>{if(!e)return null;let h=(0,s.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",onClick:t,children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-black/50 backdrop-blur-sm"}),(0,s.jsxs)("div",{className:(0,n.cn)("relative w-full rounded-xl shadow-xl transform transition-all",i?"bg-slate-800 border border-slate-700":"bg-white",{sm:"max-w-sm",md:"max-w-md",lg:"max-w-lg"}[u]),onClick:e=>e.stopPropagation(),children:[(0,s.jsxs)("div",{className:(0,n.cn)("flex items-center justify-between p-6 border-b",i?"border-slate-700":"border-gray-200"),children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(()=>{switch(a){case"error":return(0,s.jsx)(C.A,{className:"h-6 w-6 text-red-500"});case"warning":return(0,s.jsx)(w.A,{className:"h-6 w-6 text-yellow-500"});case"success":return(0,s.jsx)(b.A,{className:"h-6 w-6 text-green-500"});default:return(0,s.jsx)(v.A,{className:"h-6 w-6 text-blue-500"})}})(),(0,s.jsx)("h2",{className:(0,n.cn)("text-lg font-semibold",i?"text-white":"text-gray-900"),children:r})]}),o&&(0,s.jsx)(c,{variant:"ghost",size:"icon",onClick:t,className:"h-8 w-8 rounded-full",children:(0,s.jsx)(g.A,{className:"h-4 w-4"})})]}),(0,s.jsx)("div",{className:"p-6",children:"string"==typeof l?(0,s.jsx)("p",{className:(0,n.cn)("text-sm leading-relaxed",i?"text-slate-300":"text-gray-600"),children:l}):(0,s.jsx)("div",{className:(0,n.cn)("text-sm leading-relaxed",i?"text-slate-300":"text-gray-600"),children:l})}),(0,s.jsx)("div",{className:(0,n.cn)("flex items-center justify-end p-6 border-t",i?"border-slate-700":"border-gray-200"),children:(0,s.jsx)(c,{variant:(()=>{switch(a){case"error":return"danger";case"warning":return"warning";case"success":return"success";default:return"primary"}})(),onClick:t,className:"min-w-[80px]",children:d})})]})]});return(0,f.createPortal)(h,document.body)},P=()=>{let[e,t]=a().useState({isOpen:!1,title:"",message:""}),r=()=>{t(e=>({...e,isOpen:!1}))};return{showMessage:e=>{t({isOpen:!0,...e})},hideMessage:r,MessageBoxComponent:()=>(0,s.jsx)(k,{...e,onClose:r})}};var A=r(58869),D=r(51361),T=r(16023),M=r(30474);let W=({currentPicture:e,onUpload:t,onRemove:r,loading:a=!1,disabled:i=!1})=>{let[n,o]=(0,l.useState)(null),[d,u]=(0,l.useState)(!1),h=(0,l.useRef)(null),x=e=>{if(!e)return;if(!e.type.startsWith("image/"))return void alert("Please select an image file");if(e.size>5242880)return void alert("File size must be less than 5MB");let r=new FileReader;r.onload=e=>{o(e.target?.result)},r.readAsDataURL(e),t(e)},m=n||e;return(0,s.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"w-32 h-32 rounded-full overflow-hidden border-4 border-gray-200 bg-gray-100 flex items-center justify-center",children:m?(0,s.jsx)(M.default,{src:m,alt:"Profile Picture",width:128,height:128,className:"w-full h-full object-cover"}):(0,s.jsx)(A.A,{className:"w-16 h-16 text-gray-400"})}),a&&(0,s.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-white"})}),m&&!a&&(0,s.jsx)("button",{onClick:()=>{o(null),r&&r()},disabled:i,className:"absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors",children:(0,s.jsx)(g.A,{className:"w-4 h-4"})})]}),(0,s.jsx)("div",{className:`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${d?"border-blue-400 bg-blue-50":"border-gray-300 hover:border-gray-400"} ${i?"opacity-50 cursor-not-allowed":"cursor-pointer"}`,onDrop:e=>{e.preventDefault(),u(!1);let t=e.dataTransfer.files[0];t&&x(t)},onDragOver:e=>{e.preventDefault(),u(!0)},onDragLeave:e=>{e.preventDefault(),u(!1)},onClick:()=>!i&&h.current?.click(),children:(0,s.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,s.jsx)(D.A,{className:"w-8 h-8 text-gray-400"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-700",children:"Click to upload or drag and drop"}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"PNG, JPG, GIF up to 5MB"})]})]})}),(0,s.jsx)("input",{ref:h,type:"file",accept:"image/*",onChange:e=>{let t=e.target.files?.[0];t&&x(t)},className:"hidden",disabled:i}),(0,s.jsxs)(c,{onClick:()=>h.current?.click(),disabled:i||a,variant:"outline",size:"sm",className:"flex items-center space-x-2",children:[(0,s.jsx)(T.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"Choose File"})]})]})},S=({src:e,alt:t="Profile",size:r=40,className:a="",fallbackText:i,fallbackBgColor:n="bg-gray-500",priority:o=!1,loading:c="lazy"})=>{let[d,u]=(0,l.useState)(!1),[h,x]=(0,l.useState)(!0),m=e&&!d,f=!e||d;return(0,s.jsxs)("div",{className:`relative flex items-center justify-center overflow-hidden ${n} ${a}`,style:{width:r,height:r},children:[h&&e&&(0,s.jsx)("div",{className:"absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center",children:(0,s.jsx)("div",{className:"w-4 h-4 bg-gray-300 rounded-full"})}),m&&(0,s.jsx)(M.default,{src:e,alt:t,width:r,height:r,className:"w-full h-full object-cover",priority:o,loading:c,onError:()=>{u(!0),x(!1)},onLoad:()=>{x(!1)},style:{opacity:+!h,transition:"opacity 0.2s ease-in-out"}}),f&&(0,s.jsx)("div",{className:"flex items-center justify-center w-full h-full",children:i?(0,s.jsx)("span",{className:"text-white font-semibold",style:{fontSize:.4*r},children:i}):(0,s.jsx)(A.A,{className:"text-white",style:{width:.5*r,height:.5*r}})})]})}},49567:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>l});var s=r(12907);let l=(0,s.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\hooks\\useAuth.tsx","AuthProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\hooks\\useAuth.tsx","useAuth")},57445:(e,t,r)=>{"use strict";r.d(t,{A:()=>n,AuthProvider:()=>i});var s=r(60687),l=r(43210);let a=(0,l.createContext)(void 0),i=({children:e})=>{let[t,r]=(0,l.useState)(null),[i,n]=(0,l.useState)(!0);(0,l.useEffect)(()=>{o()},[]);let o=async()=>{try{let e=await fetch("/api/auth/me",{credentials:"include"});if(e.ok){let t=await e.json();t.success&&r(t.data)}}catch(e){console.error("Auth check failed:",e)}finally{n(!1)}},c=async(e,t)=>{n(!0);try{let s=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e,password:t})}),l=await s.json();if(!l.success)throw Error(l.error||"Login failed");r(l.data.user),setTimeout(()=>{o()},100)}catch(e){throw e}finally{n(!1)}},d=async(e,t,s,l,a,i,o,c)=>{n(!0);try{let n=o?`/api/auth/register?side=${o}`:"/api/auth/register",d=await fetch(n,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,firstName:t,lastName:s,password:l,confirmPassword:a,referralCode:i,otp:c})}),u=await d.json();if(!u.success)throw Error(u.error||"Registration failed");r({id:u.data.user.id,email:u.data.user.email,firstName:u.data.user.firstName||"",lastName:u.data.user.lastName||"",referralId:u.data.user.referralId,role:u.data.user.role||"USER",kycStatus:u.data.user.kycStatus,isActive:u.data.user.isActive||!0,profilePicture:u.data.user.profilePicture||null,createdAt:u.data.user.createdAt,updatedAt:u.data.user.updatedAt})}catch(e){throw e}finally{n(!1)}},u=async()=>{try{await fetch("/api/auth/logout",{method:"POST",credentials:"include"})}catch(e){console.error("Logout error:",e)}finally{r(null)}},h=async()=>{await o()};return(0,s.jsx)(a.Provider,{value:{user:t,loading:i,login:c,register:d,logout:u,refreshUser:h},children:e})},n=()=>{let e=(0,l.useContext)(a);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},61135:()=>{},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var s=r(31658);let l=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},71180:(e,t,r)=>{"use strict";r.d(t,{Lc:()=>i,hK:()=>n,NC:()=>a,MX:()=>l,Kj:()=>o});var s=r(60687);r(43210);let l=({className:e,size:t=24})=>(0,s.jsxs)("svg",{width:t,height:t,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:e,children:[(0,s.jsx)("rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("line",{x1:"2",y1:"8",x2:"22",y2:"8",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"2",y1:"12",x2:"22",y2:"12",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"2",y1:"16",x2:"22",y2:"16",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"6",y1:"4",x2:"6",y2:"20",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"10",y1:"4",x2:"10",y2:"20",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"14",y1:"4",x2:"14",y2:"20",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"18",y1:"4",x2:"18",y2:"20",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("circle",{cx:"20",cy:"2",r:"1",fill:"currentColor"}),(0,s.jsx)("path",{d:"M19 1l1 1-1 1-1-1z",fill:"currentColor"})]}),a=({className:e,size:t=24})=>(0,s.jsxs)("svg",{width:t,height:t,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:e,children:[(0,s.jsx)("rect",{x:"2",y:"6",width:"20",height:"12",rx:"2",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("rect",{x:"4",y:"8",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("rect",{x:"8",y:"8",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("rect",{x:"12",y:"8",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("rect",{x:"16",y:"8",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("rect",{x:"4",y:"12",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("rect",{x:"8",y:"12",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("rect",{x:"12",y:"12",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("rect",{x:"16",y:"12",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("circle",{cx:"20",cy:"4",r:"1",fill:"currentColor"}),(0,s.jsx)("line",{x1:"20",y1:"4",x2:"20",y2:"6",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"18",y1:"2",x2:"22",y2:"2",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"19",y1:"1",x2:"21",y2:"3",stroke:"currentColor",strokeWidth:"1"}),(0,s.jsx)("line",{x1:"21",y1:"1",x2:"19",y2:"3",stroke:"currentColor",strokeWidth:"1"})]}),i=({className:e,size:t=24})=>(0,s.jsxs)("svg",{width:t,height:t,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:e,children:[(0,s.jsx)("circle",{cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("path",{d:"M8 12h8M12 8v8",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("path",{d:"M10 8h4c1.1 0 2 .9 2 2s-.9 2-2 2h-4",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("path",{d:"M10 14h4c1.1 0 2 .9 2 2s-.9 2-2 2h-4",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("line",{x1:"12",y1:"6",x2:"12",y2:"8",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"12",y1:"16",x2:"12",y2:"18",stroke:"currentColor",strokeWidth:"2"})]}),n=({className:e,size:t=24})=>(0,s.jsxs)("svg",{width:t,height:t,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:e,children:[(0,s.jsx)("path",{d:"M12 2C8 2 5 5 5 9c0 5 7 13 7 13s7-8 7-13c0-4-3-7-7-7z",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("path",{d:"M12 9c0-2-1-3-3-3s-3 1-3 3 1 3 3 3 3-1 3-3z",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("path",{d:"M8 9c0 1 1 2 2 2s2-1 2-2-1-2-2-2-2 1-2 2z",fill:"currentColor"})]}),o=({className:e,size:t=24})=>(0,s.jsxs)("svg",{width:t,height:t,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:e,children:[(0,s.jsx)("line",{x1:"12",y1:"12",x2:"12",y2:"22",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("path",{d:"M12 12L8 4c-1-2 0-4 2-4s3 2 2 4l-2 8z",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("path",{d:"M12 12l8-4c2-1 4 0 4 2s-2 3-4 2l-8-2z",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("path",{d:"M12 12l-4 8c-1 2-3 2-4 0s0-3 2-4l8-2z",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("circle",{cx:"12",cy:"12",r:"1",fill:"currentColor"}),(0,s.jsx)("line",{x1:"10",y1:"22",x2:"14",y2:"22",stroke:"currentColor",strokeWidth:"2"})]})},86455:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o,metadata:()=>n});var s=r(37413),l=r(25091),a=r.n(l);r(61135);var i=r(49567);let n={title:"HashCoreX - Solar-Powered Cloud Mining",description:"Sustainable cryptocurrency mining with solar energy. Earn daily ROI with our eco-friendly mining platform."};function o({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${a().variable} font-sans antialiased`,children:(0,s.jsx)(i.AuthProvider,{children:e})})})}}};