(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{347:()=>{},658:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,1666,23)),Promise.resolve().then(a.t.bind(a,347,23)),Promise.resolve().then(a.bind(a,4105))},1666:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c",variable:"__variable_e8ce0c"}},4105:(e,t,a)=>{"use strict";a.d(t,{A:()=>o,AuthProvider:()=>l});var r=a(5155),s=a(2115);let i=(0,s.createContext)(void 0),l=e=>{let{children:t}=e,[a,l]=(0,s.useState)(null),[o,c]=(0,s.useState)(!0);(0,s.useEffect)(()=>{n()},[]);let n=async()=>{try{let e=await fetch("/api/auth/me",{credentials:"include"});if(e.ok){let t=await e.json();t.success&&l(t.data)}}catch(e){console.error("Auth check failed:",e)}finally{c(!1)}},u=async(e,t)=>{c(!0);try{let a=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e,password:t})}),r=await a.json();if(!r.success)throw Error(r.error||"Login failed");l(r.data.user),setTimeout(()=>{n()},100)}catch(e){throw e}finally{c(!1)}},d=async(e,t,a,r,s,i,o,n)=>{c(!0);try{let c=await fetch(o?"/api/auth/register?side=".concat(o):"/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,firstName:t,lastName:a,password:r,confirmPassword:s,referralCode:i,otp:n})}),u=await c.json();if(!u.success)throw Error(u.error||"Registration failed");l({id:u.data.user.id,email:u.data.user.email,firstName:u.data.user.firstName||"",lastName:u.data.user.lastName||"",referralId:u.data.user.referralId,role:u.data.user.role||"USER",kycStatus:u.data.user.kycStatus,isActive:u.data.user.isActive||!0,profilePicture:u.data.user.profilePicture||null,createdAt:u.data.user.createdAt,updatedAt:u.data.user.updatedAt})}catch(e){throw e}finally{c(!1)}},h=async()=>{try{await fetch("/api/auth/logout",{method:"POST",credentials:"include"})}catch(e){console.error("Logout error:",e)}finally{l(null)}},f=async()=>{await n()};return(0,r.jsx)(i.Provider,{value:{user:a,loading:o,login:u,register:d,logout:h,refreshUser:f},children:t})},o=()=>{let e=(0,s.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}}},e=>{var t=t=>e(e.s=t);e.O(0,[2258,8441,1684,7358],()=>t(658)),_N_E=e.O()}]);