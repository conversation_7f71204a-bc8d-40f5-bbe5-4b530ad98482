"use strict";exports.id=5112,exports.ids=[5112],exports.modules={92731:(t,n,e)=>{e.d(n,{F2:()=>s,IT:()=>m,Oh:()=>d,WL:()=>u,calculateDynamicROI:()=>o,eB:()=>c,s2:()=>l});var i=e(31183),a=e(6710),r=e(39794);async function o(t){try{let n,e,i,r=await a.rs.get("earningsRanges");if(r)try{n=JSON.parse(r)}catch(t){console.error("Error parsing earnings ranges:",t),n=null}n&&Array.isArray(n)||(n=[{minTHS:0,maxTHS:10,dailyReturnMin:.3,dailyReturnMax:.5,monthlyReturnMin:10,monthlyReturnMax:15},{minTHS:10,maxTHS:50,dailyReturnMin:.4,dailyReturnMax:.6,monthlyReturnMin:10,monthlyReturnMax:15},{minTHS:50,maxTHS:999999,dailyReturnMin:.5,dailyReturnMax:.7,monthlyReturnMin:10,monthlyReturnMax:15}]);let o=n.find(n=>t>=n.minTHS&&t<=n.maxTHS);if(o)e=o.dailyReturnMin,i=o.dailyReturnMax;else{let t=n[n.length-1];e=t.dailyReturnMin,i=t.dailyReturnMax}let s=e+Math.random()*(i-e);return Math.round(100*s)/100}catch(n){if(console.error("Error calculating dynamic ROI:",n),t>=50)return .6;if(t>=10)return .5;return .4}}async function s(t){try{let n,e,i=await a.rs.get("earningsRanges");if(i)try{n=JSON.parse(i)}catch(t){console.error("Error parsing earnings ranges:",t),n=null}n&&Array.isArray(n)||(n=[{minTHS:0,maxTHS:10,dailyReturnMin:.3,dailyReturnMax:.5,monthlyReturnMin:10,monthlyReturnMax:15},{minTHS:10,maxTHS:50,dailyReturnMin:.4,dailyReturnMax:.6,monthlyReturnMin:10,monthlyReturnMax:15},{minTHS:50,maxTHS:999999,dailyReturnMin:.5,dailyReturnMax:.7,monthlyReturnMin:10,monthlyReturnMax:15}]);let r=n.find(n=>t>=n.minTHS&&t<=n.maxTHS);e=r||n[n.length-1];let o=0;for(let t=0;t<7;t++){let t=e.dailyReturnMin+Math.random()*(e.dailyReturnMax-e.dailyReturnMin);o+=t}return o/7}catch(t){return console.error("Error calculating 7-day average ROI:",t),.4}}async function l(){try{console.log("Updating existing mining units with new ROI configuration...");let t=await i.prisma.miningUnit.findMany({where:{status:"ACTIVE",expiryDate:{gt:new Date}}});console.log(`Found ${t.length} active mining units to update`);let n=[];for(let e of t)try{let t=await o(e.thsAmount);await i.prisma.miningUnit.update({where:{id:e.id},data:{dailyROI:t}}),n.push({unitId:e.id,userId:e.userId,thsAmount:e.thsAmount,oldROI:e.dailyROI,newROI:t}),console.log(`Updated unit ${e.id}: ${e.dailyROI}% -> ${t}%`)}catch(t){console.error(`Error updating unit ${e.id}:`,t)}return await a.AJ.create({action:"MINING_UNITS_ROI_UPDATED",details:{unitsUpdated:n.length,totalUnits:t.length,updateResults:n,timestamp:new Date().toISOString()}}),console.log(`Successfully updated ${n.length} mining units with new ROI configuration`),{success:!0,unitsUpdated:n.length,totalUnits:t.length,updateResults:n}}catch(t){throw console.error("Error updating existing mining units ROI:",t),t}}async function u(){try{console.log("Starting daily ROI calculation with FIFO allocation...");let t=await i.prisma.user.findMany({where:{miningUnits:{some:{status:"ACTIVE",expiryDate:{gt:new Date}}}},include:{miningUnits:{where:{status:"ACTIVE",expiryDate:{gt:new Date}}}}});console.log(`Found ${t.length} users with active mining units`);let n=[];for(let e of t)try{let t=0,o=[];for(let n of e.miningUnits){let e=n.investmentAmount*n.dailyROI/100;t+=e,o.push({unitId:n.id,thsAmount:n.thsAmount,dailyEarnings:e})}if(t>0){let s=await a.DR.create({userId:e.id,type:"MINING_EARNINGS",amount:t,description:`Daily mining earnings - Total: ${o.map(t=>`${t.thsAmount} TH/s`).join(", ")}`,status:"PENDING"}),l=await (0,r.Py)(e.id,t,"MINING_EARNINGS",s.id,"Daily mining ROI earnings");l.totalDiscarded>0?await i.prisma.transaction.update({where:{id:s.id},data:{amount:l.totalAllocated,description:`Daily mining earnings - Total: ${o.map(t=>`${t.thsAmount} TH/s`).join(", ")}. Note: $${l.totalDiscarded.toFixed(2)} excess discarded due to 5x mining unit limit cap.`}}):await i.prisma.transaction.update({where:{id:s.id},data:{amount:l.totalAllocated}}),n.push({userId:e.id,totalEarnings:l.totalAllocated,allocations:l.allocations,unitsProcessed:e.miningUnits.length,discardedAmount:l.totalDiscarded}),console.log(`Allocated ${l.totalAllocated} of ${t} mining earnings to ${l.allocations.length} units for user ${e.id}`),l.totalDiscarded>0&&console.log(`Discarded ${l.totalDiscarded} excess mining earnings due to capacity limits for user ${e.id}`)}}catch(t){console.error(`Error processing mining earnings for user ${e.id}:`,t)}return await a.AJ.create({action:"DAILY_ROI_CALCULATED",details:{usersProcessed:n.length,totalEarnings:n.reduce((t,n)=>t+n.totalEarnings,0),totalAllocations:n.reduce((t,n)=>t+n.allocations.length,0),timestamp:new Date().toISOString()}}),console.log(`Daily ROI calculation completed. Processed ${n.length} users with FIFO allocation.`),n}catch(t){throw console.error("Daily ROI calculation error:",t),t}}async function d(){try{console.log("Starting weekly earnings distribution...");let t=await i.prisma.transaction.findMany({where:{type:"MINING_EARNINGS",status:"PENDING"},include:{user:!0}});console.log(`Found ${t.length} pending earnings transactions`);let n=new Map;for(let e of t){let t=n.get(e.userId)||0;n.set(e.userId,t+e.amount)}let e=[];for(let[t,a]of n)try{await i.prisma.transaction.updateMany({where:{userId:t,type:"MINING_EARNINGS",status:"PENDING"},data:{status:"COMPLETED"}}),e.push({userId:t,totalEarnings:a})}catch(n){console.error(`Error processing earnings for user ${t}:`,n)}return await a.AJ.create({action:"WEEKLY_EARNINGS_DISTRIBUTED",details:{usersProcessed:e.length,totalDistributed:e.reduce((t,n)=>t+n.totalEarnings,0),transactionsProcessed:t.length,timestamp:new Date().toISOString()}}),console.log(`Weekly earnings distribution completed. Processed ${e.length} users.`),e}catch(t){throw console.error("Weekly earnings distribution error:",t),t}}async function c(){try{console.log("Checking for expired mining units...");let t=await i.prisma.miningUnit.findMany({where:{status:"ACTIVE",expiryDate:{lte:new Date}},include:{user:{select:{email:!0,firstName:!0,lastName:!0}}}});for(let n of(console.log(`Found ${t.length} units to expire`),t)){await a.tg.expireUnit(n.id),await a.AJ.create({action:"MINING_UNIT_EXPIRED",userId:n.userId,details:{miningUnitId:n.id,reason:"24_months_reached",totalEarned:n.totalEarned,investmentAmount:n.investmentAmount}});try{let{emailNotificationService:t}=await Promise.all([e.e(9526),e.e(3161)]).then(e.bind(e,13161)),i=n.miningEarnings+n.referralEarnings+n.binaryEarnings;await t.sendMiningUnitExpiryNotification({userId:n.userId,email:n.user.email,firstName:n.user.firstName,lastName:n.user.lastName,thsAmount:n.thsAmount,investmentAmount:n.investmentAmount,totalEarned:i,purchaseDate:n.createdAt.toISOString(),expiryDate:n.expiryDate.toISOString(),expiryReason:"TIME_LIMIT"})}catch(t){console.error("Failed to send mining unit expiry email:",t)}}return t.length}catch(t){throw console.error("Mining unit expiry check error:",t),t}}async function m(t){try{let n=await a.tg.findActiveByUserId(t);if(0===n.length)return{next7Days:0,next30Days:0,next365Days:0,next2Years:0};let e=0;for(let t of n){let n=t.investmentAmount*t.dailyROI/100,i=5*t.investmentAmount-t.totalEarned;e+=Math.min(n,i)}return{next7Days:7*e,next30Days:30*e,next365Days:365*e,next2Years:730*e}}catch(t){throw console.error("Estimated earnings calculation error:",t),t}}}};