"use strict";(()=>{var e={};e.id=172,e.ids=[172],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29153:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>S,routeModule:()=>T,serverHooks:()=>h,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>E});var s={};r.r(s),r.d(s,{GET:()=>f,POST:()=>m});var a=r(96559),n=r(48088),i=r(37719),o=r(32190),l=r(55511),u=r.n(l);let d=new Map;class c{static generateToken(e){let t=u().randomBytes(32).toString("hex"),r=Date.now()+36e5;return d.set(t,{token:t,expires:r,userId:e}),t}static validateToken(e,t){let r=d.get(e);return!!r&&(Date.now()>r.expires?(d.delete(e),!1):!t||!r.userId||r.userId===t)}static consumeToken(e){let t=this.validateToken(e);return t&&d.delete(e),t}static setCsrfCookie(e,t){e.cookies.set("csrf-token",t,{httpOnly:!1,secure:!0,sameSite:"strict",maxAge:3600,path:"/"})}static getTokenFromRequest(e){let t=e.headers.get("x-csrf-token");if(t)return t;let r=e.headers.get("content-type");return r?.includes("application/x-www-form-urlencoded"),null}static async validateRequest(e,t){if(!["POST","PUT","PATCH","DELETE"].includes(e.method.toUpperCase()))return{valid:!0};let r=e.nextUrl.pathname;if(["/api/auth/login","/api/auth/register","/api/auth/send-otp","/api/health"].some(e=>r.startsWith(e)))return{valid:!0};let s=this.getTokenFromRequest(e);return s?this.validateToken(s,t)?{valid:!0}:{valid:!1,error:"Invalid or expired CSRF token. Please refresh the page and try again."}:{valid:!1,error:"CSRF token missing. Please refresh the page and try again."}}static cleanupExpiredTokens(){let e=Date.now();for(let[t,r]of d.entries())e>r.expires&&d.delete(t)}static getTokenStats(){let e=Date.now(),t=0,r=0;for(let s of d.values())e>s.expires?t++:r++;return{totalTokens:d.size,expiredTokens:t,activeTokens:r}}}setInterval(()=>{c.cleanupExpiredTokens()},6e5);var p=r(39542);async function f(e){try{let t;try{let{authenticated:r,user:s}=await (0,p.b9)(e);r&&s&&(t=s.id)}catch(e){}let r=c.generateToken(t),s=Date.now()+36e5,a=o.NextResponse.json({success:!0,data:{token:r,expires:s}});return c.setCsrfCookie(a,r),a.headers.set("X-Content-Type-Options","nosniff"),a.headers.set("X-Frame-Options","DENY"),a.headers.set("Cache-Control","no-store, no-cache, must-revalidate"),a}catch(e){return console.error("CSRF token generation error:",e),o.NextResponse.json({success:!1,error:"Failed to generate CSRF token"},{status:500})}}async function m(e){try{let t,{token:r}=await e.json();if(!r)return o.NextResponse.json({success:!1,error:"Token is required"},{status:400});try{let{authenticated:r,user:s}=await (0,p.b9)(e);r&&s&&(t=s.id)}catch(e){}let s=c.validateToken(r,t);return o.NextResponse.json({success:!0,data:{valid:s,message:s?"Token is valid":"Token is invalid or expired"}})}catch(e){return console.error("CSRF token validation error:",e),o.NextResponse.json({success:!1,error:"Failed to validate CSRF token"},{status:500})}}let T=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/csrf-token/route",pathname:"/api/csrf-token",filename:"route",bundlePath:"app/api/csrf-token/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\csrf-token\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:g,workUnitAsyncStorage:E,serverHooks:h}=T;function S(){return(0,i.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:E})}},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37366:e=>{e.exports=require("dns")},39542:(e,t,r)=>{r.d(t,{b9:()=>h,HU:()=>T,qc:()=>v,Lx:()=>R,DY:()=>S,DT:()=>x});var s=r(85663),a=r(43205),n=r.n(a),i=r(6710),o=r(45697);let l=o.z.object({DATABASE_URL:o.z.string().url("DATABASE_URL must be a valid PostgreSQL URL"),DIRECT_URL:o.z.string().url("DIRECT_URL must be a valid PostgreSQL URL"),JWT_SECRET:o.z.string().min(32,"JWT_SECRET must be at least 32 characters long").refine(e=>{let t=/[A-Z]/.test(e),r=/[a-z]/.test(e),s=/\d/.test(e),a=/[!@#$%^&*(),.?":{}|<>]/.test(e);return t&&r&&s&&a},"JWT_SECRET should contain uppercase, lowercase, numbers, and special characters"),JWT_EXPIRES_IN:o.z.string().default("30d"),NODE_ENV:o.z.enum(["development","production","test"]).default("development"),NEXT_PUBLIC_APP_URL:o.z.string().url().optional(),PORT:o.z.string().regex(/^\d+$/).transform(Number).default("3000"),SMTP_HOST:o.z.string().optional(),SMTP_PORT:o.z.string().regex(/^\d+$/).transform(Number).optional(),SMTP_USER:o.z.string().email().optional(),SMTP_PASSWORD:o.z.string().optional(),SMTP_SECURE:o.z.string().transform(e=>"true"===e).optional(),TRON_NETWORK:o.z.enum(["mainnet","testnet"]).default("testnet"),TRON_API_KEY:o.z.string().optional(),USDT_CONTRACT_ADDRESS:o.z.string().optional(),MAX_FILE_SIZE:o.z.string().regex(/^\d+$/).transform(Number).default("10485760"),UPLOAD_DIR:o.z.string().default("./public/uploads"),BCRYPT_ROUNDS:o.z.string().regex(/^\d+$/).transform(Number).default("12"),SESSION_TIMEOUT:o.z.string().regex(/^\d+$/).transform(Number).default("1800"),RATE_LIMIT_WINDOW_MS:o.z.string().regex(/^\d+$/).transform(Number).default("900000"),RATE_LIMIT_MAX_REQUESTS:o.z.string().regex(/^\d+$/).transform(Number).default("100"),COINGECKO_API_URL:o.z.string().url().default("https://api.coingecko.com/api/v3"),LOG_LEVEL:o.z.enum(["error","warn","info","debug"]).default("info"),ENABLE_REQUEST_LOGGING:o.z.string().transform(e=>"true"===e).default("false"),ENABLE_REGISTRATION:o.z.string().transform(e=>"true"===e).default("true"),ENABLE_KYC:o.z.string().transform(e=>"true"===e).default("true"),ENABLE_WITHDRAWALS:o.z.string().transform(e=>"true"===e).default("true")}).refine(e=>{let t=[e.SMTP_HOST,e.SMTP_PORT,e.SMTP_USER,e.SMTP_PASSWORD],r=t.some(e=>void 0!==e),s=t.every(e=>void 0!==e);return!r||!!s},{message:"If SMTP configuration is provided, all SMTP fields (HOST, PORT, USER, PASSWORD) must be provided"});function u(){try{let e=l.safeParse(process.env);if(!e.success){let t=e.error.errors.map(e=>`${e.path.join(".")}: ${e.message}`);return{success:!1,errors:t}}return{success:!0,data:e.data}}catch(e){return{success:!1,errors:["Failed to validate environment variables"]}}}let d=null;function c(){if(!d){let e=u();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),d=e.data,console.log("✅ Environment variables validated successfully")}return d}let p={jwt:{secret:()=>c().JWT_SECRET,expiresIn:()=>c().JWT_EXPIRES_IN},security:{bcryptRounds:()=>c().BCRYPT_ROUNDS,sessionTimeout:()=>c().SESSION_TIMEOUT,maxFileSize:()=>c().MAX_FILE_SIZE,uploadDir:()=>c().UPLOAD_DIR}};if("phase-production-build"!==process.env.NEXT_PHASE){let e=u();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),console.log("✅ Environment variables validated successfully")}let f=async e=>await s.Ay.hash(e,p.security.bcryptRounds()),m=async(e,t)=>await s.Ay.compare(e,t),T=e=>n().sign(e,p.jwt.secret(),{expiresIn:p.jwt.expiresIn()}),g=e=>{try{return n().verify(e,p.jwt.secret())}catch(e){return null}},E=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},h=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=g(t);if(!r)return{authenticated:!1,user:null};let s=await i.Gy.findByEmail(r.email);return s?{authenticated:!0,user:s}:{authenticated:!1,user:null}},S=async e=>{let t,s;if(await i.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await i.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let a=await f(e.password),n=!1;do s=E(),n=!await i.Gy.findByReferralId(s);while(!n);let o=await i.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:a,referralId:s});if(t){let{placeUserByReferralType:s}=await r.e(2746).then(r.bind(r,2746)),a="general";"left"===e.placementSide?a="left":"right"===e.placementSide&&(a="right"),await s(t,o.id,a)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},R=async e=>{let t=await i.Gy.findByEmail(e.email);if(!t||!await m(e.password,t.password))throw Error("Invalid email or password");return{token:T({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},x=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),v=async e=>{let t=await i.Gy.findById(e);return t?.role==="ADMIN"}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,7911,925],()=>r(29153));module.exports=s})();