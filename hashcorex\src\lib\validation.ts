/**
 * Comprehensive Input Validation and Sanitization System
 * Implements strict validation schemas and sanitization for all user inputs
 */

import { z } from 'zod';

// Custom validation helpers - simplified for build compatibility
const sanitizeString = (str: string): string => {
  // Basic sanitization - remove potentially dangerous characters
  return str.replace(/[<>\"'&]/g, '').trim();
};

const sanitizeHtml = (html: string): string => {
  // Basic HTML sanitization - strip all HTML tags for now
  return html.replace(/<[^>]*>/g, '').trim();
};

// Common validation patterns
const EMAIL_REGEX = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
const PHONE_REGEX = /^\+?[1-9]\d{1,14}$/;
const REFERRAL_CODE_REGEX = /^HC[A-Z0-9]{8}$/;
const TRON_ADDRESS_REGEX = /^T[A-Za-z0-9]{33}$/;

// Password strength validation
const passwordSchema = z.string()
  .min(8, 'Password must be at least 8 characters long')
  .max(128, 'Password must not exceed 128 characters')
  .refine((password) => /[A-Z]/.test(password), 'Password must contain at least one uppercase letter')
  .refine((password) => /[a-z]/.test(password), 'Password must contain at least one lowercase letter')
  .refine((password) => /\d/.test(password), 'Password must contain at least one number')
  .refine((password) => /[!@#$%^&*(),.?":{}|<>]/.test(password), 'Password must contain at least one special character')
  .refine((password) => !/\s/.test(password), 'Password must not contain spaces');

// Email validation with sanitization
const emailSchema = z.string()
  .min(1, 'Email is required')
  .max(254, 'Email must not exceed 254 characters')
  .email('Invalid email format')
  .regex(EMAIL_REGEX, 'Invalid email format')
  .transform(sanitizeString)
  .transform((email) => email.toLowerCase().trim());

// Name validation with sanitization
const nameSchema = z.string()
  .min(1, 'Name is required')
  .max(50, 'Name must not exceed 50 characters')
  .regex(/^[a-zA-Z\s'-]+$/, 'Name can only contain letters, spaces, hyphens, and apostrophes')
  .transform(sanitizeString)
  .transform((name) => name.trim());

// Amount validation for financial operations
const amountSchema = z.number()
  .positive('Amount must be positive')
  .finite('Amount must be a valid number')
  .refine((amount) => Number.isFinite(amount), 'Amount must be a valid number')
  .refine((amount) => amount <= 1000000, 'Amount exceeds maximum limit')
  .transform((amount) => Math.round(amount * 100) / 100); // Round to 2 decimal places

// User registration validation schema
export const registerUserSchema = z.object({
  email: emailSchema,
  firstName: nameSchema,
  lastName: nameSchema,
  password: passwordSchema,
  confirmPassword: z.string(),
  referralCode: z.string()
    .regex(REFERRAL_CODE_REGEX, 'Invalid referral code format')
    .optional()
    .or(z.literal('')),
  placementSide: z.enum(['left', 'right']).optional(),
  agreeToTerms: z.boolean().refine((val) => val === true, 'You must agree to the terms and conditions'),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

// User login validation schema
export const loginUserSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, 'Password is required'),
});

// Mining unit purchase validation schema
export const purchaseMiningUnitSchema = z.object({
  thsAmount: z.number()
    .positive('TH/s amount must be positive')
    .min(0.1, 'Minimum TH/s amount is 0.1')
    .max(1000, 'Maximum TH/s amount is 1000')
    .transform((amount) => Math.round(amount * 100) / 100),
  investmentAmount: amountSchema
    .min(50, 'Minimum investment amount is $50')
    .max(100000, 'Maximum investment amount is $100,000'),
});

// Withdrawal request validation schema
export const withdrawalRequestSchema = z.object({
  amount: amountSchema
    .min(10, 'Minimum withdrawal amount is $10')
    .max(50000, 'Maximum withdrawal amount is $50,000'),
  usdtAddress: z.string()
    .min(1, 'USDT address is required')
    .regex(TRON_ADDRESS_REGEX, 'Invalid TRON address format')
    .transform(sanitizeString),
});

// KYC document upload validation schema
export const kycUploadSchema = z.object({
  documentType: z.enum(['ID_DOCUMENT', 'SELFIE'], {
    errorMap: () => ({ message: 'Invalid document type' })
  }),
  idType: z.enum(['NATIONAL_ID', 'PASSPORT', 'DRIVING_LICENSE']).optional(),
  documentSide: z.enum(['FRONT', 'BACK']).optional(),
});

// Profile update validation schema
export const updateProfileSchema = z.object({
  firstName: nameSchema.optional(),
  lastName: nameSchema.optional(),
  withdrawalAddress: z.string()
    .regex(TRON_ADDRESS_REGEX, 'Invalid TRON address format')
    .transform(sanitizeString)
    .optional()
    .or(z.literal('')),
});

// Admin settings validation schema
export const adminSettingsSchema = z.object({
  key: z.string()
    .min(1, 'Setting key is required')
    .max(100, 'Setting key too long')
    .regex(/^[a-zA-Z0-9_]+$/, 'Setting key can only contain letters, numbers, and underscores')
    .transform(sanitizeString),
  value: z.string()
    .max(1000, 'Setting value too long')
    .transform(sanitizeString),
});

// Support ticket validation schema
export const supportTicketSchema = z.object({
  subject: z.string()
    .min(1, 'Subject is required')
    .max(200, 'Subject must not exceed 200 characters')
    .transform(sanitizeString),
  message: z.string()
    .min(1, 'Message is required')
    .max(5000, 'Message must not exceed 5000 characters')
    .transform(sanitizeHtml),
  category: z.string()
    .max(50, 'Category too long')
    .transform(sanitizeString)
    .optional(),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).optional(),
});

// Binary points update validation schema
export const binaryPointsUpdateSchema = z.object({
  userId: z.string()
    .min(1, 'User ID is required')
    .transform(sanitizeString),
  leftPoints: z.number()
    .min(0, 'Left points must be non-negative')
    .max(10000, 'Left points exceed maximum')
    .optional(),
  rightPoints: z.number()
    .min(0, 'Right points must be non-negative')
    .max(10000, 'Right points exceed maximum')
    .optional(),
  action: z.enum(['SET', 'ADD', 'SUBTRACT']).optional(),
});

// File upload validation
export const validateFileUpload = (file: File, allowedTypes: string[], maxSize: number) => {
  const errors: string[] = [];

  // Check file type
  if (!allowedTypes.includes(file.type)) {
    errors.push(`File type ${file.type} is not allowed. Allowed types: ${allowedTypes.join(', ')}`);
  }

  // Check file size
  if (file.size > maxSize) {
    errors.push(`File size ${Math.round(file.size / 1024 / 1024)}MB exceeds maximum allowed size of ${Math.round(maxSize / 1024 / 1024)}MB`);
  }

  // Check file name
  if (file.name.length > 255) {
    errors.push('File name is too long');
  }

  // Check for potentially dangerous file extensions
  const dangerousExtensions = ['.exe', '.bat', '.cmd', '.scr', '.pif', '.com', '.js', '.jar', '.php', '.asp'];
  const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
  if (dangerousExtensions.includes(fileExtension)) {
    errors.push('File type is not allowed for security reasons');
  }

  return {
    valid: errors.length === 0,
    errors,
  };
};

// Validation result type
export type ValidationResult<T> = {
  success: true;
  data: T;
} | {
  success: false;
  error: string;
  details?: z.ZodError;
};

// Generic validation function
export function validateInput<T>(schema: z.ZodSchema<T>, input: unknown): ValidationResult<T> {
  try {
    const result = schema.parse(input);
    return { success: true, data: result };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const firstError = error.errors[0];
      return {
        success: false,
        error: firstError?.message || 'Validation failed',
        details: error,
      };
    }
    return {
      success: false,
      error: 'Validation failed',
    };
  }
}

// Sanitization utilities
export const sanitize = {
  string: sanitizeString,
  html: sanitizeHtml,
  email: (email: string) => sanitizeString(email).toLowerCase().trim(),
  amount: (amount: number) => Math.round(amount * 100) / 100,
  filename: (filename: string) => {
    return sanitizeString(filename)
      .replace(/[^a-zA-Z0-9._-]/g, '_')
      .substring(0, 255);
  },
};

// Rate limiting for validation errors (prevent validation bombing)
const validationErrorCounts = new Map<string, { count: number; resetTime: number }>();

export function checkValidationRateLimit(identifier: string): boolean {
  const now = Date.now();
  const entry = validationErrorCounts.get(identifier);
  
  if (!entry || now > entry.resetTime) {
    validationErrorCounts.set(identifier, { count: 1, resetTime: now + 60000 }); // 1 minute window
    return true;
  }
  
  if (entry.count >= 20) { // Max 20 validation errors per minute
    return false;
  }
  
  entry.count++;
  return true;
}

// Clean up validation error tracking
setInterval(() => {
  const now = Date.now();
  for (const [key, entry] of validationErrorCounts.entries()) {
    if (now > entry.resetTime) {
      validationErrorCounts.delete(key);
    }
  }
}, 60000); // Clean up every minute
