"use strict";exports.id=9046,exports.ids=[9046],exports.modules={79046:(o,r,t)=>{t.d(r,{E:()=>p,processDeposits:()=>i});var e=t(6710),s=t(31183),n=t(59480);async function i(){try{console.log("Starting deposit processing...");let o=parseInt(await e.rs.get("minConfirmations")||"10"),r=await e.rs.get("depositAddress");if(!r)throw Error("No deposit address configured");let t=await a(r),s=await c(r,o),n=await d(),i={pendingVerificationProcessed:t.processed,waitingConfirmationsProcessed:s.processed,confirmedDepositsProcessed:n.processed,totalProcessed:t.processed+s.processed+n.processed,errors:[...t.errors,...s.errors,...n.errors]};return console.log(`Deposit processing completed: ${i.totalProcessed} deposits processed`),i}catch(o){throw console.error("Deposit processing error:",o),o}}async function a(o){let r={processed:0,errors:[]};try{let t=await e.J6.getPendingVerificationDeposits();for(let s of(console.log(`Found ${t.length} deposits pending verification`),t))try{let t=await (0,n.gp)(s.transactionId,o,1);t.isValid?(await e.J6.updateStatus(s.transactionId,"WAITING_FOR_CONFIRMATIONS"),await e.J6.updateConfirmations(s.transactionId,t.confirmations),console.log(`Verified deposit ${s.transactionId} with ${t.confirmations} confirmations`),r.processed++):console.log(`Deposit ${s.transactionId} not yet found on blockchain`)}catch(t){let o=`Error verifying deposit ${s.transactionId}: ${t instanceof Error?t.message:"Unknown error"}`;console.error(o),r.errors.push(o)}}catch(t){let o=`Error processing pending verification deposits: ${t instanceof Error?t.message:"Unknown error"}`;console.error(o),r.errors.push(o)}return r}async function c(o,r){let t={processed:0,errors:[]};try{let s=await e.J6.getWaitingForConfirmationsDeposits();for(let i of(console.log(`Found ${s.length} deposits waiting for confirmations`),s))try{let s=await (0,n.gp)(i.transactionId,o,1);s.isValid?(await e.J6.updateConfirmations(i.transactionId,s.confirmations),s.confirmations>=r?(await e.J6.updateStatus(i.transactionId,"CONFIRMED"),console.log(`Deposit ${i.transactionId} confirmed with ${s.confirmations} confirmations`),t.processed++):console.log(`Deposit ${i.transactionId} has ${s.confirmations}/${r} confirmations`)):console.log(`Deposit ${i.transactionId} is no longer valid during confirmation check`)}catch(r){let o=`Error checking confirmations for deposit ${i.transactionId}: ${r instanceof Error?r.message:"Unknown error"}`;console.error(o),t.errors.push(o)}}catch(r){let o=`Error processing waiting for confirmations deposits: ${r instanceof Error?r.message:"Unknown error"}`;console.error(o),t.errors.push(o)}return t}async function d(){let o={processed:0,errors:[]};try{let r=await e.J6.getConfirmedDeposits();for(let t of(console.log(`Found ${r.length} confirmed deposits to process`),r))try{await s.prisma.$transaction(async o=>{await e.k_.addDeposit(t.userId,t.amount),await e.DR.create({userId:t.userId,type:"DEPOSIT",amount:t.amount,status:"COMPLETED",reference:`deposit:${t.transactionId}`,description:`USDT Deposit - ${t.transactionId}`}),await e.J6.updateStatus(t.transactionId,"COMPLETED")}),console.log(`Processed confirmed deposit ${t.transactionId} for user ${t.userId}: $${t.amount}`),o.processed++,await e.AJ.create({action:"DEPOSIT_PROCESSED",userId:t.userId,details:{transactionId:t.transactionId,amount:t.amount,tronAddress:t.tronAddress,confirmations:t.confirmations,processedAt:new Date().toISOString()}})}catch(s){let r=`Error processing confirmed deposit ${t.transactionId}: ${s instanceof Error?s.message:"Unknown error"}`;console.error(r),o.errors.push(r),await e.AJ.create({action:"DEPOSIT_PROCESSING_ERROR",userId:t.userId,details:{transactionId:t.transactionId,error:s instanceof Error?s.message:"Unknown error",timestamp:new Date().toISOString()}})}}catch(t){let r=`Error processing confirmed deposits: ${t instanceof Error?t.message:"Unknown error"}`;console.error(r),o.errors.push(r)}return o}async function p(){try{let o=await e.J6.getPendingVerificationDeposits(),r=await e.J6.getWaitingForConfirmationsDeposits(),t=await e.J6.getConfirmedDeposits();return{pendingVerification:o.length,waitingConfirmations:r.length,confirmed:t.length,total:o.length+r.length+t.length}}catch(o){throw console.error("Error getting deposit processing status:",o),o}}}};