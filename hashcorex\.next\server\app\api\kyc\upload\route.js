"use strict";(()=>{var e={};e.id=8483,e.ids=[8483],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3829:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>D,routeModule:()=>T,serverHooks:()=>U,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>x});var i={};r.r(i),r.d(i,{POST:()=>g});var n=r(96559),o=r(48088),s=r(37719),a=r(32190),d=r(39542),u=r(6710),l=r(31183),p=r(79748),c=r(33873),m=r(23870),f=r(15501),w=r(82180);let y=async e=>{let t,r=(0,f.JR)(e);if(!r.allowed){let e=(0,w.U5)(r.retryAfter,r.remaining,r.resetTime);return(0,w.N1)(e)}let{authenticated:i,user:n}=await (0,d.b9)(e);if(!i||!n)return a.NextResponse.json({success:!1,error:"Authentication required"},{status:401});let o=await e.formData(),s=o.get("file"),y=o.get("documentType"),g=o.get("idType"),T=o.get("documentSide");if(!s){let e=(0,w.UL)("No file provided");return(0,w.N1)(e)}if(!y||!["ID_DOCUMENT","SELFIE"].includes(y)){let e=(0,w.UL)("Invalid document type");return(0,w.N1)(e)}if("ID_DOCUMENT"===y){if(!g||!["NATIONAL_ID","PASSPORT","DRIVING_LICENSE"].includes(g)){let e=(0,w.UL)("Invalid ID type");return(0,w.N1)(e)}if(!T||!["FRONT","BACK"].includes(T)){let e=(0,w.UL)("Invalid document side");return(0,w.N1)(e)}}let h={documentType:y,idType:"ID_DOCUMENT"===y?g:void 0,documentSide:"ID_DOCUMENT"===y?T:void 0},x=["image/jpeg","image/jpg","image/png","image/webp"];if(!x.includes(s.type)){let e=(0,w.UL)(`File type ${s.type} is not allowed. Allowed types: ${x.join(", ")}`);return(0,w.N1)(e)}if(s.size>0xa00000){let e=(0,w.UL)(`File size ${Math.round(s.size/1024/1024)}MB exceeds maximum allowed size of ${Math.round(10)}MB`);return(0,w.N1)(e)}if(s.size<1024){let e=(0,w.UL)("File size too small. Please upload a valid image.");return(0,w.N1)(e)}let U=(0,c.join)(process.cwd(),"public","uploads","kyc");try{await (0,p.mkdir)(U,{recursive:!0})}catch(e){}let D=s.name.split(".").pop()?.toLowerCase();if(!D||!["jpg","jpeg","png","webp"].includes(D)){let e=(0,w.UL)("Invalid file extension. Only JPG, PNG, and WebP files are allowed.");return(0,w.N1)(e)}let k=Date.now(),I=(0,m.A)().substring(0,8),N=[n.id,y,k,I];h.idType&&N.push(h.idType),h.documentSide&&N.push(h.documentSide);let S=`${N.join("_")}.${D}`,q=(0,c.join)(U,S),A=`/uploads/kyc/${S}`,M=await s.arrayBuffer(),v=Buffer.from(M);if(!function(e){return!(e.length<4)&&(255===e[0]&&216===e[1]&&255===e[2]||137===e[0]&&80===e[1]&&78===e[2]&&71===e[3]||e.length>=12&&82===e[0]&&73===e[1]&&70===e[2]&&70===e[3]&&87===e[8]&&69===e[9]&&66===e[10]&&80===e[11]||!1)}(v)){let e=(0,w.UL)("Invalid image file. File may be corrupted or not a valid image.");return(0,w.N1)(e)}await (0,p.writeFile)(q,v);let E={userId:n.id,documentType:h.documentType};"ID_DOCUMENT"===h.documentType&&(E.idType=h.idType,E.documentSide=h.documentSide);let R=await l.prisma.kYCDocument.findFirst({where:E});if(R)t=await l.prisma.kYCDocument.update({where:{id:R.id},data:{filePath:A,status:"PENDING",reviewedAt:null,reviewedBy:null,rejectionReason:null,updatedAt:new Date}});else{let e={userId:n.id,documentType:h.documentType,filePath:A,status:"PENDING"};"ID_DOCUMENT"===h.documentType&&(e.idType=h.idType,e.documentSide=h.documentSide),t=await l.prisma.kYCDocument.create({data:e})}let O=await l.prisma.kYCDocument.findMany({where:{userId:n.id}}),b=O.some(e=>"SELFIE"===e.documentType),C=!1,L=O.find(e=>"ID_DOCUMENT"===e.documentType)?.idType;if(L){let e=O.filter(e=>"ID_DOCUMENT"===e.documentType&&e.idType===L);if("PASSPORT"===L)C=e.some(e=>"FRONT"===e.documentSide);else{let t=e.some(e=>"FRONT"===e.documentSide),r=e.some(e=>"BACK"===e.documentSide);C=t&&r}}C&&b&&await l.prisma.user.update({where:{id:n.id},data:{kycStatus:"PENDING"}}),await u.AJ.create({action:"KYC_DOCUMENT_UPLOADED",userId:n.id,details:{documentType:h.documentType,idType:h.idType||null,documentSide:h.documentSide||null,fileName:S,fileSize:s.size,documentId:t.id,uploadTime:new Date().toISOString()},ipAddress:e.headers.get("x-forwarded-for")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"});let P=a.NextResponse.json({success:!0,message:"Document uploaded successfully",data:{documentId:t.id,documentType:t.documentType,status:t.status}});return P.headers.set("X-Content-Type-Options","nosniff"),P.headers.set("X-Frame-Options","DENY"),P.headers.set("X-XSS-Protection","1; mode=block"),P},g=(0,w.nC)(y,{endpoint:"/api/kyc/upload",requireAuth:!0}),T=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/kyc/upload/route",pathname:"/api/kyc/upload",filename:"route",bundlePath:"app/api/kyc/upload/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\kyc\\upload\\route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:h,workUnitAsyncStorage:x,serverHooks:U}=T;function D(){return(0,s.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:x})}},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15501:(e,t,r)=>{r.d(t,{FH:()=>p,HJ:()=>d,JR:()=>u,LH:()=>m,Zb:()=>a,c4:()=>c});let i=new Map,n={LOGIN:{windowMs:9e5,maxRequests:5,skipSuccessfulRequests:!0},REGISTER:{windowMs:36e5,maxRequests:3},FILE_UPLOAD:{windowMs:6e4,maxRequests:5}},o=[0,1e3,5e3,15e3,6e4,3e5,9e5];function s(e,t,r="general"){let n=function(e,t,r){if(r)return`${t}:${r(e)}`;let i=e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||"unknown";return`${t}:${i}`}(e,r,t.keyGenerator),a=Date.now(),d=i.get(n);if(d?.blockedUntil&&a<d.blockedUntil)return{allowed:!1,remaining:0,resetTime:d.resetTime,retryAfter:Math.ceil((d.blockedUntil-a)/1e3)};if((!d||a>d.resetTime)&&(d={count:0,resetTime:a+t.windowMs},i.set(n,d)),d.count>=t.maxRequests){let e=Math.min(d.count-t.maxRequests,o.length-1),r=o[e];return r>0&&(d.blockedUntil=a+r,i.set(n,d)),{allowed:!1,remaining:0,resetTime:d.resetTime,retryAfter:r>0?Math.ceil(r/1e3):Math.ceil((d.resetTime-a)/1e3)}}return d.count++,i.set(n,d),{allowed:!0,remaining:t.maxRequests-d.count,resetTime:d.resetTime}}let a=e=>s(e,n.LOGIN,"login"),d=e=>s(e,n.REGISTER,"register"),u=e=>s(e,n.FILE_UPLOAD,"upload"),l=new Map;function p(e){let t=l.get(e),r=Date.now();return t&&t.lockedUntil&&r<t.lockedUntil?{locked:!0,remainingTime:Math.ceil((t.lockedUntil-r)/1e3)}:{locked:!1}}function c(e){let t=Date.now(),r=l.get(e)||{attempts:0,lastAttempt:0};if(t-r.lastAttempt>36e5&&(r.attempts=0),r.attempts++,r.lastAttempt=t,r.attempts>=5){let e=[15,30,60,120,240],i=Math.min(Math.floor(r.attempts/5)-1,e.length-1);r.lockedUntil=t+60*e[i]*1e3}l.set(e,r)}function m(e){l.delete(e)}setInterval(()=>{!function(){let e=Date.now();for(let[t,r]of i.entries())e>r.resetTime&&(!r.blockedUntil||e>r.blockedUntil)&&i.delete(t)}(),function(){let e=Date.now();for(let[t,r]of l.entries())r.lockedUntil&&e>r.lockedUntil&&(r.attempts=0,r.lockedUntil=void 0,l.set(t,r))}()},3e5)},21820:e=>{e.exports=require("os")},23870:(e,t,r)=>{r.d(t,{A:()=>d});var i=r(55511);let n={randomUUID:i.randomUUID},o=new Uint8Array(256),s=o.length,a=[];for(let e=0;e<256;++e)a.push((e+256).toString(16).slice(1));let d=function(e,t,r){if(n.randomUUID&&!t&&!e)return n.randomUUID();let d=(e=e||{}).random??e.rng?.()??(s>o.length-16&&((0,i.randomFillSync)(o),s=0),o.slice(s,s+=16));if(d.length<16)throw Error("Random bytes length must be >= 16");if(d[6]=15&d[6]|64,d[8]=63&d[8]|128,t){if((r=r||0)<0||r+16>t.length)throw RangeError(`UUID byte range ${r}:${r+15} is out of buffer bounds`);for(let e=0;e<16;++e)t[r+e]=d[e];return t}return function(e,t=0){return(a[e[t+0]]+a[e[t+1]]+a[e[t+2]]+a[e[t+3]]+"-"+a[e[t+4]]+a[e[t+5]]+"-"+a[e[t+6]]+a[e[t+7]]+"-"+a[e[t+8]]+a[e[t+9]]+"-"+a[e[t+10]]+a[e[t+11]]+a[e[t+12]]+a[e[t+13]]+a[e[t+14]]+a[e[t+15]]).toLowerCase()}(d)}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37366:e=>{e.exports=require("dns")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},79748:e=>{e.exports=require("fs/promises")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[4447,580,7911,925,5033],()=>r(3829));module.exports=i})();