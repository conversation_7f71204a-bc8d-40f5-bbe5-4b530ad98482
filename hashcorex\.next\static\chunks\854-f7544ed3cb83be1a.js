"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[854],{394:(t,n,e)=>{function r(){}function i(t){return null==t?r:function(){return this.querySelector(t)}}e.d(n,{A:()=>i})},469:(t,n,e)=>{e.d(n,{G:()=>$});var r,i,o,a=e(4498),u=e(1235),s=0,l=0,c=0,h=0,f=0,p=0,d="object"==typeof performance&&performance.now?performance:Date,y="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(t){setTimeout(t,17)};function v(){return f||(y(m),f=d.now()+p)}function m(){f=0}function g(){this._call=this._time=this._next=null}function _(t,n,e){var r=new g;return r.restart(t,n,e),r}function x(){f=(h=d.now())+p,s=l=0;try{v(),++s;for(var t,n=i;n;)(t=f-n._time)>=0&&n._call.call(void 0,t),n=n._next;--s}finally{s=0,function(){for(var t,n,e=i,r=1/0;e;)e._call?(r>e._time&&(r=e._time),t=e,e=e._next):(n=e._next,e._next=null,e=t?t._next=n:i=n);o=t,b(r)}(),f=0}}function w(){var t=d.now(),n=t-h;n>1e3&&(p-=n,h=t)}function b(t){!s&&(l&&(l=clearTimeout(l)),t-f>24?(t<1/0&&(l=setTimeout(x,t-d.now()-p)),c&&(c=clearInterval(c))):(c||(h=d.now(),c=setInterval(w,1e3)),s=1,y(x)))}function A(t,n,e){var r=new g;return n=null==n?0:+n,r.restart(e=>{r.stop(),t(e+n)},n,e),r}g.prototype=_.prototype={constructor:g,restart:function(t,n,e){if("function"!=typeof t)throw TypeError("callback is not a function");e=(null==e?v():+e)+(null==n?0:+n),this._next||o===this||(o?o._next=this:i=this,o=this),this._call=t,this._time=e,b()},stop:function(){this._call&&(this._call=null,this._time=1/0,b())}};var k=(0,u.A)("start","end","cancel","interrupt"),M=[];function z(t,n,e,r,i,o){var a=t.__transition;if(a){if(e in a)return}else t.__transition={};!function(t,n,e){var r,i=t.__transition;function o(s){var l,c,h,f;if(1!==e.state)return u();for(l in i)if((f=i[l]).name===e.name){if(3===f.state)return A(o);4===f.state?(f.state=6,f.timer.stop(),f.on.call("interrupt",t,t.__data__,f.index,f.group),delete i[l]):+l<n&&(f.state=6,f.timer.stop(),f.on.call("cancel",t,t.__data__,f.index,f.group),delete i[l])}if(A(function(){3===e.state&&(e.state=4,e.timer.restart(a,e.delay,e.time),a(s))}),e.state=2,e.on.call("start",t,t.__data__,e.index,e.group),2===e.state){for(l=0,e.state=3,r=Array(h=e.tween.length),c=-1;l<h;++l)(f=e.tween[l].value.call(t,t.__data__,e.index,e.group))&&(r[++c]=f);r.length=c+1}}function a(n){for(var i=n<e.duration?e.ease.call(null,n/e.duration):(e.timer.restart(u),e.state=5,1),o=-1,a=r.length;++o<a;)r[o].call(t,i);5===e.state&&(e.on.call("end",t,t.__data__,e.index,e.group),u())}function u(){for(var r in e.state=6,e.timer.stop(),delete i[n],i)return;delete t.__transition}i[n]=e,e.timer=_(function(t){e.state=1,e.timer.restart(o,e.delay,e.time),e.delay<=t&&o(t-e.delay)},0,e.time)}(t,e,{name:n,index:r,group:i,on:k,tween:M,time:o.time,delay:o.delay,duration:o.duration,ease:o.ease,timer:null,state:0})}function E(t,n){var e=S(t,n);if(e.state>0)throw Error("too late; already scheduled");return e}function N(t,n){var e=S(t,n);if(e.state>3)throw Error("too late; already running");return e}function S(t,n){var e=t.__transition;if(!e||!(e=e[n]))throw Error("transition not found");return e}function $(t,n){var e,r,i,o=t.__transition,a=!0;if(o){for(i in n=null==n?null:n+"",o){if((e=o[i]).name!==n){a=!1;continue}r=e.state>2&&e.state<5,e.state=6,e.timer.stop(),e.on.call(r?"interrupt":"cancel",t,t.__data__,e.index,e.group),delete o[i]}a&&delete t.__transition}}function T(t,n){return t*=1,n*=1,function(e){return t*(1-e)+n*e}}var q=180/Math.PI,C={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function P(t,n,e,r,i,o){var a,u,s;return(a=Math.sqrt(t*t+n*n))&&(t/=a,n/=a),(s=t*e+n*r)&&(e-=t*s,r-=n*s),(u=Math.sqrt(e*e+r*r))&&(e/=u,r/=u,s/=u),t*r<n*e&&(t=-t,n=-n,s=-s,a=-a),{translateX:i,translateY:o,rotate:Math.atan2(n,t)*q,skewX:Math.atan(s)*q,scaleX:a,scaleY:u}}function X(t,n,e,r){function i(t){return t.length?t.pop()+" ":""}return function(o,a){var u,s,l,c,h=[],f=[];return o=t(o),a=t(a),!function(t,r,i,o,a,u){if(t!==i||r!==o){var s=a.push("translate(",null,n,null,e);u.push({i:s-4,x:T(t,i)},{i:s-2,x:T(r,o)})}else(i||o)&&a.push("translate("+i+n+o+e)}(o.translateX,o.translateY,a.translateX,a.translateY,h,f),u=o.rotate,s=a.rotate,u!==s?(u-s>180?s+=360:s-u>180&&(u+=360),f.push({i:h.push(i(h)+"rotate(",null,r)-2,x:T(u,s)})):s&&h.push(i(h)+"rotate("+s+r),l=o.skewX,c=a.skewX,l!==c?f.push({i:h.push(i(h)+"skewX(",null,r)-2,x:T(l,c)}):c&&h.push(i(h)+"skewX("+c+r),!function(t,n,e,r,o,a){if(t!==e||n!==r){var u=o.push(i(o)+"scale(",null,",",null,")");a.push({i:u-4,x:T(t,e)},{i:u-2,x:T(n,r)})}else(1!==e||1!==r)&&o.push(i(o)+"scale("+e+","+r+")")}(o.scaleX,o.scaleY,a.scaleX,a.scaleY,h,f),o=a=null,function(t){for(var n,e=-1,r=f.length;++e<r;)h[(n=f[e]).i]=n.x(t);return h.join("")}}}var j=X(function(t){let n=new("function"==typeof DOMMatrix?DOMMatrix:WebKitCSSMatrix)(t+"");return n.isIdentity?C:P(n.a,n.b,n.c,n.d,n.e,n.f)},"px, ","px)","deg)"),B=X(function(t){return null==t?C:(r||(r=document.createElementNS("http://www.w3.org/2000/svg","g")),r.setAttribute("transform",t),t=r.transform.baseVal.consolidate())?P((t=t.matrix).a,t.b,t.c,t.d,t.e,t.f):C},", ",")",")"),Y=e(6102);function O(t,n,e){var r=t._id;return t.each(function(){var t=N(this,r);(t.value||(t.value={}))[n]=e.apply(this,arguments)}),function(t){return S(t,r).value[n]}}function R(t,n,e){t.prototype=n.prototype=e,e.constructor=t}function L(t,n){var e=Object.create(t.prototype);for(var r in n)e[r]=n[r];return e}function V(){}var D="\\s*([+-]?\\d+)\\s*",H="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",I="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",G=/^#([0-9a-f]{3,8})$/,U=RegExp(`^rgb\\(${D},${D},${D}\\)$`),K=RegExp(`^rgb\\(${I},${I},${I}\\)$`),F=RegExp(`^rgba\\(${D},${D},${D},${H}\\)$`),Q=RegExp(`^rgba\\(${I},${I},${I},${H}\\)$`),W=RegExp(`^hsl\\(${H},${I},${I}\\)$`),J=RegExp(`^hsla\\(${H},${I},${I},${H}\\)$`),Z={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function tt(){return this.rgb().formatHex()}function tn(){return this.rgb().formatRgb()}function te(t){var n,e;return t=(t+"").trim().toLowerCase(),(n=G.exec(t))?(e=n[1].length,n=parseInt(n[1],16),6===e?tr(n):3===e?new ta(n>>8&15|n>>4&240,n>>4&15|240&n,(15&n)<<4|15&n,1):8===e?ti(n>>24&255,n>>16&255,n>>8&255,(255&n)/255):4===e?ti(n>>12&15|n>>8&240,n>>8&15|n>>4&240,n>>4&15|240&n,((15&n)<<4|15&n)/255):null):(n=U.exec(t))?new ta(n[1],n[2],n[3],1):(n=K.exec(t))?new ta(255*n[1]/100,255*n[2]/100,255*n[3]/100,1):(n=F.exec(t))?ti(n[1],n[2],n[3],n[4]):(n=Q.exec(t))?ti(255*n[1]/100,255*n[2]/100,255*n[3]/100,n[4]):(n=W.exec(t))?tf(n[1],n[2]/100,n[3]/100,1):(n=J.exec(t))?tf(n[1],n[2]/100,n[3]/100,n[4]):Z.hasOwnProperty(t)?tr(Z[t]):"transparent"===t?new ta(NaN,NaN,NaN,0):null}function tr(t){return new ta(t>>16&255,t>>8&255,255&t,1)}function ti(t,n,e,r){return r<=0&&(t=n=e=NaN),new ta(t,n,e,r)}function to(t,n,e,r){var i;return 1==arguments.length?((i=t)instanceof V||(i=te(i)),i)?new ta((i=i.rgb()).r,i.g,i.b,i.opacity):new ta:new ta(t,n,e,null==r?1:r)}function ta(t,n,e,r){this.r=+t,this.g=+n,this.b=+e,this.opacity=+r}function tu(){return`#${th(this.r)}${th(this.g)}${th(this.b)}`}function ts(){let t=tl(this.opacity);return`${1===t?"rgb(":"rgba("}${tc(this.r)}, ${tc(this.g)}, ${tc(this.b)}${1===t?")":`, ${t})`}`}function tl(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function tc(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function th(t){return((t=tc(t))<16?"0":"")+t.toString(16)}function tf(t,n,e,r){return r<=0?t=n=e=NaN:e<=0||e>=1?t=n=NaN:n<=0&&(t=NaN),new td(t,n,e,r)}function tp(t){if(t instanceof td)return new td(t.h,t.s,t.l,t.opacity);if(t instanceof V||(t=te(t)),!t)return new td;if(t instanceof td)return t;var n=(t=t.rgb()).r/255,e=t.g/255,r=t.b/255,i=Math.min(n,e,r),o=Math.max(n,e,r),a=NaN,u=o-i,s=(o+i)/2;return u?(a=n===o?(e-r)/u+(e<r)*6:e===o?(r-n)/u+2:(n-e)/u+4,u/=s<.5?o+i:2-o-i,a*=60):u=s>0&&s<1?0:a,new td(a,u,s,t.opacity)}function td(t,n,e,r){this.h=+t,this.s=+n,this.l=+e,this.opacity=+r}function ty(t){return(t=(t||0)%360)<0?t+360:t}function tv(t){return Math.max(0,Math.min(1,t||0))}function tm(t,n,e){return(t<60?n+(e-n)*t/60:t<180?e:t<240?n+(e-n)*(240-t)/60:n)*255}function tg(t,n,e,r,i){var o=t*t,a=o*t;return((1-3*t+3*o-a)*n+(4-6*o+3*a)*e+(1+3*t+3*o-3*a)*r+a*i)/6}R(V,te,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:tt,formatHex:tt,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return tp(this).formatHsl()},formatRgb:tn,toString:tn}),R(ta,to,L(V,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new ta(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new ta(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new ta(tc(this.r),tc(this.g),tc(this.b),tl(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:tu,formatHex:tu,formatHex8:function(){return`#${th(this.r)}${th(this.g)}${th(this.b)}${th((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:ts,toString:ts})),R(td,function(t,n,e,r){return 1==arguments.length?tp(t):new td(t,n,e,null==r?1:r)},L(V,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new td(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new td(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+(this.h<0)*360,n=isNaN(t)||isNaN(this.s)?0:this.s,e=this.l,r=e+(e<.5?e:1-e)*n,i=2*e-r;return new ta(tm(t>=240?t-240:t+120,i,r),tm(t,i,r),tm(t<120?t+240:t-120,i,r),this.opacity)},clamp(){return new td(ty(this.h),tv(this.s),tv(this.l),tl(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let t=tl(this.opacity);return`${1===t?"hsl(":"hsla("}${ty(this.h)}, ${100*tv(this.s)}%, ${100*tv(this.l)}%${1===t?")":`, ${t})`}`}}));let t_=t=>()=>t;function tx(t,n){var e,r,i=n-t;return i?(e=t,r=i,function(t){return e+t*r}):t_(isNaN(t)?n:t)}let tw=function t(n){var e,r=1==(e=+n)?tx:function(t,n){var r,i,o;return n-t?(r=t,i=n,r=Math.pow(r,o=e),i=Math.pow(i,o)-r,o=1/o,function(t){return Math.pow(r+t*i,o)}):t_(isNaN(t)?n:t)};function i(t,n){var e=r((t=to(t)).r,(n=to(n)).r),i=r(t.g,n.g),o=r(t.b,n.b),a=tx(t.opacity,n.opacity);return function(n){return t.r=e(n),t.g=i(n),t.b=o(n),t.opacity=a(n),t+""}}return i.gamma=t,i}(1);function tb(t){return function(n){var e,r,i=n.length,o=Array(i),a=Array(i),u=Array(i);for(e=0;e<i;++e)r=to(n[e]),o[e]=r.r||0,a[e]=r.g||0,u[e]=r.b||0;return o=t(o),a=t(a),u=t(u),r.opacity=1,function(t){return r.r=o(t),r.g=a(t),r.b=u(t),r+""}}}tb(function(t){var n=t.length-1;return function(e){var r=e<=0?e=0:e>=1?(e=1,n-1):Math.floor(e*n),i=t[r],o=t[r+1],a=r>0?t[r-1]:2*i-o,u=r<n-1?t[r+2]:2*o-i;return tg((e-r/n)*n,a,i,o,u)}}),tb(function(t){var n=t.length;return function(e){var r=Math.floor(((e%=1)<0?++e:e)*n),i=t[(r+n-1)%n],o=t[r%n],a=t[(r+1)%n],u=t[(r+2)%n];return tg((e-r/n)*n,i,o,a,u)}});var tA=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,tk=RegExp(tA.source,"g");function tM(t,n){var e;return("number"==typeof n?T:n instanceof te?tw:(e=te(n))?(n=e,tw):function(t,n){var e,r,i,o,a,u=tA.lastIndex=tk.lastIndex=0,s=-1,l=[],c=[];for(t+="",n+="";(i=tA.exec(t))&&(o=tk.exec(n));)(a=o.index)>u&&(a=n.slice(u,a),l[s]?l[s]+=a:l[++s]=a),(i=i[0])===(o=o[0])?l[s]?l[s]+=o:l[++s]=o:(l[++s]=null,c.push({i:s,x:T(i,o)})),u=tk.lastIndex;return u<n.length&&(a=n.slice(u),l[s]?l[s]+=a:l[++s]=a),l.length<2?c[0]?(e=c[0].x,function(t){return e(t)+""}):(r=n,function(){return r}):(n=c.length,function(t){for(var e,r=0;r<n;++r)l[(e=c[r]).i]=e.x(t);return l.join("")})})(t,n)}var tz=e(3875),tE=e(394),tN=e(9293),tS=a.Ay.prototype.constructor,t$=e(1774);function tT(t){return function(){this.style.removeProperty(t)}}var tq=0;function tC(t,n,e,r){this._groups=t,this._parents=n,this._name=e,this._id=r}var tP=a.Ay.prototype;tC.prototype=(function(t){return(0,a.Ay)().transition(t)}).prototype={constructor:tC,select:function(t){var n=this._name,e=this._id;"function"!=typeof t&&(t=(0,tE.A)(t));for(var r=this._groups,i=r.length,o=Array(i),a=0;a<i;++a)for(var u,s,l=r[a],c=l.length,h=o[a]=Array(c),f=0;f<c;++f)(u=l[f])&&(s=t.call(u,u.__data__,f,l))&&("__data__"in u&&(s.__data__=u.__data__),h[f]=s,z(h[f],n,e,f,h,S(u,e)));return new tC(o,this._parents,n,e)},selectAll:function(t){var n=this._name,e=this._id;"function"!=typeof t&&(t=(0,tN.A)(t));for(var r=this._groups,i=r.length,o=[],a=[],u=0;u<i;++u)for(var s,l=r[u],c=l.length,h=0;h<c;++h)if(s=l[h]){for(var f,p=t.call(s,s.__data__,h,l),d=S(s,e),y=0,v=p.length;y<v;++y)(f=p[y])&&z(f,n,e,y,p,d);o.push(p),a.push(s)}return new tC(o,a,n,e)},selectChild:tP.selectChild,selectChildren:tP.selectChildren,filter:function(t){"function"!=typeof t&&(t=(0,tz.A)(t));for(var n=this._groups,e=n.length,r=Array(e),i=0;i<e;++i)for(var o,a=n[i],u=a.length,s=r[i]=[],l=0;l<u;++l)(o=a[l])&&t.call(o,o.__data__,l,a)&&s.push(o);return new tC(r,this._parents,this._name,this._id)},merge:function(t){if(t._id!==this._id)throw Error();for(var n=this._groups,e=t._groups,r=n.length,i=e.length,o=Math.min(r,i),a=Array(r),u=0;u<o;++u)for(var s,l=n[u],c=e[u],h=l.length,f=a[u]=Array(h),p=0;p<h;++p)(s=l[p]||c[p])&&(f[p]=s);for(;u<r;++u)a[u]=n[u];return new tC(a,this._parents,this._name,this._id)},selection:function(){return new tS(this._groups,this._parents)},transition:function(){for(var t=this._name,n=this._id,e=++tq,r=this._groups,i=r.length,o=0;o<i;++o)for(var a,u=r[o],s=u.length,l=0;l<s;++l)if(a=u[l]){var c=S(a,n);z(a,t,e,l,u,{time:c.time+c.delay+c.duration,delay:0,duration:c.duration,ease:c.ease})}return new tC(r,this._parents,t,e)},call:tP.call,nodes:tP.nodes,node:tP.node,size:tP.size,empty:tP.empty,each:tP.each,on:function(t,n){var e,r,i,o,a,u,s=this._id;return arguments.length<2?S(this.node(),s).on.on(t):this.each((e=s,r=t,i=n,u=(r+"").trim().split(/^|\s+/).every(function(t){var n=t.indexOf(".");return n>=0&&(t=t.slice(0,n)),!t||"start"===t})?E:N,function(){var t=u(this,e),n=t.on;n!==o&&(a=(o=n).copy()).on(r,i),t.on=a}))},attr:function(t,n){var e=(0,Y.A)(t),r="transform"===e?B:tM;return this.attrTween(t,"function"==typeof n?(e.local?function(t,n,e){var r,i,o;return function(){var a,u,s=e(this);return null==s?void this.removeAttributeNS(t.space,t.local):(a=this.getAttributeNS(t.space,t.local))===(u=s+"")?null:a===r&&u===i?o:(i=u,o=n(r=a,s))}}:function(t,n,e){var r,i,o;return function(){var a,u,s=e(this);return null==s?void this.removeAttribute(t):(a=this.getAttribute(t))===(u=s+"")?null:a===r&&u===i?o:(i=u,o=n(r=a,s))}})(e,r,O(this,"attr."+t,n)):null==n?(e.local?function(t){return function(){this.removeAttributeNS(t.space,t.local)}}:function(t){return function(){this.removeAttribute(t)}})(e):(e.local?function(t,n,e){var r,i,o=e+"";return function(){var a=this.getAttributeNS(t.space,t.local);return a===o?null:a===r?i:i=n(r=a,e)}}:function(t,n,e){var r,i,o=e+"";return function(){var a=this.getAttribute(t);return a===o?null:a===r?i:i=n(r=a,e)}})(e,r,n))},attrTween:function(t,n){var e="attr."+t;if(arguments.length<2)return(e=this.tween(e))&&e._value;if(null==n)return this.tween(e,null);if("function"!=typeof n)throw Error();var r=(0,Y.A)(t);return this.tween(e,(r.local?function(t,n){var e,r;function i(){var i=n.apply(this,arguments);return i!==r&&(e=(r=i)&&function(n){this.setAttributeNS(t.space,t.local,i.call(this,n))}),e}return i._value=n,i}:function(t,n){var e,r;function i(){var i=n.apply(this,arguments);return i!==r&&(e=(r=i)&&function(n){this.setAttribute(t,i.call(this,n))}),e}return i._value=n,i})(r,n))},style:function(t,n,e){var r,i,o,a,u,s,l,c,h,f,p,d,y,v,m,g,_,x,w,b,A,k="transform"==(t+="")?j:tM;return null==n?this.styleTween(t,(r=t,function(){var t=(0,t$.j)(this,r),n=(this.style.removeProperty(r),(0,t$.j)(this,r));return t===n?null:t===i&&n===o?a:a=k(i=t,o=n)})).on("end.style."+t,tT(t)):"function"==typeof n?this.styleTween(t,(u=t,s=O(this,"style."+t,n),function(){var t=(0,t$.j)(this,u),n=s(this),e=n+"";return null==n&&(this.style.removeProperty(u),e=n=(0,t$.j)(this,u)),t===e?null:t===l&&e===c?h:(c=e,h=k(l=t,n))})).each((f=this._id,_="end."+(g="style."+(p=t)),function(){var t=N(this,f),n=t.on,e=null==t.value[g]?m||(m=tT(p)):void 0;(n!==d||v!==e)&&(y=(d=n).copy()).on(_,v=e),t.on=y})):this.styleTween(t,(x=t,A=n+"",function(){var t=(0,t$.j)(this,x);return t===A?null:t===w?b:b=k(w=t,n)}),e).on("end.style."+t,null)},styleTween:function(t,n,e){var r="style."+(t+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(null==n)return this.tween(r,null);if("function"!=typeof n)throw Error();return this.tween(r,function(t,n,e){var r,i;function o(){var o=n.apply(this,arguments);return o!==i&&(r=(i=o)&&function(n){this.style.setProperty(t,o.call(this,n),e)}),r}return o._value=n,o}(t,n,null==e?"":e))},text:function(t){var n,e;return this.tween("text","function"==typeof t?(n=O(this,"text",t),function(){var t=n(this);this.textContent=null==t?"":t}):(e=null==t?"":t+"",function(){this.textContent=e}))},textTween:function(t){var n="text";if(arguments.length<1)return(n=this.tween(n))&&n._value;if(null==t)return this.tween(n,null);if("function"!=typeof t)throw Error();return this.tween(n,function(t){var n,e;function r(){var r=t.apply(this,arguments);return r!==e&&(n=(e=r)&&function(t){this.textContent=r.call(this,t)}),n}return r._value=t,r}(t))},remove:function(){var t;return this.on("end.remove",(t=this._id,function(){var n=this.parentNode;for(var e in this.__transition)if(+e!==t)return;n&&n.removeChild(this)}))},tween:function(t,n){var e=this._id;if(t+="",arguments.length<2){for(var r,i=S(this.node(),e).tween,o=0,a=i.length;o<a;++o)if((r=i[o]).name===t)return r.value;return null}return this.each((null==n?function(t,n){var e,r;return function(){var i=N(this,t),o=i.tween;if(o!==e){r=e=o;for(var a=0,u=r.length;a<u;++a)if(r[a].name===n){(r=r.slice()).splice(a,1);break}}i.tween=r}}:function(t,n,e){var r,i;if("function"!=typeof e)throw Error();return function(){var o=N(this,t),a=o.tween;if(a!==r){i=(r=a).slice();for(var u={name:n,value:e},s=0,l=i.length;s<l;++s)if(i[s].name===n){i[s]=u;break}s===l&&i.push(u)}o.tween=i}})(e,t,n))},delay:function(t){var n=this._id;return arguments.length?this.each(("function"==typeof t?function(t,n){return function(){E(this,t).delay=+n.apply(this,arguments)}}:function(t,n){return n*=1,function(){E(this,t).delay=n}})(n,t)):S(this.node(),n).delay},duration:function(t){var n=this._id;return arguments.length?this.each(("function"==typeof t?function(t,n){return function(){N(this,t).duration=+n.apply(this,arguments)}}:function(t,n){return n*=1,function(){N(this,t).duration=n}})(n,t)):S(this.node(),n).duration},ease:function(t){var n=this._id;return arguments.length?this.each(function(t,n){if("function"!=typeof n)throw Error();return function(){N(this,t).ease=n}}(n,t)):S(this.node(),n).ease},easeVarying:function(t){var n;if("function"!=typeof t)throw Error();return this.each((n=this._id,function(){var e=t.apply(this,arguments);if("function"!=typeof e)throw Error();N(this,n).ease=e}))},end:function(){var t,n,e=this,r=e._id,i=e.size();return new Promise(function(o,a){var u={value:a},s={value:function(){0==--i&&o()}};e.each(function(){var e=N(this,r),i=e.on;i!==t&&((n=(t=i).copy())._.cancel.push(u),n._.interrupt.push(u),n._.end.push(s)),e.on=n}),0===i&&o()})},[Symbol.iterator]:tP[Symbol.iterator]};var tX={time:null,delay:0,duration:250,ease:function(t){return((t*=2)<=1?t*t*t:(t-=2)*t*t+2)/2}};a.Ay.prototype.interrupt=function(t){return this.each(function(){$(this,t)})},a.Ay.prototype.transition=function(t){var n,e;t instanceof tC?(n=t._id,t=t._name):(n=++tq,(e=tX).time=v(),t=null==t?null:t+"");for(var r=this._groups,i=r.length,o=0;o<i;++o)for(var a,u=r[o],s=u.length,l=0;l<s;++l)(a=u[l])&&z(a,t,n,l,u,e||function(t,n){for(var e;!(e=t.__transition)||!(e=e[n]);)if(!(t=t.parentNode))throw Error(`transition ${n} not found`);return e}(a,n));return new tC(r,this._parents,t,n)}},978:(t,n,e)=>{e.d(n,{A:()=>r});let r=(0,e(9946).A)("arrow-down-left",[["path",{d:"M17 7 7 17",key:"15tmo1"}],["path",{d:"M17 17H7V7",key:"1org7z"}]])},1235:(t,n,e)=>{e.d(n,{A:()=>u});var r={value:()=>{}};function i(){for(var t,n=0,e=arguments.length,r={};n<e;++n){if(!(t=arguments[n]+"")||t in r||/[\s.]/.test(t))throw Error("illegal type: "+t);r[t]=[]}return new o(r)}function o(t){this._=t}function a(t,n,e){for(var i=0,o=t.length;i<o;++i)if(t[i].name===n){t[i]=r,t=t.slice(0,i).concat(t.slice(i+1));break}return null!=e&&t.push({name:n,value:e}),t}o.prototype=i.prototype={constructor:o,on:function(t,n){var e,r=this._,i=(t+"").trim().split(/^|\s+/).map(function(t){var n="",e=t.indexOf(".");if(e>=0&&(n=t.slice(e+1),t=t.slice(0,e)),t&&!r.hasOwnProperty(t))throw Error("unknown type: "+t);return{type:t,name:n}}),o=-1,u=i.length;if(arguments.length<2){for(;++o<u;)if((e=(t=i[o]).type)&&(e=function(t,n){for(var e,r=0,i=t.length;r<i;++r)if((e=t[r]).name===n)return e.value}(r[e],t.name)))return e;return}if(null!=n&&"function"!=typeof n)throw Error("invalid callback: "+n);for(;++o<u;)if(e=(t=i[o]).type)r[e]=a(r[e],t.name,n);else if(null==n)for(e in r)r[e]=a(r[e],t.name,null);return this},copy:function(){var t={},n=this._;for(var e in n)t[e]=n[e].slice();return new o(t)},call:function(t,n){if((e=arguments.length-2)>0)for(var e,r,i=Array(e),o=0;o<e;++o)i[o]=arguments[o+2];if(!this._.hasOwnProperty(t))throw Error("unknown type: "+t);for(r=this._[t],o=0,e=r.length;o<e;++o)r[o].value.apply(n,i)},apply:function(t,n,e){if(!this._.hasOwnProperty(t))throw Error("unknown type: "+t);for(var r=this._[t],i=0,o=r.length;i<o;++i)r[i].value.apply(n,e)}};let u=i},1774:(t,n,e)=>{e.d(n,{A:()=>i,j:()=>o});var r=e(7271);function i(t,n,e){return arguments.length>1?this.each((null==n?function(t){return function(){this.style.removeProperty(t)}}:"function"==typeof n?function(t,n,e){return function(){var r=n.apply(this,arguments);null==r?this.style.removeProperty(t):this.style.setProperty(t,r,e)}}:function(t,n,e){return function(){this.style.setProperty(t,n,e)}})(t,n,null==e?"":e)):o(this.node(),t)}function o(t,n){return t.style.getPropertyValue(n)||(0,r.A)(t).getComputedStyle(t,null).getPropertyValue(n)}},2903:(t,n,e)=>{e.d(n,{A:()=>i});var r=e(4498);function i(t){return"string"==typeof t?new r.LN([[document.querySelector(t)]],[document.documentElement]):new r.LN([[t]],r.zr)}},3875:(t,n,e)=>{function r(t){return function(){return this.matches(t)}}function i(t){return function(n){return n.matches(t)}}e.d(n,{A:()=>r,j:()=>i})},4498:(t,n,e)=>{e.d(n,{LN:()=>Y,Ay:()=>R,zr:()=>B});var r=e(394),i=e(9293),o=e(3875),a=Array.prototype.find;function u(){return this.firstElementChild}var s=Array.prototype.filter;function l(){return Array.from(this.children)}function c(t){return Array(t.length)}function h(t,n){this.ownerDocument=t.ownerDocument,this.namespaceURI=t.namespaceURI,this._next=null,this._parent=t,this.__data__=n}function f(t,n,e,r,i,o){for(var a,u=0,s=n.length,l=o.length;u<l;++u)(a=n[u])?(a.__data__=o[u],r[u]=a):e[u]=new h(t,o[u]);for(;u<s;++u)(a=n[u])&&(i[u]=a)}function p(t,n,e,r,i,o,a){var u,s,l,c=new Map,f=n.length,p=o.length,d=Array(f);for(u=0;u<f;++u)(s=n[u])&&(d[u]=l=a.call(s,s.__data__,u,n)+"",c.has(l)?i[u]=s:c.set(l,s));for(u=0;u<p;++u)l=a.call(t,o[u],u,o)+"",(s=c.get(l))?(r[u]=s,s.__data__=o[u],c.delete(l)):e[u]=new h(t,o[u]);for(u=0;u<f;++u)(s=n[u])&&c.get(d[u])===s&&(i[u]=s)}function d(t){return t.__data__}function y(t,n){return t<n?-1:t>n?1:t>=n?0:NaN}h.prototype={constructor:h,appendChild:function(t){return this._parent.insertBefore(t,this._next)},insertBefore:function(t,n){return this._parent.insertBefore(t,n)},querySelector:function(t){return this._parent.querySelector(t)},querySelectorAll:function(t){return this._parent.querySelectorAll(t)}};var v=e(6102),m=e(1774);function g(t){return t.trim().split(/^|\s+/)}function _(t){return t.classList||new x(t)}function x(t){this._node=t,this._names=g(t.getAttribute("class")||"")}function w(t,n){for(var e=_(t),r=-1,i=n.length;++r<i;)e.add(n[r])}function b(t,n){for(var e=_(t),r=-1,i=n.length;++r<i;)e.remove(n[r])}function A(){this.textContent=""}function k(){this.innerHTML=""}function M(){this.nextSibling&&this.parentNode.appendChild(this)}function z(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}x.prototype={add:function(t){0>this._names.indexOf(t)&&(this._names.push(t),this._node.setAttribute("class",this._names.join(" ")))},remove:function(t){var n=this._names.indexOf(t);n>=0&&(this._names.splice(n,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(t){return this._names.indexOf(t)>=0}};var E=e(5131);function N(t){var n=(0,v.A)(t);return(n.local?function(t){return function(){return this.ownerDocument.createElementNS(t.space,t.local)}}:function(t){return function(){var n=this.ownerDocument,e=this.namespaceURI;return e===E.g&&n.documentElement.namespaceURI===E.g?n.createElement(t):n.createElementNS(e,t)}})(n)}function S(){return null}function $(){var t=this.parentNode;t&&t.removeChild(this)}function T(){var t=this.cloneNode(!1),n=this.parentNode;return n?n.insertBefore(t,this.nextSibling):t}function q(){var t=this.cloneNode(!0),n=this.parentNode;return n?n.insertBefore(t,this.nextSibling):t}function C(t){return function(){var n=this.__on;if(n){for(var e,r=0,i=-1,o=n.length;r<o;++r)(e=n[r],t.type&&e.type!==t.type||e.name!==t.name)?n[++i]=e:this.removeEventListener(e.type,e.listener,e.options);++i?n.length=i:delete this.__on}}}function P(t,n,e){return function(){var r,i=this.__on,o=function(t){n.call(this,t,this.__data__)};if(i){for(var a=0,u=i.length;a<u;++a)if((r=i[a]).type===t.type&&r.name===t.name){this.removeEventListener(r.type,r.listener,r.options),this.addEventListener(r.type,r.listener=o,r.options=e),r.value=n;return}}this.addEventListener(t.type,o,e),r={type:t.type,name:t.name,value:n,listener:o,options:e},i?i.push(r):this.__on=[r]}}var X=e(7271);function j(t,n,e){var r=(0,X.A)(t),i=r.CustomEvent;"function"==typeof i?i=new i(n,e):(i=r.document.createEvent("Event"),e?(i.initEvent(n,e.bubbles,e.cancelable),i.detail=e.detail):i.initEvent(n,!1,!1)),t.dispatchEvent(i)}var B=[null];function Y(t,n){this._groups=t,this._parents=n}function O(){return new Y([[document.documentElement]],B)}Y.prototype=O.prototype={constructor:Y,select:function(t){"function"!=typeof t&&(t=(0,r.A)(t));for(var n=this._groups,e=n.length,i=Array(e),o=0;o<e;++o)for(var a,u,s=n[o],l=s.length,c=i[o]=Array(l),h=0;h<l;++h)(a=s[h])&&(u=t.call(a,a.__data__,h,s))&&("__data__"in a&&(u.__data__=a.__data__),c[h]=u);return new Y(i,this._parents)},selectAll:function(t){if("function"==typeof t){var n;n=t,t=function(){var t;return t=n.apply(this,arguments),null==t?[]:Array.isArray(t)?t:Array.from(t)}}else t=(0,i.A)(t);for(var e=this._groups,r=e.length,o=[],a=[],u=0;u<r;++u)for(var s,l=e[u],c=l.length,h=0;h<c;++h)(s=l[h])&&(o.push(t.call(s,s.__data__,h,l)),a.push(s));return new Y(o,a)},selectChild:function(t){var n;return this.select(null==t?u:(n="function"==typeof t?t:(0,o.j)(t),function(){return a.call(this.children,n)}))},selectChildren:function(t){var n;return this.selectAll(null==t?l:(n="function"==typeof t?t:(0,o.j)(t),function(){return s.call(this.children,n)}))},filter:function(t){"function"!=typeof t&&(t=(0,o.A)(t));for(var n=this._groups,e=n.length,r=Array(e),i=0;i<e;++i)for(var a,u=n[i],s=u.length,l=r[i]=[],c=0;c<s;++c)(a=u[c])&&t.call(a,a.__data__,c,u)&&l.push(a);return new Y(r,this._parents)},data:function(t,n){if(!arguments.length)return Array.from(this,d);var e=n?p:f,r=this._parents,i=this._groups;"function"!=typeof t&&(w=t,t=function(){return w});for(var o=i.length,a=Array(o),u=Array(o),s=Array(o),l=0;l<o;++l){var c=r[l],h=i[l],y=h.length,v="object"==typeof(x=t.call(c,c&&c.__data__,l,r))&&"length"in x?x:Array.from(x),m=v.length,g=u[l]=Array(m),_=a[l]=Array(m);e(c,h,g,_,s[l]=Array(y),v,n);for(var x,w,b,A,k=0,M=0;k<m;++k)if(b=g[k]){for(k>=M&&(M=k+1);!(A=_[M])&&++M<m;);b._next=A||null}}return(a=new Y(a,r))._enter=u,a._exit=s,a},enter:function(){return new Y(this._enter||this._groups.map(c),this._parents)},exit:function(){return new Y(this._exit||this._groups.map(c),this._parents)},join:function(t,n,e){var r=this.enter(),i=this,o=this.exit();return"function"==typeof t?(r=t(r))&&(r=r.selection()):r=r.append(t+""),null!=n&&(i=n(i))&&(i=i.selection()),null==e?o.remove():e(o),r&&i?r.merge(i).order():i},merge:function(t){for(var n=t.selection?t.selection():t,e=this._groups,r=n._groups,i=e.length,o=r.length,a=Math.min(i,o),u=Array(i),s=0;s<a;++s)for(var l,c=e[s],h=r[s],f=c.length,p=u[s]=Array(f),d=0;d<f;++d)(l=c[d]||h[d])&&(p[d]=l);for(;s<i;++s)u[s]=e[s];return new Y(u,this._parents)},selection:function(){return this},order:function(){for(var t=this._groups,n=-1,e=t.length;++n<e;)for(var r,i=t[n],o=i.length-1,a=i[o];--o>=0;)(r=i[o])&&(a&&4^r.compareDocumentPosition(a)&&a.parentNode.insertBefore(r,a),a=r);return this},sort:function(t){function n(n,e){return n&&e?t(n.__data__,e.__data__):!n-!e}t||(t=y);for(var e=this._groups,r=e.length,i=Array(r),o=0;o<r;++o){for(var a,u=e[o],s=u.length,l=i[o]=Array(s),c=0;c<s;++c)(a=u[c])&&(l[c]=a);l.sort(n)}return new Y(i,this._parents).order()},call:function(){var t=arguments[0];return arguments[0]=this,t.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var t=this._groups,n=0,e=t.length;n<e;++n)for(var r=t[n],i=0,o=r.length;i<o;++i){var a=r[i];if(a)return a}return null},size:function(){let t=0;for(let n of this)++t;return t},empty:function(){return!this.node()},each:function(t){for(var n=this._groups,e=0,r=n.length;e<r;++e)for(var i,o=n[e],a=0,u=o.length;a<u;++a)(i=o[a])&&t.call(i,i.__data__,a,o);return this},attr:function(t,n){var e=(0,v.A)(t);if(arguments.length<2){var r=this.node();return e.local?r.getAttributeNS(e.space,e.local):r.getAttribute(e)}return this.each((null==n?e.local?function(t){return function(){this.removeAttributeNS(t.space,t.local)}}:function(t){return function(){this.removeAttribute(t)}}:"function"==typeof n?e.local?function(t,n){return function(){var e=n.apply(this,arguments);null==e?this.removeAttributeNS(t.space,t.local):this.setAttributeNS(t.space,t.local,e)}}:function(t,n){return function(){var e=n.apply(this,arguments);null==e?this.removeAttribute(t):this.setAttribute(t,e)}}:e.local?function(t,n){return function(){this.setAttributeNS(t.space,t.local,n)}}:function(t,n){return function(){this.setAttribute(t,n)}})(e,n))},style:m.A,property:function(t,n){return arguments.length>1?this.each((null==n?function(t){return function(){delete this[t]}}:"function"==typeof n?function(t,n){return function(){var e=n.apply(this,arguments);null==e?delete this[t]:this[t]=e}}:function(t,n){return function(){this[t]=n}})(t,n)):this.node()[t]},classed:function(t,n){var e=g(t+"");if(arguments.length<2){for(var r=_(this.node()),i=-1,o=e.length;++i<o;)if(!r.contains(e[i]))return!1;return!0}return this.each(("function"==typeof n?function(t,n){return function(){(n.apply(this,arguments)?w:b)(this,t)}}:n?function(t){return function(){w(this,t)}}:function(t){return function(){b(this,t)}})(e,n))},text:function(t){return arguments.length?this.each(null==t?A:("function"==typeof t?function(t){return function(){var n=t.apply(this,arguments);this.textContent=null==n?"":n}}:function(t){return function(){this.textContent=t}})(t)):this.node().textContent},html:function(t){return arguments.length?this.each(null==t?k:("function"==typeof t?function(t){return function(){var n=t.apply(this,arguments);this.innerHTML=null==n?"":n}}:function(t){return function(){this.innerHTML=t}})(t)):this.node().innerHTML},raise:function(){return this.each(M)},lower:function(){return this.each(z)},append:function(t){var n="function"==typeof t?t:N(t);return this.select(function(){return this.appendChild(n.apply(this,arguments))})},insert:function(t,n){var e="function"==typeof t?t:N(t),i=null==n?S:"function"==typeof n?n:(0,r.A)(n);return this.select(function(){return this.insertBefore(e.apply(this,arguments),i.apply(this,arguments)||null)})},remove:function(){return this.each($)},clone:function(t){return this.select(t?q:T)},datum:function(t){return arguments.length?this.property("__data__",t):this.node().__data__},on:function(t,n,e){var r,i,o=(t+"").trim().split(/^|\s+/).map(function(t){var n="",e=t.indexOf(".");return e>=0&&(n=t.slice(e+1),t=t.slice(0,e)),{type:t,name:n}}),a=o.length;if(arguments.length<2){var u=this.node().__on;if(u){for(var s,l=0,c=u.length;l<c;++l)for(r=0,s=u[l];r<a;++r)if((i=o[r]).type===s.type&&i.name===s.name)return s.value}return}for(r=0,u=n?P:C;r<a;++r)this.each(u(o[r],n,e));return this},dispatch:function(t,n){return this.each(("function"==typeof n?function(t,n){return function(){return j(this,t,n.apply(this,arguments))}}:function(t,n){return function(){return j(this,t,n)}})(t,n))},[Symbol.iterator]:function*(){for(var t=this._groups,n=0,e=t.length;n<e;++n)for(var r,i=t[n],o=0,a=i.length;o<a;++o)(r=i[o])&&(yield r)}};let R=O},4804:(t,n,e)=>{e.d(n,{s_:()=>A,GS:()=>d});var r=e(1235),i=e(2903);let o={capture:!0,passive:!1};function a(t){t.preventDefault(),t.stopImmediatePropagation()}function u(t){return((t=Math.exp(t))+1/t)/2}let s=function t(n,e,r){function i(t,i){var o,a,s=t[0],l=t[1],c=t[2],h=i[0],f=i[1],p=i[2],d=h-s,y=f-l,v=d*d+y*y;if(v<1e-12)a=Math.log(p/c)/n,o=function(t){return[s+t*d,l+t*y,c*Math.exp(n*t*a)]};else{var m=Math.sqrt(v),g=(p*p-c*c+r*v)/(2*c*e*m),_=(p*p-c*c-r*v)/(2*p*e*m),x=Math.log(Math.sqrt(g*g+1)-g);a=(Math.log(Math.sqrt(_*_+1)-_)-x)/n,o=function(t){var r,i,o=t*a,h=u(x),f=c/(e*m)*(h*(((r=Math.exp(2*(r=n*o+x)))-1)/(r+1))-((i=Math.exp(i=x))-1/i)/2);return[s+f*d,l+f*y,c*h/u(n*o+x)]}}return o.duration=1e3*a*n/Math.SQRT2,o}return i.rho=function(n){var e=Math.max(.001,+n),r=e*e;return t(e,r,r*r)},i}(Math.SQRT2,2,4);function l(t,n){if(t=function(t){let n;for(;n=t.sourceEvent;)t=n;return t}(t),void 0===n&&(n=t.currentTarget),n){var e=n.ownerSVGElement||n;if(e.createSVGPoint){var r=e.createSVGPoint();return r.x=t.clientX,r.y=t.clientY,[(r=r.matrixTransform(n.getScreenCTM().inverse())).x,r.y]}if(n.getBoundingClientRect){var i=n.getBoundingClientRect();return[t.clientX-i.left-n.clientLeft,t.clientY-i.top-n.clientTop]}}return[t.pageX,t.pageY]}var c=e(469);let h=t=>()=>t;function f(t,{sourceEvent:n,target:e,transform:r,dispatch:i}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:n,enumerable:!0,configurable:!0},target:{value:e,enumerable:!0,configurable:!0},transform:{value:r,enumerable:!0,configurable:!0},_:{value:i}})}function p(t,n,e){this.k=t,this.x=n,this.y=e}p.prototype={constructor:p,scale:function(t){return 1===t?this:new p(this.k*t,this.x,this.y)},translate:function(t,n){return 0===t&0===n?this:new p(this.k,this.x+this.k*t,this.y+this.k*n)},apply:function(t){return[t[0]*this.k+this.x,t[1]*this.k+this.y]},applyX:function(t){return t*this.k+this.x},applyY:function(t){return t*this.k+this.y},invert:function(t){return[(t[0]-this.x)/this.k,(t[1]-this.y)/this.k]},invertX:function(t){return(t-this.x)/this.k},invertY:function(t){return(t-this.y)/this.k},rescaleX:function(t){return t.copy().domain(t.range().map(this.invertX,this).map(t.invert,t))},rescaleY:function(t){return t.copy().domain(t.range().map(this.invertY,this).map(t.invert,t))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var d=new p(1,0,0);function y(t){t.stopImmediatePropagation()}function v(t){t.preventDefault(),t.stopImmediatePropagation()}function m(t){return(!t.ctrlKey||"wheel"===t.type)&&!t.button}function g(){var t=this;return t instanceof SVGElement?(t=t.ownerSVGElement||t).hasAttribute("viewBox")?[[(t=t.viewBox.baseVal).x,t.y],[t.x+t.width,t.y+t.height]]:[[0,0],[t.width.baseVal.value,t.height.baseVal.value]]:[[0,0],[t.clientWidth,t.clientHeight]]}function _(){return this.__zoom||d}function x(t){return-t.deltaY*(1===t.deltaMode?.05:t.deltaMode?1:.002)*(t.ctrlKey?10:1)}function w(){return navigator.maxTouchPoints||"ontouchstart"in this}function b(t,n,e){var r=t.invertX(n[0][0])-e[0][0],i=t.invertX(n[1][0])-e[1][0],o=t.invertY(n[0][1])-e[0][1],a=t.invertY(n[1][1])-e[1][1];return t.translate(i>r?(r+i)/2:Math.min(0,r)||Math.max(0,i),a>o?(o+a)/2:Math.min(0,o)||Math.max(0,a))}function A(){var t,n,e,u=m,A=g,k=b,M=x,z=w,E=[0,1/0],N=[[-1/0,-1/0],[1/0,1/0]],S=250,$=s,T=(0,r.A)("start","zoom","end"),q=0,C=10;function P(t){t.property("__zoom",_).on("wheel.zoom",L,{passive:!1}).on("mousedown.zoom",V).on("dblclick.zoom",D).filter(z).on("touchstart.zoom",H).on("touchmove.zoom",I).on("touchend.zoom touchcancel.zoom",G).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function X(t,n){return(n=Math.max(E[0],Math.min(E[1],n)))===t.k?t:new p(n,t.x,t.y)}function j(t,n,e){var r=n[0]-e[0]*t.k,i=n[1]-e[1]*t.k;return r===t.x&&i===t.y?t:new p(t.k,r,i)}function B(t){return[(+t[0][0]+ +t[1][0])/2,(+t[0][1]+ +t[1][1])/2]}function Y(t,n,e,r){t.on("start.zoom",function(){O(this,arguments).event(r).start()}).on("interrupt.zoom end.zoom",function(){O(this,arguments).event(r).end()}).tween("zoom",function(){var t=arguments,i=O(this,t).event(r),o=A.apply(this,t),a=null==e?B(o):"function"==typeof e?e.apply(this,t):e,u=Math.max(o[1][0]-o[0][0],o[1][1]-o[0][1]),s=this.__zoom,l="function"==typeof n?n.apply(this,t):n,c=$(s.invert(a).concat(u/s.k),l.invert(a).concat(u/l.k));return function(t){if(1===t)t=l;else{var n=c(t),e=u/n[2];t=new p(e,a[0]-n[0]*e,a[1]-n[1]*e)}i.zoom(null,t)}})}function O(t,n,e){return!e&&t.__zooming||new R(t,n)}function R(t,n){this.that=t,this.args=n,this.active=0,this.sourceEvent=null,this.extent=A.apply(t,n),this.taps=0}function L(t,...n){if(u.apply(this,arguments)){var e=O(this,n).event(t),r=this.__zoom,i=Math.max(E[0],Math.min(E[1],r.k*Math.pow(2,M.apply(this,arguments)))),o=l(t);if(e.wheel)(e.mouse[0][0]!==o[0]||e.mouse[0][1]!==o[1])&&(e.mouse[1]=r.invert(e.mouse[0]=o)),clearTimeout(e.wheel);else{if(r.k===i)return;e.mouse=[o,r.invert(o)],(0,c.G)(this),e.start()}v(t),e.wheel=setTimeout(function(){e.wheel=null,e.end()},150),e.zoom("mouse",k(j(X(r,i),e.mouse[0],e.mouse[1]),e.extent,N))}}function V(t,...n){if(!e&&u.apply(this,arguments)){var r,s,h,f=t.currentTarget,p=O(this,n,!0).event(t),d=(0,i.A)(t.view).on("mousemove.zoom",function(t){if(v(t),!p.moved){var n=t.clientX-g,e=t.clientY-_;p.moved=n*n+e*e>q}p.event(t).zoom("mouse",k(j(p.that.__zoom,p.mouse[0]=l(t,f),p.mouse[1]),p.extent,N))},!0).on("mouseup.zoom",function(t){var n,e,r,u;d.on("mousemove.zoom mouseup.zoom",null),n=t.view,e=p.moved,r=n.document.documentElement,u=(0,i.A)(n).on("dragstart.drag",null),e&&(u.on("click.drag",a,o),setTimeout(function(){u.on("click.drag",null)},0)),"onselectstart"in r?u.on("selectstart.drag",null):(r.style.MozUserSelect=r.__noselect,delete r.__noselect),v(t),p.event(t).end()},!0),m=l(t,f),g=t.clientX,_=t.clientY;s=(r=t.view).document.documentElement,h=(0,i.A)(r).on("dragstart.drag",a,o),"onselectstart"in s?h.on("selectstart.drag",a,o):(s.__noselect=s.style.MozUserSelect,s.style.MozUserSelect="none"),y(t),p.mouse=[m,this.__zoom.invert(m)],(0,c.G)(this),p.start()}}function D(t,...n){if(u.apply(this,arguments)){var e=this.__zoom,r=l(t.changedTouches?t.changedTouches[0]:t,this),o=e.invert(r),a=e.k*(t.shiftKey?.5:2),s=k(j(X(e,a),r,o),A.apply(this,n),N);v(t),S>0?(0,i.A)(this).transition().duration(S).call(Y,s,r,t):(0,i.A)(this).call(P.transform,s,r,t)}}function H(e,...r){if(u.apply(this,arguments)){var i,o,a,s,h=e.touches,f=h.length,p=O(this,r,e.changedTouches.length===f).event(e);for(y(e),o=0;o<f;++o)s=[s=l(a=h[o],this),this.__zoom.invert(s),a.identifier],p.touch0?p.touch1||p.touch0[2]===s[2]||(p.touch1=s,p.taps=0):(p.touch0=s,i=!0,p.taps=1+!!t);t&&(t=clearTimeout(t)),i&&(p.taps<2&&(n=s[0],t=setTimeout(function(){t=null},500)),(0,c.G)(this),p.start())}}function I(t,...n){if(this.__zooming){var e,r,i,o,a=O(this,n).event(t),u=t.changedTouches,s=u.length;for(v(t),e=0;e<s;++e)i=l(r=u[e],this),a.touch0&&a.touch0[2]===r.identifier?a.touch0[0]=i:a.touch1&&a.touch1[2]===r.identifier&&(a.touch1[0]=i);if(r=a.that.__zoom,a.touch1){var c=a.touch0[0],h=a.touch0[1],f=a.touch1[0],p=a.touch1[1],d=(d=f[0]-c[0])*d+(d=f[1]-c[1])*d,y=(y=p[0]-h[0])*y+(y=p[1]-h[1])*y;r=X(r,Math.sqrt(d/y)),i=[(c[0]+f[0])/2,(c[1]+f[1])/2],o=[(h[0]+p[0])/2,(h[1]+p[1])/2]}else{if(!a.touch0)return;i=a.touch0[0],o=a.touch0[1]}a.zoom("touch",k(j(r,i,o),a.extent,N))}}function G(t,...r){if(this.__zooming){var o,a,u=O(this,r).event(t),s=t.changedTouches,c=s.length;for(y(t),e&&clearTimeout(e),e=setTimeout(function(){e=null},500),o=0;o<c;++o)a=s[o],u.touch0&&u.touch0[2]===a.identifier?delete u.touch0:u.touch1&&u.touch1[2]===a.identifier&&delete u.touch1;if(u.touch1&&!u.touch0&&(u.touch0=u.touch1,delete u.touch1),u.touch0)u.touch0[1]=this.__zoom.invert(u.touch0[0]);else if(u.end(),2===u.taps&&(a=l(a,this),Math.hypot(n[0]-a[0],n[1]-a[1])<C)){var h=(0,i.A)(this).on("dblclick.zoom");h&&h.apply(this,arguments)}}}return P.transform=function(t,n,e,r){var i=t.selection?t.selection():t;i.property("__zoom",_),t!==i?Y(t,n,e,r):i.interrupt().each(function(){O(this,arguments).event(r).start().zoom(null,"function"==typeof n?n.apply(this,arguments):n).end()})},P.scaleBy=function(t,n,e,r){P.scaleTo(t,function(){var t=this.__zoom.k,e="function"==typeof n?n.apply(this,arguments):n;return t*e},e,r)},P.scaleTo=function(t,n,e,r){P.transform(t,function(){var t=A.apply(this,arguments),r=this.__zoom,i=null==e?B(t):"function"==typeof e?e.apply(this,arguments):e,o=r.invert(i),a="function"==typeof n?n.apply(this,arguments):n;return k(j(X(r,a),i,o),t,N)},e,r)},P.translateBy=function(t,n,e,r){P.transform(t,function(){return k(this.__zoom.translate("function"==typeof n?n.apply(this,arguments):n,"function"==typeof e?e.apply(this,arguments):e),A.apply(this,arguments),N)},null,r)},P.translateTo=function(t,n,e,r,i){P.transform(t,function(){var t=A.apply(this,arguments),i=this.__zoom,o=null==r?B(t):"function"==typeof r?r.apply(this,arguments):r;return k(d.translate(o[0],o[1]).scale(i.k).translate("function"==typeof n?-n.apply(this,arguments):-n,"function"==typeof e?-e.apply(this,arguments):-e),t,N)},r,i)},R.prototype={event:function(t){return t&&(this.sourceEvent=t),this},start:function(){return 1==++this.active&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(t,n){return this.mouse&&"mouse"!==t&&(this.mouse[1]=n.invert(this.mouse[0])),this.touch0&&"touch"!==t&&(this.touch0[1]=n.invert(this.touch0[0])),this.touch1&&"touch"!==t&&(this.touch1[1]=n.invert(this.touch1[0])),this.that.__zoom=n,this.emit("zoom"),this},end:function(){return 0==--this.active&&(delete this.that.__zooming,this.emit("end")),this},emit:function(t){var n=(0,i.A)(this.that).datum();T.call(t,this.that,new f(t,{sourceEvent:this.sourceEvent,target:P,type:t,transform:this.that.__zoom,dispatch:T}),n)}},P.wheelDelta=function(t){return arguments.length?(M="function"==typeof t?t:h(+t),P):M},P.filter=function(t){return arguments.length?(u="function"==typeof t?t:h(!!t),P):u},P.touchable=function(t){return arguments.length?(z="function"==typeof t?t:h(!!t),P):z},P.extent=function(t){return arguments.length?(A="function"==typeof t?t:h([[+t[0][0],+t[0][1]],[+t[1][0],+t[1][1]]]),P):A},P.scaleExtent=function(t){return arguments.length?(E[0]=+t[0],E[1]=+t[1],P):[E[0],E[1]]},P.translateExtent=function(t){return arguments.length?(N[0][0]=+t[0][0],N[1][0]=+t[1][0],N[0][1]=+t[0][1],N[1][1]=+t[1][1],P):[[N[0][0],N[0][1]],[N[1][0],N[1][1]]]},P.constrain=function(t){return arguments.length?(k=t,P):k},P.duration=function(t){return arguments.length?(S=+t,P):S},P.interpolate=function(t){return arguments.length?($=t,P):$},P.on=function(){var t=T.on.apply(T,arguments);return t===T?P:t},P.clickDistance=function(t){return arguments.length?(q=(t*=1)*t,P):Math.sqrt(q)},P.tapDistance=function(t){return arguments.length?(C=+t,P):C},P}p.prototype},4870:(t,n,e)=>{e.d(n,{A:()=>r});let r=(0,e(9946).A)("arrow-up-right",[["path",{d:"M7 7h10v10",key:"1tivn9"}],["path",{d:"M7 17 17 7",key:"1vkiza"}]])},5040:(t,n,e)=>{e.d(n,{A:()=>r});let r=(0,e(9946).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},5131:(t,n,e)=>{e.d(n,{A:()=>i,g:()=>r});var r="http://www.w3.org/1999/xhtml";let i={svg:"http://www.w3.org/2000/svg",xhtml:r,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"}},6102:(t,n,e)=>{e.d(n,{A:()=>i});var r=e(5131);function i(t){var n=t+="",e=n.indexOf(":");return e>=0&&"xmlns"!==(n=t.slice(0,e))&&(t=t.slice(e+1)),r.A.hasOwnProperty(n)?{space:r.A[n],local:t}:t}},6516:(t,n,e)=>{e.d(n,{A:()=>r});let r=(0,e(9946).A)("share-2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]])},6740:(t,n,e)=>{e.d(n,{A:()=>r});let r=(0,e(9946).A)("calculator",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]])},6767:(t,n,e)=>{e.d(n,{A:()=>r});let r=(0,e(9946).A)("smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]])},6785:(t,n,e)=>{e.d(n,{A:()=>r});let r=(0,e(9946).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},7271:(t,n,e)=>{e.d(n,{A:()=>r});function r(t){return t.ownerDocument&&t.ownerDocument.defaultView||t.document&&t||t.defaultView}},9037:(t,n,e)=>{e.d(n,{A:()=>r});let r=(0,e(9946).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},9121:(t,n,e)=>{function r(t){var n=0,e=t.children,r=e&&e.length;if(r)for(;--r>=0;)n+=e[r].value;else n=1;t.value=n}function i(t,n){t instanceof Map?(t=[void 0,t],void 0===n&&(n=a)):void 0===n&&(n=o);for(var e,r,i,u,c,h=new l(t),f=[h];e=f.pop();)if((i=n(e.data))&&(c=(i=Array.from(i)).length))for(e.children=i,u=c-1;u>=0;--u)f.push(r=i[u]=new l(i[u])),r.parent=e,r.depth=e.depth+1;return h.eachBefore(s)}function o(t){return t.children}function a(t){return Array.isArray(t)?t[1]:null}function u(t){void 0!==t.data.value&&(t.value=t.data.value),t.data=t.data.data}function s(t){var n=0;do t.height=n;while((t=t.parent)&&t.height<++n)}function l(t){this.data=t,this.depth=this.height=0,this.parent=null}e.d(n,{bP:()=>l,Ay:()=>i}),l.prototype=i.prototype={constructor:l,count:function(){return this.eachAfter(r)},each:function(t,n){let e=-1;for(let r of this)t.call(n,r,++e,this);return this},eachAfter:function(t,n){for(var e,r,i,o=this,a=[o],u=[],s=-1;o=a.pop();)if(u.push(o),e=o.children)for(r=0,i=e.length;r<i;++r)a.push(e[r]);for(;o=u.pop();)t.call(n,o,++s,this);return this},eachBefore:function(t,n){for(var e,r,i=this,o=[i],a=-1;i=o.pop();)if(t.call(n,i,++a,this),e=i.children)for(r=e.length-1;r>=0;--r)o.push(e[r]);return this},find:function(t,n){let e=-1;for(let r of this)if(t.call(n,r,++e,this))return r},sum:function(t){return this.eachAfter(function(n){for(var e=+t(n.data)||0,r=n.children,i=r&&r.length;--i>=0;)e+=r[i].value;n.value=e})},sort:function(t){return this.eachBefore(function(n){n.children&&n.children.sort(t)})},path:function(t){for(var n=this,e=function(t,n){if(t===n)return t;var e=t.ancestors(),r=n.ancestors(),i=null;for(t=e.pop(),n=r.pop();t===n;)i=t,t=e.pop(),n=r.pop();return i}(n,t),r=[n];n!==e;)r.push(n=n.parent);for(var i=r.length;t!==e;)r.splice(i,0,t),t=t.parent;return r},ancestors:function(){for(var t=this,n=[t];t=t.parent;)n.push(t);return n},descendants:function(){return Array.from(this)},leaves:function(){var t=[];return this.eachBefore(function(n){n.children||t.push(n)}),t},links:function(){var t=this,n=[];return t.each(function(e){e!==t&&n.push({source:e.parent,target:e})}),n},copy:function(){return i(this).eachBefore(u)},[Symbol.iterator]:function*(){var t,n,e,r,i=this,o=[i];do for(t=o.reverse(),o=[];i=t.pop();)if(yield i,n=i.children)for(e=0,r=n.length;e<r;++e)o.push(n[e]);while(o.length)}}},9212:(t,n,e)=>{function r(t,n){return t.parent===n.parent?1:2}function i(t){var n=t.children;return n?n[0]:t.t}function o(t){var n=t.children;return n?n[n.length-1]:t.t}function a(t,n){this._=t,this.parent=null,this.children=null,this.A=null,this.a=this,this.z=0,this.m=0,this.c=0,this.s=0,this.t=null,this.i=n}function u(){var t=r,n=1,e=1,u=null;function s(r){var i=function(t){for(var n,e,r,i,o,u=new a(t,0),s=[u];n=s.pop();)if(r=n._.children)for(n.children=Array(o=r.length),i=o-1;i>=0;--i)s.push(e=n.children[i]=new a(r[i],i)),e.parent=n;return(u.parent=new a(null,0)).children=[u],u}(r);if(i.eachAfter(l),i.parent.m=-i.z,i.eachBefore(c),u)r.eachBefore(h);else{var o=r,s=r,f=r;r.eachBefore(function(t){t.x<o.x&&(o=t),t.x>s.x&&(s=t),t.depth>f.depth&&(f=t)});var p=o===s?1:t(o,s)/2,d=p-o.x,y=n/(s.x+p+d),v=e/(f.depth||1);r.eachBefore(function(t){t.x=(t.x+d)*y,t.y=t.depth*v})}return r}function l(n){var e=n.children,r=n.parent.children,a=n.i?r[n.i-1]:null;if(e){!function(t){for(var n,e=0,r=0,i=t.children,o=i.length;--o>=0;)n=i[o],n.z+=e,n.m+=e,e+=n.s+(r+=n.c)}(n);var u=(e[0].z+e[e.length-1].z)/2;a?(n.z=a.z+t(n._,a._),n.m=n.z-u):n.z=u}else a&&(n.z=a.z+t(n._,a._));n.parent.A=function(n,e,r){if(e){for(var a,u,s,l=n,c=n,h=e,f=l.parent.children[0],p=l.m,d=c.m,y=h.m,v=f.m;h=o(h),l=i(l),h&&l;)f=i(f),(c=o(c)).a=n,(s=h.z+y-l.z-p+t(h._,l._))>0&&(!function(t,n,e){var r=e/(n.i-t.i);n.c-=r,n.s+=e,t.c+=r,n.z+=e,n.m+=e}((a=h,u=r,a.a.parent===n.parent?a.a:u),n,s),p+=s,d+=s),y+=h.m,p+=l.m,v+=f.m,d+=c.m;h&&!o(c)&&(c.t=h,c.m+=y-d),l&&!i(f)&&(f.t=l,f.m+=p-v,r=n)}return r}(n,a,n.parent.A||r[0])}function c(t){t._.x=t.z+t.parent.m,t.m+=t.parent.m}function h(t){t.x*=n,t.y=t.depth*e}return s.separation=function(n){return arguments.length?(t=n,s):t},s.size=function(t){return arguments.length?(u=!1,n=+t[0],e=+t[1],s):u?null:[n,e]},s.nodeSize=function(t){return arguments.length?(u=!0,n=+t[0],e=+t[1],s):u?[n,e]:null},s}e.d(n,{A:()=>u}),a.prototype=Object.create(e(9121).bP.prototype)},9293:(t,n,e)=>{function r(){return[]}function i(t){return null==t?r:function(){return this.querySelectorAll(t)}}e.d(n,{A:()=>i})},9803:(t,n,e)=>{e.d(n,{A:()=>r});let r=(0,e(9946).A)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]])},9947:(t,n,e)=>{e.d(n,{A:()=>r});let r=(0,e(9946).A)("circle-question-mark",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]])}}]);