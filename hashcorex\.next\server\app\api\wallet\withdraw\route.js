"use strict";(()=>{var e={};e.id=1938,e.ids=[1938],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10157:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>E,routeModule:()=>f,serverHooks:()=>h,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>w});var s={};r.r(s),r.d(s,{GET:()=>p,POST:()=>c});var a=r(96559),i=r(48088),n=r(37719),o=r(32190),u=r(39542),d=r(6710),l=r(31183);async function c(e){try{let{authenticated:t,user:r}=await (0,u.b9)(e);if(!t||!r)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});let{amount:s,usdtAddress:a}=await e.json();if(!s||!a||s<=0)return o.NextResponse.json({success:!1,error:"Invalid amount or USDT address"},{status:400});if(!a.match(/^T[A-Za-z1-9]{33}$/))return o.NextResponse.json({success:!1,error:"Invalid USDT TRC20 address format"},{status:400});let i=await d.rs.get("kycRequired");if("true"===i){let e=await d.Gy.findByEmail(r.email);if(!e||"APPROVED"!==e.kycStatus)return o.NextResponse.json({success:!1,error:"KYC verification required for withdrawals"},{status:400})}let n=parseFloat(await d.rs.get("minWithdrawalAmount")||"10"),l=parseFloat(await d.rs.get("withdrawalFeeFixed")||"3"),c=parseFloat(await d.rs.get("withdrawalFeePercentage")||"1");if(s<n)return o.NextResponse.json({success:!1,error:`Minimum withdrawal amount is $${n}`},{status:400});let p=s*c/100,f=l+p,m=s+f,w=(await d.k_.getOrCreate(r.id)).availableBalance;if(w<m)return o.NextResponse.json({success:!1,error:`Insufficient balance. Required: $${m.toFixed(2)} (Amount: $${s} + Fees: $${f.toFixed(2)}), Available: $${w.toFixed(2)}`},{status:400});if((await d.wJ.findPending()).filter(e=>e.userId===r.id).length>0)return o.NextResponse.json({success:!1,error:"You have a pending withdrawal request. Please wait for it to be processed."},{status:400});let h=await d.wJ.create({userId:r.id,amount:s,usdtAddress:a});return await d.k_.deductWithdrawal(r.id,m),await d.DR.create({userId:r.id,type:"WITHDRAWAL",amount:m,description:`USDT withdrawal: $${s} (Fees: $${f.toFixed(2)}) to ${a.slice(0,8)}...${a.slice(-8)}`,status:"PENDING",reference:h.id}),await d.AJ.create({action:"WITHDRAWAL_REQUESTED",userId:r.id,details:{withdrawalId:h.id,requestedAmount:s,netAmount:s,totalFees:f,fixedFee:l,percentageFee:p,totalDeduction:m,usdtAddress:a,balanceAfter:w-m},ipAddress:e.headers.get("x-forwarded-for")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"}),o.NextResponse.json({success:!0,message:"Withdrawal request submitted successfully",data:{withdrawalId:h.id,requestedAmount:s,netAmount:s,totalFees:f,fixedFee:l,percentageFee:p,totalDeduction:m,status:"PENDING"}})}catch(e){return console.error("Withdrawal request error:",e),o.NextResponse.json({success:!1,error:"Failed to create withdrawal request"},{status:500})}}async function p(e){try{let{authenticated:t,user:r}=await (0,u.b9)(e);if(!t||!r)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});let s=(await l.prisma.withdrawalRequest.findMany({where:{userId:r.id},orderBy:{createdAt:"desc"},take:50})).map(e=>({id:e.id,amount:e.amount,usdtAddress:e.usdtAddress,status:e.status,txid:e.txid,createdAt:e.createdAt,processedAt:e.processedAt,rejectionReason:e.rejectionReason}));return o.NextResponse.json({success:!0,data:s})}catch(e){return console.error("Withdrawal history fetch error:",e),o.NextResponse.json({success:!1,error:"Failed to fetch withdrawal history"},{status:500})}}let f=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/wallet/withdraw/route",pathname:"/api/wallet/withdraw",filename:"route",bundlePath:"app/api/wallet/withdraw/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\wallet\\withdraw\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:w,serverHooks:h}=f;function E(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:w})}},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37366:e=>{e.exports=require("dns")},39542:(e,t,r)=>{r.d(t,{b9:()=>R,HU:()=>w,qc:()=>T,Lx:()=>x,DY:()=>g,DT:()=>S});var s=r(85663),a=r(43205),i=r.n(a),n=r(6710),o=r(45697);let u=o.z.object({DATABASE_URL:o.z.string().url("DATABASE_URL must be a valid PostgreSQL URL"),DIRECT_URL:o.z.string().url("DIRECT_URL must be a valid PostgreSQL URL"),JWT_SECRET:o.z.string().min(32,"JWT_SECRET must be at least 32 characters long").refine(e=>{let t=/[A-Z]/.test(e),r=/[a-z]/.test(e),s=/\d/.test(e),a=/[!@#$%^&*(),.?":{}|<>]/.test(e);return t&&r&&s&&a},"JWT_SECRET should contain uppercase, lowercase, numbers, and special characters"),JWT_EXPIRES_IN:o.z.string().default("30d"),NODE_ENV:o.z.enum(["development","production","test"]).default("development"),NEXT_PUBLIC_APP_URL:o.z.string().url().optional(),PORT:o.z.string().regex(/^\d+$/).transform(Number).default("3000"),SMTP_HOST:o.z.string().optional(),SMTP_PORT:o.z.string().regex(/^\d+$/).transform(Number).optional(),SMTP_USER:o.z.string().email().optional(),SMTP_PASSWORD:o.z.string().optional(),SMTP_SECURE:o.z.string().transform(e=>"true"===e).optional(),TRON_NETWORK:o.z.enum(["mainnet","testnet"]).default("testnet"),TRON_API_KEY:o.z.string().optional(),USDT_CONTRACT_ADDRESS:o.z.string().optional(),MAX_FILE_SIZE:o.z.string().regex(/^\d+$/).transform(Number).default("10485760"),UPLOAD_DIR:o.z.string().default("./public/uploads"),BCRYPT_ROUNDS:o.z.string().regex(/^\d+$/).transform(Number).default("12"),SESSION_TIMEOUT:o.z.string().regex(/^\d+$/).transform(Number).default("1800"),RATE_LIMIT_WINDOW_MS:o.z.string().regex(/^\d+$/).transform(Number).default("900000"),RATE_LIMIT_MAX_REQUESTS:o.z.string().regex(/^\d+$/).transform(Number).default("100"),COINGECKO_API_URL:o.z.string().url().default("https://api.coingecko.com/api/v3"),LOG_LEVEL:o.z.enum(["error","warn","info","debug"]).default("info"),ENABLE_REQUEST_LOGGING:o.z.string().transform(e=>"true"===e).default("false"),ENABLE_REGISTRATION:o.z.string().transform(e=>"true"===e).default("true"),ENABLE_KYC:o.z.string().transform(e=>"true"===e).default("true"),ENABLE_WITHDRAWALS:o.z.string().transform(e=>"true"===e).default("true")}).refine(e=>{let t=[e.SMTP_HOST,e.SMTP_PORT,e.SMTP_USER,e.SMTP_PASSWORD],r=t.some(e=>void 0!==e),s=t.every(e=>void 0!==e);return!r||!!s},{message:"If SMTP configuration is provided, all SMTP fields (HOST, PORT, USER, PASSWORD) must be provided"});function d(){try{let e=u.safeParse(process.env);if(!e.success){let t=e.error.errors.map(e=>`${e.path.join(".")}: ${e.message}`);return{success:!1,errors:t}}return{success:!0,data:e.data}}catch(e){return{success:!1,errors:["Failed to validate environment variables"]}}}let l=null;function c(){if(!l){let e=d();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),l=e.data,console.log("✅ Environment variables validated successfully")}return l}let p={jwt:{secret:()=>c().JWT_SECRET,expiresIn:()=>c().JWT_EXPIRES_IN},security:{bcryptRounds:()=>c().BCRYPT_ROUNDS,sessionTimeout:()=>c().SESSION_TIMEOUT,maxFileSize:()=>c().MAX_FILE_SIZE,uploadDir:()=>c().UPLOAD_DIR}};if("phase-production-build"!==process.env.NEXT_PHASE){let e=d();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),console.log("✅ Environment variables validated successfully")}let f=async e=>await s.Ay.hash(e,p.security.bcryptRounds()),m=async(e,t)=>await s.Ay.compare(e,t),w=e=>i().sign(e,p.jwt.secret(),{expiresIn:p.jwt.expiresIn()}),h=e=>{try{return i().verify(e,p.jwt.secret())}catch(e){return null}},E=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},R=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=h(t);if(!r)return{authenticated:!1,user:null};let s=await n.Gy.findByEmail(r.email);return s?{authenticated:!0,user:s}:{authenticated:!1,user:null}},g=async e=>{let t,s;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await n.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let a=await f(e.password),i=!1;do s=E(),i=!await n.Gy.findByReferralId(s);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:a,referralId:s});if(t){let{placeUserByReferralType:s}=await r.e(2746).then(r.bind(r,2746)),a="general";"left"===e.placementSide?a="left":"right"===e.placementSide&&(a="right"),await s(t,o.id,a)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},x=async e=>{let t=await n.Gy.findByEmail(e.email);if(!t||!await m(e.password,t.password))throw Error("Invalid email or password");return{token:w({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},S=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),T=async e=>{let t=await n.Gy.findById(e);return t?.role==="ADMIN"}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,7911,925],()=>r(10157));module.exports=s})();