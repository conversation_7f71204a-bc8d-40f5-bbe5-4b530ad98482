import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest, isAdmin } from '@/lib/auth';
import { adminSettingsDb, systemLogDb } from '@/lib/database';

export async function GET(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);
    
    if (!authenticated || !user) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    const userIsAdmin = await isAdmin(user.id);
    if (!userIsAdmin) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    // Get all settings
    const settings = await adminSettingsDb.getAll();
    
    // Convert to object format
    const settingsObject: any = {};
    settings.forEach(setting => {
      try {
        // Try to parse as JSON first, fallback to string
        settingsObject[setting.key] = JSON.parse(setting.value);
      } catch {
        settingsObject[setting.key] = setting.value;
      }
    });

    // Map database keys to frontend keys for settings that need specific database keys
    if (settingsObject['MAX_BINARY_POINTS_PER_SIDE']) {
      settingsObject.maxBinaryPointsPerSide = parseFloat(settingsObject['MAX_BINARY_POINTS_PER_SIDE']);
      console.log(`Mapped MAX_BINARY_POINTS_PER_SIDE: ${settingsObject['MAX_BINARY_POINTS_PER_SIDE']} → maxBinaryPointsPerSide: ${settingsObject.maxBinaryPointsPerSide}`);
      // Remove the database key to prevent conflicts
      delete settingsObject['MAX_BINARY_POINTS_PER_SIDE'];
    }
    if (settingsObject['BINARY_POINT_VALUE']) {
      settingsObject.binaryPointValue = parseFloat(settingsObject['BINARY_POINT_VALUE']);
      delete settingsObject['BINARY_POINT_VALUE'];
    }
    if (settingsObject['BINARY_MATCHING_ENABLED']) {
      settingsObject.binaryMatchingEnabled = settingsObject['BINARY_MATCHING_ENABLED'] === 'true';
      delete settingsObject['BINARY_MATCHING_ENABLED'];
    }
    if (settingsObject['BINARY_MATCHING_SCHEDULE']) {
      settingsObject.binaryMatchingSchedule = settingsObject['BINARY_MATCHING_SCHEDULE'];
      delete settingsObject['BINARY_MATCHING_SCHEDULE'];
    }

    // Map pricing settings
    if (settingsObject['THS_PRICE']) {
      settingsObject.thsPriceUSD = parseFloat(settingsObject['THS_PRICE']);
      delete settingsObject['THS_PRICE'];
    }
    if (settingsObject['MINIMUM_PURCHASE']) {
      settingsObject.minPurchaseAmount = parseFloat(settingsObject['MINIMUM_PURCHASE']);
      delete settingsObject['MINIMUM_PURCHASE'];
    }
    if (settingsObject['MAXIMUM_PURCHASE']) {
      settingsObject.maxPurchaseAmount = parseFloat(settingsObject['MAXIMUM_PURCHASE']);
      delete settingsObject['MAXIMUM_PURCHASE'];
    }

    console.log('Settings object after mapping:', {
      'maxBinaryPointsPerSide': settingsObject.maxBinaryPointsPerSide
    });

    // Set default values if not exists
    const defaultSettings = {
      thsPriceUSD: 50.0,
      minPurchaseAmount: 100.0,
      maxPurchaseAmount: 10000.0,
      earningsRanges: [
        { minTHS: 0, maxTHS: 10, dailyReturnMin: 0.3, dailyReturnMax: 0.5, monthlyReturnMin: 10.0, monthlyReturnMax: 15.0 },
        { minTHS: 10, maxTHS: 50, dailyReturnMin: 0.4, dailyReturnMax: 0.6, monthlyReturnMin: 10.0, monthlyReturnMax: 15.0 },
        { minTHS: 50, maxTHS: 999999, dailyReturnMin: 0.5, dailyReturnMax: 0.7, monthlyReturnMin: 10.0, monthlyReturnMax: 15.0 },
      ],
      binaryBonusPercentage: 10.0,
      referralBonusPercentage: 5.0,
      maxBinaryPointsPerSide: 10, // Default value only
      binaryPointValue: 10.0,
      binaryMatchingEnabled: true,
      binaryMatchingSchedule: 'Weekly at 15:00 UTC',
      usdtDepositAddress: '',
      minDepositAmount: 10.0,
      maxDepositAmount: 10000.0,
      depositEnabled: true,
      minConfirmations: 1,
      depositFeePercentage: 0.0,

      // Tron Network Configuration
      tronNetwork: 'testnet',
      tronMainnetApiUrl: 'https://api.trongrid.io',
      tronTestnetApiUrl: 'https://api.shasta.trongrid.io',
      usdtMainnetContract: 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t',
      usdtTestnetContract: 'TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs',
      minWithdrawalAmount: 50.0,
      withdrawalFeeFixed: 3.0,
      withdrawalFeePercentage: 1.0,
      withdrawalProcessingDays: 3,
      platformFeePercentage: 1.0,
      registrationEnabled: true,
      kycRequired: true,
    };

    // Merge defaults with database settings, giving priority to database settings
    const finalSettings = { ...defaultSettings, ...settingsObject };

    return NextResponse.json({
      success: true,
      data: finalSettings,
    });

  } catch (error) {
    console.error('Admin settings fetch error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch settings' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);
    
    if (!authenticated || !user) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    const userIsAdmin = await isAdmin(user.id);
    if (!userIsAdmin) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    const rawSettings = await request.json();
    console.log('Received settings for update:', rawSettings);
    console.log('Received maxBinaryPointsPerSide:', rawSettings.maxBinaryPointsPerSide, typeof rawSettings.maxBinaryPointsPerSide);

    // Filter out database keys to prevent conflicts with frontend keys
    const databaseKeys = ['MAX_BINARY_POINTS_PER_SIDE', 'BINARY_POINT_VALUE', 'BINARY_MATCHING_ENABLED', 'BINARY_MATCHING_SCHEDULE', 'THS_PRICE', 'MINIMUM_PURCHASE', 'MAXIMUM_PURCHASE'];
    const settings = { ...rawSettings };
    databaseKeys.forEach(key => {
      if (settings[key]) {
        console.log(`Removing conflicting database key: ${key} = ${settings[key]}`);
        delete settings[key];
      }
    });

    console.log('Cleaned settings for processing:', {
      maxBinaryPointsPerSide: settings.maxBinaryPointsPerSide,
      binaryPointValue: settings.binaryPointValue,
      binaryMatchingEnabled: settings.binaryMatchingEnabled,
      binaryMatchingSchedule: settings.binaryMatchingSchedule
    });

    // Map frontend keys to database keys for settings that need specific database keys
    const keyMappings: { [key: string]: string } = {
      'maxBinaryPointsPerSide': 'MAX_BINARY_POINTS_PER_SIDE',
      'binaryPointValue': 'BINARY_POINT_VALUE',
      'binaryMatchingEnabled': 'BINARY_MATCHING_ENABLED',
      'binaryMatchingSchedule': 'BINARY_MATCHING_SCHEDULE',
      'thsPriceUSD': 'THS_PRICE',
      'minPurchaseAmount': 'MINIMUM_PURCHASE',
      'maxPurchaseAmount': 'MAXIMUM_PURCHASE',
    };

    // Update each setting with proper key mapping
    const updatePromises = Object.entries(settings).map(async ([key, value]) => {
      const dbKey = keyMappings[key] || key;

      // For settings that need specific database keys, store as simple string values (not JSON)
      if (keyMappings[key]) {
        console.log(`Mapping ${key} (${value}) → ${dbKey} (${String(value)})`);
        const result = await adminSettingsDb.set(dbKey, String(value), user.id);
        console.log(`Database update result for ${dbKey}:`, result);
        return result;
      } else {
        // For other settings, keep JSON stringify behavior
        return adminSettingsDb.set(dbKey, JSON.stringify(value), user.id);
      }
    });

    await Promise.all(updatePromises);
    console.log('All settings updates completed');

    // Log the admin action
    await systemLogDb.create({
      action: 'SYSTEM_SETTINGS_UPDATED',
      userId: user.id,
      details: { updatedSettings: Object.keys(settings) },
      ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
    });

    return NextResponse.json({
      success: true,
      message: 'Settings updated successfully',
    });

  } catch (error) {
    console.error('Admin settings update error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update settings' },
      { status: 500 }
    );
  }
}
