/**
 * 2FA Send OTP API Endpoint
 * Send two-factor authentication OTP via email
 */

import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { EmailTwoFactorAuth } from '@/lib/emailTwoFactorAuth';
import { 
  withSecureErrorHandling, 
  createAuthError,
  createSecureResponse 
} from '@/lib/secureErrorHandler';

// POST - Send 2FA OTP
const send2FAOTPHandler = async (request: NextRequest) => {
  const { authenticated, user } = await authenticateRequest(request);

  if (!authenticated || !user) {
    const error = createAuthError('Authentication required');
    return createSecureResponse(error);
  }

  // Send 2FA OTP
  const result = await EmailTwoFactorAuth.sendTwoFactorOTP(
    user.id,
    request.headers.get('x-forwarded-for') || 'unknown',
    request.headers.get('user-agent') || 'unknown'
  );

  if (!result.success) {
    return NextResponse.json(
      { success: false, error: result.error },
      { status: 400 }
    );
  }

  const response = NextResponse.json({
    success: true,
    message: 'Two-factor authentication code sent to your email',
    data: {
      expiresAt: result.expiresAt,
    },
  });

  // Add security headers
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-XSS-Protection', '1; mode=block');

  return response;
};

// Export the handler with secure error handling
export const POST = withSecureErrorHandling(send2FAOTPHandler, {
  endpoint: '/api/user/2fa/send-otp',
  requireAuth: true,
});
