/**
 * 2FA Status API Endpoint
 * Get two-factor authentication status for user
 */

import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { EmailTwoFactorAuth } from '@/lib/emailTwoFactorAuth';
import { 
  withSecureErrorHandling, 
  createAuthError,
  createSecureResponse 
} from '@/lib/secureErrorHandler';

// GET - Get 2FA status
const get2FAStatusHandler = async (request: NextRequest) => {
  const { authenticated, user } = await authenticateRequest(request);

  if (!authenticated || !user) {
    const error = createAuthError('Authentication required');
    return createSecureResponse(error);
  }

  // Get 2FA status
  const status = await EmailTwoFactorAuth.getStatus(user.id);

  const response = NextResponse.json({
    success: true,
    data: {
      enabled: status.enabled,
      enabledAt: status.enabledAt,
      method: 'email',
    },
  });

  // Add security headers
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-XSS-Protection', '1; mode=block');

  return response;
};

// Export the handler with secure error handling
export const GET = withSecureErrorHandling(get2FAStatusHandler, {
  endpoint: '/api/user/2fa/status',
  requireAuth: true,
});
