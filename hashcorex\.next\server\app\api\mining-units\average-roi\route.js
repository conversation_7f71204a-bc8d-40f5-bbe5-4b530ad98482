"use strict";(()=>{var e={};e.id=7189,e.ids=[7189],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31449:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>E,routeModule:()=>m,serverHooks:()=>f,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>g});var a={};t.r(a),t.d(a,{GET:()=>d,POST:()=>c});var n=t(96559),i=t(48088),s=t(37719),o=t(32190),l=t(39542),u=t(92731);async function c(e){try{let{authenticated:r,user:t}=await (0,l.b9)(e);if(!r||!t)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});let{thsAmounts:a}=await e.json();if(!Array.isArray(a)||0===a.length)return o.NextResponse.json({success:!1,error:"Invalid TH/s amounts array"},{status:400});let n={};for(let e of a)if("number"==typeof e&&!(e<=0))try{let r=await (0,u.F2)(e);n[e.toString()]=r}catch(r){console.error(`Error calculating average ROI for ${e} TH/s:`,r),n[e.toString()]=.4}return o.NextResponse.json({success:!0,data:n})}catch(e){return console.error("Average ROI calculation error:",e),o.NextResponse.json({success:!1,error:"Failed to calculate average ROI"},{status:500})}}async function d(e){try{let{authenticated:r,user:t}=await (0,l.b9)(e);if(!r||!t)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});let{searchParams:a}=new URL(e.url),n=parseFloat(a.get("thsAmount")||"0");if(n<=0)return o.NextResponse.json({success:!1,error:"Invalid TH/s amount"},{status:400});let i=await (0,u.F2)(n);return o.NextResponse.json({success:!0,data:{thsAmount:n,averageROI:i}})}catch(e){return console.error("Average ROI calculation error:",e),o.NextResponse.json({success:!1,error:"Failed to calculate average ROI"},{status:500})}}let m=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/mining-units/average-roi/route",pathname:"/api/mining-units/average-roi",filename:"route",bundlePath:"app/api/mining-units/average-roi/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\mining-units\\average-roi\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:p,workUnitAsyncStorage:g,serverHooks:f}=m;function E(){return(0,s.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:g})}},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37366:e=>{e.exports=require("dns")},39542:(e,r,t)=>{t.d(r,{b9:()=>y,HU:()=>f,qc:()=>_,Lx:()=>h,DY:()=>I,DT:()=>S});var a=t(85663),n=t(43205),i=t.n(n),s=t(6710),o=t(45697);let l=o.z.object({DATABASE_URL:o.z.string().url("DATABASE_URL must be a valid PostgreSQL URL"),DIRECT_URL:o.z.string().url("DIRECT_URL must be a valid PostgreSQL URL"),JWT_SECRET:o.z.string().min(32,"JWT_SECRET must be at least 32 characters long").refine(e=>{let r=/[A-Z]/.test(e),t=/[a-z]/.test(e),a=/\d/.test(e),n=/[!@#$%^&*(),.?":{}|<>]/.test(e);return r&&t&&a&&n},"JWT_SECRET should contain uppercase, lowercase, numbers, and special characters"),JWT_EXPIRES_IN:o.z.string().default("30d"),NODE_ENV:o.z.enum(["development","production","test"]).default("development"),NEXT_PUBLIC_APP_URL:o.z.string().url().optional(),PORT:o.z.string().regex(/^\d+$/).transform(Number).default("3000"),SMTP_HOST:o.z.string().optional(),SMTP_PORT:o.z.string().regex(/^\d+$/).transform(Number).optional(),SMTP_USER:o.z.string().email().optional(),SMTP_PASSWORD:o.z.string().optional(),SMTP_SECURE:o.z.string().transform(e=>"true"===e).optional(),TRON_NETWORK:o.z.enum(["mainnet","testnet"]).default("testnet"),TRON_API_KEY:o.z.string().optional(),USDT_CONTRACT_ADDRESS:o.z.string().optional(),MAX_FILE_SIZE:o.z.string().regex(/^\d+$/).transform(Number).default("10485760"),UPLOAD_DIR:o.z.string().default("./public/uploads"),BCRYPT_ROUNDS:o.z.string().regex(/^\d+$/).transform(Number).default("12"),SESSION_TIMEOUT:o.z.string().regex(/^\d+$/).transform(Number).default("1800"),RATE_LIMIT_WINDOW_MS:o.z.string().regex(/^\d+$/).transform(Number).default("900000"),RATE_LIMIT_MAX_REQUESTS:o.z.string().regex(/^\d+$/).transform(Number).default("100"),COINGECKO_API_URL:o.z.string().url().default("https://api.coingecko.com/api/v3"),LOG_LEVEL:o.z.enum(["error","warn","info","debug"]).default("info"),ENABLE_REQUEST_LOGGING:o.z.string().transform(e=>"true"===e).default("false"),ENABLE_REGISTRATION:o.z.string().transform(e=>"true"===e).default("true"),ENABLE_KYC:o.z.string().transform(e=>"true"===e).default("true"),ENABLE_WITHDRAWALS:o.z.string().transform(e=>"true"===e).default("true")}).refine(e=>{let r=[e.SMTP_HOST,e.SMTP_PORT,e.SMTP_USER,e.SMTP_PASSWORD],t=r.some(e=>void 0!==e),a=r.every(e=>void 0!==e);return!t||!!a},{message:"If SMTP configuration is provided, all SMTP fields (HOST, PORT, USER, PASSWORD) must be provided"});function u(){try{let e=l.safeParse(process.env);if(!e.success){let r=e.error.errors.map(e=>`${e.path.join(".")}: ${e.message}`);return{success:!1,errors:r}}return{success:!0,data:e.data}}catch(e){return{success:!1,errors:["Failed to validate environment variables"]}}}let c=null;function d(){if(!c){let e=u();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),c=e.data,console.log("✅ Environment variables validated successfully")}return c}let m={jwt:{secret:()=>d().JWT_SECRET,expiresIn:()=>d().JWT_EXPIRES_IN},security:{bcryptRounds:()=>d().BCRYPT_ROUNDS,sessionTimeout:()=>d().SESSION_TIMEOUT,maxFileSize:()=>d().MAX_FILE_SIZE,uploadDir:()=>d().UPLOAD_DIR}};if("phase-production-build"!==process.env.NEXT_PHASE){let e=u();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),console.log("✅ Environment variables validated successfully")}let p=async e=>await a.Ay.hash(e,m.security.bcryptRounds()),g=async(e,r)=>await a.Ay.compare(e,r),f=e=>i().sign(e,m.jwt.secret(),{expiresIn:m.jwt.expiresIn()}),E=e=>{try{return i().verify(e,m.jwt.secret())}catch(e){return null}},R=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",r="HC";for(let t=0;t<8;t++)r+=e.charAt(Math.floor(Math.random()*e.length));return r},y=async e=>{let r=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!r)return{authenticated:!1,user:null};let t=E(r);if(!t)return{authenticated:!1,user:null};let a=await s.Gy.findByEmail(t.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},I=async e=>{let r,a;if(await s.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let t=await s.Gy.findByReferralId(e.referralCode);if(!t)throw Error("Invalid referral code");r=t.id}let n=await p(e.password),i=!1;do a=R(),i=!await s.Gy.findByReferralId(a);while(!i);let o=await s.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:n,referralId:a});if(r){let{placeUserByReferralType:a}=await t.e(2746).then(t.bind(t,2746)),n="general";"left"===e.placementSide?n="left":"right"===e.placementSide&&(n="right"),await a(r,o.id,n)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},h=async e=>{let r=await s.Gy.findByEmail(e.email);if(!r||!await g(e.password,r.password))throw Error("Invalid email or password");return{token:f({userId:r.id,email:r.email}),user:{id:r.id,email:r.email,referralId:r.referralId,kycStatus:r.kycStatus}}},S=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),_=async e=>{let r=await s.Gy.findById(e);return r?.role==="ADMIN"}},39794:(e,r,t)=>{t.d(r,{Py:()=>s,k8:()=>u,kp:()=>l});var a=t(31183),n=t(6710);async function i(e){return await a.prisma.miningUnit.findMany({where:{userId:e,status:"ACTIVE",expiryDate:{gt:new Date}},orderBy:{createdAt:"asc"}})}async function s(e,r,t,s,l){let u=await i(e);if(0===u.length)throw Error("No active mining units found for earnings allocation");let c=[],d=r;for(let e of u){var m;if(d<=0)break;let r=Math.max(0,5*(m=e).investmentAmount-(m.miningEarnings+m.referralEarnings+m.binaryEarnings));if(r<=0)continue;let n=Math.min(d,r);if(n>0){let i={};switch(t){case"MINING_EARNINGS":i.miningEarnings={increment:n};break;case"DIRECT_REFERRAL":i.referralEarnings={increment:n};break;case"BINARY_BONUS":i.binaryEarnings={increment:n}}i.totalEarned={increment:n},await a.prisma.miningUnit.update({where:{id:e.id},data:i}),await a.prisma.miningUnitEarningsAllocation.create({data:{miningUnitId:e.id,transactionId:s,earningType:t,amount:n,description:l}}),c.push({miningUnitId:e.id,amount:n,remainingCapacity:r-n}),d-=n;let u=await a.prisma.miningUnit.findUnique({where:{id:e.id}});u&&function(e){let r=5*e.investmentAmount;return e.miningEarnings+e.referralEarnings+e.binaryEarnings>=r}(u)&&await o(e.id,"5x_investment_reached")}}let p=r-d,g=d;return d>0&&(console.warn(`Unable to allocate ${d} to mining units - all units at capacity. Amount discarded.`),await n.AJ.create({action:"EARNINGS_ALLOCATION_OVERFLOW",userId:e,details:{totalAmount:r,allocatedAmount:p,overflowAmount:g,earningType:t,reason:"all_units_at_capacity",note:"Excess amount discarded as per mining unit capacity limits"}})),{allocations:c,totalAllocated:p,totalDiscarded:g,allocationSuccess:0===g}}async function o(e,r){let i=await a.prisma.miningUnit.findUnique({where:{id:e},include:{user:{select:{email:!0,firstName:!0,lastName:!0}}}});if(!i)throw Error(`Mining unit ${e} not found`);await a.prisma.miningUnit.update({where:{id:e},data:{status:"EXPIRED"}});let s=i.miningEarnings+i.referralEarnings+i.binaryEarnings;await n.AJ.create({action:"MINING_UNIT_EXPIRED",userId:i.userId,details:{miningUnitId:e,reason:r,totalEarned:s,miningEarnings:i.miningEarnings,referralEarnings:i.referralEarnings,binaryEarnings:i.binaryEarnings,investmentAmount:i.investmentAmount,multiplier:s/i.investmentAmount}});try{let{emailNotificationService:e}=await Promise.all([t.e(9526),t.e(3161)]).then(t.bind(t,13161));await e.sendMiningUnitExpiryNotification({userId:i.userId,email:i.user.email,firstName:i.user.firstName,lastName:i.user.lastName,thsAmount:i.thsAmount,investmentAmount:i.investmentAmount,totalEarned:s,purchaseDate:i.createdAt.toISOString(),expiryDate:i.expiryDate.toISOString(),expiryReason:"24_months_reached"===r?"TIME_LIMIT":"RETURN_LIMIT"})}catch(e){console.error("Failed to send mining unit expiry email:",e)}console.log(`Mining unit ${e} expired due to ${r}. Total earnings: ${s}`)}async function l(e){return await a.prisma.miningUnit.findMany({where:{userId:e},orderBy:{createdAt:"asc"}})}async function u(e){return await a.prisma.miningUnitEarningsAllocation.findMany({where:{miningUnitId:e},include:{transaction:!0},orderBy:{allocatedAt:"desc"}})}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,580,7911,925,5112],()=>t(31449));module.exports=a})();