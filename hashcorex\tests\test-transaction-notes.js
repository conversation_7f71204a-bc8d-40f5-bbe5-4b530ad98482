/**
 * Test Script for Transaction Notes with 5x Limit Cap
 *
 * This script tests that transactions properly show notes when amounts are
 * reduced due to the 5x mining unit limit cap.
 *
 * Usage: node tests/test-transaction-notes.js
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function testTransactionNotes() {
  console.log('🧪 Testing Transaction Notes for 5x Limit Cap...\n');

  try {
    // Create a test user
    const testUser = await prisma.user.create({
      data: {
        email: `test-transaction-${Date.now()}@example.com`,
        firstName: 'Test',
        lastName: 'User',
        password: 'hashedpassword',
        isActive: true,
        emailVerified: true,
      },
    });

    console.log(`✅ Created test user: ${testUser.email}`);

    // Create a mining unit near capacity (5x = $500, current = $450, remaining = $50)
    const miningUnit = await prisma.miningUnit.create({
      data: {
        userId: testUser.id,
        thsAmount: 10,
        investmentAmount: 100,
        dailyROI: 0.5,
        startDate: new Date(),
        expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
        status: 'ACTIVE',
        miningEarnings: 200,
        referralEarnings: 150,
        binaryEarnings: 100, // Total: $450, Remaining capacity: $50
        totalEarned: 450,
      },
    });

    console.log(`✅ Created mining unit with $450 earned (capacity remaining: $50)`);

    // Test 1: Create a direct referral transaction that exceeds capacity
    const fullBonusAmount = 100; // $100 bonus
    const expectedAllocated = 50; // Only $50 can be allocated
    const expectedDiscarded = 50; // $50 will be discarded

    // Create transaction (this would normally be done by the referral system)
    const transaction = await prisma.transaction.create({
      data: {
        userId: testUser.id,
        type: 'DIRECT_REFERRAL',
        amount: fullBonusAmount,
        description: 'Direct referral bonus (10% of $1000) - First purchase',
        status: 'COMPLETED',
      },
    });

    console.log(`✅ Created transaction with full amount: $${fullBonusAmount}`);

    // Simulate the allocation logic (simplified version)
    const remainingCapacity = 500 - 450; // $50
    const actualAllocated = Math.min(fullBonusAmount, remainingCapacity);
    const actualDiscarded = fullBonusAmount - actualAllocated;

    // Update transaction to reflect actual allocation and add note
    if (actualDiscarded > 0) {
      await prisma.transaction.update({
        where: { id: transaction.id },
        data: {
          amount: actualAllocated,
          description: `Direct referral bonus (10% of $1000) - First purchase. Note: $${actualDiscarded.toFixed(2)} excess discarded due to 5x mining unit limit cap.`,
        },
      });

      console.log(`✅ Updated transaction amount to $${actualAllocated} with note about $${actualDiscarded} discarded`);
    }

    // Verify the transaction
    const updatedTransaction = await prisma.transaction.findUnique({
      where: { id: transaction.id },
    });

    console.log('\n📊 TRANSACTION VERIFICATION:');
    console.log(`   Original Amount: $${fullBonusAmount}`);
    console.log(`   Final Amount: $${updatedTransaction.amount}`);
    console.log(`   Description: ${updatedTransaction.description}`);

    // Check if the note is properly included
    const hasNote = updatedTransaction.description.includes('excess discarded due to 5x mining unit limit cap');
    const correctAmount = updatedTransaction.amount === expectedAllocated;

    if (hasNote && correctAmount) {
      console.log('\n🎉 TEST PASSED! 🎉');
      console.log('✅ Transaction amount correctly reduced to allocated amount');
      console.log('✅ Transaction description includes clear note about discarded excess');
      console.log('✅ Users will now see exactly what happened to their transaction');
    } else {
      console.log('\n❌ TEST FAILED!');
      if (!hasNote) console.log('❌ Missing note about discarded excess');
      if (!correctAmount) console.log('❌ Transaction amount not correctly updated');
    }

    // Cleanup
    await prisma.transaction.delete({ where: { id: transaction.id } });
    await prisma.miningUnit.delete({ where: { id: miningUnit.id } });
    await prisma.user.delete({ where: { id: testUser.id } });

    console.log('\n🧹 Cleanup completed');

  } catch (error) {
    console.error('❌ Test failed with error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testTransactionNotes();
