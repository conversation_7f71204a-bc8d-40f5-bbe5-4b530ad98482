"use strict";(()=>{var e={};e.id=7758,e.ids=[1111,7758],e.modules={812:(e,t,r)=>{r.d(t,{SL:()=>n});var a=r(31183),s=r(6710),i=r(21111);class n{static async isEnabled(e){let t=await a.prisma.user.findUnique({where:{id:e},select:{twoFactorEnabled:!0}});return t?.twoFactorEnabled||!1}static async getStatus(e){let t=await a.prisma.user.findUnique({where:{id:e},select:{twoFactorEnabled:!0,twoFactorEnabledAt:!0}});return{enabled:t?.twoFactorEnabled||!1,enabledAt:t?.twoFactorEnabledAt||void 0}}static async enableTwoFactor(e,t,r){let i=await a.prisma.user.findUnique({where:{id:e},select:{id:!0,email:!0,firstName:!0,twoFactorEnabled:!0}});return i?i.twoFactorEnabled?{success:!1,error:"2FA is already enabled"}:(await a.prisma.user.update({where:{id:e},data:{twoFactorEnabled:!0,twoFactorEnabledAt:new Date}}),await s.AJ.create({action:"EMAIL_TWO_FACTOR_ENABLED",userId:e,details:{email:i.email,enabledAt:new Date().toISOString()},ipAddress:t||"unknown",userAgent:r||"unknown"}),{success:!0}):{success:!1,error:"User not found"}}static async disableTwoFactor(e,t,r){let i=await a.prisma.user.findUnique({where:{id:e},select:{id:!0,email:!0,twoFactorEnabled:!0}});return i?i.twoFactorEnabled?(await a.prisma.user.update({where:{id:e},data:{twoFactorEnabled:!1,twoFactorEnabledAt:null}}),await s.AJ.create({action:"EMAIL_TWO_FACTOR_DISABLED",userId:e,details:{email:i.email,disabledAt:new Date().toISOString()},ipAddress:t||"unknown",userAgent:r||"unknown"}),{success:!0}):{success:!1,error:"2FA is not enabled"}:{success:!1,error:"User not found"}}static async sendTwoFactorOTP(e,t,r){let n=await a.prisma.user.findUnique({where:{id:e},select:{id:!0,email:!0,firstName:!0,twoFactorEnabled:!0}});if(!n)return{success:!1,error:"User not found"};if(!n.twoFactorEnabled)return{success:!1,error:"2FA is not enabled for this user"};let o=(0,i.X)(),l=new Date;l.setMinutes(l.getMinutes()+10),await s.oV.create({email:n.email,otp:o,purpose:"two_factor_auth",expiresAt:l});try{if(await i.emailService.sendOTPEmail(n.email,o,n.firstName,"two_factor_auth"))return await s.AJ.create({action:"EMAIL_TWO_FACTOR_OTP_SENT",userId:e,details:{email:n.email,sentAt:new Date().toISOString(),expiresAt:l.toISOString()},ipAddress:t||"unknown",userAgent:r||"unknown"}),{success:!0,expiresAt:l};return{success:!1,error:"Failed to send 2FA code. Please try again."}}catch(e){return console.error("Failed to send 2FA OTP:",e),{success:!1,error:"Failed to send 2FA code. Please try again."}}}static async verifyTwoFactorOTP(e,t,r,i){let n=await a.prisma.user.findUnique({where:{id:e},select:{id:!0,email:!0,twoFactorEnabled:!0}});if(!n||!n.twoFactorEnabled)return{valid:!1};let o=await s.oV.findValid(n.email,"two_factor_auth");return o?o.otp!==t?(await s.AJ.create({action:"EMAIL_TWO_FACTOR_VERIFICATION_FAILED",userId:e,details:{reason:"Invalid OTP",failedAt:new Date().toISOString()},ipAddress:r||"unknown",userAgent:i||"unknown"}),{valid:!1}):(await s.oV.markAsVerified(o.id),await s.AJ.create({action:"EMAIL_TWO_FACTOR_VERIFIED",userId:e,details:{email:n.email,verifiedAt:new Date().toISOString()},ipAddress:r||"unknown",userAgent:i||"unknown"}),{valid:!0}):(await s.AJ.create({action:"EMAIL_TWO_FACTOR_VERIFICATION_FAILED",userId:e,details:{reason:"No valid OTP found",failedAt:new Date().toISOString()},ipAddress:r||"unknown",userAgent:i||"unknown"}),{valid:!1})}static async requiresTwoFactorVerification(e){return await this.isEnabled(e)}static async cleanupExpiredOTPs(){try{return(await a.prisma.oTP.deleteMany({where:{purpose:"two_factor_auth",expiresAt:{lt:new Date}}})).count}catch(e){return console.error("Failed to cleanup expired 2FA OTPs:",e),0}}}setInterval(async function e(){let e=await n.cleanupExpiredOTPs();e>0&&console.log(`Cleaned up ${e} expired 2FA OTPs`)},18e5)},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15501:(e,t,r)=>{r.d(t,{FH:()=>d,HJ:()=>l,JR:()=>u,LH:()=>p,Zb:()=>o,c4:()=>m});let a=new Map,s={LOGIN:{windowMs:9e5,maxRequests:5,skipSuccessfulRequests:!0},REGISTER:{windowMs:36e5,maxRequests:3},FILE_UPLOAD:{windowMs:6e4,maxRequests:5}},i=[0,1e3,5e3,15e3,6e4,3e5,9e5];function n(e,t,r="general"){let s=function(e,t,r){if(r)return`${t}:${r(e)}`;let a=e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||"unknown";return`${t}:${a}`}(e,r,t.keyGenerator),o=Date.now(),l=a.get(s);if(l?.blockedUntil&&o<l.blockedUntil)return{allowed:!1,remaining:0,resetTime:l.resetTime,retryAfter:Math.ceil((l.blockedUntil-o)/1e3)};if((!l||o>l.resetTime)&&(l={count:0,resetTime:o+t.windowMs},a.set(s,l)),l.count>=t.maxRequests){let e=Math.min(l.count-t.maxRequests,i.length-1),r=i[e];return r>0&&(l.blockedUntil=o+r,a.set(s,l)),{allowed:!1,remaining:0,resetTime:l.resetTime,retryAfter:r>0?Math.ceil(r/1e3):Math.ceil((l.resetTime-o)/1e3)}}return l.count++,a.set(s,l),{allowed:!0,remaining:t.maxRequests-l.count,resetTime:l.resetTime}}let o=e=>n(e,s.LOGIN,"login"),l=e=>n(e,s.REGISTER,"register"),u=e=>n(e,s.FILE_UPLOAD,"upload"),c=new Map;function d(e){let t=c.get(e),r=Date.now();return t&&t.lockedUntil&&r<t.lockedUntil?{locked:!0,remainingTime:Math.ceil((t.lockedUntil-r)/1e3)}:{locked:!1}}function m(e){let t=Date.now(),r=c.get(e)||{attempts:0,lastAttempt:0};if(t-r.lastAttempt>36e5&&(r.attempts=0),r.attempts++,r.lastAttempt=t,r.attempts>=5){let e=[15,30,60,120,240],a=Math.min(Math.floor(r.attempts/5)-1,e.length-1);r.lockedUntil=t+60*e[a]*1e3}c.set(e,r)}function p(e){c.delete(e)}setInterval(()=>{!function(){let e=Date.now();for(let[t,r]of a.entries())e>r.resetTime&&(!r.blockedUntil||e>r.blockedUntil)&&a.delete(t)}(),function(){let e=Date.now();for(let[t,r]of c.entries())r.lockedUntil&&e>r.lockedUntil&&(r.attempts=0,r.lockedUntil=void 0,c.set(t,r))}()},3e5)},21111:(e,t,r)=>{r.d(t,{X:()=>o,emailService:()=>n});var a=r(49526),s=r(6710);class i{async getEmailConfig(){try{let e=await s.T8.getEmailSettings();if(!e||!e.smtpHost||!e.smtpUser||!e.smtpPassword)return console.warn("Email configuration not found or incomplete"),null;return{host:e.smtpHost,port:e.smtpPort||587,secure:e.smtpSecure||!1,user:e.smtpUser,password:e.smtpPassword,fromName:e.fromName||"HashCoreX",fromEmail:e.fromEmail||e.smtpUser}}catch(e){return console.error("Failed to get email configuration:",e),null}}async initializeTransporter(e=!1){try{if(this.config=await this.getEmailConfig(),!this.config)return console.warn("Email configuration not available - email service disabled"),!1;if(!this.config.host||!this.config.user||!this.config.password)return console.error("Email configuration incomplete - missing host, user, or password"),!1;if(this.transporter=a.createTransport({host:this.config.host,port:this.config.port,secure:this.config.secure,auth:{user:this.config.user,pass:this.config.password},tls:{rejectUnauthorized:!1},connectionTimeout:1e4,greetingTimeout:5e3,socketTimeout:1e4}),e)console.log("Email transporter initialized (verification skipped)");else{let e=this.transporter.verify(),t=new Promise((e,t)=>setTimeout(()=>t(Error("Email verification timeout")),15e3));await Promise.race([e,t]),console.log("Email transporter initialized and verified successfully")}return!0}catch(e){return console.error("Failed to initialize email transporter:",e),this.transporter=null,this.config=null,!1}}async sendEmail(e){try{if((!this.transporter||!this.config)&&(console.log("Email transporter not initialized, attempting to initialize..."),!await this.initializeTransporter(!0)))return console.warn("Email service not configured - skipping email send"),!1;if(!e.to||!e.subject)return console.error("Invalid email data - missing recipient or subject"),!1;let t={from:`"${this.config.fromName}" <${this.config.fromEmail}>`,to:e.to,subject:e.subject,html:e.html,text:e.text},r=this.transporter.sendMail(t),a=new Promise((e,t)=>setTimeout(()=>t(Error("Email send timeout")),3e4)),s=await Promise.race([r,a]);return console.log("Email sent successfully:",s.messageId),!0}catch(e){return console.error("Failed to send email:",e),this.transporter=null,this.config=null,!1}}async sendOTPEmail(e,t,r,a="email_verification"){let s="otp_verification";"password_reset"===a?s="password_reset_otp":"two_factor_auth"===a&&(s="two_factor_otp");let i=await this.getEmailTemplate(s);if(!i)return console.error(`Email template '${s}' not found. Please ensure email templates are seeded.`),!1;let n=i.htmlContent,o=i.textContent||"";return n=(n=n.replace(/{{firstName}}/g,r||"User")).replace(/{{otp}}/g,t),o=(o=o.replace(/{{firstName}}/g,r||"User")).replace(/{{otp}}/g,t),await this.sendEmail({to:e,subject:i.subject,html:n,text:o})}async getEmailTemplate(e){try{return await s.T8.getEmailTemplate(e)}catch(e){return console.error("Failed to get email template:",e),null}}async testConnection(){try{if(this.config=await this.getEmailConfig(),!this.config)return console.error("Email configuration not found or incomplete"),!1;return this.transporter=a.createTransport({host:this.config.host,port:this.config.port,secure:this.config.secure,auth:{user:this.config.user,pass:this.config.password},tls:{rejectUnauthorized:!1}}),await this.transporter.verify(),console.log("Email connection test successful"),!0}catch(e){throw console.error("Email connection test failed:",e),this.transporter=null,e}}constructor(){this.transporter=null,this.config=null}}let n=new i,o=()=>Math.floor(1e5+9e5*Math.random()).toString()},21820:e=>{e.exports=require("os")},24005:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>p,serverHooks:()=>g,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>w});var a={};r.r(a),r.d(a,{POST:()=>m});var s=r(96559),i=r(48088),n=r(37719),o=r(32190),l=r(39542),u=r(6710),c=r(15501),d=r(812);async function m(e){try{let t=(0,c.Zb)(e);if(!t.allowed){let e=o.NextResponse.json({success:!1,error:"Too many login attempts. Please try again later.",retryAfter:t.retryAfter},{status:429});return e.headers.set("X-RateLimit-Limit","5"),e.headers.set("X-RateLimit-Remaining",t.remaining.toString()),e.headers.set("X-RateLimit-Reset",new Date(t.resetTime).toISOString()),t.retryAfter&&e.headers.set("Retry-After",t.retryAfter.toString()),e}let{email:r,password:a}=await e.json();if(!r||!a)return o.NextResponse.json({success:!1,error:"Email and password are required"},{status:400});if(!(0,l.DT)(r))return o.NextResponse.json({success:!1,error:"Invalid email format"},{status:400});let s=(0,c.FH)(r.toLowerCase());if(s.locked)return o.NextResponse.json({success:!1,error:`Account temporarily locked due to multiple failed attempts. Try again in ${Math.ceil((s.remainingTime||0)/60)} minutes.`,lockedUntil:s.remainingTime},{status:423});let i=await (0,l.Lx)({email:r.toLowerCase(),password:a});if((0,c.LH)(r.toLowerCase()),await d.SL.isEnabled(i.user.id)){let t=await d.SL.sendTwoFactorOTP(i.user.id,e.headers.get("x-forwarded-for")||"unknown",e.headers.get("user-agent")||"unknown");if(!t.success)return o.NextResponse.json({success:!1,error:"Failed to send 2FA code. Please try again."},{status:500});await u.AJ.create({action:"USER_LOGIN_PENDING_2FA",userId:i.user.id,details:{email:i.user.email,loginTime:new Date().toISOString(),requires2FA:!0,method:"email"},ipAddress:e.headers.get("x-forwarded-for")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"});let r=o.NextResponse.json({success:!0,message:"Login successful. A verification code has been sent to your email.",data:{requires2FA:!0,email:i.user.email,method:"email",expiresAt:t.expiresAt}});return r.headers.set("X-Content-Type-Options","nosniff"),r.headers.set("X-Frame-Options","DENY"),r.headers.set("X-XSS-Protection","1; mode=block"),r}await u.AJ.create({action:"USER_LOGIN_SUCCESS",userId:i.user.id,details:{email:i.user.email,loginTime:new Date().toISOString(),userAgent:e.headers.get("user-agent")||"unknown",ipAddress:e.headers.get("x-forwarded-for")||"unknown"},ipAddress:e.headers.get("x-forwarded-for")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"});let n=o.NextResponse.json({success:!0,message:"Login successful",data:{user:{id:i.user.id,email:i.user.email,referralId:i.user.referralId,kycStatus:i.user.kycStatus,role:i.user.role}}});return n.cookies.set("auth-token",i.token,{httpOnly:!0,secure:!0,sameSite:"strict",maxAge:2592e3,path:"/"}),n.headers.set("X-Content-Type-Options","nosniff"),n.headers.set("X-Frame-Options","DENY"),n.headers.set("X-XSS-Protection","1; mode=block"),n}catch(a){console.error("Login error:",a);let t="unknown";try{t=(await e.json()).email||"unknown"}catch{}"unknown"!==t&&(0,l.DT)(t)&&(0,c.c4)(t.toLowerCase());try{await u.AJ.create({action:"USER_LOGIN_FAILED",details:{email:t,error:"Authentication failed",timestamp:new Date().toISOString(),userAgent:e.headers.get("user-agent")||"unknown",ipAddress:e.headers.get("x-forwarded-for")||"unknown"},ipAddress:e.headers.get("x-forwarded-for")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"})}catch(e){console.error("Failed to log login attempt:",e)}let r=o.NextResponse.json({success:!1,error:"Invalid email or password"},{status:401});return r.headers.set("X-Content-Type-Options","nosniff"),r.headers.set("X-Frame-Options","DENY"),r.headers.set("X-XSS-Protection","1; mode=block"),r}}let p=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/auth/login/route",pathname:"/api/auth/login",filename:"route",bundlePath:"app/api/auth/login/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\auth\\login\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:f,workUnitAsyncStorage:w,serverHooks:g}=p;function h(){return(0,n.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:w})}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37366:e=>{e.exports=require("dns")},39542:(e,t,r)=>{r.d(t,{b9:()=>E,HU:()=>w,qc:()=>_,Lx:()=>A,DY:()=>T,DT:()=>S});var a=r(85663),s=r(43205),i=r.n(s),n=r(6710),o=r(45697);let l=o.z.object({DATABASE_URL:o.z.string().url("DATABASE_URL must be a valid PostgreSQL URL"),DIRECT_URL:o.z.string().url("DIRECT_URL must be a valid PostgreSQL URL"),JWT_SECRET:o.z.string().min(32,"JWT_SECRET must be at least 32 characters long").refine(e=>{let t=/[A-Z]/.test(e),r=/[a-z]/.test(e),a=/\d/.test(e),s=/[!@#$%^&*(),.?":{}|<>]/.test(e);return t&&r&&a&&s},"JWT_SECRET should contain uppercase, lowercase, numbers, and special characters"),JWT_EXPIRES_IN:o.z.string().default("30d"),NODE_ENV:o.z.enum(["development","production","test"]).default("development"),NEXT_PUBLIC_APP_URL:o.z.string().url().optional(),PORT:o.z.string().regex(/^\d+$/).transform(Number).default("3000"),SMTP_HOST:o.z.string().optional(),SMTP_PORT:o.z.string().regex(/^\d+$/).transform(Number).optional(),SMTP_USER:o.z.string().email().optional(),SMTP_PASSWORD:o.z.string().optional(),SMTP_SECURE:o.z.string().transform(e=>"true"===e).optional(),TRON_NETWORK:o.z.enum(["mainnet","testnet"]).default("testnet"),TRON_API_KEY:o.z.string().optional(),USDT_CONTRACT_ADDRESS:o.z.string().optional(),MAX_FILE_SIZE:o.z.string().regex(/^\d+$/).transform(Number).default("10485760"),UPLOAD_DIR:o.z.string().default("./public/uploads"),BCRYPT_ROUNDS:o.z.string().regex(/^\d+$/).transform(Number).default("12"),SESSION_TIMEOUT:o.z.string().regex(/^\d+$/).transform(Number).default("1800"),RATE_LIMIT_WINDOW_MS:o.z.string().regex(/^\d+$/).transform(Number).default("900000"),RATE_LIMIT_MAX_REQUESTS:o.z.string().regex(/^\d+$/).transform(Number).default("100"),COINGECKO_API_URL:o.z.string().url().default("https://api.coingecko.com/api/v3"),LOG_LEVEL:o.z.enum(["error","warn","info","debug"]).default("info"),ENABLE_REQUEST_LOGGING:o.z.string().transform(e=>"true"===e).default("false"),ENABLE_REGISTRATION:o.z.string().transform(e=>"true"===e).default("true"),ENABLE_KYC:o.z.string().transform(e=>"true"===e).default("true"),ENABLE_WITHDRAWALS:o.z.string().transform(e=>"true"===e).default("true")}).refine(e=>{let t=[e.SMTP_HOST,e.SMTP_PORT,e.SMTP_USER,e.SMTP_PASSWORD],r=t.some(e=>void 0!==e),a=t.every(e=>void 0!==e);return!r||!!a},{message:"If SMTP configuration is provided, all SMTP fields (HOST, PORT, USER, PASSWORD) must be provided"});function u(){try{let e=l.safeParse(process.env);if(!e.success){let t=e.error.errors.map(e=>`${e.path.join(".")}: ${e.message}`);return{success:!1,errors:t}}return{success:!0,data:e.data}}catch(e){return{success:!1,errors:["Failed to validate environment variables"]}}}let c=null;function d(){if(!c){let e=u();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),c=e.data,console.log("✅ Environment variables validated successfully")}return c}let m={jwt:{secret:()=>d().JWT_SECRET,expiresIn:()=>d().JWT_EXPIRES_IN},security:{bcryptRounds:()=>d().BCRYPT_ROUNDS,sessionTimeout:()=>d().SESSION_TIMEOUT,maxFileSize:()=>d().MAX_FILE_SIZE,uploadDir:()=>d().UPLOAD_DIR}};if("phase-production-build"!==process.env.NEXT_PHASE){let e=u();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),console.log("✅ Environment variables validated successfully")}let p=async e=>await a.Ay.hash(e,m.security.bcryptRounds()),f=async(e,t)=>await a.Ay.compare(e,t),w=e=>i().sign(e,m.jwt.secret(),{expiresIn:m.jwt.expiresIn()}),g=e=>{try{return i().verify(e,m.jwt.secret())}catch(e){return null}},h=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},E=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=g(t);if(!r)return{authenticated:!1,user:null};let a=await n.Gy.findByEmail(r.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},T=async e=>{let t,a;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await n.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let s=await p(e.password),i=!1;do a=h(),i=!await n.Gy.findByReferralId(a);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(t){let{placeUserByReferralType:a}=await r.e(2746).then(r.bind(r,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(t,o.id,s)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},A=async e=>{let t=await n.Gy.findByEmail(e.email);if(!t||!await f(e.password,t.password))throw Error("Invalid email or password");return{token:w({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},S=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),_=async e=>{let t=await n.Gy.findById(e);return t?.role==="ADMIN"}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580,7911,9526,925],()=>r(24005));module.exports=a})();