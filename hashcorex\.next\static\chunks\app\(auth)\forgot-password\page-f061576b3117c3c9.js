(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5890],{913:(e,s,t)=>{Promise.resolve().then(t.bind(t,9540))},9540:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>y});var a=t(5155),r=t(2115),l=t(6874),d=t.n(l),i=t(5695),n=t(8505),c=t(7508),o=t(1264),m=t(7550),x=t(5196),h=t(8749),u=t(2657),p=t(9434),b=t(1470),j=t(3997);function w(){let e=(0,i.useRouter)(),[s,t]=(0,r.useState)("email"),[l,b]=(0,r.useState)(""),[w,y]=(0,r.useState)(""),[g,f]=(0,r.useState)(""),[N,v]=(0,r.useState)(!1),[P,S]=(0,r.useState)(!1),[C,O]=(0,r.useState)(!1),[k,T]=(0,r.useState)(""),[F,A]=(0,r.useState)(""),R=(0,p.Oj)(w),_=async e=>{e.preventDefault(),T(""),O(!0);try{let e=await fetch("/api/auth/send-otp",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:l,purpose:"password_reset"})}),s=await e.json();s.success?t("otp"):T(s.error||"Failed to send reset code")}catch(e){T("Failed to send reset code. Please try again.")}finally{O(!1)}},E=async e=>{T(""),O(!0);try{let s=await fetch("/api/auth/verify-otp",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:l,otp:e,purpose:"password_reset"})}),a=await s.json();a.success?t("password"):T(a.error||"Invalid OTP")}catch(e){T("Failed to verify OTP. Please try again.")}finally{O(!1)}},q=async()=>{T(""),O(!0);try{let e=await fetch("/api/auth/send-otp",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:l,purpose:"password_reset"})}),s=await e.json();s.success||T(s.error||"Failed to resend code")}catch(e){T("Failed to resend code. Please try again.")}finally{O(!1)}},J=async s=>{if(s.preventDefault(),T(""),w!==g)return void T("Passwords do not match");if(!R.isValid)return void T("Please ensure your password meets all requirements");O(!0);try{let s=await fetch("/api/auth/reset-password",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:l,newPassword:w,confirmPassword:g})}),t=await s.json();t.success?(A("Password reset successful! Redirecting to dashboard..."),setTimeout(()=>{e.push("/dashboard")},2e3)):T(t.error||"Failed to reset password")}catch(e){T("Failed to reset password. Please try again.")}finally{O(!1)}};return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"w-full max-w-lg",children:[(0,a.jsx)("div",{className:"text-center mb-8",children:(0,a.jsxs)(d(),{href:"/",className:"inline-flex items-center justify-center mb-6",children:[(0,a.jsx)(c.MX,{className:"h-8 w-8 text-yellow-500 mr-2"}),(0,a.jsx)("span",{className:"text-2xl font-bold text-gray-900",children:"HashCoreX"})]})}),(0,a.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8",children:["email"===s&&(0,a.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(o.A,{className:"w-8 h-8 text-blue-600"})}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Reset Your Password"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Enter your email address and we'll send you a verification code to reset your password."})]}),(0,a.jsxs)("form",{onSubmit:_,className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),(0,a.jsx)(n.pd,{id:"email",type:"email",value:l,onChange:e=>b(e.target.value),placeholder:"Enter your email address",required:!0,disabled:C,className:"w-full"})]}),k&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:(0,a.jsx)("p",{className:"text-sm text-red-600 text-center",children:k})}),(0,a.jsx)(n.$n,{type:"submit",disabled:C||!l,className:"w-full bg-blue-600 hover:bg-blue-700 text-white",children:C?(0,a.jsxs)("div",{className:"flex items-center justify-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Sending Code..."]}):"Send Reset Code"}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)(d(),{href:"/login",className:"text-sm text-blue-600 hover:text-blue-700 flex items-center justify-center",children:[(0,a.jsx)(m.A,{className:"w-4 h-4 mr-1"}),"Back to Login"]})})]})]}),"otp"===s&&(0,a.jsx)(j.h,{email:l,onVerified:E,onResend:q,loading:C,error:k}),"password"===s&&(0,a.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(x.A,{className:"w-8 h-8 text-green-600"})}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Create New Password"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Enter your new password below. Make sure it's strong and secure."})]}),(0,a.jsxs)("form",{onSubmit:J,className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"newPassword",className:"block text-sm font-medium text-gray-700 mb-2",children:"New Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(n.pd,{id:"newPassword",type:N?"text":"password",value:w,onChange:e=>y(e.target.value),placeholder:"Enter new password",required:!0,disabled:C,className:"w-full pr-10"}),(0,a.jsx)("button",{type:"button",onClick:()=>v(!N),className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:N?(0,a.jsx)(h.A,{className:"h-4 w-4 text-gray-400"}):(0,a.jsx)(u.A,{className:"h-4 w-4 text-gray-400"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-2",children:"Confirm New Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(n.pd,{id:"confirmPassword",type:P?"text":"password",value:g,onChange:e=>f(e.target.value),placeholder:"Confirm new password",required:!0,disabled:C,className:"w-full pr-10"}),(0,a.jsx)("button",{type:"button",onClick:()=>S(!P),className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:P?(0,a.jsx)(h.A,{className:"h-4 w-4 text-gray-400"}):(0,a.jsx)(u.A,{className:"h-4 w-4 text-gray-400"})})]})]}),w&&(0,a.jsxs)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-700 mb-2",children:"Password Requirements:"}),(0,a.jsx)("div",{className:"space-y-1",children:R.checks.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center text-sm",children:[(0,a.jsx)("div",{className:"w-4 h-4 rounded-full mr-2 flex items-center justify-center ".concat(e.valid?"bg-green-100":"bg-gray-100"),children:e.valid&&(0,a.jsx)(x.A,{className:"w-3 h-3 text-green-600"})}),(0,a.jsx)("span",{className:e.valid?"text-green-600":"text-gray-500",children:e.message})]},s))})]}),k&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:(0,a.jsx)("p",{className:"text-sm text-red-600 text-center",children:k})}),F&&(0,a.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3",children:(0,a.jsx)("p",{className:"text-sm text-green-600 text-center",children:F})}),(0,a.jsx)(n.$n,{type:"submit",disabled:C||!R.isValid||w!==g,className:"w-full bg-green-600 hover:bg-green-700 text-white",children:C?(0,a.jsxs)("div",{className:"flex items-center justify-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Resetting Password..."]}):"Reset Password"})]})]})]}),(0,a.jsx)("div",{className:"text-center mt-6",children:(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Remember your password?"," ",(0,a.jsx)(d(),{href:"/login",className:"text-blue-600 hover:text-blue-700 font-medium",children:"Sign in here"})]})})]})})}function y(){return(0,a.jsx)(b.CU,{redirectTo:"/dashboard",children:(0,a.jsx)(w,{})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[36,905,7425,8441,1684,7358],()=>s(913)),_N_E=e.O()}]);