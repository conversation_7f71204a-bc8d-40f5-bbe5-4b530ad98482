"use strict";(()=>{var e={};e.id=8901,e.ids=[8901],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},21820:e=>{e.exports=require("os")},24919:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>p,serverHooks:()=>g,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>E});var a={};t.r(a),t.d(a,{GET:()=>c});var s=t(96559),i=t(48088),n=t(37719),o=t(32190),l=t(39542),d=t(31183),u=t(82629);async function c(e){try{let{authenticated:r,user:t}=await (0,l.b9)(e);if(!r||!t)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if(!await (0,l.qc)(t.id))return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let a=new URL(e.url),s=a.searchParams.get("search")||"",i="true"===a.searchParams.get("discrepancies"),n=a.searchParams.get("startDate"),u=a.searchParams.get("endDate"),c={};if(n&&(c.gte=new Date(n)),u){let e=new Date(u);e.setHours(23,59,59,999),c.lte=e}let p=await d.prisma.user.findMany({where:{AND:[s?{OR:[{firstName:{contains:s,mode:"insensitive"}},{lastName:{contains:s,mode:"insensitive"}},{email:{contains:s,mode:"insensitive"}},{referralId:{contains:s,mode:"insensitive"}}]}:{}]},include:{transactions:{where:Object.keys(c).length>0?{createdAt:c}:void 0,orderBy:{createdAt:"desc"}},walletBalance:!0},orderBy:{createdAt:"desc"}}),m=p.map(e=>{let r=e.transactions,t=r.filter(e=>"DEPOSIT"===e.type&&"COMPLETED"===e.status).reduce((e,r)=>e+r.amount,0),a=r.filter(e=>"WITHDRAWAL"===e.type&&"COMPLETED"===e.status).reduce((e,r)=>e+r.amount,0),s=r.filter(e=>["MINING_EARNINGS","DIRECT_REFERRAL","BINARY_BONUS"].includes(e.type)&&"COMPLETED"===e.status).reduce((e,r)=>e+r.amount,0),i=r.filter(e=>"PURCHASE"===e.type&&"COMPLETED"===e.status).reduce((e,r)=>e+r.amount,0),n=r.filter(e=>"ADMIN_CREDIT"===e.type&&"COMPLETED"===e.status).reduce((e,r)=>e+r.amount,0),o=r.filter(e=>"ADMIN_DEBIT"===e.type&&"COMPLETED"===e.status).reduce((e,r)=>e+Math.abs(r.amount),0),l=t+s+n-a-i-o,d=e.walletBalance?.availableBalance||0,u=d-l,c=Math.abs(u)>=.01,p=r.length>0?r[0].createdAt.toISOString():e.createdAt.toISOString();return{userId:e.id,user:{firstName:e.firstName,lastName:e.lastName,email:e.email,referralId:e.referralId},totalDeposits:t,totalWithdrawals:a,totalEarnings:s,totalPurchases:i,adminCredits:n,adminDebits:o,currentBalance:d,calculatedBalance:l,balanceMismatch:u,transactionCount:r.length,lastActivity:p,hasDiscrepancies:c}}),E=i?m.filter(e=>e.hasDiscrepancies):m,g={totalUsers:p.length,usersWithDiscrepancies:m.filter(e=>e.hasDiscrepancies).length,totalBalanceMismatch:m.reduce((e,r)=>e+r.balanceMismatch,0),totalTransactions:m.reduce((e,r)=>e+r.transactionCount,0),auditedUsers:m.length};return o.NextResponse.json({success:!0,data:{users:E,stats:g}})}catch(r){return console.error("Account audit API error:",r),await u.v5.logApiError(e,r,"ACCOUNT_AUDIT_ERROR"),o.NextResponse.json({success:!1,error:"Failed to fetch audit data"},{status:500})}}let p=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/account-audit/route",pathname:"/api/admin/account-audit",filename:"route",bundlePath:"app/api/admin/account-audit/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\account-audit\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:m,workUnitAsyncStorage:E,serverHooks:g}=p;function f(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:E})}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37366:e=>{e.exports=require("dns")},39542:(e,r,t)=>{t.d(r,{b9:()=>h,HU:()=>g,qc:()=>S,Lx:()=>I,DY:()=>A,DT:()=>T});var a=t(85663),s=t(43205),i=t.n(s),n=t(6710),o=t(45697);let l=o.z.object({DATABASE_URL:o.z.string().url("DATABASE_URL must be a valid PostgreSQL URL"),DIRECT_URL:o.z.string().url("DIRECT_URL must be a valid PostgreSQL URL"),JWT_SECRET:o.z.string().min(32,"JWT_SECRET must be at least 32 characters long").refine(e=>{let r=/[A-Z]/.test(e),t=/[a-z]/.test(e),a=/\d/.test(e),s=/[!@#$%^&*(),.?":{}|<>]/.test(e);return r&&t&&a&&s},"JWT_SECRET should contain uppercase, lowercase, numbers, and special characters"),JWT_EXPIRES_IN:o.z.string().default("30d"),NODE_ENV:o.z.enum(["development","production","test"]).default("development"),NEXT_PUBLIC_APP_URL:o.z.string().url().optional(),PORT:o.z.string().regex(/^\d+$/).transform(Number).default("3000"),SMTP_HOST:o.z.string().optional(),SMTP_PORT:o.z.string().regex(/^\d+$/).transform(Number).optional(),SMTP_USER:o.z.string().email().optional(),SMTP_PASSWORD:o.z.string().optional(),SMTP_SECURE:o.z.string().transform(e=>"true"===e).optional(),TRON_NETWORK:o.z.enum(["mainnet","testnet"]).default("testnet"),TRON_API_KEY:o.z.string().optional(),USDT_CONTRACT_ADDRESS:o.z.string().optional(),MAX_FILE_SIZE:o.z.string().regex(/^\d+$/).transform(Number).default("10485760"),UPLOAD_DIR:o.z.string().default("./public/uploads"),BCRYPT_ROUNDS:o.z.string().regex(/^\d+$/).transform(Number).default("12"),SESSION_TIMEOUT:o.z.string().regex(/^\d+$/).transform(Number).default("1800"),RATE_LIMIT_WINDOW_MS:o.z.string().regex(/^\d+$/).transform(Number).default("900000"),RATE_LIMIT_MAX_REQUESTS:o.z.string().regex(/^\d+$/).transform(Number).default("100"),COINGECKO_API_URL:o.z.string().url().default("https://api.coingecko.com/api/v3"),LOG_LEVEL:o.z.enum(["error","warn","info","debug"]).default("info"),ENABLE_REQUEST_LOGGING:o.z.string().transform(e=>"true"===e).default("false"),ENABLE_REGISTRATION:o.z.string().transform(e=>"true"===e).default("true"),ENABLE_KYC:o.z.string().transform(e=>"true"===e).default("true"),ENABLE_WITHDRAWALS:o.z.string().transform(e=>"true"===e).default("true")}).refine(e=>{let r=[e.SMTP_HOST,e.SMTP_PORT,e.SMTP_USER,e.SMTP_PASSWORD],t=r.some(e=>void 0!==e),a=r.every(e=>void 0!==e);return!t||!!a},{message:"If SMTP configuration is provided, all SMTP fields (HOST, PORT, USER, PASSWORD) must be provided"});function d(){try{let e=l.safeParse(process.env);if(!e.success){let r=e.error.errors.map(e=>`${e.path.join(".")}: ${e.message}`);return{success:!1,errors:r}}return{success:!0,data:e.data}}catch(e){return{success:!1,errors:["Failed to validate environment variables"]}}}let u=null;function c(){if(!u){let e=d();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),u=e.data,console.log("✅ Environment variables validated successfully")}return u}let p={jwt:{secret:()=>c().JWT_SECRET,expiresIn:()=>c().JWT_EXPIRES_IN},security:{bcryptRounds:()=>c().BCRYPT_ROUNDS,sessionTimeout:()=>c().SESSION_TIMEOUT,maxFileSize:()=>c().MAX_FILE_SIZE,uploadDir:()=>c().UPLOAD_DIR}};if("phase-production-build"!==process.env.NEXT_PHASE){let e=d();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),console.log("✅ Environment variables validated successfully")}let m=async e=>await a.Ay.hash(e,p.security.bcryptRounds()),E=async(e,r)=>await a.Ay.compare(e,r),g=e=>i().sign(e,p.jwt.secret(),{expiresIn:p.jwt.expiresIn()}),f=e=>{try{return i().verify(e,p.jwt.secret())}catch(e){return null}},R=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",r="HC";for(let t=0;t<8;t++)r+=e.charAt(Math.floor(Math.random()*e.length));return r},h=async e=>{let r=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!r)return{authenticated:!1,user:null};let t=f(r);if(!t)return{authenticated:!1,user:null};let a=await n.Gy.findByEmail(t.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},A=async e=>{let r,a;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let t=await n.Gy.findByReferralId(e.referralCode);if(!t)throw Error("Invalid referral code");r=t.id}let s=await m(e.password),i=!1;do a=R(),i=!await n.Gy.findByReferralId(a);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(r){let{placeUserByReferralType:a}=await t.e(2746).then(t.bind(t,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(r,o.id,s)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},I=async e=>{let r=await n.Gy.findByEmail(e.email);if(!r||!await E(e.password,r.password))throw Error("Invalid email or password");return{token:g({userId:r.id,email:r.email}),user:{id:r.id,email:r.email,referralId:r.referralId,kycStatus:r.kycStatus}}},T=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),S=async e=>{let r=await n.Gy.findById(e);return r?.role==="ADMIN"}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},82629:(e,r,t)=>{t.d(r,{v5:()=>s});var a=t(6710);class s{static async logError(e){try{let r=e.error instanceof Error?e.error:Error(String(e.error)),t={action:e.action,userId:e.userId,adminId:e.adminId,details:{message:r.message,stack:r.stack,name:r.name,requestUrl:e.requestUrl,requestMethod:e.requestMethod,requestBody:e.requestBody,additionalData:e.additionalData,timestamp:new Date().toISOString()},ipAddress:e.ipAddress,userAgent:e.userAgent};await a.AJ.create(t),console.error(`[${e.action}] Error logged:`,{message:r.message,stack:r.stack,userId:e.userId,adminId:e.adminId,url:e.requestUrl})}catch(r){console.error("Failed to log error to database:",r),console.error("Original error:",e.error)}}static async logApiError(e,r,t,a,s,i){try{let n=null;try{if("GET"!==e.method&&e.headers.get("content-type")?.includes("application/json")){let r=e.clone();(n=await r.json()).password&&(n.password="[REDACTED]"),n.token&&(n.token="[REDACTED]"),n.apiKey&&(n.apiKey="[REDACTED]")}}catch(e){}await this.logError({action:t,error:r,userId:a,adminId:s,ipAddress:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||"unknown",userAgent:e.headers.get("user-agent")||"unknown",requestUrl:e.url,requestMethod:e.method,requestBody:n,additionalData:i})}catch(e){console.error("Failed to log API error:",e),console.error("Original error:",r)}}static async logClientError(e){try{await a.AJ.create({action:"CLIENT_ERROR",userId:e.userId,details:{message:e.message,stack:e.stack,url:e.url,timestamp:e.timestamp,additionalData:e.additionalData},userAgent:e.userAgent})}catch(e){console.error("Failed to log client error:",e)}}static async logAuthError(e,r,t,a){await this.logApiError(e,r,"AUTH_ERROR",void 0,void 0,{email:t,...a})}static async logDatabaseError(e,r,t,s,i){try{await a.AJ.create({action:"DATABASE_ERROR",userId:s,details:{message:e instanceof Error?e.message:String(e),stack:e instanceof Error?e.stack:void 0,operation:r,table:t,timestamp:new Date().toISOString(),additionalData:i}})}catch(r){console.error("Failed to log database error:",r),console.error("Original error:",e)}}static async logBusinessError(e,r,t,a,s){await this.logError({action:"BUSINESS_LOGIC_ERROR",error:e,userId:t,adminId:a,additionalData:{operation:r,...s}})}static async logExternalApiError(e,r,t,a,s){await this.logError({action:"EXTERNAL_API_ERROR",error:e,userId:a,additionalData:{service:r,endpoint:t,...s}})}static async logValidationError(e,r,t,a,s){await this.logError({action:"VALIDATION_ERROR",error:e,userId:a,additionalData:{field:r,value:t,...s}})}}},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,580,7911,925],()=>t(24919));module.exports=a})();