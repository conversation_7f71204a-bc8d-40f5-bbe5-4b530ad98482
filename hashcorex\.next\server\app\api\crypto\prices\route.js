(()=>{var e={};e.id=6358,e.ids=[6358],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12330:(e,r,n)=>{"use strict";n.r(r),n.d(r,{patchFetch:()=>g,routeModule:()=>m,serverHooks:()=>l,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>d});var t={};n.r(t),n.d(t,{GET:()=>u});var a=n(96559),c=n(48088),i=n(37719),o=n(32190);let s=null,p=["bitcoin","ethereum","tether","binancecoin","solana","usd-coin","xrp","staked-ether","dogecoin","cardano","tron","avalanche-2","chainlink","polygon","wrapped-bitcoin","internet-computer","near","uniswap","litecoin","dai","ethereum-classic","stellar","monero","bitcoin-cash","cosmos"];async function u(e){try{if(s&&Date.now()-s.timestamp<6e4)return o.NextResponse.json({success:!0,data:s.data,cached:!0,timestamp:s.timestamp});let e=await fetch(`https://api.coingecko.com/api/v3/simple/price?ids=${p.join(",")}&vs_currencies=usd&include_24hr_change=true`,{headers:{Accept:"application/json","User-Agent":"HashCoreX/1.0"},signal:AbortSignal.timeout(1e4)});if(!e.ok)throw Error(`CoinGecko API error: ${e.status}`);let r=await e.json(),n=p.map(e=>{var n,t,a;let c=r[e];return c?{id:e,symbol:({bitcoin:"BTC",ethereum:"ETH",tether:"USDT",binancecoin:"BNB",solana:"SOL","usd-coin":"USDC",xrp:"XRP","staked-ether":"stETH",dogecoin:"DOGE",cardano:"ADA",tron:"TRX","avalanche-2":"AVAX",chainlink:"LINK",polygon:"MATIC","wrapped-bitcoin":"WBTC","internet-computer":"ICP",near:"NEAR",uniswap:"UNI",litecoin:"LTC",dai:"DAI","ethereum-classic":"ETC",stellar:"XLM",monero:"XMR","bitcoin-cash":"BCH",cosmos:"ATOM"})[n=e]||n.toUpperCase(),name:({bitcoin:"Bitcoin",ethereum:"Ethereum",tether:"Tether",binancecoin:"BNB",solana:"Solana","usd-coin":"USD Coin",xrp:"XRP","staked-ether":"Lido Staked Ether",dogecoin:"Dogecoin",cardano:"Cardano",tron:"TRON","avalanche-2":"Avalanche",chainlink:"Chainlink",polygon:"Polygon","wrapped-bitcoin":"Wrapped Bitcoin","internet-computer":"Internet Computer",near:"NEAR Protocol",uniswap:"Uniswap",litecoin:"Litecoin",dai:"Dai","ethereum-classic":"Ethereum Classic",stellar:"Stellar",monero:"Monero","bitcoin-cash":"Bitcoin Cash",cosmos:"Cosmos"})[t=e]||t,current_price:c.usd,price_change_percentage_24h:c.usd_24h_change||0,image:(a=e,`/crypto-icons/${a}.png`)}:null}).filter(Boolean);return s={data:n,timestamp:Date.now()},o.NextResponse.json({success:!0,data:n,cached:!1,timestamp:Date.now()})}catch(e){if(console.error("Error fetching crypto prices:",e),s)return o.NextResponse.json({success:!0,data:s.data,cached:!0,stale:!0,timestamp:s.timestamp,error:"Using cached data due to API error"});return o.NextResponse.json({success:!0,data:[{id:"bitcoin",symbol:"BTC",name:"Bitcoin",current_price:45e3,price_change_percentage_24h:2.5,image:"/crypto-icons/bitcoin.png"},{id:"ethereum",symbol:"ETH",name:"Ethereum",current_price:2800,price_change_percentage_24h:1.8,image:"/crypto-icons/ethereum.png"},{id:"tether",symbol:"USDT",name:"Tether",current_price:1,price_change_percentage_24h:.1,image:"/crypto-icons/tether.png"},{id:"binancecoin",symbol:"BNB",name:"BNB",current_price:320,price_change_percentage_24h:-.5,image:"/crypto-icons/binancecoin.png"},{id:"solana",symbol:"SOL",name:"Solana",current_price:95,price_change_percentage_24h:3.2,image:"/crypto-icons/solana.png"},{id:"xrp",symbol:"XRP",name:"XRP",current_price:.52,price_change_percentage_24h:-1.2,image:"/crypto-icons/xrp.png"},{id:"dogecoin",symbol:"DOGE",name:"Dogecoin",current_price:.08,price_change_percentage_24h:4.1,image:"/crypto-icons/dogecoin.png"},{id:"cardano",symbol:"ADA",name:"Cardano",current_price:.45,price_change_percentage_24h:1.5,image:"/crypto-icons/cardano.png"},{id:"tron",symbol:"TRX",name:"TRON",current_price:.11,price_change_percentage_24h:2.8,image:"/crypto-icons/tron.png"},{id:"avalanche-2",symbol:"AVAX",name:"Avalanche",current_price:28,price_change_percentage_24h:-.8,image:"/crypto-icons/avalanche-2.png"}],fallback:!0,error:e instanceof Error?e.message:"Unknown error",timestamp:Date.now()})}}let m=new a.AppRouteRouteModule({definition:{kind:c.RouteKind.APP_ROUTE,page:"/api/crypto/prices/route",pathname:"/api/crypto/prices",filename:"route",bundlePath:"app/api/crypto/prices/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\crypto\\prices\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:h,workUnitAsyncStorage:d,serverHooks:l}=m;function g(){return(0,i.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:d})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var n=e=>r(r.s=e),t=r.X(0,[4447,580],()=>n(12330));module.exports=t})();