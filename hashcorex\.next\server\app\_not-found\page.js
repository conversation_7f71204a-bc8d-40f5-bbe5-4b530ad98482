(()=>{var e={};e.id=9492,e.ids=[9492],e.modules={2470:(e,t,r)=>{Promise.resolve().then(r.bind(r,57445))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26207:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34014:(e,t,r)=>{Promise.resolve().then(r.bind(r,49567))},49567:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>o});var s=r(12907);let o=(0,s.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\hooks\\useAuth.tsx","AuthProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\hooks\\useAuth.tsx","useAuth")},57445:(e,t,r)=>{"use strict";r.d(t,{A:()=>a,AuthProvider:()=>i});var s=r(60687),o=r(43210);let n=(0,o.createContext)(void 0),i=({children:e})=>{let[t,r]=(0,o.useState)(null),[i,a]=(0,o.useState)(!0);(0,o.useEffect)(()=>{d()},[]);let d=async()=>{try{let e=await fetch("/api/auth/me",{credentials:"include"});if(e.ok){let t=await e.json();t.success&&r(t.data)}}catch(e){console.error("Auth check failed:",e)}finally{a(!1)}},l=async(e,t)=>{a(!0);try{let s=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e,password:t})}),o=await s.json();if(!o.success)throw Error(o.error||"Login failed");r(o.data.user),setTimeout(()=>{d()},100)}catch(e){throw e}finally{a(!1)}},u=async(e,t,s,o,n,i,d,l)=>{a(!0);try{let a=d?`/api/auth/register?side=${d}`:"/api/auth/register",u=await fetch(a,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,firstName:t,lastName:s,password:o,confirmPassword:n,referralCode:i,otp:l})}),c=await u.json();if(!c.success)throw Error(c.error||"Registration failed");r({id:c.data.user.id,email:c.data.user.email,firstName:c.data.user.firstName||"",lastName:c.data.user.lastName||"",referralId:c.data.user.referralId,role:c.data.user.role||"USER",kycStatus:c.data.user.kycStatus,isActive:c.data.user.isActive||!0,profilePicture:c.data.user.profilePicture||null,createdAt:c.data.user.createdAt,updatedAt:c.data.user.updatedAt})}catch(e){throw e}finally{a(!1)}},c=async()=>{try{await fetch("/api/auth/logout",{method:"POST",credentials:"include"})}catch(e){console.error("Logout error:",e)}finally{r(null)}},h=async()=>{await d()};return(0,s.jsx)(n.Provider,{value:{user:t,loading:i,login:l,register:u,logout:c,refreshUser:h},children:e})},a=()=>{let e=(0,o.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},86455:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},86726:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>h,tree:()=>l});var s=r(65239),o=r(48088),n=r(88170),i=r.n(n),a=r(30893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);r.d(t,d);let l={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=[],c={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>a});var s=r(37413),o=r(25091),n=r.n(o);r(61135);var i=r(49567);let a={title:"HashCoreX - Solar-Powered Cloud Mining",description:"Sustainable cryptocurrency mining with solar energy. Earn daily ROI with our eco-friendly mining platform."};function d({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${n().variable} font-sans antialiased`,children:(0,s.jsx)(i.AuthProvider,{children:e})})})}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,1771],()=>r(86726));module.exports=s})();