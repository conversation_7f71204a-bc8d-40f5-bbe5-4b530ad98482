import nodemailer from 'nodemailer';
import { systemSettingsDb } from './database';

export interface EmailConfig {
  host: string;
  port: number;
  secure: boolean;
  user: string;
  password: string;
  fromName: string;
  fromEmail: string;
}

export interface EmailTemplate {
  subject: string;
  htmlContent: string;
  textContent?: string;
}

export interface EmailData {
  to: string;
  subject: string;
  html: string;
  text?: string;
}

class EmailService {
  private transporter: nodemailer.Transporter | null = null;
  private config: EmailConfig | null = null;

  async getEmailConfig(): Promise<EmailConfig | null> {
    try {
      const settings = await systemSettingsDb.getEmailSettings();
      if (!settings || !settings.smtpHost || !settings.smtpUser || !settings.smtpPassword) {
        console.warn('Email configuration not found or incomplete');
        return null;
      }

      return {
        host: settings.smtpHost,
        port: settings.smtpPort || 587,
        secure: settings.smtpSecure || false,
        user: settings.smtpUser,
        password: settings.smtpPassword,
        fromName: settings.fromName || 'HashCoreX',
        fromEmail: settings.fromEmail || settings.smtpUser,
      };
    } catch (error) {
      console.error('Failed to get email configuration:', error);
      return null;
    }
  }

  async initializeTransporter(skipVerification: boolean = false): Promise<boolean> {
    try {
      this.config = await this.getEmailConfig();
      if (!this.config) {
        console.warn('Email configuration not available - email service disabled');
        return false;
      }

      // Validate required fields
      if (!this.config.host || !this.config.user || !this.config.password) {
        console.error('Email configuration incomplete - missing host, user, or password');
        return false;
      }

      this.transporter = nodemailer.createTransport({
        host: this.config.host,
        port: this.config.port,
        secure: this.config.secure,
        auth: {
          user: this.config.user,
          pass: this.config.password,
        },
        tls: {
          rejectUnauthorized: false, // For development/testing
        },
        connectionTimeout: 10000, // 10 seconds
        greetingTimeout: 5000, // 5 seconds
        socketTimeout: 10000, // 10 seconds
      });

      // Only verify connection if not skipping verification
      if (!skipVerification) {
        // Verify connection with timeout
        const verifyPromise = this.transporter.verify();
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Email verification timeout')), 15000)
        );

        await Promise.race([verifyPromise, timeoutPromise]);
        console.log('Email transporter initialized and verified successfully');
      } else {
        console.log('Email transporter initialized (verification skipped)');
      }

      return true;
    } catch (error) {
      console.error('Failed to initialize email transporter:', error);
      this.transporter = null;
      this.config = null;
      return false;
    }
  }

  async sendEmail(emailData: EmailData): Promise<boolean> {
    try {
      // Check if email service is configured
      if (!this.transporter || !this.config) {
        console.log('Email transporter not initialized, attempting to initialize...');
        const initialized = await this.initializeTransporter(true); // Skip verification during lazy init
        if (!initialized) {
          console.warn('Email service not configured - skipping email send');
          return false; // Return false instead of throwing error
        }
      }

      // Validate email data
      if (!emailData.to || !emailData.subject) {
        console.error('Invalid email data - missing recipient or subject');
        return false;
      }

      const mailOptions = {
        from: `"${this.config!.fromName}" <${this.config!.fromEmail}>`,
        to: emailData.to,
        subject: emailData.subject,
        html: emailData.html,
        text: emailData.text,
      };

      // Send email with timeout
      const sendPromise = this.transporter!.sendMail(mailOptions);
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Email send timeout')), 30000)
      );

      const result = await Promise.race([sendPromise, timeoutPromise]);
      console.log('Email sent successfully:', result.messageId);
      return true;
    } catch (error) {
      console.error('Failed to send email:', error);
      // Reset transporter on error to force reinitialization
      this.transporter = null;
      this.config = null;
      return false;
    }
  }

  async sendOTPEmail(email: string, otp: string, firstName?: string, purpose: string = 'email_verification'): Promise<boolean> {
    let templateName = 'otp_verification';
    if (purpose === 'password_reset') {
      templateName = 'password_reset_otp';
    } else if (purpose === 'two_factor_auth') {
      templateName = 'two_factor_otp';
    }

    const template = await this.getEmailTemplate(templateName);

    if (!template) {
      console.error(`Email template '${templateName}' not found. Please ensure email templates are seeded.`);
      return false;
    }

    // Use custom template
    let html = template.htmlContent;
    let text = template.textContent || '';
    
    // Replace placeholders
    html = html.replace(/{{firstName}}/g, firstName || 'User');
    html = html.replace(/{{otp}}/g, otp);
    text = text.replace(/{{firstName}}/g, firstName || 'User');
    text = text.replace(/{{otp}}/g, otp);

    return await this.sendEmail({
      to: email,
      subject: template.subject,
      html,
      text,
    });
  }

  async getEmailTemplate(templateName: string): Promise<EmailTemplate | null> {
    try {
      const template = await systemSettingsDb.getEmailTemplate(templateName);
      return template;
    } catch (error) {
      console.error('Failed to get email template:', error);
      return null;
    }
  }

  async testConnection(): Promise<boolean> {
    try {
      // Get fresh config
      this.config = await this.getEmailConfig();
      if (!this.config) {
        console.error('Email configuration not found or incomplete');
        return false;
      }

      // Create transporter
      this.transporter = nodemailer.createTransport({
        host: this.config.host,
        port: this.config.port,
        secure: this.config.secure,
        auth: {
          user: this.config.user,
          pass: this.config.password,
        },
        tls: {
          rejectUnauthorized: false,
        },
      });

      // Test connection
      await this.transporter.verify();
      console.log('Email connection test successful');
      return true;
    } catch (error) {
      console.error('Email connection test failed:', error);
      this.transporter = null;
      throw error; // Re-throw to get specific error message
    }
  }
}

// Export singleton instance
export const emailService = new EmailService();

// Utility functions
export const generateOTP = (): string => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};
