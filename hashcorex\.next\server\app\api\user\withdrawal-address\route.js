"use strict";(()=>{var e={};e.id=2880,e.ids=[2880],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37366:e=>{e.exports=require("dns")},39542:(e,r,t)=>{t.d(r,{b9:()=>g,HU:()=>w,qc:()=>x,Lx:()=>T,DY:()=>S,DT:()=>R});var s=t(85663),a=t(43205),i=t.n(a),o=t(6710),n=t(45697);let l=n.z.object({DATABASE_URL:n.z.string().url("DATABASE_URL must be a valid PostgreSQL URL"),DIRECT_URL:n.z.string().url("DIRECT_URL must be a valid PostgreSQL URL"),JWT_SECRET:n.z.string().min(32,"JWT_SECRET must be at least 32 characters long").refine(e=>{let r=/[A-Z]/.test(e),t=/[a-z]/.test(e),s=/\d/.test(e),a=/[!@#$%^&*(),.?":{}|<>]/.test(e);return r&&t&&s&&a},"JWT_SECRET should contain uppercase, lowercase, numbers, and special characters"),JWT_EXPIRES_IN:n.z.string().default("30d"),NODE_ENV:n.z.enum(["development","production","test"]).default("development"),NEXT_PUBLIC_APP_URL:n.z.string().url().optional(),PORT:n.z.string().regex(/^\d+$/).transform(Number).default("3000"),SMTP_HOST:n.z.string().optional(),SMTP_PORT:n.z.string().regex(/^\d+$/).transform(Number).optional(),SMTP_USER:n.z.string().email().optional(),SMTP_PASSWORD:n.z.string().optional(),SMTP_SECURE:n.z.string().transform(e=>"true"===e).optional(),TRON_NETWORK:n.z.enum(["mainnet","testnet"]).default("testnet"),TRON_API_KEY:n.z.string().optional(),USDT_CONTRACT_ADDRESS:n.z.string().optional(),MAX_FILE_SIZE:n.z.string().regex(/^\d+$/).transform(Number).default("10485760"),UPLOAD_DIR:n.z.string().default("./public/uploads"),BCRYPT_ROUNDS:n.z.string().regex(/^\d+$/).transform(Number).default("12"),SESSION_TIMEOUT:n.z.string().regex(/^\d+$/).transform(Number).default("1800"),RATE_LIMIT_WINDOW_MS:n.z.string().regex(/^\d+$/).transform(Number).default("900000"),RATE_LIMIT_MAX_REQUESTS:n.z.string().regex(/^\d+$/).transform(Number).default("100"),COINGECKO_API_URL:n.z.string().url().default("https://api.coingecko.com/api/v3"),LOG_LEVEL:n.z.enum(["error","warn","info","debug"]).default("info"),ENABLE_REQUEST_LOGGING:n.z.string().transform(e=>"true"===e).default("false"),ENABLE_REGISTRATION:n.z.string().transform(e=>"true"===e).default("true"),ENABLE_KYC:n.z.string().transform(e=>"true"===e).default("true"),ENABLE_WITHDRAWALS:n.z.string().transform(e=>"true"===e).default("true")}).refine(e=>{let r=[e.SMTP_HOST,e.SMTP_PORT,e.SMTP_USER,e.SMTP_PASSWORD],t=r.some(e=>void 0!==e),s=r.every(e=>void 0!==e);return!t||!!s},{message:"If SMTP configuration is provided, all SMTP fields (HOST, PORT, USER, PASSWORD) must be provided"});function u(){try{let e=l.safeParse(process.env);if(!e.success){let r=e.error.errors.map(e=>`${e.path.join(".")}: ${e.message}`);return{success:!1,errors:r}}return{success:!0,data:e.data}}catch(e){return{success:!1,errors:["Failed to validate environment variables"]}}}let d=null;function c(){if(!d){let e=u();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),d=e.data,console.log("✅ Environment variables validated successfully")}return d}let p={jwt:{secret:()=>c().JWT_SECRET,expiresIn:()=>c().JWT_EXPIRES_IN},security:{bcryptRounds:()=>c().BCRYPT_ROUNDS,sessionTimeout:()=>c().SESSION_TIMEOUT,maxFileSize:()=>c().MAX_FILE_SIZE,uploadDir:()=>c().UPLOAD_DIR}};if("phase-production-build"!==process.env.NEXT_PHASE){let e=u();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),console.log("✅ Environment variables validated successfully")}let f=async e=>await s.Ay.hash(e,p.security.bcryptRounds()),m=async(e,r)=>await s.Ay.compare(e,r),w=e=>i().sign(e,p.jwt.secret(),{expiresIn:p.jwt.expiresIn()}),E=e=>{try{return i().verify(e,p.jwt.secret())}catch(e){return null}},h=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",r="HC";for(let t=0;t<8;t++)r+=e.charAt(Math.floor(Math.random()*e.length));return r},g=async e=>{let r=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!r)return{authenticated:!1,user:null};let t=E(r);if(!t)return{authenticated:!1,user:null};let s=await o.Gy.findByEmail(t.email);return s?{authenticated:!0,user:s}:{authenticated:!1,user:null}},S=async e=>{let r,s;if(await o.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let t=await o.Gy.findByReferralId(e.referralCode);if(!t)throw Error("Invalid referral code");r=t.id}let a=await f(e.password),i=!1;do s=h(),i=!await o.Gy.findByReferralId(s);while(!i);let n=await o.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:a,referralId:s});if(r){let{placeUserByReferralType:s}=await t.e(2746).then(t.bind(t,2746)),a="general";"left"===e.placementSide?a="left":"right"===e.placementSide&&(a="right"),await s(r,n.id,a)}return{id:n.id,email:n.email,referralId:n.referralId,kycStatus:n.kycStatus}},T=async e=>{let r=await o.Gy.findByEmail(e.email);if(!r||!await m(e.password,r.password))throw Error("Invalid email or password");return{token:w({userId:r.id,email:r.email}),user:{id:r.id,email:r.email,referralId:r.referralId,kycStatus:r.kycStatus}}},R=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),x=async e=>{let r=await o.Gy.findById(e);return r?.role==="ADMIN"}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},83732:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>c,serverHooks:()=>m,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>f});var s={};t.r(s),t.d(s,{GET:()=>u,POST:()=>d});var a=t(96559),i=t(48088),o=t(37719),n=t(32190),l=t(39542);async function u(e){try{let{authenticated:r,user:s}=await (0,l.b9)(e);if(!r||!s)return n.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});let{prisma:a}=await Promise.resolve().then(t.bind(t,31183)),i=await a.user.findUnique({where:{email:s.email},select:{id:!0,email:!0,withdrawalAddress:!0}});if(!i)return n.NextResponse.json({success:!1,error:"User not found"},{status:404});let o=i.withdrawalAddress||"";return n.NextResponse.json({success:!0,data:{withdrawalAddress:o}})}catch(e){return console.error("Get withdrawal address error:",e),n.NextResponse.json({success:!1,error:"Failed to get withdrawal address",details:e.message},{status:500})}}async function d(e){try{let{authenticated:r,user:s}=await (0,l.b9)(e);if(!r||!s)return n.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});let{withdrawalAddress:a}=await e.json();if(a&&!a.match(/^T[A-Za-z1-9]{33}$/))return n.NextResponse.json({success:!1,error:"Invalid USDT TRC20 address format"},{status:400});try{console.log("Attempting to update withdrawal address for user:",s.email),console.log("New withdrawal address:",a);let{prisma:e}=await Promise.resolve().then(t.bind(t,31183)),r=await e.user.update({where:{email:s.email},data:{withdrawalAddress:a||null}});console.log("Successfully updated user:",r.id)}catch(e){throw console.error("Database update error:",e),console.error("Error details:",e.message),console.error("Error code:",e.code),Error(`Database error: ${e.message}`)}return n.NextResponse.json({success:!0,message:"Withdrawal address updated successfully"})}catch(e){return console.error("Update withdrawal address error:",e),n.NextResponse.json({success:!1,error:"Failed to update withdrawal address",details:e.message},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/user/withdrawal-address/route",pathname:"/api/user/withdrawal-address",filename:"route",bundlePath:"app/api/user/withdrawal-address/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\user\\withdrawal-address\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:p,workUnitAsyncStorage:f,serverHooks:m}=c;function w(){return(0,o.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:f})}},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,7911,925],()=>t(83732));module.exports=s})();