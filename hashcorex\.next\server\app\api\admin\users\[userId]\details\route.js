"use strict";(()=>{var e={};e.id=5692,e.ids=[5692],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37366:e=>{e.exports=require("dns")},39542:(e,t,r)=>{r.d(t,{b9:()=>T,HU:()=>E,qc:()=>R,Lx:()=>_,DY:()=>I,DT:()=>h});var a=r(85663),s=r(43205),i=r.n(s),n=r(6710),o=r(45697);let l=o.z.object({DATABASE_URL:o.z.string().url("DATABASE_URL must be a valid PostgreSQL URL"),DIRECT_URL:o.z.string().url("DIRECT_URL must be a valid PostgreSQL URL"),JWT_SECRET:o.z.string().min(32,"JWT_SECRET must be at least 32 characters long").refine(e=>{let t=/[A-Z]/.test(e),r=/[a-z]/.test(e),a=/\d/.test(e),s=/[!@#$%^&*(),.?":{}|<>]/.test(e);return t&&r&&a&&s},"JWT_SECRET should contain uppercase, lowercase, numbers, and special characters"),JWT_EXPIRES_IN:o.z.string().default("30d"),NODE_ENV:o.z.enum(["development","production","test"]).default("development"),NEXT_PUBLIC_APP_URL:o.z.string().url().optional(),PORT:o.z.string().regex(/^\d+$/).transform(Number).default("3000"),SMTP_HOST:o.z.string().optional(),SMTP_PORT:o.z.string().regex(/^\d+$/).transform(Number).optional(),SMTP_USER:o.z.string().email().optional(),SMTP_PASSWORD:o.z.string().optional(),SMTP_SECURE:o.z.string().transform(e=>"true"===e).optional(),TRON_NETWORK:o.z.enum(["mainnet","testnet"]).default("testnet"),TRON_API_KEY:o.z.string().optional(),USDT_CONTRACT_ADDRESS:o.z.string().optional(),MAX_FILE_SIZE:o.z.string().regex(/^\d+$/).transform(Number).default("10485760"),UPLOAD_DIR:o.z.string().default("./public/uploads"),BCRYPT_ROUNDS:o.z.string().regex(/^\d+$/).transform(Number).default("12"),SESSION_TIMEOUT:o.z.string().regex(/^\d+$/).transform(Number).default("1800"),RATE_LIMIT_WINDOW_MS:o.z.string().regex(/^\d+$/).transform(Number).default("900000"),RATE_LIMIT_MAX_REQUESTS:o.z.string().regex(/^\d+$/).transform(Number).default("100"),COINGECKO_API_URL:o.z.string().url().default("https://api.coingecko.com/api/v3"),LOG_LEVEL:o.z.enum(["error","warn","info","debug"]).default("info"),ENABLE_REQUEST_LOGGING:o.z.string().transform(e=>"true"===e).default("false"),ENABLE_REGISTRATION:o.z.string().transform(e=>"true"===e).default("true"),ENABLE_KYC:o.z.string().transform(e=>"true"===e).default("true"),ENABLE_WITHDRAWALS:o.z.string().transform(e=>"true"===e).default("true")}).refine(e=>{let t=[e.SMTP_HOST,e.SMTP_PORT,e.SMTP_USER,e.SMTP_PASSWORD],r=t.some(e=>void 0!==e),a=t.every(e=>void 0!==e);return!r||!!a},{message:"If SMTP configuration is provided, all SMTP fields (HOST, PORT, USER, PASSWORD) must be provided"});function u(){try{let e=l.safeParse(process.env);if(!e.success){let t=e.error.errors.map(e=>`${e.path.join(".")}: ${e.message}`);return{success:!1,errors:t}}return{success:!0,data:e.data}}catch(e){return{success:!1,errors:["Failed to validate environment variables"]}}}let d=null;function c(){if(!d){let e=u();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),d=e.data,console.log("✅ Environment variables validated successfully")}return d}let m={jwt:{secret:()=>c().JWT_SECRET,expiresIn:()=>c().JWT_EXPIRES_IN},security:{bcryptRounds:()=>c().BCRYPT_ROUNDS,sessionTimeout:()=>c().SESSION_TIMEOUT,maxFileSize:()=>c().MAX_FILE_SIZE,uploadDir:()=>c().UPLOAD_DIR}};if("phase-production-build"!==process.env.NEXT_PHASE){let e=u();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),console.log("✅ Environment variables validated successfully")}let p=async e=>await a.Ay.hash(e,m.security.bcryptRounds()),f=async(e,t)=>await a.Ay.compare(e,t),E=e=>i().sign(e,m.jwt.secret(),{expiresIn:m.jwt.expiresIn()}),S=e=>{try{return i().verify(e,m.jwt.secret())}catch(e){return null}},g=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},T=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=S(t);if(!r)return{authenticated:!1,user:null};let a=await n.Gy.findByEmail(r.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},I=async e=>{let t,a;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await n.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let s=await p(e.password),i=!1;do a=g(),i=!await n.Gy.findByReferralId(a);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(t){let{placeUserByReferralType:a}=await r.e(2746).then(r.bind(r,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(t,o.id,s)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},_=async e=>{let t=await n.Gy.findByEmail(e.email);if(!t||!await f(e.password,t.password))throw Error("Invalid email or password");return{token:E({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},h=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),R=async e=>{let t=await n.Gy.findById(e);return t?.role==="ADMIN"}},44823:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>p,serverHooks:()=>S,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>E});var a={};r.r(a),r.d(a,{GET:()=>m});var s=r(96559),i=r(48088),n=r(37719),o=r(32190),l=r(39542),u=r(31183),d=r(6710),c=r(2746);async function m(e,{params:t}){try{let{authenticated:r,user:a}=await (0,l.b9)(e);if(!r||!a)return o.NextResponse.json({success:!1,error:"Authentication required"},{status:401});if("ADMIN"!==a.role)return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let{userId:s}=await t,i=await u.prisma.user.findUnique({where:{id:s},select:{id:!0,email:!0,firstName:!0,lastName:!0,role:!0,kycStatus:!0,isActive:!0,createdAt:!0,referralId:!0,referrerId:!0}});if(!i)return o.NextResponse.json({success:!1,error:"User not found"},{status:404});let n=await d.k_.getOrCreate(s),m=(await u.prisma.miningUnit.findMany({where:{userId:s},select:{id:!0,thsAmount:!0,investmentAmount:!0,totalEarned:!0,status:!0,expiryDate:!0,createdAt:!0}})).reduce((e,t)=>{let r="ACTIVE"===t.status&&t.expiryDate>new Date;return{totalUnits:e.totalUnits+1,totalInvestment:e.totalInvestment+t.investmentAmount,totalTHS:e.totalTHS+t.thsAmount,activeTHS:e.activeTHS+(r?t.thsAmount:0)}},{totalUnits:0,totalInvestment:0,totalTHS:0,activeTHS:0}),p=await d.FW.findByUserId(s),f=await (0,c.QS)(s),E=await (0,c.l6)(s),S=(await u.prisma.transaction.findMany({where:{userId:s},orderBy:{createdAt:"desc"},take:10,select:{id:!0,type:!0,amount:!0,description:!0,status:!0,createdAt:!0}})).map(e=>({type:e.type,description:e.description||`${e.type.replace("_"," ")} - ${e.status}`,amount:e.amount,date:e.createdAt.toISOString()})),g=await u.prisma.transaction.aggregate({where:{userId:s,type:{in:["MINING_EARNINGS","DIRECT_REFERRAL","BINARY_BONUS"]},status:"COMPLETED"},_sum:{amount:!0}}),T=await u.prisma.transaction.aggregate({where:{userId:s,type:"DEPOSIT",status:"COMPLETED"},_sum:{amount:!0}}),I=await u.prisma.transaction.aggregate({where:{userId:s,type:"WITHDRAWAL",status:"COMPLETED"},_sum:{amount:!0}}),_={id:i.id,email:i.email,firstName:i.firstName,lastName:i.lastName,role:i.role,kycStatus:i.kycStatus,isActive:i.isActive,createdAt:i.createdAt.toISOString(),referralId:i.referralId,referrerId:i.referrerId,walletBalance:{availableBalance:n.availableBalance,totalEarnings:g._sum.amount||0,totalDeposits:T._sum.amount||0,totalWithdrawals:I._sum.amount||0},miningUnits:m,binaryPoints:{leftPoints:p?.leftPoints||0,rightPoints:p?.rightPoints||0,totalMatched:p?.totalMatched||0,lastMatchDate:p?.lastMatchDate?.toISOString()},referralStats:{directReferrals:f,leftTeam:E.left,rightTeam:E.right,totalTeam:E.total},recentActivity:S};return o.NextResponse.json({success:!0,data:_})}catch(e){return console.error("User details fetch error:",e),o.NextResponse.json({success:!1,error:"Failed to fetch user details"},{status:500})}}let p=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/users/[userId]/details/route",pathname:"/api/admin/users/[userId]/details",filename:"route",bundlePath:"app/api/admin/users/[userId]/details/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\users\\[userId]\\details\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:f,workUnitAsyncStorage:E,serverHooks:S}=p;function g(){return(0,n.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:E})}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580,7911,925,2746],()=>r(44823));module.exports=a})();