"use strict";(()=>{var e={};e.id=7485,e.ids=[1111,7485],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},21111:(e,r,t)=>{t.d(r,{X:()=>n,emailService:()=>i});var s=t(49526),o=t(6710);class a{async getEmailConfig(){try{let e=await o.T8.getEmailSettings();if(!e||!e.smtpHost||!e.smtpUser||!e.smtpPassword)return console.warn("Email configuration not found or incomplete"),null;return{host:e.smtpHost,port:e.smtpPort||587,secure:e.smtpSecure||!1,user:e.smtpUser,password:e.smtpPassword,fromName:e.fromName||"HashCoreX",fromEmail:e.fromEmail||e.smtpUser}}catch(e){return console.error("Failed to get email configuration:",e),null}}async initializeTransporter(e=!1){try{if(this.config=await this.getEmailConfig(),!this.config)return console.warn("Email configuration not available - email service disabled"),!1;if(!this.config.host||!this.config.user||!this.config.password)return console.error("Email configuration incomplete - missing host, user, or password"),!1;if(this.transporter=s.createTransport({host:this.config.host,port:this.config.port,secure:this.config.secure,auth:{user:this.config.user,pass:this.config.password},tls:{rejectUnauthorized:!1},connectionTimeout:1e4,greetingTimeout:5e3,socketTimeout:1e4}),e)console.log("Email transporter initialized (verification skipped)");else{let e=this.transporter.verify(),r=new Promise((e,r)=>setTimeout(()=>r(Error("Email verification timeout")),15e3));await Promise.race([e,r]),console.log("Email transporter initialized and verified successfully")}return!0}catch(e){return console.error("Failed to initialize email transporter:",e),this.transporter=null,this.config=null,!1}}async sendEmail(e){try{if((!this.transporter||!this.config)&&(console.log("Email transporter not initialized, attempting to initialize..."),!await this.initializeTransporter(!0)))return console.warn("Email service not configured - skipping email send"),!1;if(!e.to||!e.subject)return console.error("Invalid email data - missing recipient or subject"),!1;let r={from:`"${this.config.fromName}" <${this.config.fromEmail}>`,to:e.to,subject:e.subject,html:e.html,text:e.text},t=this.transporter.sendMail(r),s=new Promise((e,r)=>setTimeout(()=>r(Error("Email send timeout")),3e4)),o=await Promise.race([t,s]);return console.log("Email sent successfully:",o.messageId),!0}catch(e){return console.error("Failed to send email:",e),this.transporter=null,this.config=null,!1}}async sendOTPEmail(e,r,t,s="email_verification"){let o="otp_verification";"password_reset"===s?o="password_reset_otp":"two_factor_auth"===s&&(o="two_factor_otp");let a=await this.getEmailTemplate(o);if(!a)return console.error(`Email template '${o}' not found. Please ensure email templates are seeded.`),!1;let i=a.htmlContent,n=a.textContent||"";return i=(i=i.replace(/{{firstName}}/g,t||"User")).replace(/{{otp}}/g,r),n=(n=n.replace(/{{firstName}}/g,t||"User")).replace(/{{otp}}/g,r),await this.sendEmail({to:e,subject:a.subject,html:i,text:n})}async getEmailTemplate(e){try{return await o.T8.getEmailTemplate(e)}catch(e){return console.error("Failed to get email template:",e),null}}async testConnection(){try{if(this.config=await this.getEmailConfig(),!this.config)return console.error("Email configuration not found or incomplete"),!1;return this.transporter=s.createTransport({host:this.config.host,port:this.config.port,secure:this.config.secure,auth:{user:this.config.user,pass:this.config.password},tls:{rejectUnauthorized:!1}}),await this.transporter.verify(),console.log("Email connection test successful"),!0}catch(e){throw console.error("Email connection test failed:",e),this.transporter=null,e}}constructor(){this.transporter=null,this.config=null}}let i=new a,n=()=>Math.floor(1e5+9e5*Math.random()).toString()},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37366:e=>{e.exports=require("dns")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45796:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>p,serverHooks:()=>h,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{POST:()=>d});var o=t(96559),a=t(48088),i=t(37719),n=t(32190),l=t(6710),c=t(21111),u=t(82629);async function d(e){try{let{email:r,firstName:t,purpose:s="email_verification"}=await e.json();if(!r)return n.NextResponse.json({success:!1,error:"Email is required"},{status:400});let o=await l.Gy.findByEmail(r);if("email_verification"===s){if(o)return n.NextResponse.json({success:!1,error:"Email is already registered"},{status:400})}else if("password_reset"===s&&!o)return n.NextResponse.json({success:!1,error:"Email not found. Please check your email address."},{status:400});let a=(0,c.X)(),i=new Date;i.setMinutes(i.getMinutes()+10),await l.oV.create({email:r,otp:a,purpose:s,expiresAt:i});let u=await l.XB.create({to:r,subject:"password_reset"===s?"Password Reset - HashCoreX":"Email Verification - HashCoreX",template:"password_reset"===s?"password_reset_otp":"otp_verification",status:"PENDING"});try{let e=o?.firstName||t||"";if(await c.emailService.sendOTPEmail(r,a,e,s))return await l.XB.updateStatus(u.id,"SENT"),n.NextResponse.json({success:!0,message:"OTP sent successfully to your email",data:{email:r,expiresAt:i.toISOString()}});return await l.XB.updateStatus(u.id,"FAILED","Email service error"),n.NextResponse.json({success:!1,error:"Failed to send OTP email. Please try again."},{status:500})}catch(e){return await l.XB.updateStatus(u.id,"FAILED",e instanceof Error?e.message:"Unknown error"),console.error("Email sending error:",e),n.NextResponse.json({success:!1,error:"Failed to send OTP email. Please check your email configuration."},{status:500})}}catch(r){return console.error("Send OTP error:",r),await u.v5.logApiError(e,r,"SEND_OTP_ERROR"),n.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}let p=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/auth/send-otp/route",pathname:"/api/auth/send-otp",filename:"route",bundlePath:"app/api/auth/send-otp/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\auth\\send-otp\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:g,serverHooks:h}=p;function f(){return(0,i.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:g})}},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},82629:(e,r,t)=>{t.d(r,{v5:()=>o});var s=t(6710);class o{static async logError(e){try{let r=e.error instanceof Error?e.error:Error(String(e.error)),t={action:e.action,userId:e.userId,adminId:e.adminId,details:{message:r.message,stack:r.stack,name:r.name,requestUrl:e.requestUrl,requestMethod:e.requestMethod,requestBody:e.requestBody,additionalData:e.additionalData,timestamp:new Date().toISOString()},ipAddress:e.ipAddress,userAgent:e.userAgent};await s.AJ.create(t),console.error(`[${e.action}] Error logged:`,{message:r.message,stack:r.stack,userId:e.userId,adminId:e.adminId,url:e.requestUrl})}catch(r){console.error("Failed to log error to database:",r),console.error("Original error:",e.error)}}static async logApiError(e,r,t,s,o,a){try{let i=null;try{if("GET"!==e.method&&e.headers.get("content-type")?.includes("application/json")){let r=e.clone();(i=await r.json()).password&&(i.password="[REDACTED]"),i.token&&(i.token="[REDACTED]"),i.apiKey&&(i.apiKey="[REDACTED]")}}catch(e){}await this.logError({action:t,error:r,userId:s,adminId:o,ipAddress:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||"unknown",userAgent:e.headers.get("user-agent")||"unknown",requestUrl:e.url,requestMethod:e.method,requestBody:i,additionalData:a})}catch(e){console.error("Failed to log API error:",e),console.error("Original error:",r)}}static async logClientError(e){try{await s.AJ.create({action:"CLIENT_ERROR",userId:e.userId,details:{message:e.message,stack:e.stack,url:e.url,timestamp:e.timestamp,additionalData:e.additionalData},userAgent:e.userAgent})}catch(e){console.error("Failed to log client error:",e)}}static async logAuthError(e,r,t,s){await this.logApiError(e,r,"AUTH_ERROR",void 0,void 0,{email:t,...s})}static async logDatabaseError(e,r,t,o,a){try{await s.AJ.create({action:"DATABASE_ERROR",userId:o,details:{message:e instanceof Error?e.message:String(e),stack:e instanceof Error?e.stack:void 0,operation:r,table:t,timestamp:new Date().toISOString(),additionalData:a}})}catch(r){console.error("Failed to log database error:",r),console.error("Original error:",e)}}static async logBusinessError(e,r,t,s,o){await this.logError({action:"BUSINESS_LOGIC_ERROR",error:e,userId:t,adminId:s,additionalData:{operation:r,...o}})}static async logExternalApiError(e,r,t,s,o){await this.logError({action:"EXTERNAL_API_ERROR",error:e,userId:s,additionalData:{service:r,endpoint:t,...o}})}static async logValidationError(e,r,t,s,o){await this.logError({action:"VALIDATION_ERROR",error:e,userId:s,additionalData:{field:r,value:t,...o}})}}},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,9526,925],()=>t(45796));module.exports=s})();