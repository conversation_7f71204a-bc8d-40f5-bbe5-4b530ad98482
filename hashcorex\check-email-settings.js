const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkEmailSettings() {
  try {
    console.log('Checking email settings in database...');
    
    // Check for email-related settings
    const emailKeys = [
      'smtpHost',
      'smtpPort', 
      'smtpSecure',
      'smtpUser',
      'smtpPassword',
      'fromName',
      'fromEmail',
      'emailEnabled'
    ];
    
    const settings = await prisma.adminSettings.findMany({
      where: {
        key: {
          in: emailKeys
        }
      }
    });
    
    console.log('Found email settings:');
    settings.forEach(setting => {
      console.log(`${setting.key}: ${setting.key === 'smtpPassword' ? '***' : setting.value}`);
    });
    
    if (settings.length === 0) {
      console.log('No email settings found in database.');
      console.log('You need to configure email settings through the admin panel.');
    }
    
  } catch (error) {
    console.error('Error checking email settings:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkEmailSettings();
