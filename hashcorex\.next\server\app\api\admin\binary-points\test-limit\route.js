"use strict";(()=>{var e={};e.id=5892,e.ids=[5892],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37366:e=>{e.exports=require("dns")},39542:(e,t,r)=>{r.d(t,{b9:()=>T,HU:()=>g,qc:()=>P,Lx:()=>h,DY:()=>R,DT:()=>_});var s=r(85663),i=r(43205),a=r.n(i),n=r(6710),o=r(45697);let l=o.z.object({DATABASE_URL:o.z.string().url("DATABASE_URL must be a valid PostgreSQL URL"),DIRECT_URL:o.z.string().url("DIRECT_URL must be a valid PostgreSQL URL"),JWT_SECRET:o.z.string().min(32,"JWT_SECRET must be at least 32 characters long").refine(e=>{let t=/[A-Z]/.test(e),r=/[a-z]/.test(e),s=/\d/.test(e),i=/[!@#$%^&*(),.?":{}|<>]/.test(e);return t&&r&&s&&i},"JWT_SECRET should contain uppercase, lowercase, numbers, and special characters"),JWT_EXPIRES_IN:o.z.string().default("30d"),NODE_ENV:o.z.enum(["development","production","test"]).default("development"),NEXT_PUBLIC_APP_URL:o.z.string().url().optional(),PORT:o.z.string().regex(/^\d+$/).transform(Number).default("3000"),SMTP_HOST:o.z.string().optional(),SMTP_PORT:o.z.string().regex(/^\d+$/).transform(Number).optional(),SMTP_USER:o.z.string().email().optional(),SMTP_PASSWORD:o.z.string().optional(),SMTP_SECURE:o.z.string().transform(e=>"true"===e).optional(),TRON_NETWORK:o.z.enum(["mainnet","testnet"]).default("testnet"),TRON_API_KEY:o.z.string().optional(),USDT_CONTRACT_ADDRESS:o.z.string().optional(),MAX_FILE_SIZE:o.z.string().regex(/^\d+$/).transform(Number).default("10485760"),UPLOAD_DIR:o.z.string().default("./public/uploads"),BCRYPT_ROUNDS:o.z.string().regex(/^\d+$/).transform(Number).default("12"),SESSION_TIMEOUT:o.z.string().regex(/^\d+$/).transform(Number).default("1800"),RATE_LIMIT_WINDOW_MS:o.z.string().regex(/^\d+$/).transform(Number).default("900000"),RATE_LIMIT_MAX_REQUESTS:o.z.string().regex(/^\d+$/).transform(Number).default("100"),COINGECKO_API_URL:o.z.string().url().default("https://api.coingecko.com/api/v3"),LOG_LEVEL:o.z.enum(["error","warn","info","debug"]).default("info"),ENABLE_REQUEST_LOGGING:o.z.string().transform(e=>"true"===e).default("false"),ENABLE_REGISTRATION:o.z.string().transform(e=>"true"===e).default("true"),ENABLE_KYC:o.z.string().transform(e=>"true"===e).default("true"),ENABLE_WITHDRAWALS:o.z.string().transform(e=>"true"===e).default("true")}).refine(e=>{let t=[e.SMTP_HOST,e.SMTP_PORT,e.SMTP_USER,e.SMTP_PASSWORD],r=t.some(e=>void 0!==e),s=t.every(e=>void 0!==e);return!r||!!s},{message:"If SMTP configuration is provided, all SMTP fields (HOST, PORT, USER, PASSWORD) must be provided"});function u(){try{let e=l.safeParse(process.env);if(!e.success){let t=e.error.errors.map(e=>`${e.path.join(".")}: ${e.message}`);return{success:!1,errors:t}}return{success:!0,data:e.data}}catch(e){return{success:!1,errors:["Failed to validate environment variables"]}}}let d=null;function c(){if(!d){let e=u();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),d=e.data,console.log("✅ Environment variables validated successfully")}return d}let p={jwt:{secret:()=>c().JWT_SECRET,expiresIn:()=>c().JWT_EXPIRES_IN},security:{bcryptRounds:()=>c().BCRYPT_ROUNDS,sessionTimeout:()=>c().SESSION_TIMEOUT,maxFileSize:()=>c().MAX_FILE_SIZE,uploadDir:()=>c().UPLOAD_DIR}};if("phase-production-build"!==process.env.NEXT_PHASE){let e=u();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),console.log("✅ Environment variables validated successfully")}let m=async e=>await s.Ay.hash(e,p.security.bcryptRounds()),f=async(e,t)=>await s.Ay.compare(e,t),g=e=>a().sign(e,p.jwt.secret(),{expiresIn:p.jwt.expiresIn()}),E=e=>{try{return a().verify(e,p.jwt.secret())}catch(e){return null}},S=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},T=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=E(t);if(!r)return{authenticated:!1,user:null};let s=await n.Gy.findByEmail(r.email);return s?{authenticated:!0,user:s}:{authenticated:!1,user:null}},R=async e=>{let t,s;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await n.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let i=await m(e.password),a=!1;do s=S(),a=!await n.Gy.findByReferralId(s);while(!a);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:i,referralId:s});if(t){let{placeUserByReferralType:s}=await r.e(2746).then(r.bind(r,2746)),i="general";"left"===e.placementSide?i="left":"right"===e.placementSide&&(i="right"),await s(t,o.id,i)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},h=async e=>{let t=await n.Gy.findByEmail(e.email);if(!t||!await f(e.password,t.password))throw Error("Invalid email or password");return{token:g({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},_=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),P=async e=>{let t=await n.Gy.findById(e);return t?.role==="ADMIN"}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71088:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>c,serverHooks:()=>f,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{POST:()=>d});var i=r(96559),a=r(48088),n=r(37719),o=r(32190),l=r(39542),u=r(6710);async function d(e){try{let{authenticated:t,user:r}=await (0,l.b9)(e);if(!t||!r)return o.NextResponse.json({success:!1,error:"Authentication required"},{status:401});if("ADMIN"!==r.role)return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let{testUserId:s,testSide:i,testPoints:a}=await e.json();if(!s||!i||!a)return o.NextResponse.json({success:!1,error:"Missing required test parameters"},{status:400});let n=parseFloat(await u.rs.get("MAX_BINARY_POINTS_PER_SIDE")||"10"),d=await u.FW.findByUserId(s),c=d?.leftPoints||0,p=d?.rightPoints||0;console.log("Binary Points Limit Test:"),console.log(`Max Points Per Side: ${n}`),console.log(`Current Left Points: ${c}`),console.log(`Current Right Points: ${p}`),console.log(`Test Side: ${i}`),console.log(`Test Points to Add: ${a}`);let m=0,f=!1,g="";if("LEFT"===i?c>=n?(g=`Left side has reached maximum (${c}/${n}). No points can be added.`,f=!1):(m=Math.min(a,n-c),f=!0,g=`Can add ${m} points to left side (${m<a?"capped at limit":"full amount"})`):p>=n?(g=`Right side has reached maximum (${p}/${n}). No points can be added.`,f=!1):(m=Math.min(a,n-p),f=!0,g=`Can add ${m} points to right side (${m<a?"capped at limit":"full amount"})`),console.log(`Result: ${g}`),f&&m>0){let e="LEFT"===i?{leftPoints:m}:{rightPoints:m};await u.FW.upsert({userId:s,...e}),console.log(`Successfully added ${m} points to ${i} side for user ${s}`)}let E=await u.FW.findByUserId(s);return o.NextResponse.json({success:!0,message:"Binary points limit test completed",data:{settings:{maxPointsPerSide:n},before:{leftPoints:c,rightPoints:p},test:{side:i,requestedPoints:a,pointsAdded:m,canAddPoints:f,reason:g},after:{leftPoints:E?.leftPoints||0,rightPoints:E?.rightPoints||0}}})}catch(e){return console.error("Binary points limit test error:",e),o.NextResponse.json({success:!1,error:"Failed to test binary points limit"},{status:500})}}let c=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/admin/binary-points/test-limit/route",pathname:"/api/admin/binary-points/test-limit",filename:"route",bundlePath:"app/api/admin/binary-points/test-limit/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\binary-points\\test-limit\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:p,workUnitAsyncStorage:m,serverHooks:f}=c;function g(){return(0,n.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:m})}},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,7911,925],()=>r(71088));module.exports=s})();