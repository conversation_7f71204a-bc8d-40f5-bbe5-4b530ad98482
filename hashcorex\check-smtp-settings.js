const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkSMTPSettings() {
  try {
    console.log('Checking SMTP settings in database...\n');
    
    // Get all email-related settings
    const emailKeys = [
      'smtpHost',
      'smtpPort', 
      'smtpSecure',
      'smtpUser',
      'smtpPassword',
      'fromName',
      'fromEmail',
      'emailEnabled'
    ];
    
    const settings = await prisma.adminSettings.findMany({
      where: {
        key: {
          in: emailKeys
        }
      },
      orderBy: {
        key: 'asc'
      }
    });
    
    console.log('Current SMTP settings:');
    console.log('=====================');
    
    if (settings.length === 0) {
      console.log('❌ No SMTP settings found in database');
      return;
    }
    
    const settingsObject = {};
    settings.forEach(setting => {
      settingsObject[setting.key] = setting.value;
    });
    
    // Display settings with validation
    emailKeys.forEach(key => {
      const value = settingsObject[key];
      const status = value ? '✅' : '❌';
      console.log(`${status} ${key}: ${value || 'NOT SET'}`);
    });
    
    console.log('\nValidation:');
    console.log('===========');
    
    // Check required fields
    const requiredFields = ['smtpHost', 'smtpUser', 'smtpPassword', 'fromEmail'];
    let allRequired = true;
    
    requiredFields.forEach(field => {
      if (!settingsObject[field]) {
        console.log(`❌ Missing required field: ${field}`);
        allRequired = false;
      }
    });
    
    if (allRequired) {
      console.log('✅ All required SMTP fields are present');
    }
    
    // Check for common issues
    console.log('\nCommon Issues Check:');
    console.log('===================');
    
    if (settingsObject.smtpHost && settingsObject.smtpHost.includes('"')) {
      console.log('⚠️  SMTP Host contains quotes - this may cause connection issues');
    }
    
    if (settingsObject.smtpUser && settingsObject.smtpUser.includes('"')) {
      console.log('⚠️  SMTP User contains quotes - this may cause authentication issues');
    }
    
    if (settingsObject.fromEmail && settingsObject.fromEmail.includes('"')) {
      console.log('⚠️  From Email contains quotes - this may cause sending issues');
    }
    
    const port = parseInt(settingsObject.smtpPort);
    if (isNaN(port) || port < 1 || port > 65535) {
      console.log('⚠️  Invalid SMTP port number');
    }
    
    console.log('\nRecommendations:');
    console.log('===============');
    
    if (settingsObject.smtpHost === 'premium315.web-hosting.com') {
      console.log('💡 Using premium315.web-hosting.com - ensure this hostname is correct and accessible');
      console.log('💡 Try using mail.premium315.web-hosting.com or smtp.premium315.web-hosting.com instead');
    }
    
    if (settingsObject.smtpPort === '465') {
      console.log('💡 Port 465 detected - ensure smtpSecure is set to true for SSL');
    } else if (settingsObject.smtpPort === '587') {
      console.log('💡 Port 587 detected - this is typically used with STARTTLS (smtpSecure: false)');
    }
    
  } catch (error) {
    console.error('Error checking SMTP settings:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkSMTPSettings();
