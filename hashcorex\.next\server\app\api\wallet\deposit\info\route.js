"use strict";(()=>{var e={};e.id=6377,e.ids=[6377],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37366:e=>{e.exports=require("dns")},39542:(e,t,r)=>{r.d(t,{b9:()=>T,HU:()=>E,qc:()=>A,Lx:()=>_,DY:()=>I,DT:()=>R});var s=r(85663),a=r(43205),i=r.n(a),n=r(6710),o=r(45697);let l=o.z.object({DATABASE_URL:o.z.string().url("DATABASE_URL must be a valid PostgreSQL URL"),DIRECT_URL:o.z.string().url("DIRECT_URL must be a valid PostgreSQL URL"),JWT_SECRET:o.z.string().min(32,"JWT_SECRET must be at least 32 characters long").refine(e=>{let t=/[A-Z]/.test(e),r=/[a-z]/.test(e),s=/\d/.test(e),a=/[!@#$%^&*(),.?":{}|<>]/.test(e);return t&&r&&s&&a},"JWT_SECRET should contain uppercase, lowercase, numbers, and special characters"),JWT_EXPIRES_IN:o.z.string().default("30d"),NODE_ENV:o.z.enum(["development","production","test"]).default("development"),NEXT_PUBLIC_APP_URL:o.z.string().url().optional(),PORT:o.z.string().regex(/^\d+$/).transform(Number).default("3000"),SMTP_HOST:o.z.string().optional(),SMTP_PORT:o.z.string().regex(/^\d+$/).transform(Number).optional(),SMTP_USER:o.z.string().email().optional(),SMTP_PASSWORD:o.z.string().optional(),SMTP_SECURE:o.z.string().transform(e=>"true"===e).optional(),TRON_NETWORK:o.z.enum(["mainnet","testnet"]).default("testnet"),TRON_API_KEY:o.z.string().optional(),USDT_CONTRACT_ADDRESS:o.z.string().optional(),MAX_FILE_SIZE:o.z.string().regex(/^\d+$/).transform(Number).default("10485760"),UPLOAD_DIR:o.z.string().default("./public/uploads"),BCRYPT_ROUNDS:o.z.string().regex(/^\d+$/).transform(Number).default("12"),SESSION_TIMEOUT:o.z.string().regex(/^\d+$/).transform(Number).default("1800"),RATE_LIMIT_WINDOW_MS:o.z.string().regex(/^\d+$/).transform(Number).default("900000"),RATE_LIMIT_MAX_REQUESTS:o.z.string().regex(/^\d+$/).transform(Number).default("100"),COINGECKO_API_URL:o.z.string().url().default("https://api.coingecko.com/api/v3"),LOG_LEVEL:o.z.enum(["error","warn","info","debug"]).default("info"),ENABLE_REQUEST_LOGGING:o.z.string().transform(e=>"true"===e).default("false"),ENABLE_REGISTRATION:o.z.string().transform(e=>"true"===e).default("true"),ENABLE_KYC:o.z.string().transform(e=>"true"===e).default("true"),ENABLE_WITHDRAWALS:o.z.string().transform(e=>"true"===e).default("true")}).refine(e=>{let t=[e.SMTP_HOST,e.SMTP_PORT,e.SMTP_USER,e.SMTP_PASSWORD],r=t.some(e=>void 0!==e),s=t.every(e=>void 0!==e);return!r||!!s},{message:"If SMTP configuration is provided, all SMTP fields (HOST, PORT, USER, PASSWORD) must be provided"});function u(){try{let e=l.safeParse(process.env);if(!e.success){let t=e.error.errors.map(e=>`${e.path.join(".")}: ${e.message}`);return{success:!1,errors:t}}return{success:!0,data:e.data}}catch(e){return{success:!1,errors:["Failed to validate environment variables"]}}}let d=null;function p(){if(!d){let e=u();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),d=e.data,console.log("✅ Environment variables validated successfully")}return d}let c={jwt:{secret:()=>p().JWT_SECRET,expiresIn:()=>p().JWT_EXPIRES_IN},security:{bcryptRounds:()=>p().BCRYPT_ROUNDS,sessionTimeout:()=>p().SESSION_TIMEOUT,maxFileSize:()=>p().MAX_FILE_SIZE,uploadDir:()=>p().UPLOAD_DIR}};if("phase-production-build"!==process.env.NEXT_PHASE){let e=u();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),console.log("✅ Environment variables validated successfully")}let f=async e=>await s.Ay.hash(e,c.security.bcryptRounds()),m=async(e,t)=>await s.Ay.compare(e,t),E=e=>i().sign(e,c.jwt.secret(),{expiresIn:c.jwt.expiresIn()}),g=e=>{try{return i().verify(e,c.jwt.secret())}catch(e){return null}},S=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},T=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=g(t);if(!r)return{authenticated:!1,user:null};let s=await n.Gy.findByEmail(r.email);return s?{authenticated:!0,user:s}:{authenticated:!1,user:null}},I=async e=>{let t,s;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await n.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let a=await f(e.password),i=!1;do s=S(),i=!await n.Gy.findByReferralId(s);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:a,referralId:s});if(t){let{placeUserByReferralType:s}=await r.e(2746).then(r.bind(r,2746)),a="general";"left"===e.placementSide?a="left":"right"===e.placementSide&&(a="right"),await s(t,o.id,a)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},_=async e=>{let t=await n.Gy.findByEmail(e.email);if(!t||!await m(e.password,t.password))throw Error("Invalid email or password");return{token:E({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},R=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),A=async e=>{let t=await n.Gy.findById(e);return t?.role==="ADMIN"}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},54027:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>c,serverHooks:()=>E,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{GET:()=>p});var a=r(96559),i=r(48088),n=r(37719),o=r(32190),l=r(39542),u=r(6710),d=r(59480);async function p(e){try{let{authenticated:t,user:r}=await (0,l.b9)(e);if(!t||!r)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});let s=await u.rs.get("usdtDepositAddress");s||(s=await u.rs.get("USDT_DEPOSIT_ADDRESS")),s&&(s=s.replace(/['"]/g,"").trim());let a=await u.rs.get("minDepositAmount");a||(a=await u.rs.get("MIN_DEPOSIT_AMOUNT")),a=parseFloat(a||"10");let i=await u.rs.get("maxDepositAmount");i||(i=await u.rs.get("MAX_DEPOSIT_AMOUNT")),i=parseFloat(i||"10000");let n=await u.rs.get("depositEnabled");n||(n=await u.rs.get("DEPOSIT_ENABLED")),n="true"===n||!0===n;let p=await u.rs.get("minConfirmations");p||(p=await u.rs.get("MIN_CONFIRMATIONS")),p=parseInt(p||"1");let c=await u.rs.get("depositFeePercentage");c||(c=await u.rs.get("DEPOSIT_FEE_PERCENTAGE")),c=parseFloat(c||"0");let{searchParams:f}=new URL(e.url),m=parseInt(f.get("limit")||"10"),E=parseInt(f.get("offset")||"0"),g=await u.J6.findByUserId(r.id,{limit:Math.min(m,50),offset:E}),S=g.filter(e=>"COMPLETED"===e.status||"CONFIRMED"===e.status),T=g.filter(e=>["PENDING","PENDING_VERIFICATION","WAITING_FOR_CONFIRMATIONS"].includes(e.status)),I=S.reduce((e,t)=>e+t.usdtAmount,0),_=S.length,R=T.length,A=await (0,d.i4)();return o.NextResponse.json({success:!0,data:{depositInfo:{depositAddress:s||null,minDepositAmount:a,maxDepositAmount:i,depositEnabled:n,minConfirmations:p,depositFeePercentage:c,network:"TRC20",currency:"USDT",tronNetwork:A.network,tronApiUrl:A.apiUrl,usdtContract:A.usdtContract},userStats:{totalDeposited:I,depositCount:_,pendingDeposits:R},deposits:g.map(e=>({id:e.id,transactionId:e.transactionId,amount:e.usdtAmount,status:e.status,confirmations:e.confirmations,blockNumber:e.blockNumber,senderAddress:e.senderAddress,createdAt:e.createdAt,verifiedAt:e.verifiedAt,processedAt:e.processedAt,failureReason:e.failureReason})),pagination:{limit:m,offset:E,hasMore:g.length===m}}})}catch(e){return console.error("Deposit info error:",e),o.NextResponse.json({success:!1,error:"Failed to fetch deposit information"},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/wallet/deposit/info/route",pathname:"/api/wallet/deposit/info",filename:"route",bundlePath:"app/api/wallet/deposit/info/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\wallet\\deposit\\info\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:m,serverHooks:E}=c;function g(){return(0,n.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:m})}},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,7911,925,9480],()=>r(54027));module.exports=s})();