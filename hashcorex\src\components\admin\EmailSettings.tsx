'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>eader, Card<PERSON><PERSON>le, CardContent, Button, Input, useMessageBox } from '@/components/ui';
import { Mail, Send, Settings, Shield, Eye, EyeOff, TestTube, FileText, Plus, Edit, Trash2 } from 'lucide-react';
import { EmailTemplateModal } from './EmailTemplateModal';

interface EmailSettings {
  smtpHost: string;
  smtpPort: number;
  smtpSecure: boolean;
  smtpUser: string;
  smtpPassword: string;
  fromName: string;
  fromEmail: string;
  emailEnabled: boolean;
}

interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  htmlContent: string;
  textContent?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export const EmailSettings: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'settings' | 'templates'>('settings');
  const [settings, setSettings] = useState<EmailSettings>({
    smtpHost: '',
    smtpPort: 587,
    smtpSecure: false,
    smtpUser: '',
    smtpPassword: '',
    fromName: 'HashCoreX',
    fromEmail: '',
    emailEnabled: true,
  });
  const [templates, setTemplates] = useState<EmailTemplate[]>([]);

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [testing, setTesting] = useState(false);
  const [seedingTemplates, setSeedingTemplates] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [testEmail, setTestEmail] = useState('');
  const [editingTemplate, setEditingTemplate] = useState<EmailTemplate | null>(null);
  const [showTemplateModal, setShowTemplateModal] = useState(false);
  const [smtpLocked, setSmtpLocked] = useState(false);
  const [showEditConfirm, setShowEditConfirm] = useState(false);

  const { showMessage, MessageBoxComponent } = useMessageBox();

  useEffect(() => {
    fetchEmailSettings();
    if (activeTab === 'templates') {
      fetchEmailTemplates();
    }
  }, [activeTab]);

  const fetchEmailSettings = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/email-settings', {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          // Clean up quotes from string values and ensure all values are properly initialized
          const cleanString = (value: any) => {
            if (typeof value === 'string') {
              return value.replace(/^["']|["']$/g, ''); // Remove leading/trailing quotes
            }
            return value || '';
          };

          const cleanedSettings = {
            smtpHost: cleanString(data.data.smtpHost),
            smtpPort: data.data.smtpPort || 587,
            smtpSecure: Boolean(data.data.smtpSecure),
            smtpUser: cleanString(data.data.smtpUser),
            smtpPassword: cleanString(data.data.smtpPassword),
            fromName: cleanString(data.data.fromName) || 'HashCoreX',
            fromEmail: cleanString(data.data.fromEmail),
            emailEnabled: Boolean(data.data.emailEnabled),
          };

          setSettings(cleanedSettings);

          // Lock SMTP settings if they are configured
          const hasSmtpConfig = cleanedSettings.smtpHost && cleanedSettings.smtpUser && cleanedSettings.smtpPassword;
          setSmtpLocked(hasSmtpConfig);
        }
      }
    } catch (error) {
      console.error('Failed to fetch email settings:', error);
      showMessage({
        title: 'Error',
        message: 'Failed to load email settings',
        variant: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      
      const response = await fetch('/api/admin/email-settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(settings),
      });

      const data = await response.json();

      if (data.success) {
        showMessage({
          title: 'Success',
          message: 'Email settings saved successfully',
          variant: 'success',
        });
      } else {
        throw new Error(data.error || 'Failed to save settings');
      }
    } catch (error) {
      console.error('Failed to save email settings:', error);
      showMessage({
        title: 'Error',
        message: error instanceof Error ? error.message : 'Failed to save email settings',
        variant: 'error',
      });
    } finally {
      setSaving(false);
    }
  };

  const handleTestConnection = async () => {
    try {
      setTesting(true);

      const response = await fetch('/api/admin/email-settings/test-connection', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });

      const data = await response.json();

      if (data.success && data.connected) {
        showMessage({
          title: 'Success',
          message: 'SMTP connection successful! Your email settings are working correctly.',
          variant: 'success',
        });
      } else {
        showMessage({
          title: 'Connection Failed',
          message: data.error || 'SMTP connection failed. Please check your settings.',
          variant: 'error',
        });
      }
    } catch (error) {
      console.error('Failed to test SMTP connection:', error);
      showMessage({
        title: 'Error',
        message: error instanceof Error ? error.message : 'Failed to test SMTP connection',
        variant: 'error',
      });
    } finally {
      setTesting(false);
    }
  };

  const handleTestEmail = async () => {
    if (!testEmail) {
      showMessage({
        title: 'Error',
        message: 'Please enter a test email address',
        variant: 'error',
      });
      return;
    }

    try {
      setTesting(true);

      const response = await fetch('/api/admin/email-settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ testEmail }),
      });

      const data = await response.json();

      if (data.success) {
        showMessage({
          title: 'Success',
          message: 'Test email sent successfully! Check your inbox.',
          variant: 'success',
        });
      } else {
        throw new Error(data.error || 'Failed to send test email');
      }
    } catch (error) {
      console.error('Failed to send test email:', error);
      showMessage({
        title: 'Error',
        message: error instanceof Error ? error.message : 'Failed to send test email',
        variant: 'error',
      });
    } finally {
      setTesting(false);
    }
  };

  const handleInputChange = (field: keyof EmailSettings, value: any) => {
    setSettings(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // Template management functions
  const fetchEmailTemplates = async () => {
    try {
      const response = await fetch('/api/admin/email-templates', {
        credentials: 'include',
      });

      const data = await response.json();

      if (data.success) {
        setTemplates(data.data);
      } else {
        throw new Error(data.error || 'Failed to fetch templates');
      }
    } catch (error) {
      console.error('Failed to fetch email templates:', error);
      showMessage({
        title: 'Error',
        message: 'Failed to fetch email templates',
        variant: 'error',
      });
    }
  };

  const handleCreateTemplate = () => {
    setEditingTemplate(null);
    setShowTemplateModal(true);
  };

  const handleEditTemplate = (template: EmailTemplate) => {
    setEditingTemplate(template);
    setShowTemplateModal(true);
  };

  const handleDeleteTemplate = async (templateName: string) => {
    if (!confirm('Are you sure you want to delete this template?')) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/email-templates/${templateName}`, {
        method: 'DELETE',
        credentials: 'include',
      });

      const data = await response.json();

      if (data.success) {
        showMessage({
          title: 'Success',
          message: 'Template deleted successfully',
          variant: 'success',
        });
        fetchEmailTemplates();
      } else {
        throw new Error(data.error || 'Failed to delete template');
      }
    } catch (error) {
      console.error('Failed to delete template:', error);
      showMessage({
        title: 'Error',
        message: error instanceof Error ? error.message : 'Failed to delete template',
        variant: 'error',
      });
    }
  };

  const handleSmtpEdit = () => {
    if (smtpLocked) {
      setShowEditConfirm(true);
    }
  };

  const confirmSmtpEdit = () => {
    setSmtpLocked(false);
    setShowEditConfirm(false);
    showMessage({
      title: 'SMTP Settings Unlocked',
      message: 'You can now edit SMTP configuration. Remember to save your changes.',
      variant: 'success',
    });
  };

  const handleSmtpInputChange = (field: string, value: any) => {
    if (!smtpLocked) {
      handleInputChange(field, value);
    } else {
      handleSmtpEdit();
    }
  };

  const handleSeedTemplates = async () => {
    try {
      setSeedingTemplates(true);
      const response = await fetch('/api/admin/email-templates/seed', {
        method: 'POST',
        credentials: 'include',
      });

      const data = await response.json();

      if (data.success) {
        const createdCount = data.data.filter((result: any) => result.status === 'created').length;
        const existingCount = data.data.filter((result: any) => result.status === 'exists').length;

        showMessage({
          title: 'Success',
          message: `Templates seeded successfully! Created: ${createdCount}, Already existed: ${existingCount}`,
          variant: 'success',
        });
        fetchEmailTemplates();
      } else {
        throw new Error(data.error || 'Failed to seed templates');
      }
    } catch (error) {
      console.error('Failed to seed email templates:', error);
      showMessage({
        title: 'Error',
        message: 'Failed to seed email templates',
        variant: 'error',
      });
    } finally {
      setSeedingTemplates(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <MessageBoxComponent />
      
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">Email Settings</h1>
          <p className="text-slate-400 mt-1">Configure SMTP settings and email templates</p>
        </div>
        <div className="flex items-center space-x-3">
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={settings.emailEnabled}
              onChange={(e) => handleInputChange('emailEnabled', e.target.checked)}
              className="rounded border-slate-600 text-blue-600 focus:ring-blue-500"
            />
            <span className="text-sm text-slate-300">Email Enabled</span>
          </label>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-slate-800 p-1 rounded-lg">
        <button
          onClick={() => setActiveTab('settings')}
          className={`flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'settings'
              ? 'bg-blue-600 text-white'
              : 'text-slate-400 hover:text-white hover:bg-slate-700'
          }`}
        >
          <Settings className="w-4 h-4 mr-2" />
          SMTP Settings
        </button>
        <button
          onClick={() => setActiveTab('templates')}
          className={`flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'templates'
              ? 'bg-blue-600 text-white'
              : 'text-slate-400 hover:text-white hover:bg-slate-700'
          }`}
        >
          <FileText className="w-4 h-4 mr-2" />
          Email Templates
        </button>
      </div>

      {activeTab === 'settings' && (
        <>
      {/* SMTP Configuration */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="flex items-center justify-between text-white">
            <div className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              SMTP Configuration
            </div>
            {smtpLocked && (
              <div className="flex items-center gap-2">
                <Shield className="h-4 w-4 text-green-400" />
                <span className="text-sm text-green-400">Protected</span>
              </div>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {smtpLocked && (
            <div className="bg-blue-900/20 border border-blue-700 rounded-lg p-4">
              <div className="flex items-center gap-2 text-blue-300 mb-2">
                <Shield className="h-4 w-4" />
                <span className="font-medium">SMTP Settings Protected</span>
              </div>
              <p className="text-sm text-blue-200 mb-3">
                SMTP configuration is locked to prevent accidental changes. Click on any field to unlock and edit.
              </p>
              <Button
                onClick={confirmSmtpEdit}
                size="sm"
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                Unlock SMTP Settings
              </Button>
            </div>
          )}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                SMTP Host *
              </label>
              <Input
                type="text"
                value={settings.smtpHost}
                onChange={(e) => handleSmtpInputChange('smtpHost', e.target.value)}
                onClick={handleSmtpEdit}
                placeholder="smtp.gmail.com"
                className={`bg-slate-700 border-slate-600 text-white ${smtpLocked ? 'cursor-pointer' : ''}`}
                readOnly={smtpLocked}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                SMTP Port
              </label>
              <Input
                type="number"
                value={settings.smtpPort}
                onChange={(e) => handleSmtpInputChange('smtpPort', parseInt(e.target.value))}
                onClick={handleSmtpEdit}
                placeholder="587"
                className={`bg-slate-700 border-slate-600 text-white ${smtpLocked ? 'cursor-pointer' : ''}`}
                readOnly={smtpLocked}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                SMTP User *
              </label>
              <Input
                type="text"
                value={settings.smtpUser}
                onChange={(e) => handleSmtpInputChange('smtpUser', e.target.value)}
                onClick={handleSmtpEdit}
                placeholder="<EMAIL>"
                className={`bg-slate-700 border-slate-600 text-white ${smtpLocked ? 'cursor-pointer' : ''}`}
                readOnly={smtpLocked}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                SMTP Password *
              </label>
              <div className="relative">
                <Input
                  type={showPassword ? 'text' : 'password'}
                  value={settings.smtpPassword}
                  onChange={(e) => handleSmtpInputChange('smtpPassword', e.target.value)}
                  onClick={handleSmtpEdit}
                  placeholder="App password or SMTP password"
                  className={`bg-slate-700 border-slate-600 text-white pr-10 ${smtpLocked ? 'cursor-pointer' : ''}`}
                  readOnly={smtpLocked}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4 text-slate-400" />
                  ) : (
                    <Eye className="h-4 w-4 text-slate-400" />
                  )}
                </button>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={settings.smtpSecure}
                onChange={(e) => handleInputChange('smtpSecure', e.target.checked)}
                className="rounded border-slate-600 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-slate-300">Use SSL/TLS</span>
            </label>
          </div>
        </CardContent>
      </Card>

      {/* Email Configuration */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-white">
            <Mail className="h-5 w-5" />
            Email Configuration
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                From Name
              </label>
              <Input
                type="text"
                value={settings.fromName}
                onChange={(e) => handleInputChange('fromName', e.target.value)}
                placeholder="HashCoreX"
                className="bg-slate-700 border-slate-600 text-white"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                From Email *
              </label>
              <Input
                type="email"
                value={settings.fromEmail}
                onChange={(e) => handleInputChange('fromEmail', e.target.value)}
                placeholder="<EMAIL>"
                className="bg-slate-700 border-slate-600 text-white"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Test Email */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-white">
            <TestTube className="h-5 w-5" />
            Test Email Configuration
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-end space-x-4">
            <div className="flex-1">
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Test Email Address
              </label>
              <Input
                type="email"
                value={testEmail}
                onChange={(e) => setTestEmail(e.target.value)}
                placeholder="<EMAIL>"
                className="bg-slate-700 border-slate-600 text-white"
              />
            </div>
            <Button
              onClick={handleTestEmail}
              disabled={testing || !testEmail}
              variant="outline"
              className="border-blue-600 text-blue-400 hover:bg-blue-600 hover:text-white"
            >
              {testing ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" />
              ) : (
                <Send className="h-4 w-4 mr-2" />
              )}
              Send Test
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex justify-end space-x-3">
        <Button
          onClick={handleTestConnection}
          disabled={testing || !settings.smtpHost || !settings.smtpUser}
          className="bg-green-600 hover:bg-green-700 text-white"
        >
          {testing ? (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
          ) : (
            <TestTube className="h-4 w-4 mr-2" />
          )}
          Test Connection
        </Button>
        <Button
          onClick={handleSave}
          disabled={saving}
          className="bg-blue-600 hover:bg-blue-700 text-white"
        >
          {saving ? (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
          ) : (
            <Shield className="h-4 w-4 mr-2" />
          )}
          Save Settings
        </Button>
      </div>
        </>
      )}

      {activeTab === 'templates' && (
        <div className="space-y-6">
          {/* Templates Header */}
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-white">Email Templates</h2>
              <p className="text-slate-400 text-sm">Manage email templates with variables like {`{{firstName}}, {{otp}}, etc.`}</p>
            </div>
            <div className="flex items-center gap-3">
              <Button
                onClick={handleSeedTemplates}
                variant="outline"
                className="border-blue-600 text-blue-400 hover:bg-blue-900/20"
                loading={seedingTemplates}
              >
                <FileText className="w-4 h-4 mr-2" />
                {seedingTemplates ? 'Seeding...' : 'Seed Default Templates'}
              </Button>
              <Button
                onClick={handleCreateTemplate}
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                <Plus className="w-4 h-4 mr-2" />
                New Template
              </Button>
            </div>
          </div>

          {/* Templates List - Card Layout */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {templates.map((template) => (
              <Card key={template.id} className="bg-slate-800 border-slate-700 hover:border-slate-600 transition-colors">
                <CardContent className="p-6">
                  <div className="space-y-4">
                    {/* Header */}
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h3 className="font-semibold text-white text-lg mb-2">{template.name}</h3>
                        <span className={`inline-flex px-3 py-1 rounded-full text-xs font-medium ${
                          template.isActive
                            ? 'bg-green-100 text-green-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {template.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </div>
                    </div>

                    {/* Content */}
                    <div className="space-y-2">
                      <div>
                        <p className="text-slate-300 text-sm font-medium">Subject:</p>
                        <p className="text-slate-400 text-sm line-clamp-2">{template.subject}</p>
                      </div>
                      <div>
                        <p className="text-slate-300 text-sm font-medium">Last Updated:</p>
                        <p className="text-slate-500 text-xs">
                          {new Date(template.updatedAt).toLocaleDateString()}
                        </p>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center gap-2 pt-2 border-t border-slate-700">
                      <Button
                        onClick={() => handleEditTemplate(template)}
                        variant="outline"
                        size="sm"
                        className="flex-1 border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white"
                      >
                        <Edit className="w-4 h-4 mr-2" />
                        Edit
                      </Button>
                      <Button
                        onClick={() => handleDeleteTemplate(template.name)}
                        variant="outline"
                        size="sm"
                        className="border-red-600 text-red-400 hover:bg-red-900/20 hover:border-red-500"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}

            {templates.length === 0 && (
              <div className="text-center py-12">
                <FileText className="w-12 h-12 text-slate-600 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-slate-400 mb-2">No templates found</h3>
                <p className="text-slate-500 mb-4">Create your first email template to get started.</p>
                <Button
                  onClick={handleCreateTemplate}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Create Template
                </Button>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Template Modal */}
      <EmailTemplateModal
        isOpen={showTemplateModal}
        onClose={() => setShowTemplateModal(false)}
        template={editingTemplate}
        onSave={fetchEmailTemplates}
      />

      {/* SMTP Edit Confirmation Dialog */}
      {showEditConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-slate-800 rounded-lg shadow-xl max-w-md w-full">
            <div className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <Shield className="h-6 w-6 text-yellow-400" />
                <h3 className="text-lg font-semibold text-white">Unlock SMTP Settings</h3>
              </div>
              <p className="text-slate-300 mb-6">
                Are you sure you want to unlock SMTP configuration? This will allow editing of sensitive email settings.
              </p>
              <div className="flex items-center gap-3">
                <Button
                  onClick={() => setShowEditConfirm(false)}
                  variant="outline"
                  className="flex-1 border-slate-600 text-slate-300"
                >
                  Cancel
                </Button>
                <Button
                  onClick={confirmSmtpEdit}
                  className="flex-1 bg-yellow-600 hover:bg-yellow-700 text-white"
                >
                  Unlock Settings
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Message Box */}
      <MessageBoxComponent />
    </div>
  );
};
