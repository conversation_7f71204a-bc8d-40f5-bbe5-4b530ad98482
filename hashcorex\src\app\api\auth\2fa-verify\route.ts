/**
 * 2FA Verification API Endpoint
 * Verify email-based two-factor authentication during login
 */

import { NextRequest, NextResponse } from 'next/server';
import { EmailTwoFactorAuth } from '@/lib/emailTwoFactorAuth';
import { userDb, systemLogDb } from '@/lib/database';
import { generateToken } from '@/lib/auth';
import { 
  withSecureErrorHandling, 
  createValidationError,
  createSecureResponse 
} from '@/lib/secureErrorHandler';

// POST - Verify 2FA OTP during login
const verify2FAHandler = async (request: NextRequest) => {
  const body = await request.json();
  const { email, otp } = body;

  // Validate input
  if (!email || !otp) {
    const error = createValidationError('Email and verification code are required');
    return createSecureResponse(error);
  }

  if (!/^\d{6}$/.test(otp)) {
    const error = createValidationError('Verification code must be 6 digits');
    return createSecureResponse(error);
  }

  // Find user
  const user = await userDb.findByEmail(email.toLowerCase());
  if (!user) {
    const error = createValidationError('Invalid credentials');
    return createSecureResponse(error);
  }

  // Check if 2FA is enabled
  const isEnabled = await EmailTwoFactorAuth.isEnabled(user.id);
  if (!isEnabled) {
    const error = createValidationError('Two-factor authentication is not enabled');
    return createSecureResponse(error);
  }

  // Verify 2FA OTP
  const verification = await EmailTwoFactorAuth.verifyTwoFactorOTP(
    user.id,
    otp,
    request.headers.get('x-forwarded-for') || 'unknown',
    request.headers.get('user-agent') || 'unknown'
  );

  if (!verification.valid) {
    const error = createValidationError('Invalid or expired verification code');
    return createSecureResponse(error);
  }

  // Generate JWT token for successful login
  const jwtToken = generateToken({
    userId: user.id,
    email: user.email,
  });

  // Log successful 2FA login
  await systemLogDb.create({
    action: 'EMAIL_TWO_FACTOR_LOGIN_SUCCESS',
    userId: user.id,
    details: {
      email: user.email,
      loginTime: new Date().toISOString(),
    },
    ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
    userAgent: request.headers.get('user-agent') || 'unknown',
  });

  // Create response
  const response = NextResponse.json({
    success: true,
    message: 'Two-factor authentication successful',
    data: {
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        referralId: user.referralId,
        kycStatus: user.kycStatus,
        role: user.role,
      },
    },
  });

  // Set secure HTTP-only cookie
  response.cookies.set('auth-token', jwtToken, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: 30 * 24 * 60 * 60, // 30 days
    path: '/',
  });

  // Add security headers
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-XSS-Protection', '1; mode=block');

  return response;
};

// Export the handler with secure error handling
export const POST = withSecureErrorHandling(verify2FAHandler, {
  endpoint: '/api/auth/2fa-verify',
  requireAuth: false,
});
