import { NextRequest, NextResponse } from 'next/server';
import { validateEmail, generateToken } from '@/lib/auth';
import { validatePassword } from '@/lib/utils';
import { userDb, otpDb, systemLogDb } from '@/lib/database';
import { ErrorLogger } from '@/lib/errorLogger';
import { SessionManager } from '@/lib/sessionManager';
import bcrypt from 'bcryptjs';

// POST - Reset password with OTP verification
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, newPassword, confirmPassword } = body;

    // Validation
    if (!email || !newPassword || !confirmPassword) {
      return NextResponse.json(
        { success: false, error: 'All fields are required' },
        { status: 400 }
      );
    }

    if (!validateEmail(email)) {
      return NextResponse.json(
        { success: false, error: 'Invalid email format' },
        { status: 400 }
      );
    }

    if (newPassword !== confirmPassword) {
      return NextResponse.json(
        { success: false, error: 'Passwords do not match' },
        { status: 400 }
      );
    }

    const passwordValidation = validatePassword(newPassword);
    if (!passwordValidation.isValid) {
      return NextResponse.json(
        { success: false, error: passwordValidation.errors.join(', ') },
        { status: 400 }
      );
    }

    // Check if user exists
    const user = await userDb.findByEmail(email);
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 400 }
      );
    }

    // Verify that there's a verified OTP for password reset
    const verifiedOtp = await otpDb.findVerified(email, 'password_reset');
    if (!verifiedOtp) {
      return NextResponse.json(
        { success: false, error: 'No verified OTP found. Please complete email verification first.' },
        { status: 400 }
      );
    }

    // Hash the new password
    const hashedPassword = await bcrypt.hash(newPassword, 12);

    // Update user password
    await userDb.updatePassword(user.id, hashedPassword);

    // Invalidate all existing sessions for security (password change)
    await SessionManager.invalidateOnSecurityEvent(user.id, 'PASSWORD_CHANGE');

    // Clean up the used OTP
    await otpDb.cleanup();

    // Log password reset
    await systemLogDb.create({
      action: 'PASSWORD_RESET',
      userId: user.id,
      details: {
        email: user.email,
        resetTime: new Date().toISOString(),
      },
      ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
    });

    // Generate token for automatic login after password reset
    const token = generateToken({
      userId: user.id,
      email: user.email,
    });

    // Set HTTP-only cookie for automatic login
    const response = NextResponse.json({
      success: true,
      message: 'Password reset successful',
      data: {
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          referralId: user.referralId,
          role: user.role,
          kycStatus: user.kycStatus,
          isActive: user.isActive,
          profilePicture: user.profilePicture,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt,
        },
        token,
      },
    });

    response.cookies.set('auth-token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 30 * 24 * 60 * 60, // 30 days
      path: '/',
    });

    return response;

  } catch (error: any) {
    console.error('Password reset error:', error);
    
    await ErrorLogger.logApiError(
      request,
      error as Error,
      'PASSWORD_RESET_ERROR'
    );

    return NextResponse.json(
      { success: false, error: error.message || 'Password reset failed' },
      { status: 500 }
    );
  }
}
