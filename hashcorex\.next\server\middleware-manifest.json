{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|uploads|crypto-icons).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|uploads|crypto-icons).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "avIl-agPOAA7aC2GJof-c", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "JbkaL9WKBPyo3Ykt65oQt4RvobSFsc6k70HnVTZ4FfQ=", "__NEXT_PREVIEW_MODE_ID": "9d561d10e2f260f54088adfb354e6cfd", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "2007ee88a48f10bd1d180b4eb227dceb1e1c08ab03ceb43df390144aae919dea", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "781ee9a72363ad7972c0ebb92145e6c01154dcbefa8cad7e8f5a6975156827a6"}}}, "functions": {}, "sortedMiddleware": ["/"]}