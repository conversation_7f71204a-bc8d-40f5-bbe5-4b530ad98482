const nodemailer = require('nodemailer');
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testSMTPConnection() {
  try {
    console.log('Testing SMTP connection...');
    
    // Get email settings from database
    const emailKeys = [
      'smtpHost',
      'smtpPort', 
      'smtpSecure',
      'smtpUser',
      'smtpPassword',
      'fromName',
      'fromEmail',
      'emailEnabled'
    ];
    
    const settings = await prisma.adminSettings.findMany({
      where: {
        key: {
          in: emailKeys
        }
      }
    });
    
    const settingsObject = {};
    settings.forEach(setting => {
      settingsObject[setting.key] = setting.value;
    });
    
    console.log('Using SMTP settings:');
    console.log('Host:', settingsObject.smtpHost);
    console.log('Port:', settingsObject.smtpPort);
    console.log('Secure:', settingsObject.smtpSecure);
    console.log('User:', settingsObject.smtpUser);
    console.log('From:', settingsObject.fromEmail);
    
    // Create transporter
    const transporter = nodemailer.createTransport({
      host: settingsObject.smtpHost,
      port: parseInt(settingsObject.smtpPort) || 587,
      secure: settingsObject.smtpSecure === 'true',
      auth: {
        user: settingsObject.smtpUser,
        pass: settingsObject.smtpPassword,
      },
      tls: {
        rejectUnauthorized: false,
      },
      connectionTimeout: 10000,
      greetingTimeout: 5000,
      socketTimeout: 10000,
    });
    
    console.log('\nTesting SMTP connection...');
    
    // Test connection
    const verified = await transporter.verify();
    console.log('✅ SMTP connection verified successfully:', verified);
    
    // Test sending a simple email
    console.log('\nTesting email send...');
    const testEmail = {
      from: `"${settingsObject.fromName}" <${settingsObject.fromEmail}>`,
      to: settingsObject.smtpUser, // Send to self for testing
      subject: 'SMTP Test - HashCoreX',
      text: 'This is a test email to verify SMTP configuration.',
      html: '<p>This is a test email to verify SMTP configuration.</p>'
    };
    
    const result = await transporter.sendMail(testEmail);
    console.log('✅ Test email sent successfully:', result.messageId);
    
  } catch (error) {
    console.error('❌ SMTP test failed:', error.message);
    console.error('Full error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testSMTPConnection();
