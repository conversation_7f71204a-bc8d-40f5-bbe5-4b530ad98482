"use strict";(()=>{var e={};e.id=3555,e.ids=[3555],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37366:e=>{e.exports=require("dns")},39542:(e,t,r)=>{r.d(t,{b9:()=>I,HU:()=>f,qc:()=>A,Lx:()=>_,DY:()=>y,DT:()=>T});var a=r(85663),n=r(43205),i=r.n(n),s=r(6710),o=r(45697);let u=o.z.object({DATABASE_URL:o.z.string().url("DATABASE_URL must be a valid PostgreSQL URL"),DIRECT_URL:o.z.string().url("DIRECT_URL must be a valid PostgreSQL URL"),JWT_SECRET:o.z.string().min(32,"JWT_SECRET must be at least 32 characters long").refine(e=>{let t=/[A-Z]/.test(e),r=/[a-z]/.test(e),a=/\d/.test(e),n=/[!@#$%^&*(),.?":{}|<>]/.test(e);return t&&r&&a&&n},"JWT_SECRET should contain uppercase, lowercase, numbers, and special characters"),JWT_EXPIRES_IN:o.z.string().default("30d"),NODE_ENV:o.z.enum(["development","production","test"]).default("development"),NEXT_PUBLIC_APP_URL:o.z.string().url().optional(),PORT:o.z.string().regex(/^\d+$/).transform(Number).default("3000"),SMTP_HOST:o.z.string().optional(),SMTP_PORT:o.z.string().regex(/^\d+$/).transform(Number).optional(),SMTP_USER:o.z.string().email().optional(),SMTP_PASSWORD:o.z.string().optional(),SMTP_SECURE:o.z.string().transform(e=>"true"===e).optional(),TRON_NETWORK:o.z.enum(["mainnet","testnet"]).default("testnet"),TRON_API_KEY:o.z.string().optional(),USDT_CONTRACT_ADDRESS:o.z.string().optional(),MAX_FILE_SIZE:o.z.string().regex(/^\d+$/).transform(Number).default("10485760"),UPLOAD_DIR:o.z.string().default("./public/uploads"),BCRYPT_ROUNDS:o.z.string().regex(/^\d+$/).transform(Number).default("12"),SESSION_TIMEOUT:o.z.string().regex(/^\d+$/).transform(Number).default("1800"),RATE_LIMIT_WINDOW_MS:o.z.string().regex(/^\d+$/).transform(Number).default("900000"),RATE_LIMIT_MAX_REQUESTS:o.z.string().regex(/^\d+$/).transform(Number).default("100"),COINGECKO_API_URL:o.z.string().url().default("https://api.coingecko.com/api/v3"),LOG_LEVEL:o.z.enum(["error","warn","info","debug"]).default("info"),ENABLE_REQUEST_LOGGING:o.z.string().transform(e=>"true"===e).default("false"),ENABLE_REGISTRATION:o.z.string().transform(e=>"true"===e).default("true"),ENABLE_KYC:o.z.string().transform(e=>"true"===e).default("true"),ENABLE_WITHDRAWALS:o.z.string().transform(e=>"true"===e).default("true")}).refine(e=>{let t=[e.SMTP_HOST,e.SMTP_PORT,e.SMTP_USER,e.SMTP_PASSWORD],r=t.some(e=>void 0!==e),a=t.every(e=>void 0!==e);return!r||!!a},{message:"If SMTP configuration is provided, all SMTP fields (HOST, PORT, USER, PASSWORD) must be provided"});function l(){try{let e=u.safeParse(process.env);if(!e.success){let t=e.error.errors.map(e=>`${e.path.join(".")}: ${e.message}`);return{success:!1,errors:t}}return{success:!0,data:e.data}}catch(e){return{success:!1,errors:["Failed to validate environment variables"]}}}let d=null;function c(){if(!d){let e=l();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),d=e.data,console.log("✅ Environment variables validated successfully")}return d}let m={jwt:{secret:()=>c().JWT_SECRET,expiresIn:()=>c().JWT_EXPIRES_IN},security:{bcryptRounds:()=>c().BCRYPT_ROUNDS,sessionTimeout:()=>c().SESSION_TIMEOUT,maxFileSize:()=>c().MAX_FILE_SIZE,uploadDir:()=>c().UPLOAD_DIR}};if("phase-production-build"!==process.env.NEXT_PHASE){let e=l();e.success||(console.error("❌ Environment validation failed:"),e.errors?.forEach(e=>console.error(`  - ${e}`)),process.exit(1)),console.log("✅ Environment variables validated successfully")}let p=async e=>await a.Ay.hash(e,m.security.bcryptRounds()),E=async(e,t)=>await a.Ay.compare(e,t),f=e=>i().sign(e,m.jwt.secret(),{expiresIn:m.jwt.expiresIn()}),g=e=>{try{return i().verify(e,m.jwt.secret())}catch(e){return null}},R=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},I=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=g(t);if(!r)return{authenticated:!1,user:null};let a=await s.Gy.findByEmail(r.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},y=async e=>{let t,a;if(await s.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await s.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let n=await p(e.password),i=!1;do a=R(),i=!await s.Gy.findByReferralId(a);while(!i);let o=await s.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:n,referralId:a});if(t){let{placeUserByReferralType:a}=await r.e(2746).then(r.bind(r,2746)),n="general";"left"===e.placementSide?n="left":"right"===e.placementSide&&(n="right"),await a(t,o.id,n)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},_=async e=>{let t=await s.Gy.findByEmail(e.email);if(!t||!await E(e.password,t.password))throw Error("Invalid email or password");return{token:f({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},T=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),A=async e=>{let t=await s.Gy.findById(e);return t?.role==="ADMIN"}},39794:(e,t,r)=>{r.d(t,{Py:()=>s,k8:()=>l,kp:()=>u});var a=r(31183),n=r(6710);async function i(e){return await a.prisma.miningUnit.findMany({where:{userId:e,status:"ACTIVE",expiryDate:{gt:new Date}},orderBy:{createdAt:"asc"}})}async function s(e,t,r,s,u){let l=await i(e);if(0===l.length)throw Error("No active mining units found for earnings allocation");let d=[],c=t;for(let e of l){var m;if(c<=0)break;let t=Math.max(0,5*(m=e).investmentAmount-(m.miningEarnings+m.referralEarnings+m.binaryEarnings));if(t<=0)continue;let n=Math.min(c,t);if(n>0){let i={};switch(r){case"MINING_EARNINGS":i.miningEarnings={increment:n};break;case"DIRECT_REFERRAL":i.referralEarnings={increment:n};break;case"BINARY_BONUS":i.binaryEarnings={increment:n}}i.totalEarned={increment:n},await a.prisma.miningUnit.update({where:{id:e.id},data:i}),await a.prisma.miningUnitEarningsAllocation.create({data:{miningUnitId:e.id,transactionId:s,earningType:r,amount:n,description:u}}),d.push({miningUnitId:e.id,amount:n,remainingCapacity:t-n}),c-=n;let l=await a.prisma.miningUnit.findUnique({where:{id:e.id}});l&&function(e){let t=5*e.investmentAmount;return e.miningEarnings+e.referralEarnings+e.binaryEarnings>=t}(l)&&await o(e.id,"5x_investment_reached")}}let p=t-c,E=c;return c>0&&(console.warn(`Unable to allocate ${c} to mining units - all units at capacity. Amount discarded.`),await n.AJ.create({action:"EARNINGS_ALLOCATION_OVERFLOW",userId:e,details:{totalAmount:t,allocatedAmount:p,overflowAmount:E,earningType:r,reason:"all_units_at_capacity",note:"Excess amount discarded as per mining unit capacity limits"}})),{allocations:d,totalAllocated:p,totalDiscarded:E,allocationSuccess:0===E}}async function o(e,t){let i=await a.prisma.miningUnit.findUnique({where:{id:e},include:{user:{select:{email:!0,firstName:!0,lastName:!0}}}});if(!i)throw Error(`Mining unit ${e} not found`);await a.prisma.miningUnit.update({where:{id:e},data:{status:"EXPIRED"}});let s=i.miningEarnings+i.referralEarnings+i.binaryEarnings;await n.AJ.create({action:"MINING_UNIT_EXPIRED",userId:i.userId,details:{miningUnitId:e,reason:t,totalEarned:s,miningEarnings:i.miningEarnings,referralEarnings:i.referralEarnings,binaryEarnings:i.binaryEarnings,investmentAmount:i.investmentAmount,multiplier:s/i.investmentAmount}});try{let{emailNotificationService:e}=await Promise.all([r.e(9526),r.e(3161)]).then(r.bind(r,13161));await e.sendMiningUnitExpiryNotification({userId:i.userId,email:i.user.email,firstName:i.user.firstName,lastName:i.user.lastName,thsAmount:i.thsAmount,investmentAmount:i.investmentAmount,totalEarned:s,purchaseDate:i.createdAt.toISOString(),expiryDate:i.expiryDate.toISOString(),expiryReason:"24_months_reached"===t?"TIME_LIMIT":"RETURN_LIMIT"})}catch(e){console.error("Failed to send mining unit expiry email:",e)}console.log(`Mining unit ${e} expired due to ${t}. Total earnings: ${s}`)}async function u(e){return await a.prisma.miningUnit.findMany({where:{userId:e},orderBy:{createdAt:"asc"}})}async function l(e){return await a.prisma.miningUnitEarningsAllocation.findMany({where:{miningUnitId:e},include:{transaction:!0},orderBy:{allocatedAt:"desc"}})}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71299:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>m,serverHooks:()=>f,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>E});var a={};r.r(a),r.d(a,{GET:()=>c});var n=r(96559),i=r(48088),s=r(37719),o=r(32190),u=r(39542),l=r(6710),d=r(92731);async function c(e){try{let{authenticated:t,user:r}=await (0,u.b9)(e);if(!t||!r)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});let a=(await l.DR.findByUserId(r.id,100)).filter(e=>"MINING_EARNINGS"===e.type||"DIRECT_REFERRAL"===e.type||"BINARY_BONUS"===e.type),n=a.filter(e=>"COMPLETED"===e.status).reduce((e,t)=>e+t.amount,0),i=a.filter(e=>"PENDING"===e.status).reduce((e,t)=>e+t.amount,0),s=a.filter(e=>"MINING_EARNINGS"===e.type&&"COMPLETED"===e.status).reduce((e,t)=>e+t.amount,0),c=a.filter(e=>("DIRECT_REFERRAL"===e.type||"BINARY_BONUS"===e.type)&&"COMPLETED"===e.status).reduce((e,t)=>e+t.amount,0),m=await (0,d.IT)(r.id),p=new Date;p.setDate(p.getDate()-30);let E=a.filter(e=>new Date(e.createdAt)>=p&&"COMPLETED"===e.status);return o.NextResponse.json({success:!0,data:{totalEarnings:n,pendingEarnings:i,miningEarnings:s,referralEarnings:c,estimatedEarnings:m,recentEarnings:E.map(e=>({id:e.id,type:e.type,amount:e.amount,description:e.description,createdAt:e.createdAt})),earningsBreakdown:{mining:s,directReferral:a.filter(e=>"DIRECT_REFERRAL"===e.type&&"COMPLETED"===e.status).reduce((e,t)=>e+t.amount,0),binaryBonus:a.filter(e=>"BINARY_BONUS"===e.type&&"COMPLETED"===e.status).reduce((e,t)=>e+t.amount,0)}}})}catch(e){return console.error("Earnings fetch error:",e),o.NextResponse.json({success:!1,error:"Failed to fetch earnings data"},{status:500})}}let m=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/earnings/route",pathname:"/api/earnings",filename:"route",bundlePath:"app/api/earnings/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\earnings\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:p,workUnitAsyncStorage:E,serverHooks:f}=m;function g(){return(0,s.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:E})}},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580,7911,925,5112],()=>r(71299));module.exports=a})();