/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/earnings/route";
exports.ids = ["app/api/earnings/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fearnings%2Froute&page=%2Fapi%2Fearnings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fearnings%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fearnings%2Froute&page=%2Fapi%2Fearnings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fearnings%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_dream_Desktop_Hash_Minings_hashcorex_src_app_api_earnings_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/earnings/route.ts */ \"(rsc)/./src/app/api/earnings/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/earnings/route\",\n        pathname: \"/api/earnings\",\n        filename: \"route\",\n        bundlePath: \"app/api/earnings/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\api\\\\earnings\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_dream_Desktop_Hash_Minings_hashcorex_src_app_api_earnings_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fearnings%2Froute&page=%2Fapi%2Fearnings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fearnings%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/earnings/route.ts":
/*!***************************************!*\
  !*** ./src/app/api/earnings/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/database */ \"(rsc)/./src/lib/database.ts\");\n/* harmony import */ var _lib_mining__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/mining */ \"(rsc)/./src/lib/mining.ts\");\n\n\n\n\n// GET - Fetch user's earnings data\nasync function GET(request) {\n    try {\n        const { authenticated, user } = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.authenticateRequest)(request);\n        if (!authenticated || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Not authenticated'\n            }, {\n                status: 401\n            });\n        }\n        // Get all earnings transactions\n        const earningsTransactions = await _lib_database__WEBPACK_IMPORTED_MODULE_2__.transactionDb.findByUserId(user.id, 100);\n        // Filter by earnings types\n        const miningEarnings = earningsTransactions.filter((t)=>t.type === 'MINING_EARNINGS' || t.type === 'DIRECT_REFERRAL' || t.type === 'BINARY_BONUS');\n        // Calculate totals\n        const totalEarnings = miningEarnings.filter((t)=>t.status === 'COMPLETED').reduce((sum, t)=>sum + t.amount, 0);\n        const pendingEarnings = miningEarnings.filter((t)=>t.status === 'PENDING').reduce((sum, t)=>sum + t.amount, 0);\n        const miningEarningsTotal = miningEarnings.filter((t)=>t.type === 'MINING_EARNINGS' && t.status === 'COMPLETED').reduce((sum, t)=>sum + t.amount, 0);\n        const referralEarningsTotal = miningEarnings.filter((t)=>(t.type === 'DIRECT_REFERRAL' || t.type === 'BINARY_BONUS') && t.status === 'COMPLETED').reduce((sum, t)=>sum + t.amount, 0);\n        // Get estimated future earnings\n        const estimatedEarnings = await (0,_lib_mining__WEBPACK_IMPORTED_MODULE_3__.calculateEstimatedEarnings)(user.id);\n        // Get recent earnings (last 30 days)\n        const thirtyDaysAgo = new Date();\n        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\n        const recentEarnings = miningEarnings.filter((t)=>new Date(t.createdAt) >= thirtyDaysAgo && t.status === 'COMPLETED');\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                totalEarnings,\n                pendingEarnings,\n                miningEarnings: miningEarningsTotal,\n                referralEarnings: referralEarningsTotal,\n                estimatedEarnings,\n                recentEarnings: recentEarnings.map((t)=>({\n                        id: t.id,\n                        type: t.type,\n                        amount: t.amount,\n                        description: t.description,\n                        createdAt: t.createdAt\n                    })),\n                earningsBreakdown: {\n                    mining: miningEarningsTotal,\n                    directReferral: miningEarnings.filter((t)=>t.type === 'DIRECT_REFERRAL' && t.status === 'COMPLETED').reduce((sum, t)=>sum + t.amount, 0),\n                    binaryBonus: miningEarnings.filter((t)=>t.type === 'BINARY_BONUS' && t.status === 'COMPLETED').reduce((sum, t)=>sum + t.amount, 0)\n                }\n            }\n        });\n    } catch (error) {\n        console.error('Earnings fetch error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to fetch earnings data'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/earnings/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authenticateRequest: () => (/* binding */ authenticateRequest),\n/* harmony export */   createSession: () => (/* binding */ createSession),\n/* harmony export */   generateReferralId: () => (/* binding */ generateReferralId),\n/* harmony export */   generateToken: () => (/* binding */ generateToken),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   isAdmin: () => (/* binding */ isAdmin),\n/* harmony export */   loginUser: () => (/* binding */ loginUser),\n/* harmony export */   registerUser: () => (/* binding */ registerUser),\n/* harmony export */   validateEmail: () => (/* binding */ validateEmail),\n/* harmony export */   validatePassword: () => (/* binding */ validatePassword),\n/* harmony export */   validateSession: () => (/* binding */ validateSession),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./database */ \"(rsc)/./src/lib/database.ts\");\n/* harmony import */ var _envValidation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./envValidation */ \"(rsc)/./src/lib/envValidation.ts\");\n\n\n\n\n// Password utilities\nconst hashPassword = async (password)=>{\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].hash(password, _envValidation__WEBPACK_IMPORTED_MODULE_3__.config.security.bcryptRounds());\n};\nconst verifyPassword = async (password, hashedPassword)=>{\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].compare(password, hashedPassword);\n};\n// JWT utilities\nconst generateToken = (payload)=>{\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, _envValidation__WEBPACK_IMPORTED_MODULE_3__.config.jwt.secret(), {\n        expiresIn: _envValidation__WEBPACK_IMPORTED_MODULE_3__.config.jwt.expiresIn()\n    });\n};\nconst verifyToken = (token)=>{\n    try {\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, _envValidation__WEBPACK_IMPORTED_MODULE_3__.config.jwt.secret());\n        return decoded;\n    } catch (error) {\n        return null;\n    }\n};\n// Generate unique referral ID\nconst generateReferralId = ()=>{\n    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\n    let result = 'HC'; // HashCoreX prefix\n    for(let i = 0; i < 8; i++){\n        result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n};\n// Authentication middleware\nconst authenticateRequest = async (request)=>{\n    const token = request.headers.get('authorization')?.replace('Bearer ', '') || request.cookies.get('auth-token')?.value;\n    if (!token) {\n        return {\n            authenticated: false,\n            user: null\n        };\n    }\n    const decoded = verifyToken(token);\n    if (!decoded) {\n        return {\n            authenticated: false,\n            user: null\n        };\n    }\n    const user = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByEmail(decoded.email);\n    if (!user) {\n        return {\n            authenticated: false,\n            user: null\n        };\n    }\n    return {\n        authenticated: true,\n        user\n    };\n};\n// User registration\nconst registerUser = async (data)=>{\n    // Check if user already exists\n    const existingUser = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByEmail(data.email);\n    if (existingUser) {\n        throw new Error('User already exists with this email');\n    }\n    // Validate referral code if provided\n    let referrerId;\n    if (data.referralCode) {\n        const referrer = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByReferralId(data.referralCode);\n        if (!referrer) {\n            throw new Error('Invalid referral code');\n        }\n        referrerId = referrer.id;\n    }\n    // Hash password\n    const passwordHash = await hashPassword(data.password);\n    // Generate unique referral ID\n    let referralId;\n    let isUnique = false;\n    do {\n        referralId = generateReferralId();\n        const existing = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByReferralId(referralId);\n        isUnique = !existing;\n    }while (!isUnique);\n    // Create user in PostgreSQL\n    const user = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.create({\n        email: data.email,\n        firstName: data.firstName,\n        lastName: data.lastName,\n        password: passwordHash,\n        referralId\n    });\n    // Create referral relationship if referrer exists\n    if (referrerId) {\n        const { placeUserByReferralType } = await __webpack_require__.e(/*! import() */ \"_rsc_src_lib_referral_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./referral */ \"(rsc)/./src/lib/referral.ts\"));\n        // Determine referral type based on placementSide parameter\n        let referralType = 'general';\n        if (data.placementSide === 'left') {\n            referralType = 'left';\n        } else if (data.placementSide === 'right') {\n            referralType = 'right';\n        }\n        // Place user using the new unified placement function\n        await placeUserByReferralType(referrerId, user.id, referralType);\n    }\n    return {\n        id: user.id,\n        email: user.email,\n        referralId: user.referralId,\n        kycStatus: user.kycStatus\n    };\n};\n// User login\nconst loginUser = async (data)=>{\n    const user = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByEmail(data.email);\n    if (!user) {\n        throw new Error('Invalid email or password');\n    }\n    const isValidPassword = await verifyPassword(data.password, user.password);\n    if (!isValidPassword) {\n        throw new Error('Invalid email or password');\n    }\n    const token = generateToken({\n        userId: user.id,\n        email: user.email\n    });\n    return {\n        token,\n        user: {\n            id: user.id,\n            email: user.email,\n            referralId: user.referralId,\n            kycStatus: user.kycStatus\n        }\n    };\n};\n// Password validation\nconst validatePassword = (password)=>{\n    const errors = [];\n    if (password.length < 8) {\n        errors.push('Password must be at least 8 characters long');\n    }\n    if (!/[A-Z]/.test(password)) {\n        errors.push('Password must contain at least one uppercase letter');\n    }\n    if (!/[a-z]/.test(password)) {\n        errors.push('Password must contain at least one lowercase letter');\n    }\n    if (!/\\d/.test(password)) {\n        errors.push('Password must contain at least one number');\n    }\n    if (!/[!@#$%^&*(),.?\":{}|<>]/.test(password)) {\n        errors.push('Password must contain at least one special character');\n    }\n    return {\n        valid: errors.length === 0,\n        errors\n    };\n};\n// Email validation\nconst validateEmail = (email)=>{\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n};\n// Session management\nconst createSession = (userId, email)=>{\n    return generateToken({\n        userId,\n        email\n    });\n};\nconst validateSession = (token)=>{\n    return verifyToken(token);\n};\n// Admin authentication\nconst isAdmin = async (userId)=>{\n    const user = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findById(userId);\n    return user?.role === 'ADMIN';\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   adminSettingsDb: () => (/* binding */ adminSettingsDb),\n/* harmony export */   binaryPointsDb: () => (/* binding */ binaryPointsDb),\n/* harmony export */   depositTransactionDb: () => (/* binding */ depositTransactionDb),\n/* harmony export */   emailLogDb: () => (/* binding */ emailLogDb),\n/* harmony export */   emailTemplateDb: () => (/* binding */ emailTemplateDb),\n/* harmony export */   miningUnitDb: () => (/* binding */ miningUnitDb),\n/* harmony export */   otpDb: () => (/* binding */ otpDb),\n/* harmony export */   referralDb: () => (/* binding */ referralDb),\n/* harmony export */   supportTicketDb: () => (/* binding */ supportTicketDb),\n/* harmony export */   systemLogDb: () => (/* binding */ systemLogDb),\n/* harmony export */   systemSettingsDb: () => (/* binding */ systemSettingsDb),\n/* harmony export */   ticketResponseDb: () => (/* binding */ ticketResponseDb),\n/* harmony export */   transactionDb: () => (/* binding */ transactionDb),\n/* harmony export */   userDb: () => (/* binding */ userDb),\n/* harmony export */   walletBalanceDb: () => (/* binding */ walletBalanceDb),\n/* harmony export */   withdrawalDb: () => (/* binding */ withdrawalDb)\n/* harmony export */ });\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n// User Database Operations\nconst userDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.create({\n            data: {\n                email: data.email,\n                firstName: data.firstName,\n                lastName: data.lastName,\n                password: data.password,\n                referralId: data.referralId || undefined\n            }\n        });\n    },\n    async findByEmail (email) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                email\n            },\n            include: {\n                miningUnits: true,\n                transactions: true,\n                binaryPoints: true\n            }\n        });\n    },\n    async findById (id) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                id\n            },\n            include: {\n                miningUnits: true,\n                transactions: true,\n                binaryPoints: true\n            }\n        });\n    },\n    async findByReferralId (referralId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                referralId\n            }\n        });\n    },\n    async update (id, data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id\n            },\n            data\n        });\n    },\n    async updateKYCStatus (userId, status) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id: userId\n            },\n            data: {\n                kycStatus: status\n            }\n        });\n    },\n    async updateWithdrawalAddress (email, withdrawalAddress) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                email\n            },\n            data: {\n                withdrawalAddress\n            }\n        });\n    },\n    async updateProfilePicture (id, profilePicture) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id\n            },\n            data: {\n                profilePicture\n            },\n            select: {\n                id: true,\n                email: true,\n                firstName: true,\n                lastName: true,\n                referralId: true,\n                role: true,\n                kycStatus: true,\n                profilePicture: true,\n                createdAt: true,\n                updatedAt: true\n            }\n        });\n    },\n    async updatePassword (id, hashedPassword) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id\n            },\n            data: {\n                password: hashedPassword\n            }\n        });\n    }\n};\n// Mining Unit Database Operations\nconst miningUnitDb = {\n    async create (data) {\n        const expiryDate = new Date();\n        expiryDate.setFullYear(expiryDate.getFullYear() + 2); // 24 months from now\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.create({\n            data: {\n                userId: data.userId,\n                thsAmount: data.thsAmount,\n                investmentAmount: data.investmentAmount,\n                dailyROI: data.dailyROI,\n                expiryDate\n            }\n        });\n    },\n    async findActiveByUserId (userId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.findMany({\n            where: {\n                userId,\n                status: 'ACTIVE',\n                expiryDate: {\n                    gt: new Date()\n                }\n            }\n        });\n    },\n    async updateTotalEarned (unitId, amount) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.update({\n            where: {\n                id: unitId\n            },\n            data: {\n                totalEarned: {\n                    increment: amount\n                }\n            }\n        });\n    },\n    async expireUnit (unitId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.update({\n            where: {\n                id: unitId\n            },\n            data: {\n                status: 'EXPIRED'\n            }\n        });\n    },\n    async findAllActive () {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.findMany({\n            where: {\n                status: 'ACTIVE',\n                expiryDate: {\n                    gt: new Date()\n                }\n            },\n            include: {\n                user: true\n            }\n        });\n    },\n    async updateEarnings (unitId, earningType, amount) {\n        const updateData = {\n            totalEarned: {\n                increment: amount\n            }\n        };\n        switch(earningType){\n            case 'mining':\n                updateData.miningEarnings = {\n                    increment: amount\n                };\n                break;\n            case 'referral':\n                updateData.referralEarnings = {\n                    increment: amount\n                };\n                break;\n            case 'binary':\n                updateData.binaryEarnings = {\n                    increment: amount\n                };\n                break;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.update({\n            where: {\n                id: unitId\n            },\n            data: updateData\n        });\n    }\n};\n// Transaction Database Operations\nconst transactionDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.create({\n            data: {\n                userId: data.userId,\n                type: data.type,\n                amount: data.amount,\n                description: data.description,\n                reference: data.reference,\n                status: data.status || 'PENDING'\n            }\n        });\n    },\n    async findByUserId (userId, filters) {\n        const where = {\n            userId\n        };\n        if (filters?.types && filters.types.length > 0) {\n            where.type = {\n                in: filters.types\n            };\n        }\n        if (filters?.status) {\n            where.status = filters.status;\n        }\n        if (filters?.search) {\n            where.OR = [\n                {\n                    description: {\n                        contains: filters.search,\n                        mode: 'insensitive'\n                    }\n                },\n                {\n                    type: {\n                        contains: filters.search,\n                        mode: 'insensitive'\n                    }\n                },\n                {\n                    reference: {\n                        contains: filters.search,\n                        mode: 'insensitive'\n                    }\n                }\n            ];\n        }\n        const include = filters?.includeUser ? {\n            user: {\n                select: {\n                    id: true,\n                    email: true,\n                    firstName: true,\n                    lastName: true\n                }\n            }\n        } : undefined;\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.findMany({\n            where,\n            include,\n            orderBy: {\n                createdAt: 'desc'\n            },\n            take: filters?.limit || 50,\n            skip: filters?.offset\n        });\n    },\n    async updateStatus (transactionId, status, additionalData) {\n        const updateData = {\n            status\n        };\n        if (additionalData?.amount !== undefined) {\n            updateData.amount = additionalData.amount;\n        }\n        if (additionalData?.description) {\n            updateData.description = additionalData.description;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.update({\n            where: {\n                id: transactionId\n            },\n            data: updateData\n        });\n    },\n    async findPendingByTypeAndDescription (userId, type, descriptionPattern) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.findFirst({\n            where: {\n                userId,\n                type,\n                description: {\n                    contains: descriptionPattern\n                },\n                status: 'PENDING'\n            }\n        });\n    },\n    async updateByReference (reference, type, status, additionalData) {\n        const updateData = {\n            status\n        };\n        if (additionalData?.amount !== undefined) {\n            updateData.amount = additionalData.amount;\n        }\n        if (additionalData?.description) {\n            updateData.description = additionalData.description;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.updateMany({\n            where: {\n                reference,\n                type,\n                status: 'PENDING'\n            },\n            data: updateData\n        });\n    }\n};\n// Referral Database Operations\nconst referralDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.create({\n            data: {\n                referrerId: data.referrerId,\n                referredId: data.referredId,\n                placementSide: data.placementSide\n            }\n        });\n    },\n    async findByReferrerId (referrerId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findMany({\n            where: {\n                referrerId\n            },\n            include: {\n                referred: {\n                    select: {\n                        id: true,\n                        email: true,\n                        createdAt: true\n                    }\n                }\n            }\n        });\n    }\n};\n// Binary Points Database Operations\nconst binaryPointsDb = {\n    async upsert (data) {\n        // Round to 2 decimal places to ensure precision\n        const leftPoints = data.leftPoints !== undefined ? Math.round(data.leftPoints * 100) / 100 : undefined;\n        const rightPoints = data.rightPoints !== undefined ? Math.round(data.rightPoints * 100) / 100 : undefined;\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.binaryPoints.upsert({\n            where: {\n                userId: data.userId\n            },\n            update: {\n                leftPoints: leftPoints !== undefined ? {\n                    increment: leftPoints\n                } : undefined,\n                rightPoints: rightPoints !== undefined ? {\n                    increment: rightPoints\n                } : undefined\n            },\n            create: {\n                userId: data.userId,\n                leftPoints: leftPoints || 0,\n                rightPoints: rightPoints || 0\n            }\n        });\n    },\n    async findByUserId (userId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.binaryPoints.findUnique({\n            where: {\n                userId\n            }\n        });\n    },\n    async resetPoints (userId, leftPoints, rightPoints) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.binaryPoints.update({\n            where: {\n                userId\n            },\n            data: {\n                leftPoints,\n                rightPoints,\n                flushDate: new Date()\n            }\n        });\n    }\n};\n// Withdrawal Database Operations\nconst withdrawalDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.withdrawalRequest.create({\n            data: {\n                userId: data.userId,\n                amount: data.amount,\n                usdtAddress: data.usdtAddress\n            }\n        });\n    },\n    async findPending () {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.withdrawalRequest.findMany({\n            where: {\n                status: 'PENDING'\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        kycStatus: true\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'asc'\n            }\n        });\n    },\n    async updateStatus (requestId, status, processedBy, txid, rejectionReason) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.withdrawalRequest.update({\n            where: {\n                id: requestId\n            },\n            data: {\n                status,\n                processedBy,\n                txid,\n                rejectionReason,\n                processedAt: new Date()\n            }\n        });\n    }\n};\n// Admin Settings Database Operations\nconst adminSettingsDb = {\n    async get (key) {\n        const setting = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.findUnique({\n            where: {\n                key\n            }\n        });\n        return setting?.value;\n    },\n    async set (key, value, updatedBy) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.upsert({\n            where: {\n                key\n            },\n            update: {\n                value\n            },\n            create: {\n                key,\n                value\n            }\n        });\n    },\n    async getAll () {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.findMany();\n    }\n};\n// System Logs\nconst systemLogDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.systemLog.create({\n            data: {\n                action: data.action,\n                userId: data.userId,\n                adminId: data.adminId,\n                details: data.details ? JSON.stringify(data.details) : null,\n                ipAddress: data.ipAddress,\n                userAgent: data.userAgent\n            }\n        });\n    }\n};\n// Wallet Balance Database Operations\nconst walletBalanceDb = {\n    async getOrCreate (userId) {\n        let walletBalance = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.findUnique({\n            where: {\n                userId\n            }\n        });\n        if (!walletBalance) {\n            walletBalance = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.create({\n                data: {\n                    userId,\n                    availableBalance: 0,\n                    pendingBalance: 0,\n                    totalDeposits: 0,\n                    totalWithdrawals: 0,\n                    totalEarnings: 0\n                }\n            });\n        }\n        return walletBalance;\n    },\n    async updateBalance (userId, updates) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.update({\n            where: {\n                userId\n            },\n            data: {\n                ...updates,\n                lastUpdated: new Date()\n            }\n        });\n    },\n    async addDeposit (userId, amount) {\n        const wallet = await this.getOrCreate(userId);\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.update({\n            where: {\n                userId\n            },\n            data: {\n                availableBalance: wallet.availableBalance + amount,\n                totalDeposits: wallet.totalDeposits + amount,\n                lastUpdated: new Date()\n            }\n        });\n    },\n    async addEarnings (userId, amount) {\n        const wallet = await this.getOrCreate(userId);\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.update({\n            where: {\n                userId\n            },\n            data: {\n                availableBalance: wallet.availableBalance + amount,\n                totalEarnings: wallet.totalEarnings + amount,\n                lastUpdated: new Date()\n            }\n        });\n    },\n    async deductWithdrawal (userId, amount) {\n        const wallet = await this.getOrCreate(userId);\n        if (wallet.availableBalance < amount) {\n            throw new Error('Insufficient balance');\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.update({\n            where: {\n                userId\n            },\n            data: {\n                availableBalance: wallet.availableBalance - amount,\n                totalWithdrawals: wallet.totalWithdrawals + amount,\n                lastUpdated: new Date()\n            }\n        });\n    },\n    async findByUserId (userId) {\n        return await this.getOrCreate(userId);\n    }\n};\n// Deposit Transaction Database Operations\nconst depositTransactionDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.create({\n            data: {\n                userId: data.userId,\n                transactionId: data.transactionId,\n                amount: data.amount,\n                usdtAmount: data.usdtAmount,\n                tronAddress: data.tronAddress,\n                senderAddress: data.senderAddress,\n                blockNumber: data.blockNumber,\n                blockTimestamp: data.blockTimestamp,\n                confirmations: data.confirmations || 0,\n                status: 'PENDING'\n            }\n        });\n    },\n    async findByTransactionId (transactionId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.findUnique({\n            where: {\n                transactionId\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    async findByUserId (userId, filters) {\n        const where = {\n            userId\n        };\n        if (filters?.status) {\n            where.status = filters.status;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.findMany({\n            where,\n            orderBy: {\n                createdAt: 'desc'\n            },\n            take: filters?.limit || 50,\n            skip: filters?.offset,\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    async findAll (filters) {\n        const where = {};\n        if (filters?.status) {\n            where.status = filters.status;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.findMany({\n            where,\n            orderBy: {\n                createdAt: 'desc'\n            },\n            take: filters?.limit || 100,\n            skip: filters?.offset,\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    async updateStatus (transactionId, status, updates) {\n        const updateData = {\n            status\n        };\n        if (updates?.verifiedAt) updateData.verifiedAt = updates.verifiedAt;\n        if (updates?.processedAt) updateData.processedAt = updates.processedAt;\n        if (updates?.failureReason) updateData.failureReason = updates.failureReason;\n        if (updates?.confirmations !== undefined) updateData.confirmations = updates.confirmations;\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.update({\n            where: {\n                transactionId\n            },\n            data: updateData\n        });\n    },\n    async markAsCompleted (transactionId) {\n        return await this.updateStatus(transactionId, 'COMPLETED', {\n            processedAt: new Date()\n        });\n    },\n    async markAsFailed (transactionId, reason) {\n        return await this.updateStatus(transactionId, 'FAILED', {\n            failureReason: reason,\n            processedAt: new Date()\n        });\n    },\n    async getPendingDeposits () {\n        return await this.findAll({\n            status: 'PENDING'\n        });\n    },\n    async getPendingVerificationDeposits () {\n        return await this.findAll({\n            status: 'PENDING_VERIFICATION'\n        });\n    },\n    async getWaitingForConfirmationsDeposits () {\n        return await this.findAll({\n            status: 'WAITING_FOR_CONFIRMATIONS'\n        });\n    },\n    async findByStatus (status) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.findMany({\n            where: {\n                status\n            },\n            orderBy: {\n                createdAt: 'desc'\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    async updateConfirmations (transactionId, confirmations) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.update({\n            where: {\n                transactionId\n            },\n            data: {\n                confirmations\n            }\n        });\n    },\n    async getDepositStats () {\n        const stats = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.aggregate({\n            _count: {\n                id: true\n            },\n            _sum: {\n                usdtAmount: true\n            },\n            where: {\n                status: {\n                    in: [\n                        'COMPLETED',\n                        'CONFIRMED'\n                    ]\n                }\n            }\n        });\n        const pendingCount = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.count({\n            where: {\n                status: {\n                    in: [\n                        'PENDING',\n                        'PENDING_VERIFICATION',\n                        'WAITING_FOR_CONFIRMATIONS'\n                    ]\n                }\n            }\n        });\n        return {\n            totalDeposits: stats._count.id || 0,\n            totalAmount: stats._sum.usdtAmount || 0,\n            pendingDeposits: pendingCount\n        };\n    }\n};\n// Support Ticket Database Operations\nconst supportTicketDb = {\n    create: async (data)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.create({\n            data,\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            }\n        });\n    },\n    findByUserId: async (userId)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.findMany({\n            where: {\n                userId\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    },\n    findById: async (id)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.findUnique({\n            where: {\n                id\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            }\n        });\n    },\n    findAll: async ()=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.findMany({\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    },\n    updateStatus: async (id, status)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.update({\n            where: {\n                id\n            },\n            data: {\n                status,\n                updatedAt: new Date()\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            }\n        });\n    }\n};\n// Ticket Response Database Operations\nconst ticketResponseDb = {\n    create: async (data)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.ticketResponse.create({\n            data,\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    findByTicketId: async (ticketId)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.ticketResponse.findMany({\n            where: {\n                ticketId\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'asc'\n            }\n        });\n    }\n};\n// System Settings Database Operations\nconst systemSettingsDb = {\n    async getSetting (key) {\n        const setting = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.findUnique({\n            where: {\n                key\n            }\n        });\n        return setting?.value || null;\n    },\n    async getSettings (keys) {\n        const settings = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.findMany({\n            where: {\n                key: {\n                    in: keys\n                }\n            }\n        });\n        const result = {};\n        settings.forEach((setting)=>{\n            result[setting.key] = setting.value;\n        });\n        return result;\n    },\n    async updateSettings (settings) {\n        const updates = Object.entries(settings).map(([key, value])=>({\n                key,\n                value: typeof value === 'string' ? value : JSON.stringify(value)\n            }));\n        // Use transaction to update multiple settings\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.$transaction(updates.map(({ key, value })=>_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.upsert({\n                where: {\n                    key\n                },\n                update: {\n                    value\n                },\n                create: {\n                    key,\n                    value\n                }\n            })));\n    },\n    async getEmailSettings () {\n        const settings = await this.getSettings([\n            'smtpHost',\n            'smtpPort',\n            'smtpSecure',\n            'smtpUser',\n            'smtpPassword',\n            'fromName',\n            'fromEmail',\n            'emailEnabled'\n        ]);\n        return {\n            smtpHost: settings.smtpHost,\n            smtpPort: settings.smtpPort ? parseInt(settings.smtpPort) : 587,\n            smtpSecure: settings.smtpSecure === 'true',\n            smtpUser: settings.smtpUser,\n            smtpPassword: settings.smtpPassword,\n            fromName: settings.fromName || 'HashCoreX',\n            fromEmail: settings.fromEmail,\n            emailEnabled: settings.emailEnabled !== 'false'\n        };\n    },\n    async updateEmailSettings (emailSettings) {\n        const settings = {};\n        if (emailSettings.smtpHost !== undefined) settings.smtpHost = emailSettings.smtpHost;\n        if (emailSettings.smtpPort !== undefined) settings.smtpPort = emailSettings.smtpPort.toString();\n        if (emailSettings.smtpSecure !== undefined) settings.smtpSecure = emailSettings.smtpSecure.toString();\n        if (emailSettings.smtpUser !== undefined) settings.smtpUser = emailSettings.smtpUser;\n        if (emailSettings.smtpPassword !== undefined) settings.smtpPassword = emailSettings.smtpPassword;\n        if (emailSettings.fromName !== undefined) settings.fromName = emailSettings.fromName;\n        if (emailSettings.fromEmail !== undefined) settings.fromEmail = emailSettings.fromEmail;\n        if (emailSettings.emailEnabled !== undefined) settings.emailEnabled = emailSettings.emailEnabled.toString();\n        await this.updateSettings(settings);\n    },\n    async getEmailTemplate (name) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.findUnique({\n            where: {\n                name,\n                isActive: true\n            }\n        });\n    }\n};\n// OTP Verification Database Operations\nconst otpDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.oTPVerification.create({\n            data\n        });\n    },\n    async findValid (email, purpose) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.oTPVerification.findFirst({\n            where: {\n                email,\n                purpose,\n                verified: false,\n                expiresAt: {\n                    gt: new Date()\n                }\n            },\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    },\n    async verify (id) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.oTPVerification.update({\n            where: {\n                id\n            },\n            data: {\n                verified: true\n            }\n        });\n    },\n    async findVerified (email, purpose) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.oTPVerification.findFirst({\n            where: {\n                email,\n                purpose,\n                verified: true,\n                expiresAt: {\n                    gt: new Date()\n                }\n            },\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    },\n    async cleanup () {\n        // Remove expired OTPs\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.oTPVerification.deleteMany({\n            where: {\n                expiresAt: {\n                    lt: new Date()\n                }\n            }\n        });\n    }\n};\n// Email Template Database Operations\nconst emailTemplateDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.create({\n            data\n        });\n    },\n    async findAll () {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.findMany({\n            orderBy: {\n                name: 'asc'\n            }\n        });\n    },\n    async findByName (name) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.findUnique({\n            where: {\n                name\n            }\n        });\n    },\n    async update (name, data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.update({\n            where: {\n                name\n            },\n            data\n        });\n    },\n    async delete (name) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.delete({\n            where: {\n                name\n            }\n        });\n    }\n};\n// Email Log Database Operations\nconst emailLogDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailLog.create({\n            data\n        });\n    },\n    async updateStatus (id, status, error) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailLog.update({\n            where: {\n                id\n            },\n            data: {\n                status,\n                error,\n                sentAt: status === 'SENT' ? new Date() : undefined\n            }\n        });\n    },\n    async findRecent (limit = 50) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailLog.findMany({\n            take: limit,\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/envValidation.ts":
/*!**********************************!*\
  !*** ./src/lib/envValidation.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkCriticalEnvVars: () => (/* binding */ checkCriticalEnvVars),\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   generateStrongJWTSecret: () => (/* binding */ generateStrongJWTSecret),\n/* harmony export */   getValidatedEnv: () => (/* binding */ getValidatedEnv),\n/* harmony export */   validateEnvironment: () => (/* binding */ validateEnvironment)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n/**\n * Environment Variable Validation\n * Validates all required environment variables on application startup\n */ \n// Environment validation schema\nconst envSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    // Database\n    DATABASE_URL: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().url('DATABASE_URL must be a valid PostgreSQL URL'),\n    DIRECT_URL: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().url('DIRECT_URL must be a valid PostgreSQL URL'),\n    // JWT Configuration\n    JWT_SECRET: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(32, 'JWT_SECRET must be at least 32 characters long').refine((secret)=>{\n        // Check for strong secret\n        const hasUpperCase = /[A-Z]/.test(secret);\n        const hasLowerCase = /[a-z]/.test(secret);\n        const hasNumbers = /\\d/.test(secret);\n        const hasSpecialChars = /[!@#$%^&*(),.?\":{}|<>]/.test(secret);\n        return hasUpperCase && hasLowerCase && hasNumbers && hasSpecialChars;\n    }, 'JWT_SECRET should contain uppercase, lowercase, numbers, and special characters'),\n    JWT_EXPIRES_IN: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().default('30d'),\n    // Node Environment\n    NODE_ENV: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        'development',\n        'production',\n        'test'\n    ]).default('development'),\n    // Application Configuration\n    NEXT_PUBLIC_APP_URL: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().url().optional(),\n    PORT: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().regex(/^\\d+$/).transform(Number).default('3000'),\n    // Email Configuration (optional but validated if provided)\n    SMTP_HOST: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    SMTP_PORT: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().regex(/^\\d+$/).transform(Number).optional(),\n    SMTP_USER: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().email().optional(),\n    SMTP_PASSWORD: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    SMTP_SECURE: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().transform((val)=>val === 'true').optional(),\n    // Tron Network Configuration\n    TRON_NETWORK: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        'mainnet',\n        'testnet'\n    ]).default('testnet'),\n    TRON_API_KEY: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    USDT_CONTRACT_ADDRESS: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    // File Upload Configuration\n    MAX_FILE_SIZE: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().regex(/^\\d+$/).transform(Number).default('10485760'),\n    UPLOAD_DIR: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().default('./public/uploads'),\n    // Security Configuration\n    BCRYPT_ROUNDS: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().regex(/^\\d+$/).transform(Number).default('12'),\n    SESSION_TIMEOUT: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().regex(/^\\d+$/).transform(Number).default('1800'),\n    // Rate Limiting Configuration\n    RATE_LIMIT_WINDOW_MS: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().regex(/^\\d+$/).transform(Number).default('900000'),\n    RATE_LIMIT_MAX_REQUESTS: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().regex(/^\\d+$/).transform(Number).default('100'),\n    // External API Configuration\n    COINGECKO_API_URL: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().url().default('https://api.coingecko.com/api/v3'),\n    // Monitoring and Logging\n    LOG_LEVEL: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        'error',\n        'warn',\n        'info',\n        'debug'\n    ]).default('info'),\n    ENABLE_REQUEST_LOGGING: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().transform((val)=>val === 'true').default('false'),\n    // Feature Flags\n    ENABLE_REGISTRATION: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().transform((val)=>val === 'true').default('true'),\n    ENABLE_KYC: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().transform((val)=>val === 'true').default('true'),\n    ENABLE_WITHDRAWALS: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().transform((val)=>val === 'true').default('true')\n});\n// Conditional validation for email configuration\nconst envSchemaWithConditionals = envSchema.refine((data)=>{\n    // If any SMTP config is provided, all should be provided\n    const smtpFields = [\n        data.SMTP_HOST,\n        data.SMTP_PORT,\n        data.SMTP_USER,\n        data.SMTP_PASSWORD\n    ];\n    const hasAnySmtp = smtpFields.some((field)=>field !== undefined);\n    const hasAllSmtp = smtpFields.every((field)=>field !== undefined);\n    if (hasAnySmtp && !hasAllSmtp) {\n        return false;\n    }\n    return true;\n}, {\n    message: 'If SMTP configuration is provided, all SMTP fields (HOST, PORT, USER, PASSWORD) must be provided'\n});\n// Validate environment variables\nfunction validateEnvironment() {\n    try {\n        const result = envSchemaWithConditionals.safeParse(process.env);\n        if (!result.success) {\n            const errors = result.error.errors.map((err)=>`${err.path.join('.')}: ${err.message}`);\n            return {\n                success: false,\n                errors\n            };\n        }\n        return {\n            success: true,\n            data: result.data\n        };\n    } catch (error) {\n        return {\n            success: false,\n            errors: [\n                'Failed to validate environment variables'\n            ]\n        };\n    }\n}\n// Get validated environment variables\nlet validatedEnv = null;\nfunction getValidatedEnv() {\n    if (!validatedEnv) {\n        const validation = validateEnvironment();\n        if (!validation.success) {\n            console.error('❌ Environment validation failed:');\n            validation.errors?.forEach((error)=>console.error(`  - ${error}`));\n            process.exit(1);\n        }\n        validatedEnv = validation.data;\n        console.log('✅ Environment variables validated successfully');\n    }\n    return validatedEnv;\n}\n// Environment-specific configurations\nconst config = {\n    isDevelopment: ()=>getValidatedEnv().NODE_ENV === 'development',\n    isProduction: ()=>getValidatedEnv().NODE_ENV === 'production',\n    isTest: ()=>getValidatedEnv().NODE_ENV === 'test',\n    database: {\n        url: ()=>getValidatedEnv().DATABASE_URL,\n        directUrl: ()=>getValidatedEnv().DIRECT_URL\n    },\n    jwt: {\n        secret: ()=>getValidatedEnv().JWT_SECRET,\n        expiresIn: ()=>getValidatedEnv().JWT_EXPIRES_IN\n    },\n    server: {\n        port: ()=>getValidatedEnv().PORT,\n        appUrl: ()=>getValidatedEnv().NEXT_PUBLIC_APP_URL\n    },\n    email: {\n        isConfigured: ()=>{\n            const env = getValidatedEnv();\n            return !!(env.SMTP_HOST && env.SMTP_PORT && env.SMTP_USER && env.SMTP_PASSWORD);\n        },\n        host: ()=>getValidatedEnv().SMTP_HOST,\n        port: ()=>getValidatedEnv().SMTP_PORT,\n        user: ()=>getValidatedEnv().SMTP_USER,\n        password: ()=>getValidatedEnv().SMTP_PASSWORD,\n        secure: ()=>getValidatedEnv().SMTP_SECURE\n    },\n    tron: {\n        network: ()=>getValidatedEnv().TRON_NETWORK,\n        apiKey: ()=>getValidatedEnv().TRON_API_KEY,\n        usdtContract: ()=>getValidatedEnv().USDT_CONTRACT_ADDRESS\n    },\n    security: {\n        bcryptRounds: ()=>getValidatedEnv().BCRYPT_ROUNDS,\n        sessionTimeout: ()=>getValidatedEnv().SESSION_TIMEOUT,\n        maxFileSize: ()=>getValidatedEnv().MAX_FILE_SIZE,\n        uploadDir: ()=>getValidatedEnv().UPLOAD_DIR\n    },\n    rateLimit: {\n        windowMs: ()=>getValidatedEnv().RATE_LIMIT_WINDOW_MS,\n        maxRequests: ()=>getValidatedEnv().RATE_LIMIT_MAX_REQUESTS\n    },\n    features: {\n        registrationEnabled: ()=>getValidatedEnv().ENABLE_REGISTRATION,\n        kycEnabled: ()=>getValidatedEnv().ENABLE_KYC,\n        withdrawalsEnabled: ()=>getValidatedEnv().ENABLE_WITHDRAWALS\n    },\n    logging: {\n        level: ()=>getValidatedEnv().LOG_LEVEL,\n        requestLogging: ()=>getValidatedEnv().ENABLE_REQUEST_LOGGING\n    },\n    external: {\n        coingeckoApiUrl: ()=>getValidatedEnv().COINGECKO_API_URL\n    }\n};\n// Validate environment on module load (server-side only)\nif (true) {\n    // Skip validation during build process\n    const isBuilding = process.env.NEXT_PHASE === 'phase-production-build';\n    if (!isBuilding) {\n        // Only validate in server environment\n        const validation = validateEnvironment();\n        if (!validation.success) {\n            console.error('❌ Environment validation failed:');\n            validation.errors?.forEach((error)=>console.error(`  - ${error}`));\n            // In development, show helpful error message\n            if (true) {\n                console.error('\\n💡 To fix these errors:');\n                console.error('1. Check your .env.local file');\n                console.error('2. Ensure all required environment variables are set');\n                console.error('3. Verify JWT_SECRET is at least 32 characters with mixed case, numbers, and special characters');\n                console.error('4. Ensure database URLs are valid PostgreSQL connection strings');\n            }\n            process.exit(1);\n        }\n        console.log('✅ Environment variables validated successfully');\n    }\n}\n// Helper function to check if all critical environment variables are set\nfunction checkCriticalEnvVars() {\n    const critical = [\n        'DATABASE_URL',\n        'DIRECT_URL',\n        'JWT_SECRET'\n    ];\n    const missing = [];\n    for (const key of critical){\n        if (!process.env[key]) {\n            missing.push(key);\n        }\n    }\n    return {\n        valid: missing.length === 0,\n        missing\n    };\n}\n// Helper function to generate a strong JWT secret\nfunction generateStrongJWTSecret() {\n    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()';\n    let result = '';\n    // Ensure at least one of each required character type\n    result += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'[Math.floor(Math.random() * 26)]; // Uppercase\n    result += 'abcdefghijklmnopqrstuvwxyz'[Math.floor(Math.random() * 26)]; // Lowercase\n    result += '0123456789'[Math.floor(Math.random() * 10)]; // Number\n    result += '!@#$%^&*()'[Math.floor(Math.random() * 10)]; // Special char\n    // Fill the rest randomly\n    for(let i = 4; i < 64; i++){\n        result += chars[Math.floor(Math.random() * chars.length)];\n    }\n    // Shuffle the string\n    return result.split('').sort(()=>Math.random() - 0.5).join('');\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (config);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/envValidation.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/mining.ts":
/*!***************************!*\
  !*** ./src/lib/mining.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculate7DayAverageROI: () => (/* binding */ calculate7DayAverageROI),\n/* harmony export */   calculateDailyROI: () => (/* binding */ calculateDailyROI),\n/* harmony export */   calculateDynamicROI: () => (/* binding */ calculateDynamicROI),\n/* harmony export */   calculateEstimatedEarnings: () => (/* binding */ calculateEstimatedEarnings),\n/* harmony export */   expireOldMiningUnits: () => (/* binding */ expireOldMiningUnits),\n/* harmony export */   getMiningStats: () => (/* binding */ getMiningStats),\n/* harmony export */   getMonthlyReturnLimits: () => (/* binding */ getMonthlyReturnLimits),\n/* harmony export */   processWeeklyEarnings: () => (/* binding */ processWeeklyEarnings),\n/* harmony export */   updateExistingMiningUnitsROI: () => (/* binding */ updateExistingMiningUnitsROI),\n/* harmony export */   validateMonthlyReturn: () => (/* binding */ validateMonthlyReturn)\n/* harmony export */ });\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./database */ \"(rsc)/./src/lib/database.ts\");\n/* harmony import */ var _miningUnitEarnings__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./miningUnitEarnings */ \"(rsc)/./src/lib/miningUnitEarnings.ts\");\n\n\n\n// Note: User active status is now handled dynamically in the referral system\n// The isActive field in the database is only used for account access control\n// Mining activity status is computed on-demand for binary tree display\n// Calculate dynamic ROI based on TH/s amount and admin-configured ranges\nasync function calculateDynamicROI(thsAmount) {\n    try {\n        // Get earnings ranges from admin settings\n        const earningsRangesStr = await _database__WEBPACK_IMPORTED_MODULE_1__.adminSettingsDb.get('earningsRanges');\n        let earningsRanges;\n        if (earningsRangesStr) {\n            try {\n                earningsRanges = JSON.parse(earningsRangesStr);\n            } catch (parseError) {\n                console.error('Error parsing earnings ranges:', parseError);\n                earningsRanges = null;\n            }\n        }\n        // Fallback to default ranges if not configured\n        if (!earningsRanges || !Array.isArray(earningsRanges)) {\n            earningsRanges = [\n                {\n                    minTHS: 0,\n                    maxTHS: 10,\n                    dailyReturnMin: 0.3,\n                    dailyReturnMax: 0.5,\n                    monthlyReturnMin: 10.0,\n                    monthlyReturnMax: 15.0\n                },\n                {\n                    minTHS: 10,\n                    maxTHS: 50,\n                    dailyReturnMin: 0.4,\n                    dailyReturnMax: 0.6,\n                    monthlyReturnMin: 10.0,\n                    monthlyReturnMax: 15.0\n                },\n                {\n                    minTHS: 50,\n                    maxTHS: 999999,\n                    dailyReturnMin: 0.5,\n                    dailyReturnMax: 0.7,\n                    monthlyReturnMin: 10.0,\n                    monthlyReturnMax: 15.0\n                }\n            ];\n        }\n        // Find the appropriate range for the TH/s amount\n        const applicableRange = earningsRanges.find((range)=>thsAmount >= range.minTHS && thsAmount <= range.maxTHS);\n        let minROI;\n        let maxROI;\n        if (applicableRange) {\n            minROI = applicableRange.dailyReturnMin;\n            maxROI = applicableRange.dailyReturnMax;\n        } else {\n            // Fallback to highest range if no match found\n            const highestRange = earningsRanges[earningsRanges.length - 1];\n            minROI = highestRange.dailyReturnMin;\n            maxROI = highestRange.dailyReturnMax;\n        }\n        // Add randomization within the range\n        const randomROI = minROI + Math.random() * (maxROI - minROI);\n        // Round to 2 decimal places\n        return Math.round(randomROI * 100) / 100;\n    } catch (error) {\n        console.error('Error calculating dynamic ROI:', error);\n        // Fallback to default values based on TH/s amount\n        if (thsAmount >= 50) return 0.6;\n        if (thsAmount >= 10) return 0.5;\n        return 0.4;\n    }\n}\n// Validate monthly return doesn't exceed admin-configured limits for specific TH/s amount\nasync function validateMonthlyReturn(dailyROI, thsAmount) {\n    try {\n        // Get earnings ranges from admin settings\n        const earningsRangesStr = await _database__WEBPACK_IMPORTED_MODULE_1__.adminSettingsDb.get('earningsRanges');\n        let earningsRanges;\n        if (earningsRangesStr) {\n            try {\n                earningsRanges = JSON.parse(earningsRangesStr);\n            } catch (parseError) {\n                console.error('Error parsing earnings ranges:', parseError);\n                earningsRanges = null;\n            }\n        }\n        // Fallback to default ranges if not configured\n        if (!earningsRanges || !Array.isArray(earningsRanges)) {\n            earningsRanges = [\n                {\n                    minTHS: 0,\n                    maxTHS: 10,\n                    dailyReturnMin: 0.3,\n                    dailyReturnMax: 0.5,\n                    monthlyReturnMin: 10.0,\n                    monthlyReturnMax: 15.0\n                },\n                {\n                    minTHS: 10,\n                    maxTHS: 50,\n                    dailyReturnMin: 0.4,\n                    dailyReturnMax: 0.6,\n                    monthlyReturnMin: 10.0,\n                    monthlyReturnMax: 15.0\n                },\n                {\n                    minTHS: 50,\n                    maxTHS: 999999,\n                    dailyReturnMin: 0.5,\n                    dailyReturnMax: 0.7,\n                    monthlyReturnMin: 10.0,\n                    monthlyReturnMax: 15.0\n                }\n            ];\n        }\n        // Find the appropriate range for the TH/s amount\n        const applicableRange = earningsRanges.find((range)=>thsAmount >= range.minTHS && thsAmount <= range.maxTHS);\n        let monthlyMin = 10.0;\n        let monthlyMax = 15.0;\n        if (applicableRange) {\n            monthlyMin = applicableRange.monthlyReturnMin || 10.0;\n            monthlyMax = applicableRange.monthlyReturnMax || 15.0;\n        } else {\n            // Fallback to highest range if no match found\n            const highestRange = earningsRanges[earningsRanges.length - 1];\n            monthlyMin = highestRange.monthlyReturnMin || 10.0;\n            monthlyMax = highestRange.monthlyReturnMax || 15.0;\n        }\n        const monthlyReturn = dailyROI * 30; // Approximate monthly return\n        return monthlyReturn >= monthlyMin && monthlyReturn <= monthlyMax;\n    } catch (error) {\n        console.error('Error validating monthly return:', error);\n        // Fallback to default 10-15% range\n        const monthlyReturn = dailyROI * 30;\n        return monthlyReturn >= 10 && monthlyReturn <= 15;\n    }\n}\n// Get monthly return limits for a specific TH/s amount\nasync function getMonthlyReturnLimits(thsAmount) {\n    try {\n        // Get earnings ranges from admin settings\n        const earningsRangesStr = await _database__WEBPACK_IMPORTED_MODULE_1__.adminSettingsDb.get('earningsRanges');\n        let earningsRanges;\n        if (earningsRangesStr) {\n            try {\n                earningsRanges = JSON.parse(earningsRangesStr);\n            } catch (parseError) {\n                console.error('Error parsing earnings ranges:', parseError);\n                earningsRanges = null;\n            }\n        }\n        // Fallback to default ranges if not configured\n        if (!earningsRanges || !Array.isArray(earningsRanges)) {\n            earningsRanges = [\n                {\n                    minTHS: 0,\n                    maxTHS: 10,\n                    dailyReturnMin: 0.3,\n                    dailyReturnMax: 0.5,\n                    monthlyReturnMin: 10.0,\n                    monthlyReturnMax: 15.0\n                },\n                {\n                    minTHS: 10,\n                    maxTHS: 50,\n                    dailyReturnMin: 0.4,\n                    dailyReturnMax: 0.6,\n                    monthlyReturnMin: 10.0,\n                    monthlyReturnMax: 15.0\n                },\n                {\n                    minTHS: 50,\n                    maxTHS: 999999,\n                    dailyReturnMin: 0.5,\n                    dailyReturnMax: 0.7,\n                    monthlyReturnMin: 10.0,\n                    monthlyReturnMax: 15.0\n                }\n            ];\n        }\n        // Find the appropriate range for the TH/s amount\n        const applicableRange = earningsRanges.find((range)=>thsAmount >= range.minTHS && thsAmount <= range.maxTHS);\n        if (applicableRange) {\n            return {\n                min: applicableRange.monthlyReturnMin || 10.0,\n                max: applicableRange.monthlyReturnMax || 15.0\n            };\n        } else {\n            // Fallback to highest range if no match found\n            const highestRange = earningsRanges[earningsRanges.length - 1];\n            return {\n                min: highestRange.monthlyReturnMin || 10.0,\n                max: highestRange.monthlyReturnMax || 15.0\n            };\n        }\n    } catch (error) {\n        console.error('Error getting monthly return limits:', error);\n        // Fallback to default 10-15% range\n        return {\n            min: 10.0,\n            max: 15.0\n        };\n    }\n}\n// Calculate 7-day average ROI for a specific TH/s amount based on admin configuration\nasync function calculate7DayAverageROI(thsAmount) {\n    try {\n        // Get earnings ranges from admin settings\n        const earningsRangesStr = await _database__WEBPACK_IMPORTED_MODULE_1__.adminSettingsDb.get('earningsRanges');\n        let earningsRanges;\n        if (earningsRangesStr) {\n            try {\n                earningsRanges = JSON.parse(earningsRangesStr);\n            } catch (parseError) {\n                console.error('Error parsing earnings ranges:', parseError);\n                earningsRanges = null;\n            }\n        }\n        // Fallback to default ranges if not configured\n        if (!earningsRanges || !Array.isArray(earningsRanges)) {\n            earningsRanges = [\n                {\n                    minTHS: 0,\n                    maxTHS: 10,\n                    dailyReturnMin: 0.3,\n                    dailyReturnMax: 0.5,\n                    monthlyReturnMin: 10.0,\n                    monthlyReturnMax: 15.0\n                },\n                {\n                    minTHS: 10,\n                    maxTHS: 50,\n                    dailyReturnMin: 0.4,\n                    dailyReturnMax: 0.6,\n                    monthlyReturnMin: 10.0,\n                    monthlyReturnMax: 15.0\n                },\n                {\n                    minTHS: 50,\n                    maxTHS: 999999,\n                    dailyReturnMin: 0.5,\n                    dailyReturnMax: 0.7,\n                    monthlyReturnMin: 10.0,\n                    monthlyReturnMax: 15.0\n                }\n            ];\n        }\n        // Find the appropriate range for the TH/s amount\n        const applicableRange = earningsRanges.find((range)=>thsAmount >= range.minTHS && thsAmount <= range.maxTHS);\n        let range;\n        if (applicableRange) {\n            range = applicableRange;\n        } else {\n            // Fallback to highest range if no match found\n            range = earningsRanges[earningsRanges.length - 1];\n        }\n        // Calculate 7-day average by simulating daily ROI variations\n        let totalROI = 0;\n        const days = 7;\n        for(let day = 0; day < days; day++){\n            // Generate a random ROI within the range for each day\n            const dailyROI = range.dailyReturnMin + Math.random() * (range.dailyReturnMax - range.dailyReturnMin);\n            totalROI += dailyROI;\n        }\n        // Return the 7-day average\n        return totalROI / days;\n    } catch (error) {\n        console.error('Error calculating 7-day average ROI:', error);\n        return 0.4; // Default fallback\n    }\n}\n// Update existing mining units when earnings configuration changes\nasync function updateExistingMiningUnitsROI() {\n    try {\n        console.log('Updating existing mining units with new ROI configuration...');\n        // Get all active mining units\n        const activeMiningUnits = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.findMany({\n            where: {\n                status: 'ACTIVE',\n                expiryDate: {\n                    gt: new Date()\n                }\n            }\n        });\n        console.log(`Found ${activeMiningUnits.length} active mining units to update`);\n        const updateResults = [];\n        for (const unit of activeMiningUnits){\n            try {\n                // Calculate new ROI based on current TH/s amount\n                const newROI = await calculateDynamicROI(unit.thsAmount);\n                // Update the mining unit with new ROI\n                await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.update({\n                    where: {\n                        id: unit.id\n                    },\n                    data: {\n                        dailyROI: newROI\n                    }\n                });\n                updateResults.push({\n                    unitId: unit.id,\n                    userId: unit.userId,\n                    thsAmount: unit.thsAmount,\n                    oldROI: unit.dailyROI,\n                    newROI\n                });\n                console.log(`Updated unit ${unit.id}: ${unit.dailyROI}% -> ${newROI}%`);\n            } catch (unitError) {\n                console.error(`Error updating unit ${unit.id}:`, unitError);\n            }\n        }\n        // Log the update process\n        await _database__WEBPACK_IMPORTED_MODULE_1__.systemLogDb.create({\n            action: 'MINING_UNITS_ROI_UPDATED',\n            details: {\n                unitsUpdated: updateResults.length,\n                totalUnits: activeMiningUnits.length,\n                updateResults,\n                timestamp: new Date().toISOString()\n            }\n        });\n        console.log(`Successfully updated ${updateResults.length} mining units with new ROI configuration`);\n        return {\n            success: true,\n            unitsUpdated: updateResults.length,\n            totalUnits: activeMiningUnits.length,\n            updateResults\n        };\n    } catch (error) {\n        console.error('Error updating existing mining units ROI:', error);\n        throw error;\n    }\n}\n// Calculate daily ROI for all active mining units using FIFO allocation\nasync function calculateDailyROI() {\n    try {\n        console.log('Starting daily ROI calculation with FIFO allocation...');\n        // Get all users with active mining units\n        const usersWithMiningUnits = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findMany({\n            where: {\n                miningUnits: {\n                    some: {\n                        status: 'ACTIVE',\n                        expiryDate: {\n                            gt: new Date()\n                        }\n                    }\n                }\n            },\n            include: {\n                miningUnits: {\n                    where: {\n                        status: 'ACTIVE',\n                        expiryDate: {\n                            gt: new Date()\n                        }\n                    }\n                }\n            }\n        });\n        console.log(`Found ${usersWithMiningUnits.length} users with active mining units`);\n        const results = [];\n        for (const user of usersWithMiningUnits){\n            try {\n                // Calculate total daily earnings for this user\n                let totalDailyEarnings = 0;\n                const unitEarnings = [];\n                for (const unit of user.miningUnits){\n                    const dailyEarnings = unit.investmentAmount * unit.dailyROI / 100;\n                    totalDailyEarnings += dailyEarnings;\n                    unitEarnings.push({\n                        unitId: unit.id,\n                        thsAmount: unit.thsAmount,\n                        dailyEarnings\n                    });\n                }\n                if (totalDailyEarnings > 0) {\n                    // Create transaction first with full amount\n                    const transaction = await _database__WEBPACK_IMPORTED_MODULE_1__.transactionDb.create({\n                        userId: user.id,\n                        type: 'MINING_EARNINGS',\n                        amount: totalDailyEarnings,\n                        description: `Daily mining earnings - Total: ${unitEarnings.map((u)=>`${u.thsAmount} TH/s`).join(', ')}`,\n                        status: 'PENDING'\n                    });\n                    // Allocate earnings to mining units using FIFO logic\n                    const allocationSummary = await (0,_miningUnitEarnings__WEBPACK_IMPORTED_MODULE_2__.allocateEarningsToUnits)(user.id, totalDailyEarnings, 'MINING_EARNINGS', transaction.id, 'Daily mining ROI earnings');\n                    // Update transaction amount to reflect only what was actually allocated\n                    if (allocationSummary.totalAllocated !== totalDailyEarnings) {\n                        await _database__WEBPACK_IMPORTED_MODULE_1__.transactionDb.updateStatus(transaction.id, 'PENDING', {\n                            amount: allocationSummary.totalAllocated,\n                            description: `Daily mining earnings - Allocated: ${allocationSummary.totalAllocated}, Discarded: ${allocationSummary.totalDiscarded} (capacity limits)`\n                        });\n                    }\n                    results.push({\n                        userId: user.id,\n                        totalEarnings: allocationSummary.totalAllocated,\n                        allocations: allocationSummary.allocations,\n                        unitsProcessed: user.miningUnits.length,\n                        discardedAmount: allocationSummary.totalDiscarded\n                    });\n                    console.log(`Allocated ${allocationSummary.totalAllocated} of ${totalDailyEarnings} mining earnings to ${allocationSummary.allocations.length} units for user ${user.id}`);\n                    if (allocationSummary.totalDiscarded > 0) {\n                        console.log(`Discarded ${allocationSummary.totalDiscarded} excess mining earnings due to capacity limits for user ${user.id}`);\n                    }\n                }\n            } catch (userError) {\n                console.error(`Error processing mining earnings for user ${user.id}:`, userError);\n            }\n        }\n        // Log the daily ROI calculation\n        await _database__WEBPACK_IMPORTED_MODULE_1__.systemLogDb.create({\n            action: 'DAILY_ROI_CALCULATED',\n            details: {\n                usersProcessed: results.length,\n                totalEarnings: results.reduce((sum, r)=>sum + r.totalEarnings, 0),\n                totalAllocations: results.reduce((sum, r)=>sum + r.allocations.length, 0),\n                timestamp: new Date().toISOString()\n            }\n        });\n        console.log(`Daily ROI calculation completed. Processed ${results.length} users with FIFO allocation.`);\n        return results;\n    } catch (error) {\n        console.error('Daily ROI calculation error:', error);\n        throw error;\n    }\n}\n// Process weekly earnings distribution (Saturday 15:00 UTC)\nasync function processWeeklyEarnings() {\n    try {\n        console.log('Starting weekly earnings distribution...');\n        // Get all pending mining earnings\n        const pendingEarnings = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.findMany({\n            where: {\n                type: 'MINING_EARNINGS',\n                status: 'PENDING'\n            },\n            include: {\n                user: true\n            }\n        });\n        console.log(`Found ${pendingEarnings.length} pending earnings transactions`);\n        const userEarnings = new Map();\n        // Group earnings by user\n        for (const transaction of pendingEarnings){\n            const currentTotal = userEarnings.get(transaction.userId) || 0;\n            userEarnings.set(transaction.userId, currentTotal + transaction.amount);\n        }\n        const results = [];\n        // Process each user's earnings\n        for (const [userId, totalEarnings] of userEarnings){\n            try {\n                // Mark all pending transactions as completed\n                await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.updateMany({\n                    where: {\n                        userId,\n                        type: 'MINING_EARNINGS',\n                        status: 'PENDING'\n                    },\n                    data: {\n                        status: 'COMPLETED'\n                    }\n                });\n                results.push({\n                    userId,\n                    totalEarnings\n                });\n            } catch (userError) {\n                console.error(`Error processing earnings for user ${userId}:`, userError);\n            }\n        }\n        // Log the weekly distribution\n        await _database__WEBPACK_IMPORTED_MODULE_1__.systemLogDb.create({\n            action: 'WEEKLY_EARNINGS_DISTRIBUTED',\n            details: {\n                usersProcessed: results.length,\n                totalDistributed: results.reduce((sum, r)=>sum + r.totalEarnings, 0),\n                transactionsProcessed: pendingEarnings.length,\n                timestamp: new Date().toISOString()\n            }\n        });\n        console.log(`Weekly earnings distribution completed. Processed ${results.length} users.`);\n        return results;\n    } catch (error) {\n        console.error('Weekly earnings distribution error:', error);\n        throw error;\n    }\n}\n// Check and expire mining units that have reached 24 months\nasync function expireOldMiningUnits() {\n    try {\n        console.log('Checking for expired mining units...');\n        const expiredUnits = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.findMany({\n            where: {\n                status: 'ACTIVE',\n                expiryDate: {\n                    lte: new Date()\n                }\n            },\n            include: {\n                user: {\n                    select: {\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n        console.log(`Found ${expiredUnits.length} units to expire`);\n        for (const unit of expiredUnits){\n            await _database__WEBPACK_IMPORTED_MODULE_1__.miningUnitDb.expireUnit(unit.id);\n            // Note: User active status is now computed dynamically\n            await _database__WEBPACK_IMPORTED_MODULE_1__.systemLogDb.create({\n                action: 'MINING_UNIT_EXPIRED',\n                userId: unit.userId,\n                details: {\n                    miningUnitId: unit.id,\n                    reason: '24_months_reached',\n                    totalEarned: unit.totalEarned,\n                    investmentAmount: unit.investmentAmount\n                }\n            });\n            // Send email notification\n            try {\n                const { emailNotificationService } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/nodemailer\"), __webpack_require__.e(\"_rsc_src_lib_emailNotificationService_ts\")]).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/emailNotificationService */ \"(rsc)/./src/lib/emailNotificationService.ts\"));\n                const totalEarnings = unit.miningEarnings + unit.referralEarnings + unit.binaryEarnings;\n                await emailNotificationService.sendMiningUnitExpiryNotification({\n                    userId: unit.userId,\n                    email: unit.user.email,\n                    firstName: unit.user.firstName,\n                    lastName: unit.user.lastName,\n                    thsAmount: unit.thsAmount,\n                    investmentAmount: unit.investmentAmount,\n                    totalEarned: totalEarnings,\n                    purchaseDate: unit.createdAt.toISOString(),\n                    expiryDate: unit.expiryDate.toISOString(),\n                    expiryReason: 'TIME_LIMIT'\n                });\n            } catch (emailError) {\n                console.error('Failed to send mining unit expiry email:', emailError);\n            // Don't fail the expiry if email fails\n            }\n        }\n        return expiredUnits.length;\n    } catch (error) {\n        console.error('Mining unit expiry check error:', error);\n        throw error;\n    }\n}\n// Get mining statistics\nasync function getMiningStats() {\n    try {\n        const stats = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.$transaction([\n            // Total TH/s sold\n            _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.aggregate({\n                _sum: {\n                    thsAmount: true\n                }\n            }),\n            // Active TH/s\n            _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.aggregate({\n                where: {\n                    status: 'ACTIVE'\n                },\n                _sum: {\n                    thsAmount: true\n                }\n            }),\n            // Total investment\n            _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.aggregate({\n                _sum: {\n                    investmentAmount: true\n                }\n            }),\n            // Total earnings distributed\n            _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.aggregate({\n                where: {\n                    type: 'MINING_EARNINGS',\n                    status: 'COMPLETED'\n                },\n                _sum: {\n                    amount: true\n                }\n            }),\n            // Active mining units count\n            _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.count({\n                where: {\n                    status: 'ACTIVE'\n                }\n            }),\n            // Total mining units count\n            _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.count()\n        ]);\n        return {\n            totalTHSSold: stats[0]._sum.thsAmount || 0,\n            activeTHS: stats[1]._sum.thsAmount || 0,\n            totalInvestment: stats[2]._sum.investmentAmount || 0,\n            totalEarningsDistributed: stats[3]._sum.amount || 0,\n            activeMiningUnits: stats[4],\n            totalMiningUnits: stats[5]\n        };\n    } catch (error) {\n        console.error('Mining stats error:', error);\n        throw error;\n    }\n}\n// Calculate user's estimated earnings\nasync function calculateEstimatedEarnings(userId) {\n    try {\n        const activeMiningUnits = await _database__WEBPACK_IMPORTED_MODULE_1__.miningUnitDb.findActiveByUserId(userId);\n        if (activeMiningUnits.length === 0) {\n            return {\n                next7Days: 0,\n                next30Days: 0,\n                next365Days: 0,\n                next2Years: 0\n            };\n        }\n        let totalDaily = 0;\n        for (const unit of activeMiningUnits){\n            const dailyEarnings = unit.investmentAmount * unit.dailyROI / 100;\n            const maxEarnings = unit.investmentAmount * 5;\n            const remainingEarnings = maxEarnings - unit.totalEarned;\n            // Use the lower of daily earnings or remaining earnings\n            totalDaily += Math.min(dailyEarnings, remainingEarnings);\n        }\n        return {\n            next7Days: totalDaily * 7,\n            next30Days: totalDaily * 30,\n            next365Days: totalDaily * 365,\n            next2Years: totalDaily * 730\n        };\n    } catch (error) {\n        console.error('Estimated earnings calculation error:', error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mining.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/miningUnitEarnings.ts":
/*!***************************************!*\
  !*** ./src/lib/miningUnitEarnings.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   allocateEarningsToUnits: () => (/* binding */ allocateEarningsToUnits),\n/* harmony export */   calculateRemainingCapacity: () => (/* binding */ calculateRemainingCapacity),\n/* harmony export */   expireMiningUnit: () => (/* binding */ expireMiningUnit),\n/* harmony export */   getActiveMiningUnitsFIFO: () => (/* binding */ getActiveMiningUnitsFIFO),\n/* harmony export */   getMiningUnitEarningsHistory: () => (/* binding */ getMiningUnitEarningsHistory),\n/* harmony export */   getUserMiningUnitsWithEarnings: () => (/* binding */ getUserMiningUnitsWithEarnings),\n/* harmony export */   shouldExpireUnit: () => (/* binding */ shouldExpireUnit)\n/* harmony export */ });\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/database */ \"(rsc)/./src/lib/database.ts\");\n\n\n/**\n * Get active mining units for a user ordered by creation date (FIFO)\n */ async function getActiveMiningUnitsFIFO(userId) {\n    return await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.findMany({\n        where: {\n            userId,\n            status: 'ACTIVE',\n            expiryDate: {\n                gt: new Date()\n            }\n        },\n        orderBy: {\n            createdAt: 'asc'\n        }\n    });\n}\n/**\n * Calculate remaining earning capacity for a mining unit (5x - current earnings)\n */ function calculateRemainingCapacity(unit) {\n    const maxEarnings = unit.investmentAmount * 5;\n    const currentTotalEarnings = unit.miningEarnings + unit.referralEarnings + unit.binaryEarnings;\n    return Math.max(0, maxEarnings - currentTotalEarnings);\n}\n/**\n * Check if a mining unit should expire based on 5x earnings\n */ function shouldExpireUnit(unit) {\n    const maxEarnings = unit.investmentAmount * 5;\n    const currentTotalEarnings = unit.miningEarnings + unit.referralEarnings + unit.binaryEarnings;\n    return currentTotalEarnings >= maxEarnings;\n}\n/**\n * Allocate earnings to mining units using FIFO logic\n * Returns summary showing allocations and any discarded excess amounts\n */ async function allocateEarningsToUnits(userId, totalAmount, earningType, transactionId, description) {\n    const activeMiningUnits = await getActiveMiningUnitsFIFO(userId);\n    if (activeMiningUnits.length === 0) {\n        throw new Error('No active mining units found for earnings allocation');\n    }\n    const allocations = [];\n    let remainingAmount = totalAmount;\n    for (const unit of activeMiningUnits){\n        if (remainingAmount <= 0) break;\n        const remainingCapacity = calculateRemainingCapacity(unit);\n        if (remainingCapacity <= 0) {\n            continue;\n        }\n        // Allocate the minimum of remaining amount or remaining capacity\n        const allocationAmount = Math.min(remainingAmount, remainingCapacity);\n        if (allocationAmount > 0) {\n            // Update the mining unit earnings based on type\n            const updateData = {};\n            switch(earningType){\n                case 'MINING_EARNINGS':\n                    updateData.miningEarnings = {\n                        increment: allocationAmount\n                    };\n                    break;\n                case 'DIRECT_REFERRAL':\n                    updateData.referralEarnings = {\n                        increment: allocationAmount\n                    };\n                    break;\n                case 'BINARY_BONUS':\n                    updateData.binaryEarnings = {\n                        increment: allocationAmount\n                    };\n                    break;\n            }\n            // Update total earned for legacy compatibility\n            updateData.totalEarned = {\n                increment: allocationAmount\n            };\n            await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.update({\n                where: {\n                    id: unit.id\n                },\n                data: updateData\n            });\n            // Create earnings allocation record\n            await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnitEarningsAllocation.create({\n                data: {\n                    miningUnitId: unit.id,\n                    transactionId,\n                    earningType,\n                    amount: allocationAmount,\n                    description\n                }\n            });\n            allocations.push({\n                miningUnitId: unit.id,\n                amount: allocationAmount,\n                remainingCapacity: remainingCapacity - allocationAmount\n            });\n            remainingAmount -= allocationAmount;\n            // Check if unit should expire after this allocation\n            const updatedUnit = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.findUnique({\n                where: {\n                    id: unit.id\n                }\n            });\n            if (updatedUnit && shouldExpireUnit(updatedUnit)) {\n                await expireMiningUnit(unit.id, '5x_investment_reached');\n            }\n        }\n    }\n    const totalAllocated = totalAmount - remainingAmount;\n    const totalDiscarded = remainingAmount;\n    // If there's still remaining amount, it means all units are at capacity\n    if (remainingAmount > 0) {\n        console.warn(`Unable to allocate ${remainingAmount} to mining units - all units at capacity. Amount discarded.`);\n        // Log this situation\n        await _lib_database__WEBPACK_IMPORTED_MODULE_1__.systemLogDb.create({\n            action: 'EARNINGS_ALLOCATION_OVERFLOW',\n            userId,\n            details: {\n                totalAmount,\n                allocatedAmount: totalAllocated,\n                overflowAmount: totalDiscarded,\n                earningType,\n                reason: 'all_units_at_capacity',\n                note: 'Excess amount discarded as per mining unit capacity limits'\n            }\n        });\n    }\n    return {\n        allocations,\n        totalAllocated,\n        totalDiscarded,\n        allocationSuccess: totalDiscarded === 0\n    };\n}\n/**\n * Expire a mining unit and log the action\n */ async function expireMiningUnit(unitId, reason) {\n    const unit = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.findUnique({\n        where: {\n            id: unitId\n        },\n        include: {\n            user: {\n                select: {\n                    email: true,\n                    firstName: true,\n                    lastName: true\n                }\n            }\n        }\n    });\n    if (!unit) {\n        throw new Error(`Mining unit ${unitId} not found`);\n    }\n    await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.update({\n        where: {\n            id: unitId\n        },\n        data: {\n            status: 'EXPIRED'\n        }\n    });\n    const totalEarnings = unit.miningEarnings + unit.referralEarnings + unit.binaryEarnings;\n    await _lib_database__WEBPACK_IMPORTED_MODULE_1__.systemLogDb.create({\n        action: 'MINING_UNIT_EXPIRED',\n        userId: unit.userId,\n        details: {\n            miningUnitId: unitId,\n            reason,\n            totalEarned: totalEarnings,\n            miningEarnings: unit.miningEarnings,\n            referralEarnings: unit.referralEarnings,\n            binaryEarnings: unit.binaryEarnings,\n            investmentAmount: unit.investmentAmount,\n            multiplier: totalEarnings / unit.investmentAmount\n        }\n    });\n    // Send email notification\n    try {\n        const { emailNotificationService } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/nodemailer\"), __webpack_require__.e(\"_rsc_src_lib_emailNotificationService_ts\")]).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/emailNotificationService */ \"(rsc)/./src/lib/emailNotificationService.ts\"));\n        await emailNotificationService.sendMiningUnitExpiryNotification({\n            userId: unit.userId,\n            email: unit.user.email,\n            firstName: unit.user.firstName,\n            lastName: unit.user.lastName,\n            thsAmount: unit.thsAmount,\n            investmentAmount: unit.investmentAmount,\n            totalEarned: totalEarnings,\n            purchaseDate: unit.createdAt.toISOString(),\n            expiryDate: unit.expiryDate.toISOString(),\n            expiryReason: reason === '24_months_reached' ? 'TIME_LIMIT' : 'RETURN_LIMIT'\n        });\n    } catch (emailError) {\n        console.error('Failed to send mining unit expiry email:', emailError);\n    // Don't fail the expiry if email fails\n    }\n    console.log(`Mining unit ${unitId} expired due to ${reason}. Total earnings: ${totalEarnings}`);\n}\n/**\n * Get detailed earnings breakdown for a user's mining units\n */ async function getUserMiningUnitsWithEarnings(userId) {\n    return await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.findMany({\n        where: {\n            userId\n        },\n        orderBy: {\n            createdAt: 'asc'\n        }\n    });\n}\n/**\n * Get earnings allocation history for a mining unit\n */ async function getMiningUnitEarningsHistory(miningUnitId) {\n    return await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnitEarningsAllocation.findMany({\n        where: {\n            miningUnitId\n        },\n        include: {\n            transaction: true\n        },\n        orderBy: {\n            allocatedAt: 'desc'\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/miningUnitEarnings.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFFOUMsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsR0FBRztBQUVuRSxJQUFJSSxJQUFxQyxFQUFFSCxnQkFBZ0JFLE1BQU0sR0FBR0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZHJlYW1cXERlc2t0b3BcXEhhc2hfTWluaW5nc1xcaGFzaGNvcmV4XFxzcmNcXGxpYlxccHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50JztcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XG59O1xuXG5leHBvcnQgY29uc3QgcHJpc21hID0gZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/PyBuZXcgUHJpc21hQ2xpZW50KCk7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hO1xuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJwcm9jZXNzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/zod","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fearnings%2Froute&page=%2Fapi%2Fearnings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fearnings%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();